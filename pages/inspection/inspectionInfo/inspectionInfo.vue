<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 bg-white border-right-line">
        <div class="h-[46px] w-ful bg-image-url">
          <span class="text-[16px] text-[#00190c] leading-[46px] ml-[32px]">验标信息</span>
        </div>
        <div>
          <span class="text-[14px] text-[rgba(0,0,0,0.60)] leading-[46px] ml-[32px]">验标任务号：</span>
          <span class="text-[14px] text-[#333333]">{{ baseData.insrCheckNo }}</span>
        </div>
        <a-spin :spinning="loading">
          <a-form :colon="false">
            <div class="p-16px space-y-16px pt-[0px]">
              <InfoGroup id="basic-info" title="基本信息">
                <a-form ref="searchForm" :colon="false" class="flex-grow" :model="baseData" :label-col="{ style: { width: pxToRem(80) } }">
                  <div class="grid grid-cols-3 gap-x-16px">
                    <a-form-item label="出单机构" name="departmentName">{{ baseData.departmentCode }}-{{ baseData.departmentName }}</a-form-item>
                    <a-form-item label="业务员信息" name="createdBy">{{ baseData.createdBy }}</a-form-item>
                    <a-form-item label="标的类型" name="objectAddress">{{ baseData.riskTypeNameAll }}</a-form-item>
                  </div>
                  <div class="grid grid-cols-3 gap-x-16px">
                    <a-form-item label="产品名称" name="insuredName">{{ baseData.productName }}</a-form-item>
                    <a-form-item label="标的地址" name="lastRiskType">{{ baseData.objectAddress }}</a-form-item>
                  </div>
                  <div class="grid grid-cols-3 gap-x-16px">
                    <a-form-item label="保险数量" name="createDate">
                      <span>{{ baseData.insureNum }}</span>
                      <span>{{ baseData.insureUnit }}</span>
                    </a-form-item>
                    <a-form-item label="承保户数" name="createDate">
                      <span>{{ baseData.farmerCount }}</span>
                    </a-form-item>
                  </div>
                  <template v-if="insuredExpand">
                    <div class="grid grid-cols-3 gap-x-16px">
                      <a-form-item label="补贴类型" name="riskCheckStatus">
                        <span>{{ baseData.subsidyTypeName }}</span>
                      </a-form-item>
                      <a-form-item v-if="showCustomerAndRisk" label="客户类型" name="riskCheckStatus">
                        <span>{{ baseData.customerTypeName }}</span>
                      </a-form-item>
                      <a-form-item v-if="showCustomerAndRisk" label="风险程度" name="riskCheckStatus">
                        <span>{{ baseData.riskRate }}</span>
                      </a-form-item>
                    </div>
                    <div class="grid grid-cols-3 gap-x-16px">
                      <a-form-item label="是否共保" name="riskCheckStatus">
                        <span>{{ baseData.isCoinsurance }}</span>
                      </a-form-item>
                      <a-form-item v-if="baseData.isCoinsurance !== '否'" label="是否主承" name="riskCheckStatus">
                        <span>{{ baseData.isMainUnderwrite }}</span>
                      </a-form-item>
                      <a-form-item label="投保方式" name="riskCheckStatus">
                        <span>{{ baseData.applyMethodTypeName }}</span>
                      </a-form-item>
                    </div>
                  </template>
                </a-form>
                <div :class="['flex', 'justify-end', 'items-center']">
                  <div :class="['flex', 'items-center', 'space-x-[4px]', 'text-[#576B95]', 'cursor-pointer']" @click="handleExpand()">
                    <VueIcon :icon="insuredExpand ? IconChevronLeftDoubleFont : IconChevronRightDoubleFont" class="rotate-90" />
                    <span>{{ insuredExpand ? '收起' : '展示' }}更多</span>
                  </div>
                </div>
              </InfoGroup>
              <InfoGroup id="publicity-request" title="验标要求">
                <a-form ref="searchForm" :colon="false" class="flex-grow" :model="baseData" :label-col="{ style: { width: pxToRem(80) } }">
                  <div class="grid grid-cols-3 gap-x-16px">
                    <a-form-item label="应采户数" name="departmentCode">{{ baseData.farmerShapeRequiredAmount }}</a-form-item>
                    <a-form-item label="应拍户数" name="insureDate">{{ baseData.farmerPhotoRequiredAmount }}</a-form-item>
                  </div>
                </a-form>
              </InfoGroup>
              <InfoGroup id="publicity-dispose" title="验标处理">
                <div>
                  <div v-if="!isHiddenProcess" class="flex">
                    <div class="block bg-[#fff] py-[16px] px-[18px] w-[50%] mr-[10px]">
                      <div class="form-title">
                        <span>验标地块农户进度</span>
                      </div>
                      <a-progress :percent="disposeGather.farmerShapedRatio" :size="[16, 16]" />
                      <div class="flex items-center justify-center">
                        <p class="flex items-center mr-[20px]">
                          <span class="inline-block w-[12px] h-[12px] bg-[#07C160] rounded-[2px] mr-[6px] border border-[rgba(0,168,80,1)]" />
                          <span>已验</span>
                          <span>{{ disposeGather.farmerShapedAmount }}</span>
                        </p>
                        <p class="flex items-center">
                          <span class="inline-block w-[12px] h-[12px] bg-[rgba(7,193,96,0.09)] rounded-[2px] mr-[6px] border border-[rgba(7,193,96,0.15)]" />
                          <span>未验</span>
                          <span>{{ disposeGather.farmerUnShapeAmount }}</span>
                        </p>
                      </div>
                    </div>
                    <div class="block bg-[#fff] py-[16px] px-[18px] w-[50%] ml-[10px]">
                      <div class="form-title">
                        <span>验标拍照农户进度</span>
                      </div>
                      <a-progress :percent="disposeGather.farmerPhotoedRatio" :size="[16, 16]" />
                      <div class="flex items-center justify-center">
                        <p class="flex items-center mr-[20px]">
                          <span class="inline-block w-[12px] h-[12px] bg-[#07C160] rounded-[2px] mr-[6px]" />
                          <span>已验</span>
                          <span>{{ disposeGather.farmerPhotoedAmount }}</span>
                        </p>
                        <p class="flex items-center">
                          <span class="inline-block w-[12px] h-[12px] bg-[rgba(7,193,96,0.09)] rounded-[2px] mr-[6px]" />
                          <span>未验</span>
                          <span>{{ disposeGather.farmerUnPhotoAmount }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="mt-[20px] bg-[#fff] px-[16px] pt-[12px]">
                    <div v-if="!isHiddenProcess" class="flex w-[100%] items-center justify-between mb-[16px]">
                      <div class="form-title">
                        <span>验标农户列表</span>
                      </div>
                      <a-button :loading="btnLoading" type="primary" @click="exportList">导出</a-button>
                    </div>
                    <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loadingTableData" :scroll="{ x: 'max-content' }">
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'insuranceBeginDate'">{{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }} </template>
                        <template v-if="column.dataIndex === 'operation'">
                          <!-- 操作区域 -->
                          <div>
                            <a-button type="link" class="" @click="handlerBaseInfo(record.insrCheckTaskNo, record.idInsrCheckFarmer, record.insuranceType, record.riskObjectNo)">基础信息</a-button>
                            <a-button type="link" class="mx-[10px]" @click="handlerTargetScope(record)">标的范围</a-button>
                            <a-button type="link" @click="handlerTargetPhoto(record.farmerId, record.insrCheckTaskNo, record.riskObjectNo)">标的照片</a-button>
                          </div>
                        </template>
                      </template>
                    </a-table>
                  </div>
                  <!-- 审核模块 -->
                  <div v-if="route.query?.isAudit === 'isAudit'" class="mt-[20px] bg-[#fff] px-[16px] py-[12px]">
                    <div class="flex w-[100%] items-center justify-between mb-[16px]">
                      <div class="form-title">
                        <span>审核</span>
                      </div>
                    </div>
                    <a-radio-group v-model:value="auditTaskInfo.approveStatus" name="radioGroup" @change="changeApproveStatus">
                      <a-radio value="B4">通过</a-radio>
                      <a-radio value="B7">不通过</a-radio>
                    </a-radio-group>
                    <a-textarea v-model:value="auditTaskInfo.approveResultDesc" placeholder="请输入原因" :rows="2" />
                  </div>
                  <div v-else-if="baseData.approvalStatus === '2' || route.query?.statusCode === 'B4' || route.query?.statusCode === 'B7'" class="mt-[20px] bg-[#fff] px-[16px] py-[12px]">
                    <div class="flex w-[100%] items-center justify-between mb-[16px]">
                      <div class="form-title">
                        <span>审核</span>
                      </div>
                    </div>
                    <div class="flex mb-[10px]">
                      <div class="w-[80px] shrink-0">核保结论：</div>
                      <div class="grow">{{ baseData.approvalStatus === '2' || route.query?.statusCode === 'B7' ? '不通过' : '通过' }}</div>
                    </div>
                    <div class="flex">
                      <div class="w-[80px] shrink-0">核保意见：</div>
                      <div class="grow">
                        <p class="m-0">{{ baseData.approvalOpinion }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </InfoGroup>
            </div>
          </a-form>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer id="operation-info" class="footer-wrapper space-x-8px">
      <a-button type="primary" @click="handlerBack">返回</a-button>
      <a-button v-if="route.query?.isAudit === 'isAudit'" type="primary" @click="submitTaskAudit">提交</a-button>
      <AuthButton code="checkLandView" type="primary" :disabled="loading" @click="enterCheckMassif">验标地块全景</AuthButton>
      <AuthButton code="downloadReport" type="primary" :disabled="loading" @click="downloadReport">下载报告</AuthButton>
    </footer>
    <!-- 基础信息弹窗 -->
    <a-modal v-model:open="baseInfoVisible" width="50%" :centered="true" title="基础信息" @ok="baseInfoVisible = false">
      <div class="bg-[#fff]">
        <p class="mt-[0px] mb-[8px]">
          <VueIcon :icon="IconJichuxinxiFont" />
          <span class="ml-[2px]">基础信息</span>
        </p>
        <div class="bg-[#F2F3F5] p-[15px]">
          <a-form v-if="baseInsuranceType === 'A' || baseInsuranceType === 'C'" :colon="false" class="flex-grow" :label-col="{ style: { width: pxToRem(90) } }">
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="播种品种" name="departmentCode">
                <span>{{ farmersInfo?.seedVariety }}</span>
              </a-form-item>
              <a-form-item label="实际种植规模" name="insureDate">
                <span>{{ farmersInfo?.actualPlantingScale }}</span>
                <span>{{ farmersInfo?.actualPlantingScaleUnit }}</span>
              </a-form-item>
              <a-form-item label="出苗期" name="policyNo">
                <span>{{ farmersInfo?.emergencePeriod }}</span>
              </a-form-item>
            </div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="种植密度" name="departmentCode">
                <span>{{ farmersInfo?.plantingDensity }}</span>
                <span>{{ farmersInfo?.plantingDensityUnit }}</span>
              </a-form-item>
            </div>
          </a-form>
          <a-form v-if="baseInsuranceType === 'B'" :colon="false" class="flex-grow" :label-col="{ style: { width: pxToRem(110) } }">
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="保险期限" name="departmentCode">
                <span>{{ farmersInfo?.insuranceBeginEndDate }}</span>
              </a-form-item>
              <a-form-item label="标的/作物名称" name="insureDate">
                <span>{{ farmersInfo?.riskTypeName }}</span>
              </a-form-item>
              <a-form-item label="投保数量/面积" name="policyNo">
                <span>{{ farmersInfo?.insuranceNums }}</span>
                <span>{{ farmersInfo?.insureUnit }}</span>
              </a-form-item>
            </div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="养殖地点" name="departmentCode">
                <span>{{ farmersInfo?.addressProvinceName }}</span>
                <span>{{ farmersInfo?.addressCityName }}</span>
                <span>{{ farmersInfo?.addressCountyName }}</span>
                <span>{{ farmersInfo?.addressTownName }}</span>
                <span>{{ farmersInfo?.addressVillageName }}</span>
              </a-form-item>
            </div>
          </a-form>
        </div>
      </div>
      <template #footer>
        <a-space>
          <a-button type="primary" @click="baseInfoVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal>
    <!-- 标的照片弹窗 -->
    <a-modal v-model:open="targetPhotoVisible" width="60%" :style="{ 'max-height': pxToRem(642), overflow: 'auto' }" :centered="true" title="标的照片" @ok="targetPhotoVisible = false">
      <div v-for="(item, index) in photoList" :key="index">
        <div class="form-title">
          <span>{{ item.itemName }}</span>
        </div>
        <div class="flex flex-wrap">
          <div v-for="(photo, phothIndex) in item.documentTypeList" :key="phothIndex" class="m-[5px]">
            <img :src="photo.smallDownloadUrl" class="w-[180px] h-[130px] p-[4px] box-border" @click="preview(phothIndex, item.documentTypeList)" />
          </div>
        </div>
      </div>
      <ImageViewer v-if="previewFileList.length > 0" ref="imgViewer" :list="previewFileList" :current="currentPhotoIndex" />
      <template #footer>
        <a-space>
          <a-button type="primary" @click="targetPhotoVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { IconChevronLeftDoubleFont, IconChevronRightDoubleFont, IconJichuxinxiFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import type { BaseInfoTitle, TableDataType, TargetPhotoType, FarmerListType, ProgressType, FarmerInfoType, PhotoListType, PreviewFileType } from './baseInfo';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { usePost, $postOnClient, useGet } from '@/composables/request';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';
import { $post } from '@/utils/request';
import { debounce } from 'lodash-es';

const route = useRoute();
const { gateWay, service } = useRuntimeConfig().public || {};
const getBaseInfoDetail = await usePost<BaseInfoTitle>(`${gateWay}${service.check}/web/check/queryCheckTaskDetail`); // 查询基本信息
const queryCheckFarmerList = await usePost<TableDataType>(`${gateWay}${service.check}/web/farmer/queryCheckFarmerList`); // 查询农户列表
const queryCheckFarmerRiskPhotoItems = await usePost<TargetPhotoType[]>(`${gateWay}${service.check}/web/farmer/queryCheckFarmerRiskPhotoItems`); // 查询标的照片列表
const queryCheckRatio = await usePost<ProgressType>(`${gateWay}${service.check}/web/check/queryCheckRatio`); // 查询进度
const queryCheckFarmerDetail = await usePost<FarmerInfoType>(`${gateWay}${service.check}/web/farmer/queryCheckFarmerDetail`); // 查询农户基本信息
const getCurrentTaskInfo = await useGet<Record<string, string>>(`${gateWay}${service.ums}/underwriteCommonApproval/getCurrentTaskInfo`); // 核保审核详情页调用获取当前审批taskId（进入审核页面调用，查看详情不要调用）
const otherUdwProcess = await usePost<string>(`${gateWay}${service.ums}/underwriteCommonApproval/otherUdwProcess`); // 审批接口
const queryUnderwriteRecordByType = await useGet<Record<string, string>>(`${gateWay}${service.ums}/underwrite/queryUnderwriteRecordByType`); // 审核结果
const insuredExpand = ref(false); // 被保险人信息展开/收起
const isHiddenProcess = ref<boolean>(false); // 是否隐藏进度
const auditTaskId = ref<string>(''); // 审批任务id
const auditTaskInfo = ref<Record<string, string>>({
  approveStatus: 'B4', // 状态
  approveResultDesc: '通过', // 原因
}); // 审批任务信息
const farmersInfo = ref<FarmerInfoType>({
  seedVariety: '', // 播种品种
  actualPlantingScale: '', // 实际种植规模
  actualPlantingScaleUnit: '', // 实际种植规模单位
  emergencePeriod: '', // 出苗期
  plantingDensity: '', // 种植密度
  plantingDensityUnit: '', // 种植密度单位
  insuranceBeginEndDate: '', // 养殖险日期
  riskTypeName: '', // 养殖险标的名称
  insuranceNums: '', // 保险数量
  insureUnit: '', // 单位
  addressProvinceName: '', // 省名
  addressCityName: '', // 市名
  addressCountyName: '', // 区县名
  addressTownName: '', // 乡镇名
  addressVillageName: '', // 村名
});
const dataSource = ref<FarmerListType[]>([]); // 表格数据
// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#basic-info', title: '基本信息' },
  { key: '3', href: '#publicity-request', title: '验标要求' },
  { key: '3', href: '#publicity-dispose', title: '验标处理' },
]);
const baseInsuranceType = ref<string>('');
// 定义基本数据
const baseData = ref<BaseInfoTitle>({
  applyPolicyNo: '', // 投保单号
  insrCheckNo: '', // 验标任务号
  taskName: '', // 任务名称
  subsidyTypeName: '', // 补贴类型
  farmerCount: '', // 承保户数
  insureUnit: '', // 保险单位数量
  insureNum: '', // 保险数量
  objectAddress: '', // 标的完整地址
  departmentCode: '', // 机构编码
  departmentName: '', // 机构名称
  createdBy: '', // 业务员
  isMainUnderwrite: '', // 是否主承
  isCoinsurance: '', // 是否共保
  riskRate: 0, // 风险程度
  applyMethodTypeName: '', // 投保方式
  checkNum: '', // 应验面积
  checkCount: '', // 应验户数
  productName: '', // 产品明名称
  customerTypeName: '', // 客户类型
  insrCheckTaskNo: '', // 主验标任务号
  subjectTypeName: '', // 标的类型
  oneLevTargetName: '', // 一级标的
  twoLevTargetName: '', // 二级标的
  threeLevTargetName: '', // 三级标的
  fourLevTargetName: '', // 四级标的
  fiveLevTargetName: '', // 五级标的
  farmerPhotoRequiredAmount: '', // 农户应拍户数
  farmerShapeRequiredAmount: '', // 农户应拍户数
  approvalStatus: '', // 审核状态
  approvalOpinion: '', // 审核意见
});
// 定义验标处理数据
const disposeGather = ref<ProgressType>({
  farmerShapedAmount: '', // 地块农户已验
  farmerUnShapeAmount: '', // 地块农户未验
  farmerPhotoedAmount: '', // 农户已拍
  farmerUnPhotoAmount: '', // 农户未拍
  farmerPhotoedRatio: 0, // 农户已拍户数占应拍户数的比例:
  farmerShapedRatio: 0, // 农户已采户数占应采户数的比例:
});
const router = useRouter(); // 路由
const loading = ref(false); // 加载基础信息loading
const loadingTableData = ref(false); // 加载农户表格数据loading
const baseInfoVisible = ref<boolean>(false); // 基本信息弹窗
const targetPhotoVisible = ref<boolean>(false); // 标的照片弹窗
const scrollWrapper = ref(null);
const photoList = ref<TargetPhotoType[]>([]); // 标的照片列表
const currentPhotoIndex = ref(0); // 点击当前照片的index
const listColumns: TableColumnsType = [
  { title: '姓名', dataIndex: 'farmerName' },
  { title: '联系方式', dataIndex: 'phone' },
  { title: '标的名称', dataIndex: 'riskTypeName' },
  { title: '应验数量', dataIndex: 'checkNum' },
  { title: '已验数量', dataIndex: 'checkedNum' },
  { title: '验标状态', dataIndex: 'checkResultName' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 查询基本数据
const getBaseInfo = async () => {
  loading.value = true;
  const baseInfoResult = await getBaseInfoDetail.fetchData({ applyPolicyNo: route.query?.applyPolicyNo });
  if (baseInfoResult?.code === SUCCESS_CODE) {
    const { oneLevTargetName, twoLevTargetName, threeLevTargetName, fourLevTargetName, fiveLevTargetName } = baseInfoResult.data;
    baseInfoResult.data.subjectTypeName = `${oneLevTargetName ? oneLevTargetName + '-' : ''}${twoLevTargetName ? twoLevTargetName + '-' : ''}${threeLevTargetName ? threeLevTargetName + '-' : ''}${fourLevTargetName ? fourLevTargetName + '-' : ''}${fiveLevTargetName ? fiveLevTargetName : ''}`; // 拼接标的类型
    baseData.value = baseInfoResult.data;
  } else {
    message.warning(baseInfoResult?.msg);
  }
  loading.value = false;
};
// 默认不展示
const showCustomerAndRisk = ref<boolean>(false);
// 获取隐藏客户字段开关
const getCustomerSwitch = async () => {
  const url = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const params = {
    switchCode: 'CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH',
    allowanceTypeCode: baseData?.value?.subsidyTypeCode || '',
    agriculturalRiskObjectClassCode: baseData?.value?.fiveLevTargetCode || '',
    departmentNo: baseData?.value?.departmentCode,
  };
  try {
    const res = await $post(url, params);
    if (res && res.code === SUCCESS_CODE) {
      showCustomerAndRisk.value = res.data;
    } else {
      showCustomerAndRisk.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};
// 根据机构及标的地址控制开关显隐客户类型及风险程度字段
const debounceGetCustomerSwitch = debounce(getCustomerSwitch, 1000);
watch([() => baseData?.value?.subsidyTypeCode, () => baseData?.value?.departmentCode, () => baseData?.value?.fiveLevTargetCode], () => {
  if (baseData?.value?.departmentCode) {
    debounceGetCustomerSwitch();
  }
});
// 查询农户列表数据
const queryFarmersList = async () => {
  loadingTableData.value = true;
  try {
    const params = {
      applyPolicyNo: route.query?.applyPolicyNo,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await queryCheckFarmerList.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loadingTableData.value = false;
  }
};
// 进度查询
const queryProgress = async () => {
  try {
    const progressResult = await queryCheckRatio.fetchData({ applyPolicyNo: route.query?.applyPolicyNo });
    if (progressResult?.code === SUCCESS_CODE) {
      progressResult.data.farmerShapedRatio = progressResult.data.farmerShapedRatio * 100; // 返回数据需要放大100倍，组件默认加%
      progressResult.data.farmerPhotoedRatio = progressResult.data.farmerPhotoedRatio * 100; // 返回数据需要放大100倍，组件默认加%
      disposeGather.value = progressResult.data;
    } else {
      message.error(progressResult?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
const { pagination } = usePagination(queryFarmersList); // 分页查询
// 展开收起切换
const handleExpand = () => {
  insuredExpand.value = !insuredExpand.value;
};
// 点击基本信息按钮弹窗
const handlerBaseInfo = async (insrCheckTaskNo: string, idInsrCheckFarmer: string, insuranceType: string, riskObjectNo?: string) => {
  baseInsuranceType.value = insuranceType;
  try {
    const params = {
      insrCheckTaskNo: insrCheckTaskNo,
      idInsrCheckFarmer: idInsrCheckFarmer,
      riskType: insuranceType,
    };
    if (riskObjectNo) {
      params.riskObjectNo = riskObjectNo;
    }
    const farmerInfoResult = await queryCheckFarmerDetail.fetchData(params);
    if (farmerInfoResult?.code === SUCCESS_CODE) {
      farmersInfo.value = farmerInfoResult?.data;
    } else {
      message.error(farmerInfoResult?.msg);
    }
  } catch (error) {
    console.log(error);
  }
  baseInfoVisible.value = true;
};
// 进入标的范围
const handlerTargetScope = (record) => {
  router.push({
    name: 'targetScope',
    query: {
      farmerId: record.farmerId,
      insrCheckTaskNo: record.insrCheckTaskNo,
      village: baseData.value.village,
      villageName: baseData.value.objectAddress,
      hiddenList: 1,
    },
  });
};
// 点击标的照片按钮弹窗
const handlerTargetPhoto = async (farmerId: string, insrCheckTaskNo: string, riskObjectNo?: string) => {
  const params = {
    farmerId: farmerId,
    insrCheckTaskNo: insrCheckTaskNo,
  };
  // 多标的增加一个参数
  if (riskObjectNo) {
    params.riskObjectNo = riskObjectNo;
  }
  const targetPhotoResult = await queryCheckFarmerRiskPhotoItems.fetchData(params); // 获取照片接口
  if (targetPhotoResult?.code === SUCCESS_CODE) {
    targetPhotoResult.data.forEach((item) => {
      item.documentTypeList.forEach((el) => {
        el.thumbnail = el.smallDownloadUrl; // 滑动预览组件所需字段
        el.documentFormat = el.originName.split('.').pop(); // 后续后端会更改，不需要切割
      });
    });
    photoList.value = targetPhotoResult.data || [];
  } else {
    message.error(targetPhotoResult?.msg);
  }
  targetPhotoVisible.value = true;
};
const imgViewer = ref(); // 预览照片ref
const previewFileList = ref<PreviewFileType[]>([]); // 当前要预览的文件列表
// 预览照片
const preview = (photoIndex: number, fileList: PhotoListType[]) => {
  currentPhotoIndex.value = photoIndex; // 当前照片下标
  previewFileList.value = fileList.map((item) => {
    return {
      uploadPath: item.uploadPath as string, // iobs的key|原图url
      thumbnail: item.thumbnail as string, // 缩略图url
      documentFormat: item.documentFormat as string, // 文件类型 img/pdf/word/excel 4种类型，非后缀名
      bucketName: item.bucketName as string, // iobs桶，有外网桶和内网桶
      documentName: item.documentName,
    };
  });
  // 打开滑动照片
  if (imgViewer.value) {
    imgViewer.value?.openPreview();
  }
};
const getContainer = () => scrollWrapper.value || window; // 滚动数据
const handlerBack = () => router.go(-1); // 返回上一页
// 进入地块全景
const enterCheckMassif = () => {
  router.push({
    name: 'targetScope',
    query: {
      insrCheckTaskNo: baseData.value.insrCheckTaskNo,
      farmerListNo: baseData.value.farmerlistNo,
      village: baseData.value.village,
      villageName: baseData.value.objectAddress,
    },
  });
};
// 下载报告
const downloadReport = async () => {
  const params = {
    insrCheckTaskNo: baseData.value.insrCheckTaskNo,
  };
  await $postOnClient(`${gateWay}${service.check}/web/file/downloadCheckTaskReport`, params, {
    onResponse({ response }) {
      if (response._data instanceof Blob) {
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          const fileName = match ? decodeURI(match[1]) : '';
          downloadBlob(fileData, fileName, fileType);
        }
      } else {
        const { code, data, msg = '' } = response._data;
        if (code === SUCCESS_CODE) {
          message.success(data);
        } else if (msg) {
          message.error(msg);
        }
      }
    },
  });
};
// 获取审核taskId
const getAuditTaskId = async () => {
  try {
    const params = {
      voucherNo: route.query?.insrCheckNo,
      underwriteTypeCode: '3',
    };
    const res = await getCurrentTaskInfo.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      auditTaskId.value = res.data?.taskId;
    }
  } catch (error) {
    console.log(error);
  }
};
// 获取审核结果
const getAuditResult = async () => {
  try {
    const params = {
      voucherNo: route.query?.insrCheckNo,
      underwriteTypeCode: '3',
    };
    const res = await queryUnderwriteRecordByType.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      auditTaskInfo.value.approveStatus = res.data.agriculturalUnderwriteStatusCode; // 状态code
      auditTaskInfo.value.approveResultDesc = res.data.approveResultDesc; // 结论
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 提交任务审核
const submitTaskAudit = async () => {
  try {
    const params = {
      taskId: auditTaskId.value,
      underwriteTypeCode: '3', // 核保审核类型  3-验标后补审核 4-信息修改资料审核
      voucherNo: route.query?.insrCheckNo, // 业务凭证号（投保单号/保单号/清单号等）
      approveStatus: auditTaskInfo.value.approveStatus, // 审核状态、B4-同意、B7-不同意
      approveResultDesc: auditTaskInfo.value.approveResultDesc, // 审批意见
    };
    const auditResult = await otherUdwProcess.fetchData({ udwProcess: params });
    if (auditResult?.code === SUCCESS_CODE) {
      message.success('提交成功');
      router.go(-1);
    } else {
      message.warning(auditResult?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
const changeApproveStatus = () => {
  if (auditTaskInfo.value.approveStatus === 'B4') {
    auditTaskInfo.value.approveResultDesc = '通过';
  } else {
    auditTaskInfo.value.approveResultDesc = '不通过';
  }
};
const btnLoading = ref<boolean>(false);
// 导出
const exportList = async () => {
  try {
    const params = {
      applyPolicyNo: route.query?.applyPolicyNo,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    btnLoading.value = true;
    const fetchUrl = `${gateWay}${service.check}/web/farmer/exportCheckFarmerList`;
    await $postOnClient(fetchUrl, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('导出成功');
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } catch (err) {
    console.log(err);
  } finally {
    btnLoading.value = false;
  }
};
onMounted(() => {
  getBaseInfo(); // 查询基本信息
  queryFarmersList(); // 农户列表
  isHiddenProcess.value = route.query?.isHiddenProcess ? true : false; // 是否隐藏进度数据
  if (!isHiddenProcess.value) queryProgress(); // 隐藏进度条就不需要查询该接口
  if (route.query?.isAudit === 'isAudit') getAuditTaskId(); // 不是审批进来的不需要调用
  if (route.query?.statusCode === 'B4' || route.query?.statusCode === 'B7') getAuditResult(); // 只有通过(B4)，或者不通过(B7)且不是详情进来的才会调用结果
});
onActivated(() => {
  getBaseInfo(); // 查询基本信息
  queryFarmersList(); // 农户列表
  isHiddenProcess.value = route.query?.isHiddenProcess ? true : false; // 是否隐藏进度数据
  if (!isHiddenProcess.value) queryProgress(); // 隐藏进度条就不需要查询该接口
  if (route.query?.isAudit === 'isAudit') getAuditTaskId(); // 不是审批进来的不需要调用
  if (route.query?.statusCode === 'B4' || route.query?.statusCode === 'B7') getAuditResult(); // 只有通过(B4)，或者不通过(B7)且不是详情进来的才会调用结果
});
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;
  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
  :deep(.ant-progress-outer) {
    width: 95%;
  }
}
</style>
