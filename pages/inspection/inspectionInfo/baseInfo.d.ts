// 验标任务基础数据
export interface BaseInfoTitle {
  applyPolicyNo: string; // 投保单号
  insrCheckNo: string; // 验标任务号
  taskName: string; // 任务名称
  productName: string; // 产品名称
  subsidyTypeName: string; // 补贴类型
  farmerCount: string; // 承保户数
  insureUnit: string; // 保险单位数量
  insureNum: string; // 保险数量
  objectAddress: string; // 标的完整地址
  departmentCode: string; // 机构编码
  departmentName: string; // 机构名称
  createdBy: string; // 业务员
  isMainUnderwrite: string; // 是否主承
  isCoinsurance: string; // 是否共保
  riskRate: number; // 风险程度
  applyMethodTypeName: string; // 投保方式
  checkNum: string; // 应验面积
  checkCount: string; // 应验户数
  customerTypeName: string; // 客户类型
  insrCheckTaskNo: string; // 主验标任务号
  subjectTypeName: string; // 标的类型
  oneLevTargetName: string; // 一级标的
  twoLevTargetName: string; // 二级标的
  threeLevTargetName: string; // 三级标的
  fourLevTargetName: string; // 四级标的
  fiveLevTargetName: string; // 五级标的
  farmerShapeRequiredAmount: string; // 农户应采户数
  farmerPhotoRequiredAmount: string; // 农户应拍户数
  approvalOpinion: string; // 验标审核意见
  approvalStatus: '0' | '1' | '2' | '3' | ''; // 审核状态, 0 无审核，1 待审核，2 审核失败 ,3审核通过
}
// 农户列表字段
export interface FarmerListType {
  farmerName: string; // 姓名
  phone: string; // 联系方式
  riskTypeName: string; // 标的名称
  checkNum: string; // 应验数量
  checkedNum: string; // 已验数量
  checkResult: string; // 验标状态
  checkByName: string; // 任务处理人
  farmerId: string; // 农户id
  insrCheckTaskNo: string; // 验标任务号
}
// 农户列表数据接口返回
export interface TableDataType {
  records: FarmerListType[]; // 表格数据
  code: string; // 状态码
  total: number; // 总数
  current: number; // 当前页
  size: number; // 每页条数
}
// 标的照片列表
export interface TargetPhotoType {
  documentTypeList: PhotoListType[]; // 照片列表
  classCode?: string; // 险种大类
  uploadType?: string; // 文件类型
  itemName?: string; // 列表细项名称
}
// 照片值类型
export interface PhotoListType {
  smallDownloadUrl?: string; // url地址
  documentType?: string; // url地址
  thumbnail?: string; // 缩略图url地址
  uploadPath?: string; // 真实url地址
  documentFormat?: string; // 文件类型
  originName: string; // 文件名
  bucketName?: string; // 桶名
  documentName: string; // 文件名
}
// 下载报告类型
export interface ReportResultType {
  code: string; // 状态码
  data: string; // 下载链接
  msg: string; // 提示信息
}
//  验标进度类型
export interface ProgressType {
  farmerPhotoedRatio: number; // 农户已拍户数占应拍户数的比例:
  farmerShapedRatio: number; // 农户已采户数占应采户数的比例:
  farmerShapedAmount: string; // 地块农户已验
  farmerUnShapeAmount: string; // 地块农户未验
  farmerPhotoedAmount: string; // 农户已拍
  farmerUnPhotoAmount: string; // 农户未拍
  code?: string; // 状态码
  msg?: string; // 提示语
}
//  农户弹窗基础信息
export interface FarmerInfoType {
  seedVariety: string; // 播种品种
  actualPlantingScale: string; // 实际种植规模
  actualPlantingScaleUnit: string; // 实际种植规模单位
  emergencePeriod: string; // 出苗期
  plantingDensity: string; // 种植密度
  plantingDensityUnit: string; // 种植密度单位
  insuranceBeginEndDate?: string; // 养殖险日期
  riskTypeName?: string; // 养殖险标的名称
  insuranceNums?: string; // 保险数量
  insureUnit?: string; // 单位
  addressProvinceName?: string; // 省名
  addressCityName?: string; // 市名
  addressCountyName?: string; // 区县名
  addressTownName?: string; // 乡镇名
  addressVillageName?: string; // 村名
}
// 预览文件类型
export interface PreviewFileType {
  // 多文件预览
  uploadPath: string; // iobs的key|原图url
  thumbnail: string; // 缩略图url
  documentFormat: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
  bucketName: string; // iobs桶，有外网桶和内网桶
}
