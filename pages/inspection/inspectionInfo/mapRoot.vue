<template>
  <div class="m-[16px] page-container box-border flex flex-col">
    <div v-show="showVerificationArea" class="area-tips">
      <span>验标地块总面积：{{ totalAreaInMu }}</span>
    </div>
    <!-- 地块显示 -->
    <div id="agrmap" class="grow" />
    <!-- 地图图例和控制面板 -->
    <div class="map-legend">
      <div class="legend-title">
        <div>图层详情</div>
        <span @click="resetInit">重置</span>
      </div>

      <div class="legend-item">
        <div class="left-group">
          <img src="@/assets/images/map/quantusizhi.png" />
          <span>全图四至</span>
        </div>
        <a-switch v-model:checked="showRectangle" size="small" @change="drawRectangle" />
      </div>

      <div class="legend-item">
        <div class="left-group">
          <img src="@/assets/images/map/photoLocation.png" />
          <span>照片位置分布图</span>
        </div>
        <a-switch v-model:checked="showImageMarker" size="small" @change="drawImageMarker" />
      </div>

      <div class="legend-item">
        <div class="left-group">
          <img src="@/assets/images/map/padding.png" />
          <span>边距</span>
        </div>
        <a-switch v-model:checked="showDistance" size="small" @change="drawDistance" />
      </div>

      <div class="legend-item">
        <div class="left-group">
          <img src="@/assets/images/map/landArea.png" />
          <span>验标地块面积</span>
        </div>
        <a-switch v-model:checked="showVerificationArea" size="small" @change="toggleVerificationArea" />
      </div>

      <div v-if="route.query.hiddenList !== '1'" class="legend-item">
        <div class="left-group">
          <img src="@/assets/images/map/landLayer.png" />
          <span>清单地块</span>
        </div>
        <a-switch v-model:checked="showListParcels" size="small" @change="toggleListParcels" />
      </div>

      <!-- <div class="legend-item">
        <a-button type="primary" size="small" @click="clearAll">清理</a-button>
        <div class="area-text">地块总面积: {{ totalAreaInMu }}</div>
      </div> -->
    </div>

    <div class="map-legend-right">
      <div class="legend-title">图例</div>
      <div class="flex items-center justify-between mb-[4px]">
        <span class="text-[#bbc2d1] text-[12px]">验标地块</span>
        <img src="@/assets/images/map/line10.png" />
      </div>
      <div class="flex items-center justify-between mb-[4px]">
        <span class="text-[#bbc2d1] text-[12px]">清单地块</span>
        <img src="@/assets/images/map/line.png" />
      </div>
      <div class="flex items-center justify-between mb-[4px]">
        <span class="text-[#bbc2d1] text-[12px]">村边界</span>
        <img src="@/assets/images/map/line11.png" />
      </div>
    </div>

    <!-- 右侧农户清单面板 -->
    <div v-if="showListParcels" class="farmer-list-panel" :class="{ collapsed: isPanelCollapsed }">
      <!-- <div class="panel-header">
        <span>农户清单</span>
        <a-button type="text" @click="togglePanel">
          <template #icon>
            <icon-component :type="isPanelCollapsed ? 'left' : 'right'" />
          </template>
        </a-button>
      </div> -->
      <div v-if="!isPanelCollapsed" class="panel-content">
        <a-form>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="地区" :label-col="{ style: { width: '60px' } }">
                <a-input :value="route.query.villageName" :disabled="true"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="确权人" :label-col="{ style: { width: '60px' } }">
                <a-input v-model:value="searchForm.farmerName" placeholder="请输入确权人" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="证件">
                <a-input v-model:value="searchForm.accountNo" placeholder="请输入证件号码" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="action-buttons">
          <a-button class="reset-btn" @click="resetQuery">重置</a-button>
          <a-button type="primary" ghost @click="onSearch">查询</a-button>
        </div>
        <div class="table-title">农户清单列表</div>
        <a-table :columns="tableColumns" :data-source="tableData" :scroll="{ x: 'max-content' }" :pagination="pagination" size="small" :custom-row="addCustomRowProperty" @change="handleTableChange">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'farmerName'">
              <div class="flex items-center">
                <div>{{ text }}</div>
                <a-button v-if="record.authStatus === 1" class="ml-[8px]" type="link" @click.stop="jumpToFarmer(record)">流转</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <ImageModal v-model:show="imageModalShow" :list="imageModalList" />
  </div>
</template>

<script setup lang="ts">
import * as turf from '@turf/turf';
import { v4 as uuidv4 } from 'uuid';
import type { QueryCheckFarmerShapeListReqParams, AllCheckFile, AllCheckFileListKmeans } from './targetScope';
import ImageModal from './components/ImageModal.vue';
import type { TargetPhotoType } from './baseInfo';
import { usePost, $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

import EcosmapL from 'ecosmap-leaflet';
import 'ecosmap-leaflet/dist/main.css';

const route = useRoute();
const { gateWay, service } = useRuntimeConfig().public || {};
const queryCheckFarmerShapeListReq = await usePost(`${gateWay}${service.check}/web/shape/queryCheckFarmerShapeListForMap`); // 查询基本信息
const queryAllCheckFileListReq = await usePost(`${gateWay}${service.check}/web/file/queryAllCheckFileList`); // 查询基本信息
const leafletMap = shallowRef({});
const totalAreaInMu = ref();
const showRectangle = ref(false);
const showDistance = ref(false);
const showImageMarker = ref(false);
const showVerificationArea = ref(false);
const showListParcels = ref(false);
const allCheckFileList = ref<AllCheckFile[]>([]);
const allCheckFileListKmeansList = shallowRef<AllCheckFileListKmeans[]>([]);
const imageModalShow = ref(false);
const imageModalList = ref<TargetPhotoType[]>([]);

const landList = ref<{ landNoStr: string; areaInMu: string; farmerName: string; collectionAreaMu: string }[]>([]);
// 新增面板相关状态
const isPanelCollapsed = ref(false);
const searchForm = ref({
  farmerName: '',
  accountNo: '',
});

const onSearch = async () => {
  if (searchForm.value.farmerName || searchForm.value.accountNo) {
    let filterList = farmerLands.value;
    if (searchForm.value.farmerName) {
      filterList = filterList.filter((k) => k.farmerName.indexOf(searchForm.value.farmerName) > -1);
    }
    if (searchForm.value.accountNo) {
      filterList = filterList.filter((k) => k.authCertificateNo.indexOf(searchForm.value.accountNo) > -1);
    }
    try {
      await leafletMap.value.addActiveLandLayer({
        code: route.query.village,
        level: 5,
        landList: filterList,
      });
    } catch (err) {
      console.log(err);
    }
  }
};

const resetQuery = async () => {
  // 清空searchForm
  searchForm.value.farmerName = '';
  searchForm.value.accountNo = '';
  try {
    await leafletMap.value?.queryQGLandInfoData({
      code: route.query.village,
      level: 5,
      landList: toRaw(farmerLands.value),
      landText: ['landNoStr'],
      extraPopupInfo: ['default', 'insureNum', 'landType', 'insurantName', 'insurantCardNo', 'actualUsageName'],
      style: { fillColor: '#E3E3E3', fillOpacity: 0.2, weight: 1, color: '#E3E3E3' },
    });
  } catch (err) {
    console.log(err);
  }
};

const resetInit = () => {
  if (leafletMap.value) {
    // Reset all toggle states
    showVerificationArea.value = false;
    showListParcels.value = false;
    // Reset all map layers
    leafletMap.value.queryQGLandInfoData({ close: true });
    if (showRectangle.value) {
      showRectangle.value = false;
      leafletMap.value?.removeFourBoundary();
    }
    if (showDistance.value) {
      showDistance.value = false;
      leafletMap.value?.removeDistance();
    }
    if (showImageMarker.value) {
      showImageMarker.value = false;
      leafletMap.value?.removeImageMarker();
    }
    // Re-add the base land layer
    leafletMap.value?.addQGLandLayer({
      code: route.query.village,
      level: 5,
      landText: ['farmerName'],
      extraPopupInfo: ['farmerName', 'areaInMu', 'collectionAreaMu'],
      landList: toRaw(landList.value),
    });
  }
};

// 表格列定义
const tableColumns = [
  {
    title: '被保人',
    dataIndex: 'farmerName',
    key: 'farmerName',
  },
  {
    title: '证件号码',
    dataIndex: 'certificateNo',
    key: 'certificateNo',
  },
  {
    title: '地块数',
    dataIndex: 'shapeNum',
    key: 'shapeNum',
  },
  {
    title: '地块面积',
    dataIndex: 'landArea',
    key: 'landArea',
  },
  {
    title: '投保面积',
    dataIndex: 'insuranceNums',
    key: 'insuranceNums',
  },
];

// 模拟表格数据
const tableData = ref([]);
// 分页设置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`,
});

// 处理表格变化（分页、排序、筛选）
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
};

const router = useRouter();
const jumpToFarmer = (record) => {
  router.push({
    path: '/farmerInfo',
    query: {
      farmerListNo: route.query.farmerListNo,
      idFarmerlistCustomInfo: record?.idFarmerlistCustomInfo,
    },
  });
};

onBeforeMount(() => {
  getFarmerList();
});

onMounted(async () => {
  await Promise.all([createLeafletMap(), loadLandData()]);
  if (leafletMap.value) {
    try {
      await leafletMap.value?.addDistrictLayer({
        code: route.query.village,
        level: 5,
      });
    } catch (err) {
      console.error(err);
    }
    try {
      await leafletMap.value?.addQGLandLayer({
        code: route.query.village,
        level: 5,
        landText: ['farmerName'],
        extraPopupInfo: ['farmerName', 'areaInMu', 'collectionAreaMu'],
        landList: toRaw(landList.value),
      });
    } catch (err) {
      console.error(err);
    }
    try {
      await leafletMap.value?.addCheckPointLayer({ code: route.query.village, landList: toRaw(pointList.value), iconSize: [15, 18] });
    } catch (err) {
      console.error(err);
    }
  }
});

// Add watcher for query parameters
watch(
  () => [route.query.insrCheckTaskNo, route.query.farmerId],
  async (newVal, oldVal) => {
    if (newVal[0] !== oldVal[0] || newVal[1] !== oldVal[1]) {
      // Reset data
      farmerLands.value = [];
      tableData.value = [];
      landList.value = [];

      // Reload data
      await getFarmerList();
      await loadLandData();

      // Reset map
      resetInit();

      // Re-add layers
      if (leafletMap.value) {
        // await leafletMap.value?.addQueryLandLayer({
        //   code: route.query.village,
        //   level: 5,
        //   landText: ['farmerName'],
        //   extraPopupInfo: ['farmerName', 'areaInMu', 'collectionAreaMu'],
        //   landList: landList.value,
        // });
        await leafletMap.value?.addDistrictLayer({
          code: route.query.village,
          level: 5,
        });
      }
    }
  },
  { deep: true },
);

const createLeafletMap = async () => {
  const url = gateWay + service.farmer + '/ecos/getEcosUserAuthParam';
  const { ecosEnv } = useRuntimeConfig().public;
  try {
    const res = await $getOnClient(url);
    if (res) {
      const { umAccount, depId, systemId, randomNumber, unixTimeStamp, signature } = res.data;
      leafletMap.value = await new EcosmapL({
        id: 'agrmap',
        env: ecosEnv,
        loginInfo: {
          umAccount,
          depId,
          systemId,
          randomNumber,
          unixTimeStamp,
          signature,
        },
        config: {
          zoomControl: false,
        },
      }).init();
      // console.log('[ leafletMap.value ] >', leafletMap.value);
    }
  } catch (err) {
    message.error(err as string);
  }
};

// 全图四至（画一个包含所有地块的矩形）
const drawRectangle = (checked: boolean) => {
  if (!checked) {
    leafletMap.value?.removeFourBoundary();
  } else {
    leafletMap.value?.drawFourBoundary({ code: route.query.village, landNoList: toRaw(landList.value.map((l) => l.landNoStr)) });
  }
};

// 显示边距，2个点的距离，单位为米
const drawDistance = async (checked: boolean) => {
  if (checked) {
    await leafletMap.value?.drawDistance({ code: route.query.village, landNoList: toRaw(landList.value.map((l) => l.landNoStr)) });
  } else {
    await leafletMap.value?.removeDistance();
  }
};

// marker点击事件
const markerClick = (e) => {
  const id = e.id || '';
  const item = allCheckFileListKmeansList.value.find((val) => val.id === id);
  if (item) {
    // 拿图片
    imageModalList.value = [{ documentTypeList: item.fileList }];
    imageModalShow.value = true;
  }
};

const clustersKmeans = () => {
  const points = [];
  const pointsCheckFileList = [];
  for (const item of allCheckFileList.value) {
    if (item.longitude && item.latitude) {
      points.push(turf.point([item.longitude, item.latitude]));
      pointsCheckFileList.push(item); // 有些数据并没有经纬度，却有图片，做下兼容
    }
  }

  const clusters = turf.clustersKmeans({
    type: 'FeatureCollection',
    features: points,
  });

  // 获取分类定位
  const clustersCenter: AllCheckFileListKmeans[] = [];
  for (let i = 0; i < clusters.features.length; i++) {
    const index = clustersCenter.findIndex((val) => val.feature.properties.cluster === clusters.features[i].properties.cluster);
    if (index < 0) {
      // 新增一个
      clustersCenter.push({
        id: uuidv4(),
        fileList: [pointsCheckFileList[i]],
        feature: clusters.features[i],
      });
    } else {
      clustersCenter[index].fileList.push(pointsCheckFileList[i]);
    }
  }

  allCheckFileListKmeansList.value = clustersCenter;
};

const requestAllCheckFileList = async () => {
  const params: QueryCheckFarmerShapeListReqParams = {
    insrCheckTaskNo: route.query.insrCheckTaskNo,
  };
  if (route.query.farmerId) {
    params.farmerId = route.query.farmerId;
  }
  try {
    const { code, data, msg } = (await queryAllCheckFileListReq.fetchData(params)) || {};
    if (code === SUCCESS_CODE) {
      allCheckFileList.value = (data || []).map((val) => {
        return {
          latitude: val.latitude,
          longitude: val.longitude,
          originName: val.originName,
          uploadPath: val.uploadPath,
          downloadUrl: val.downloadUrl,
          bucketName: val.bucketName,
          thumbnail: val.smallDownloadUrl, // 滑动预览组件所需字段
          documentFormat: val.originName.split('.').pop(), // 后续后端会更改，不需要切割
        };
      });
      if (allCheckFileList.value.length) {
        // K均值算法聚类
        clustersKmeans();
        // showImageMarker.value = !showImageMarker.value;
      } else {
        message.info('暂无照片可查看');
      }
    } else {
      allCheckFileList.value = [];
      message.error(msg || '接口有误，请稍后重试');
    }
  } catch (err) {
    allCheckFileList.value = [];
    message.error(err.mesaage || '接口有误，请稍后重试');
  }
};
// 照片位置分布图
// 在各个标的附件的经纬度进行K均值算法聚类
const drawImageMarker = async (checked: boolean) => {
  const locationList = [];
  if (!checked) {
    leafletMap.value?.removeImageMarker();
    // 复位
    await leafletMap.value?.addQGLandLayer({
      code: route.query.village,
      level: 5,
      landText: ['farmerName'],
      extraPopupInfo: ['farmerName', 'areaInMu', 'collectionAreaMu'],
      landList: toRaw(landList.value),
    });
  } else {
    if (!allCheckFileList.value || !allCheckFileList.value.length) {
      await requestAllCheckFileList();
      // 改造
      allCheckFileListKmeansList.value.forEach((a) => {
        if (a.fileList) {
          a.fileList.forEach((b) => {
            locationList.push({
              latitude: b.latitude,
              longitude: b.longitude,
              id: a.id,
            });
          });
        }
      });
      leafletMap.value?.drawImageMarker({ locationList, clickHandler: markerClick });
    } else {
      allCheckFileListKmeansList.value.forEach((a) => {
        if (a.fileList) {
          a.fileList.forEach((b) => {
            locationList.push({
              latitude: b.latitude,
              longitude: b.longitude,
              id: a.id,
            });
          });
        }
      });
      leafletMap.value?.drawImageMarker({ locationList, clickHandler: markerClick });
    }
  }
};

const pointList = ref([]);
const loadLandData = async () => {
  const params: QueryCheckFarmerShapeListReqParams = {
    insrCheckTaskNo: route.query?.insrCheckTaskNo as string,
  };
  if (route.query.farmerId) {
    params.farmerId = route.query.farmerId;
  }
  try {
    const { code, data, msg } = await queryCheckFarmerShapeListReq.fetchData(params);
    if (SUCCESS_CODE === code) {
      const result = [];
      const _point = [];
      data?.shapeList.forEach((val) => {
        if (val && val.landNoStr) {
          if (val.shapeType === '0' || val.shapeType === '5' || val.shapeType === '6') {
            // 天津绘测需求之后，shapeType多了几种类型，5-耕地，6-大棚
            result.push({
              landNoStr: val.landNoStr,
              farmerName: val.farmerName,
              areaInMu: val.areaInMu, // 地块面积
              collectionAreaMu: val.collectionAreaMu, // 勾画面积
            });
          }
          if (val.shapeType === '3') {
            _point.push({
              landNoStr: val.landNoStr,
              content: val.farmerName,
            });
          }
        }
      });
      landList.value = result;
      pointList.value = _point;
      totalAreaInMu.value = String(data.totalAreaInMu) + '亩' || '';
    } else {
      message.error(msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

// 验标地块面积显示/隐藏
const toggleVerificationArea = async (checked: boolean) => {
  if (!leafletMap.value) return;
  const landText = checked ? ['farmerName', 'areaInMu', 'collectionAreaMu'] : ['farmerName'];
  const res = await leafletMap.value.addQGLandLayer({
    code: route.query.village,
    level: 5,
    landText,
    extraPopupInfo: ['farmerName', 'areaInMu', 'collectionAreaMu'],
    landList: toRaw(landList.value),
  });
  if (res.areaInMuSum) {
    totalAreaInMu.value = res.areaInMuSum + '亩';
  }
};

// 清单地块显示/隐藏
const toggleListParcels = async (checked: boolean) => {
  if (checked) {
    // 实现显示清单地块的逻辑
    try {
      const data = {
        code: route.query.village,
        level: 5,
        landList: toRaw(farmerLands.value),
        extraPopupInfo: ['default', 'insureNum', 'landType', 'insurantName', 'insurantCardNo', 'actualUsageName'],
      };
      await leafletMap.value?.queryQGLandInfoData(data);
    } catch (err) {
      console.log(err);
    }
  } else {
    await leafletMap.value.queryQGLandInfoData({ close: true });
  }
};

const farmerLands = ref([]);
const getFarmerList = async () => {
  const url = gateWay + service.farmer + '/farmerList/queryFarmerList';
  try {
    // tableLoading.value = true;
    const res = await $postOnClient(url, {
      farmerListNo: route.query.farmerListNo,
    });
    if (res?.code === SUCCESS_CODE && Array.isArray(res.data)) {
      tableData.value = res.data;
      pagination.value.total = res.data.length; // Update total count

      res.data.forEach((f) => {
        const tempName = f.farmerName;
        const tempNo = f.certificateNo;
        f.shapeList.forEach((k) => {
          farmerLands.value.push({
            landNoStr: k.landNoView, // 地块编码
            landArea: k.landArea, // 地块面积
            insureNum: k.insuranceNums + (k.insuranceNumsUnit || '亩'), // 投保数量
            landLayerType: k.landLayerType, // 地块属性
            authCertificateNo: k.authCertificateNo, // 所有人证件号码
            farmerName: k.authName, // 所有人姓名
            insurantName: tempName, // 被保险人姓名
            insurantCardNo: tempNo, // 被保险人证件号
          });
        });
      });
    }
  } catch (e) {
    message.error(String(e));
  } finally {
    // tableLoading.value = false;
  }
};

const addCustomRowProperty = (record) => {
  // 添加行点击事件
  return {
    onClick: async () => {
      try {
        const landList = record.shapeList.map((k) => ({
          landNoStr: k.landNoView, // 地块编码
          collectionAreaMu: '', // 采集面积
          insureNum: k.insuranceNums, // 投保数量
          insureUnit: k.insuranceNumsUnit || '', // 投保数量单位
          farmerName: k.authName, // 农户姓名
          insurantName: record.farmerName, // 被保险人姓名
          insurantCardNo: record.certificateNo, // 被保险人证件号
        }));
        await leafletMap.value.addActiveLandLayer({
          code: route.query.village,
          level: 5,
          landList: landList,
        });
      } catch (err) {
        console.log(err);
      }
    },
  };
};
</script>

<style lang="less" scoped>
.page-container {
  height: calc(100vh - 66px);
}

.area-tips {
  position: absolute;
  top: 70px;
  left: 30px;
  background: #222d44;
  box-shadow: 0px 4px 6px 0px rgba(66, 70, 86, 0.2);
  padding: 0 12px;
  border-radius: 8px;
  z-index: 1000;
  height: 46px;
  line-height: 46px;
  color: #ffffff;
}

.map-legend {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: #222d44;
  box-shadow: 0px 4px 6px 0px rgba(66, 70, 86, 0.2);
  border-radius: 8px;
  padding: 12px;
  z-index: 1000;
  width: 146px;

  .legend-title {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > span {
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
    }
  }

  .legend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .left-group {
      display: flex;
      align-items: center;
      > img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }

    span {
      font-size: 12px;
      color: #bbc2d1;
    }

    .area-text {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.85);
      margin-left: 10px;
    }
  }
}

.map-legend-right {
  position: absolute;
  bottom: 30px;
  left: 210px;
  background: #222d44;
  box-shadow: 0px 4px 6px 0px rgba(66, 70, 86, 0.2);
  border-radius: 8px;
  padding: 12px;
  z-index: 1000;
  width: 90px;
  .legend-title {
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 12px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.farmer-list-panel {
  position: absolute;
  top: 60px;
  right: 20px;
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  z-index: 1000;
  transition: all 0.3s;
  width: 400px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;

  &.collapsed {
    width: 50px;
    overflow: hidden;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
  }

  .panel-content {
    padding: 16px;

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;

      .reset-btn {
        margin-right: 6px;
      }
    }

    .table-title {
      font-weight: 600;
      margin: 16px 0 8px;
    }
  }
}
</style>
