<template>
  <!-- 标的照片弹窗 -->
  <a-modal v-model:open="show" width="60%" :style="{ 'max-height': pxToRem(642), overflow: 'auto' }" :centered="true" title="标的照片" @ok="show = false">
    <div v-for="(item, index) in list" :key="index">
      <div v-if="item.itemName" class="form-title">
        <span>{{ item.itemName }}</span>
      </div>
      <div class="flex flex-wrap">
        <div v-for="(photo, phothIndex) in item.documentTypeList" :key="phothIndex" class="m-[5px]">
          <img :src="photo.thumbnail" class="w-[180px] h-[130px] p-[4px] box-border cursor-pointer" @click="preview(phothIndex, item.documentTypeList)" />
        </div>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="show = false">确定</a-button>
      </a-space>
    </template>
    <ImageViewer ref="imgViewer" :list="previewFileList" :current="currentPhotoIndex" />
  </a-modal>
</template>

<script lang="ts" setup>
import type { TargetPhotoType, PhotoListType, PreviewFileType } from '../baseInfo.d';
import { pxToRem } from '@/utils/tools';
import ImageViewer from '@/components/ui/ImageViewer.vue';

const { list } = defineProps<{ list: TargetPhotoType[] }>();

const show = defineModel<boolean>('show');

const imgViewer = ref(); // 预览照片ref
const previewFileList = ref<PreviewFileType[]>([]); // 当前要预览的文件列表
const currentPhotoIndex = ref(0); // 点击当前照片的index
// 预览照片
const preview = (photoIndex: number, fileList: PhotoListType[]) => {
  currentPhotoIndex.value = photoIndex; // 当前照片下标
  previewFileList.value = fileList.map((item) => {
    return {
      uploadPath: item.uploadPath as string, // iobs的key|原图url
      thumbnail: item.thumbnail as string, // 缩略图url
      documentFormat: item.documentFormat as string, // 文件类型 img/pdf/word/excel 4种类型，非后缀名
      bucketName: item.bucketName as string, // iobs桶，有外网桶和内网桶
      documentName: item.originName,
    };
  });
  // 打开滑动照片
  if (imgViewer.value) {
    imgViewer.value?.openPreview();
  }
};
</script>
