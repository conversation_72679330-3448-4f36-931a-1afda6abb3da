<template>
  <div class="p-14px inspection-task">
    <div class="bg-white p-16px rounded-md rounded-tl-none mb-[14px]">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="modelRef">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="机构" name="departmentCode" :label-col="{ style: { width: pxToRem(80) } }">
                <department-search v-model:contain-child-depart="modelRef.containChildDepart" :dept-code="modelRef.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="产品名称" :label-col="{ style: { width: pxToRem(80) } }" name="productCode">
                <ProductSelect v-model:value="modelRef.productCode" :department-code="modelRef.departmentCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="被保险人" :label-col="{ style: { width: pxToRem(80) } }" name="insuredName">
                <a-input v-model:value.trim="modelRef.insuredName" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="单证类型" name="voucherType" :label-col="{ style: { width: pxToRem(80) } }">
                <a-input-group>
                  <a-row :gutter="8">
                    <a-col :span="5">
                      <a-select v-model:value="modelRef.voucherType" :options="voucherTypeOpitons" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                    </a-col>
                    <a-col :span="19">
                      <a-input v-model:value.trim="modelRef.voucherNo" placeholder="请输入" @change="handleType" />
                    </a-col>
                  </a-row>
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="验标日期" :label-col="{ style: { width: pxToRem(80) } }" name="checkDate">
                <a-range-picker v-model:value="modelRef.checkDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="24">
            <a-col :span="24">
              <a-form-item label="标的类型" name="agriculturalRiskObjectDetailCode" :label-col="{ style: { width: pxToRem(80) } }">
                <RiskCodeSelect v-model:value="modelRef.agriculturalRiskObjectDetailCode" :department-code="modelRef.departmentCode" :show-search="false" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="24">
            <a-col :span="24">
              <a-form-item label="投保区域" name="riskObjectCompleteAddress" :label-col="{ style: { width: pxToRem(80) } }">
                <div class="flex gap-x-8px">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="address.province" v-model:city="address.city" v-model:county="address.county" v-model:town="address.town" v-model:village="address.village" style="width: 65%" @change-selected="regionChange" />
                  </a-form-item-rest>
                  <a-input v-model:value="modelRef.riskObjectCompleteAddress" placeholder="请输入" style="width: 35%" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="验标状态" name="status" required :label-col="{ style: { width: pxToRem(80) } }">
                <check-box-group v-model:checked-list="modelRef.status" :options="statusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" @click="queryTask">查询</a-button>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md space-y-16px">
      <div class="flex items-center space-x-[12px]">
        <span class="text-font-primary text-xl font-bold">查询结果</span>
      </div>

      <a-table :columns="columns" :data-source="dataSource" :scroll="{ x: 'max-content' }" :pagination="pagination" :loading="loading" class="table-box">
        <template #bodyCell="{ record, column, text, index }">
          <template v-if="record">
            <template v-if="column.key === 'applyPolicyNo'">
              <div class="flex items-center">
                <CopyLink :text="record.applyPolicyNo" @click="openApplyPolicyDetail(record.applyPolicyNo)" />
                <a-tag v-if="record.doublePrecisionFlag" color="green">双</a-tag>
              </div>
            </template>
            <template v-if="column.key === 'policyNo'">
              <div class="flex items-center">
                <CopyLink :text="record.policyNo" @click="openPolicyDetail(record.policyNo, record.applyPolicyNo)" />
              </div>
            </template>
            <template v-if="column.key === 'insrCheckNo'">
              <div class="flex items-center">
                <span>{{ record.insrCheckNo }}</span>
                <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
              </div>
            </template>
            <template v-if="column.key === 'departmentName'">
              <span>{{ record.departmentCode }}{{ record.departmentName }}</span>
            </template>
            <template v-if="column.key === 'insureNum'">
              <span>{{ record.insureNum }}{{ record.insureUnit || '' }}</span>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :bordered="false" :color="tagMap[record.status].color">
                <template #icon>
                  <VueIcon :icon="tagMap[record.status].icon" />
                </template>
                {{ tagMap[record.status].text }}
              </a-tag>
            </template>
            <template v-if="['productName', 'insurantName'].includes(column.dataIndex as string)">
              <a-tooltip placement="topLeft" :title="(column.key === 'productName' ? record.productCode : '') + (text || '-')">
                <div class="table-ellipsis-multiline">{{ (column.key === 'productName' ? record.productCode : '') + (text || '-') }}</div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'operation'">
              <!-- <AuthButton code="checkDeal" type="link" size="small">验标处理</AuthButton> -->
              <AuthButton code="checkView" type="link" size="small" @click="openDetail(record.applyPolicyNo)">查看</AuthButton>
              <template v-if="!record.isRevocationBtnAvailable">
                <template v-if="record.disabledReason">
                  <a-tooltip placement="left">
                    <template #title><div v-html="record.disabledReason.replace(/\r\n/gi, '<br/>')"></div></template>
                    <a-button type="link" size="small" :disabled="!record.isRevocationBtnAvailable">撤回修改</a-button>
                  </a-tooltip>
                </template>
                <template v-else>
                  <a-button type="link" size="small" :disabled="!record.isRevocationBtnAvailable">撤回修改</a-button>
                </template>
              </template>
              <template v-else>
                <a-button type="link" size="small" @click="revocationCheck(record, index)">撤回修改</a-button>
              </template>
              <a-tooltip placement="left">
                <template #title><div class="whitespace-pre-wrap" v-html="getToolTipContent(record as IToolTipContent)"></div></template>
                <a-button type="link" size="small" :disabled="!record.isDistributionBtnAvailable" @click="handleDispatch(record?.insrCheckTaskNo)">派发</a-button>
              </a-tooltip>
            </template>
          </template>
          <template v-else>
            <div>-</div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <a-modal v-model:open="openRevocationModal" title="验标撤回" :mask-closable="false">
    <template #footer>
      <a-button key="back" @click="cancelModal">取消</a-button>
      <a-button key="back" @click="redirctToEndorse">去批改清单</a-button>
      <a-button key="submit" type="primary" :loading="modalLoading" @click="submitModal">已确认，修改验标</a-button>
    </template>
    <p>请确认分户清单是否为最新数据，如要变更分户清单信息，请先前往【批改申请-整单批改】；如选择修改验标，将撤回验标任务，需前往移动端重新完成验标</p>
  </a-modal>
  <DispatchModal v-if="showDispatchModal" v-model:visible="showDispatchModal" :insr-check-task-no="insrCheckTask" @ok="queryTask" />
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import { IconCheckCircleFilledFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import type { AddressType, SelectOptions, QueryDataType, CheckTaskTableDataType, TableDataType, IToolTipContent } from './inspectionTask.d';
import FormFold from '@/components/ui/FormFold.vue';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { useUserStore } from '@/stores/useUserStore';
import { pxToRem } from '@/utils/tools';
import { usePost, useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import AuthButton from '@/components/ui/AuthButton.vue';
import CopyLink from '@/components/ui/CopyLink.vue';
import { $post } from '@/utils/request';
import { message } from '@/components/ui/Message';
import { Modal } from 'ant-design-vue';
import DispatchModal from './dispatchModal.vue';

const router = useRouter();
const route = useRoute();
const searchForm = ref(); // 表单ref
const { gateWay, service } = useRuntimeConfig().public || {};
const queryCheckTaskList = await usePost<CheckTaskTableDataType>(`${gateWay}${service.check}/web/check/queryCheckTaskList`); // 查询验标任务列表
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/public/getSelectListForLevel`); // 查询业务类型
const getTypeReq = await useGet<Record<string, string>>(`${gateWay}${service.administrate}/public/getBizTypeByBizNo`); // 查询是投保单or保单业务类型
const voucherTypeOpitons = ref<SelectOptions[]>([]); // 业务类型下拉值
// 是否验证单选
const statusOptions = [
  {
    label: '验标中',
    value: '2',
  },
  {
    label: '完成验标',
    value: '3',
  },
  {
    label: '审核中',
    value: 'B21',
  },
  {
    label: '审核通过',
    value: 'B4',
  },
  {
    label: '审核不通过',
    value: 'B7',
  },
];
const loading = ref<boolean>(false); // 表格loading
const expand = ref(false); // 是否折叠
const { userInfo } = useUserStore(); // 获取用户信息
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : '')); // 获取默认机构
const changeDeptCode = (val: string) => (modelRef.value.departmentCode = val); // 机构赋值
// 初始化地址值省市区县等
const addressOrigin = () => {
  return {
    province: undefined, // 省
    provinceName: '', // 省
    city: undefined, // 市
    cityName: '', // 市
    county: undefined, // 区
    countyName: '', // 区
    town: undefined, // 县
    townName: '', // 县
    village: undefined, // 村
    villageName: '', // 村
  };
};
const address = ref<AddressType>(addressOrigin()); // 地址省市区
const modelRef = ref<QueryDataType>({
  departmentCode: defaultDeptCode.value, // 机构编码
  containChildDepart: true, // 是否包含下级机构
  voucherType: '', // 业务号类型
  voucherNo: '', // 业务号
  insuredPerson: '', // 被保险人
  riskObjectCompleteAddress: '', // 投保区域
  productCode: '', // 产品编码
  status: ['2', '3'], // 验标状态
  insuredName: '', // 被保险人
  agriculturalRiskObjectDetailCode: '', // 标的类型
  checkDate: ['', ''], // 验标日期
});

// 重置数据
const resetForm = () => {
  searchForm.value.resetFields(); // 重置查询表单其他数据
  address.value = addressOrigin(); // 重置省市区等
  modelRef.value.voucherNo = ''; // 单独置空
};

const columns: TableColumnsType = [
  {
    title: '投保单号',
    key: 'applyPolicyNo',
  },
  {
    title: '保单号',
    key: 'policyNo',
  },
  // {
  //   title: '验标任务号',
  //   dataIndex: 'insrCheckTaskNo',
  //   key: 'insrCheckTaskNo',
  // },
  {
    title: '出单机构',
    dataIndex: 'departmentName',
    key: 'departmentName',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: '250px',
  },
  {
    title: '被保险人',
    dataIndex: 'insurantName',
    key: 'insurantName',
    width: '180px',
  },
  {
    title: '保险数量',
    key: 'insureNum',
  },
  {
    title: '验标数量',
    dataIndex: 'checkNum',
    key: 'checkNum',
  },
  {
    title: '验标户数',
    dataIndex: 'checkCount',
    key: 'checkCount',
  },
  {
    title: '验标状态',
    key: 'status',
  },
  {
    title: '验标任务号',
    dataIndex: 'insrCheckNo',
    key: 'insrCheckNo',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
  },
];
const dataSource = ref<TableDataType[]>([]); // 表格数据
// 查询任务表格数据
const queryTask = async () => {
  // 点击查询的时候需要初始化当前页，与页展示数
  pagination.current = 1;
  pagination.pageSize = 10;
  queryTaskTableData(); // 执行查询列表数据
};
// 执行查询列表数据
const queryTaskTableData = async () => {
  await searchForm.value.validate(); // 必填项校验
  loading.value = true;
  try {
    const params = {
      insureDepartmentNo: modelRef.value.departmentCode, // 机构编码
      containChildDepart: modelRef.value.containChildDepart ? '1' : '0', // 是否包含下级机构
      voucherType: modelRef.value.voucherType, // 业务号类型
      voucherNo: modelRef.value.voucherNo, // 业务号
      insuredName: modelRef.value.insuredName, // 被保险人
      checkBeginDate: modelRef.value.checkDate[0], // 验标开始日期
      checkEndDate: modelRef.value.checkDate[1], // 验标结束日期
      status: modelRef.value.status.join(','), // 验标状态
      riskObjectCompleteAddress: modelRef.value.riskObjectCompleteAddress, // 投保区域
      agriculturalRiskObjectDetailCode: modelRef.value.agriculturalRiskObjectDetailCode, // 标的类型
      productCode: modelRef.value.productCode, // 产品名称
      pageNum: pagination.current, // 第几页
      pageSize: pagination.pageSize, // 每页条数
    };
    const res = await queryCheckTaskList.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res?.data || {};
      // 增加loading
      // records.forEach(val => val.revocationLoading = false);
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(queryTaskTableData); // 分页配置查询
// 地址拼接
const regionChange = (selected: { label: string }, type: string) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    address.value[`${type}Name` as 'provinceName' | 'cityName' | 'countyName' | 'townName' | 'village'] = selected?.label || '';
  }
  modelRef.value.riskObjectCompleteAddress = address.value.provinceName + address.value.cityName + address.value.countyName + address.value.townName + address.value.villageName; // 地址最终赋值
};
// 获取选项
const initOption = async () => {
  try {
    const params = ['policyQueryNoType'];
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      voucherTypeOpitons.value = res?.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
const openApplyPolicyDetail = (applyPolicyNo: string) => {
  router.push({
    path: '/applyPolicyDetails',
    query: {
      applyPolicyNo,
    },
  });
};
const openPolicyDetail = (policyNo: string, applyPolicyNo: string) => {
  router.push({
    path: '/policyDetails',
    query: {
      applyPolicyNo,
      policyNo,
    },
  });
};
// 查看详情
const openDetail = (applyPolicyNo: string) => {
  console.log(applyPolicyNo);
  router.push({
    name: 'inspectionInfo',
    query: {
      applyPolicyNo,
    },
  });
};
// 验标状态样式
const tagMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
  2: { color: 'processing', text: '验标中', icon: IconErrorCircleFilledFont },
  3: { color: 'success', text: '完成验标', icon: IconCheckCircleFilledFont },
};
// 查询回显投保单号或保单号类型
const handleType = async () => {
  try {
    const params = {
      bizNo: modelRef.value.voucherNo,
    };
    const selectTypeResult = await getTypeReq.fetchData(params);
    if (selectTypeResult && selectTypeResult?.code === SUCCESS_CODE) {
      modelRef.value.voucherType = selectTypeResult.data?.bizType || ''; // 回显下拉类型的值
    } else {
      message.error(selectTypeResult?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => route.query,
  (val) => {
    if (val?.bizNo) {
      modelRef.value.voucherType = '2';
      modelRef.value.voucherNo = val.bizNo as string;
    }
  },
  { deep: true, immediate: true },
);
const openRevocationModal = ref(false);
const modalLoading = ref(false);
const modalRecord = reactive<{ record: TableDataType; index: number }>({
  record: {},
  index: 0,
});
const revocationCheck = async (record: TableDataType, index: number) => {
  if (!record.isRevocationBtnAvailable) {
    if (record.disabledReason) {
      // 展示提示
      message.error(record.disabledReason, { duration: 5000 });
    }
    return;
  }
  // 二次弹窗
  openRevocationModal.value = true;
  modalRecord.record = record;
  modalRecord.index = index;
  modalLoading.value = false;
};

const cancelModal = () => {
  openRevocationModal.value = false;
};

const redirctToEndorse = () => {
  cancelModal();
  router.push({
    path: '/endorseApply',
    query: {
      bizNo: modalRecord.record?.policyNo || '',
    },
  });
};

const submitModal = async () => {
  try {
    modalLoading.value = true;
    const { code, msg } = await $post(`${gateWay}${service.check}/web/check/revocationFinishedCheckTask`, { insrCheckTaskNo: modalRecord.record?.insrCheckTaskNo || '' });
    modalLoading.value = false;
    cancelModal();
    if (code === SUCCESS_CODE) {
      // 撤回成功
      message.success(`验标任务${modalRecord.record?.insrCheckNo || ''}已撤回，请前往移动端修改并完成验标`);
      // 刷新当前页
      queryTaskTableData(); // 查询表格
    } else if (code === 'Y0B0200454') {
      // 该单存在批改申请或信息修改，强提示
      Modal.confirm({
        title: '温馨提示',
        content: msg || '',
        centered: true,
      });
    } else {
      message.error(msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  } finally {
    modalLoading.value = false;
    cancelModal();
  }
};

const getToolTipContent = ({ isDistributionBtnAvailable, distributionDisabledReason, distributedCount }: IToolTipContent) => {
  if (isDistributionBtnAvailable) {
    return `已派发${distributedCount || '-'}人`;
  } else {
    return distributionDisabledReason || '-';
  }
};

const showDispatchModal = ref(false);
const insrCheckTask = ref('');
const handleDispatch = (insrCheckTaskNo: string) => {
  insrCheckTask.value = insrCheckTaskNo;
  showDispatchModal.value = true;
};

onMounted(() => {
  initOption(); // 初始化查询业务类型
  queryTask(); // 查询表格
});
onActivated(() => {
  queryTask(); // 查询表格
});
</script>
