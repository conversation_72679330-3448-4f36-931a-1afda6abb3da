<template>
  <a-modal v-model:open="visible" wrap-class-name="look-model" class="dispatch-modal-container" width="778px" title="验标任务派发" @cancel="visible = false">
    <a-spin :spinning="loading">
      <div class="pt-[12px] px-[20px]">
        <a-tabs v-model:active-key="activeKey" @change="handleChangeTabs">
          <a-tab-pane key="1" tab="业务员"></a-tab-pane>
          <a-tab-pane key="2" tab="协保员"></a-tab-pane>
        </a-tabs>
        <a-form :model="formState" :colon="false" :label-col="{ style: { width: pxToRem(80) } }">
          <div class="grid grid-cols-2 gap-x-16px">
            <a-form-item v-if="activeKey === '1'" label="业务员姓名/手机号" :label-col="{ style: { width: pxToRem(160) } }">
              <a-input v-model:value="formState.q" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item v-else label="协保员姓名/手机号" :label-col="{ style: { width: pxToRem(160) } }">
              <a-input v-model:value="formState.q" placeholder="请输入"></a-input>
            </a-form-item>
            <div class="flex justify-center items-center space-x-8px">
              <a-button @click="resetForm">重置</a-button>
              <a-button type="primary" ghost @click="refresh">查询</a-button>
            </div>
          </div>
        </a-form>
        <div v-if="activeKey === '1' && employeeList && employeeList?.length" class="flex flex-col">
          <div class="mb-[8px] overflow-y-auto h-[300px]">
            <template v-for="item in employeeList" :key="item.recipientUserCode">
              <div class="px-[16px] py-[12px] bg-[#f8f8f8] rounded-[4px] mt-[8px]">
                <div class="grid grid-cols-10 gap-x-16px">
                  <div class="flex items-baseline col-span-7">
                    <a-radio v-model:checked="item.check" :disabled="item.checked"></a-radio>
                    <div>
                      <div class="flex items-baseline">
                        <div class="text-[14px] font-[600]">{{ item.recipientNameZh }}</div>
                        <div v-if="item.distributorDate" class="ml-[8px] flex items-center text-[12px] text-[#999]">
                          <div class="flex-shrink-0">派发时间：</div>
                          <div>{{ item.distributorDate }}</div>
                        </div>
                      </div>
                      <div class="flex items-center text-[12px]">
                        <div class="text-[#666] flex-shrink-0">机构名称：</div>
                        <div class="text-[#333]">{{ item.departmentCode }}{{ item.departmentName }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-end text-[12px] col-span-3">
                    <div class="text-[#666]">业务员ID：</div>
                    <div class="text-[#333]">{{ item.recipientUserCode }}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <a-pagination v-model:current="formState.current" v-model:page-size="formState.size" class="text-end" :total="formState.total" show-size-changer @change="refreshEmployee" />
        </div>
        <div v-else-if="activeKey === '2' && xbUserList && xbUserList?.length" class="flex flex-col">
          <div class="mb-[8px] overflow-y-auto h-[300px]">
            <template v-for="item in xbUserList" :key="item.recipientUserCode">
              <div class="px-[16px] py-[12px] bg-[#f8f8f8] rounded-[4px] mt-[8px]">
                <div class="grid grid-cols-11 gap-x-8px">
                  <div class="flex items-baseline col-span-4">
                    <a-radio v-model:checked="item.check" :disabled="item.checked"></a-radio>
                    <div>
                      <div class="flex items-baseline">
                        <div class="text-[14px] font-[600]">{{ item.recipientNameZh }}</div>
                        <div v-if="item.distributorDate" class="ml-[8px] flex items-center text-[12px] text-[#999]">
                          <div class="flex-shrink-0">派发时间：</div>
                          <div>{{ item.distributorDate }}</div>
                        </div>
                      </div>
                      <div class="flex items-center text-[12px]">
                        <div class="text-[#666] flex-shrink-0">机构名称：</div>
                        <div class="text-[#333]">{{ item.departmentCode }}{{ item.departmentName }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-end text-[12px] col-span-4">
                    <div class="text-[#666]">业务员ID：</div>
                    <div class="text-[#333]">{{ item.recipientUserCode }}</div>
                  </div>
                  <div class="flex items-end text-[12px] col-span-3">
                    <div class="text-[#666]">手机号码：</div>
                    <div class="text-[#333]">{{ item.phone }}</div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <a-pagination v-model:current="formState.current" v-model:page-size="formState.size" class="text-end" :total="formState.total" show-size-changer @change="refreshXbUser" />
        </div>
        <template v-else>
          <a-empty class="mt-[100px]" />
        </template>
      </div>
    </a-spin>
    <template #footer>
      <a-button @click="visible = false">取消</a-button>
      <a-button type="primary" @click="handleDispatch">派发</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { pxToRem } from '@/utils/tools';
import { $get, $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { insrCheckTaskNo } = defineProps<{
  insrCheckTaskNo: string;
}>();

const { gateWay, service } = useRuntimeConfig().public || {};
const emit = defineEmits(['ok']);

const visible = defineModel<boolean>('visible', { default: false });

const formState = ref<{ q: string; current: number; size: number; total: number }>({
  q: '', // 搜索关键字
  current: 1, // 当前页
  size: 10, // 页宽
  total: 0, // 总数
});
// 1-业务员 2-协保员
const activeKey = ref<'1' | '2'>('1');

const loading = ref(false);
const employeeList = ref();
const xbUserList = ref();

const refreshEmployee = async (val?: number) => {
  formState.value.current = val || 1;
  employeeList.value = [];
  xbUserList.value = [];
  loading.value = true;
  const fetchUrl = gateWay + service.check + '/web/check/getSalesManListByInsrCheckTaskNo'; // 获取业务员列表
  try {
    const { q, current, size } = formState.value;
    const res = await $get<{ records: Record<string, string>[]; size: number; current: number; total: number }>(fetchUrl, { insrCheckTaskNo, q, page: current, size });
    if (res && res.code === SUCCESS_CODE && res.data) {
      employeeList.value = res.data.records.map((it: Record<string, string>) => {
        return {
          ...it,
          checked: it.check === '1', // 已派发 1勾选,0未勾选
          check: it.check === '1' ? true : false,
        };
      });
      formState.value.current = res.data.current || 1;
      formState.value.size = res.data.size || 10;
      formState.value.total = res.data.total || 0;
    } else {
      message.error(res?.msg || '获取列表失败');
    }
  } catch (e) {
    console.log(e);
  }
  loading.value = false;
};

const refreshXbUser = async (val?: number) => {
  formState.value.current = val || 1;
  employeeList.value = [];
  xbUserList.value = [];
  loading.value = true;
  const fetchUrl = gateWay + service.check + '/web/check/getInsuranceAssistantListByInsrCheckTaskNo'; // 获取机构协保员列表
  try {
    const { q, current, size } = formState.value;
    const res = await $get<{ records: []; size: number; current: number; total: number }>(fetchUrl, { insrCheckTaskNo, q, page: current, size });
    if (res && res.code === SUCCESS_CODE && res.data) {
      console.log('res: ', res);
      xbUserList.value = res.data.records.map((it: Record<string, string>) => {
        return {
          ...it,
          checked: it.check === '1', // 已派发 1勾选,0未勾选
          check: it.check === '1' ? true : false,
        };
      });
      formState.value.current = res.data.current || 1;
      formState.value.size = res.data.size || 10;
      formState.value.total = res.data.total || 0;
    } else {
      message.error(res?.msg || '获取列表失败');
    }
  } catch (e) {
    console.log(e);
  }
  loading.value = false;
};

const resetForm = () => {
  formState.value.q = '';
  refresh();
};

const refresh = () => {
  formState.value.current = 1;
  formState.value.size = 10;
  formState.value.total = 0;
  if (activeKey.value === '1') {
    refreshEmployee();
  } else if (activeKey.value === '2') {
    refreshXbUser();
  }
};

const handleChangeTabs = () => {
  formState.value.q = '';
  refresh();
};

const handleDispatch = async () => {
  const fetchUrl = gateWay + service.check + '/web/check/distributionCheckTaskOfApplyPolicy'; // 派发验标任务
  try {
    let recipientList = [];
    if (activeKey.value === '1') {
      recipientList = employeeList.value.filter((it: Record<string, string>) => {
        return it.check && !it.checked; // 提交新勾选的，历史勾选的不传
      });
    } else if (activeKey.value === '2') {
      recipientList = xbUserList.value.filter((it: Record<string, string>) => {
        return it.check && !it.checked; // 提交新勾选的，历史勾选的不传
      });
    }
    if (!recipientList?.length && activeKey.value === '1') {
      message.error('请先勾选业务员');
      return;
    } else if (!recipientList?.length && activeKey.value === '2') {
      message.error('请先勾选协保员');
      return;
    }
    const res = await $post(fetchUrl, { insrCheckTaskNo, recipientList });
    if (res && res.code === SUCCESS_CODE && res.data) {
      message.success(res.msg);
      visible.value = false;
      emit('ok');
    } else {
      message.error(res.msg);
    }
  } catch (e) {
    console.log(e);
  }
};

onMounted(() => {
  refresh();
});
</script>
<style>
.look-model {
  .ant-modal-body {
    /* max-height: 1700px; */
  }
}
</style>
<style lang="less" scoped></style>
