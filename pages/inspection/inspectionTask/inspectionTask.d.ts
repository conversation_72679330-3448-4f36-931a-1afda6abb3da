// 验标任务列表
export interface TableDataType {
  applyPolicyNo: string; // 投保单号
  insrCheckNo: string; // 验标任务号
  taskName: string; // 任务名称
  productName: string; // 产品名称
  departmentName: string; // 机构名称
  insureNum: string; // 保险数量
  insureUnit: string; // 保险数量单位
  farmerCount: string; // 承保户数
  checkRate: string; // 验标比例
  checkNum: string; // 验标数量
  checkCount: string; // 应验户数
  checkedCount: string; // 已验户数
  checkByName: string; // 验标人
  checkDate: string; // 验标日期
  checkRate: string; // 验标比例
  status: string; // 验标状态
  revocationLoading: boolean; // 按钮loading
  insrCheckTaskNo: string; // 验标号
  isRevocationBtnAvailable: boolean; // 是否展示撤回按钮
  disabledReason: string; // 撤回按钮不可用的原因
  policyNo?: string; // 保单号
}
// 业务后选项
export interface SelectOptions {
  label: string; // 下拉名
  value: string; // 下拉值
  disabled?: boolean; // 是否禁用
  children?: SelectOptions[]; // 子项
  isLeaf?: boolean;
}
// 省市区
export interface AddressType {
  province: string | undefined; // 省
  provinceName: string; // 省
  city: string | undefined; // 市
  cityName: string; // 市
  county: string | undefined; // 区
  countyName: string; // 区
  town: string | undefined; // 县
  townName: string; // 县
  village: string | undefined; // 村
  villageName: string; // 村
}
// 查询条件
export interface QueryDataType {
  departmentCode: string; // 机构编码
  containChildDepart: boolean; // 是否包含下级机构
  voucherType: string; // 业务号类型
  voucherNo: string; // 业务号
  insuredPerson: string; // 被保险人
  riskObjectCompleteAddress: string; // 投保区域
  productCode: string; // 产品编码
  status: string[]; // 验标状态
  insuredName: string; // 被保险人
  agriculturalRiskObjectDetailCode: string; // 标的类型
  checkDate: RangeValue; // 验标日期
}
// 验标任务列表数据
export interface CheckTaskTableDataType {
  records: TableDataType[]; // 表格数据
  code: string; // 状态码
  total: number; // 总数
  current: number; // 当前页
  size: number; // 每页条数
}
export type RangeValue = [string, string]; // 日期

export interface IToolTipContent {
  isDistributionBtnAvailable: boolean;
  distributionDisabledReason: string;
  distributedCount: number;
}
