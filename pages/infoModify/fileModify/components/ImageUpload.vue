<template>
  <div class="relative">
    <div v-show="showFileUploadLoading" class="absolute top-0 left-0 w-full h-full bg-white/50 flex justify-center items-center z-[1000]">
      <a-spin :tip="showFileUploadLoadingTips" />
    </div>
    <div class="m-16px flex gap-x-[12px] pd-[56px] pb-[20px]">
      <div class="rounded bg-white w-[291px] p-12px container-height" @dragenter.prevent="handleDragEnter" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop">
        <a-upload :multiple="true" :custom-request="() => {}" :before-upload="(file, fileList) => handleFileChange(file, fileList, noclassify.typeCode, noclassify.fileDetailList)" :show-upload-list="false">
          <div class="upload-box" :class="{ 'upload-box-dragging': isDragging }">
            <div class="flex flex-col items-center">
              <img class="w-[46px] h-[54px]" :src="uploadImg" />
              <div class="text-xs text-[#333333] mt-[8px] mb-[4px]">可直接拖拽文件到这里，或点击添加</div>
              <div class="text-xs text-[rgba(0,0,0,0.6)]">单次不超过<span class="font-number-medium text-[#07c160]">200&nbsp;</span>份</div>
            </div>
          </div>
        </a-upload>
        <div class="flex items-center justify-between mt-[16px] mb-[8px]">
          <div class="text-rgba(0,0,0,0.9) text-[16px] font-bold">未分类文件</div>
          <a-checkbox v-model:checked="noclassify.checkedAll" :indeterminate="noclassify.indeterminate" @change="(e) => handleAllCheckedChange(e, noclassify)">
            <span class="text-xs text-[#606060]">全选</span>
          </a-checkbox>
        </div>
        <div v-if="noclassify.fileDetailList?.length > 0" class="grid grid-cols-2 gap-[8px] noclassify-height">
          <!-- 可拖拽文件 -->
          <div v-for="file in noclassify.fileDetailList" :key="file.documentId" class="file-box" draggable="true" @dragstart="dragStart(file, noclassify)" @dragend="dragEnd">
            <a-checkbox v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(noclassify)" />
            <div class="h-full flex flex-col justify-center items-center">
              <img v-if="file.thumbnail" class="h-[80px] w-[70px]" :src="file.thumbnail" />
              <div v-else class="text-[50px]">
                <VueIcon :icon="getFileIcon(file.documentName)" />
              </div>
              <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]">{{ file.documentName }}</div>
            </div>
          </div>
        </div>
        <div v-else class="mt-[100px]">
          <a-empty :image="simpleImage" />
        </div>
      </div>
      <div class="rounded bg-white grow p-12px container-height">
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold ml-12px">资料({{ totalCount }})</div>
        <div class="flex justify-between mb-8px">
          <div class="flex items-center gap-x-16px text-[rgba(0,0,0,0.6)] ml-12px">
            <span>注：1.单个附件大小不超过10M</span>
            <span class="gap-[5px]">
              2.图片格式
              <a-tooltip placement="top">
                <VueIcon :icon="IconErrorCircleFilledFont" />
                <template #title>
                  <span>图片附件格式为：BMP, DIB, JPG, JPEG, JPE, JFIF, GIF, TIF, TIFF, PNG</span>
                </template>
              </a-tooltip>
            </span>
            <span class="gap-[5px]">
              3.文档格式
              <a-tooltip placement="top">
                <VueIcon :icon="IconErrorCircleFilledFont" />
                <template #title>
                  <span>文档附件格式为：DOC, XLS, PDF, MSG, HTM, PPT, PPTX, RAR, ZIP, TXT, XLSX, DOCX</span>
                </template>
              </a-tooltip>
            </span>
          </div>
          <div class="flex items-center gap-x-8px">
            <a-select v-model:value="docTypeValue" class="w-[277px]" placeholder="请选择资料类型" allow-clear show-search option-filter-prop="label" :options="docTypeList" />
            <a-button @click="addDocType">
              <span class="text-[#5a5a5a]">
                <VueIcon :icon="IconAddFont" />
                添加资料类型
              </span>
            </a-button>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-12px">
          <!-- 可拖入区域 -->
          <div v-for="classify in classifyList" :key="classify.typeCode" class="classify-container" @dragenter.prevent="handleDragEnterItem" @dragover.prevent="handleDragOverItem" @dragleave.prevent="handleDragLeaveItem" @drop.prevent="(e) => handleDropItem(e, classify)">
            <div class="flex items-center justify-between mb-[8px]">
              <div class="flex items-center">
                <a-checkbox v-model:checked="classify.checkedAll" :indeterminate="classify.indeterminate" @change="(e) => handleAllCheckedChange(e, classify)">
                  <span class="sub-title">{{ classify.typeName }}</span>
                </a-checkbox>
                <!-- 必传数量 -->
                <span v-if="['01', '02', '03'].includes(classify.docLimitFlag)" class="text-xs text-[rgba(0,0,0,0.6)] ml-[5px]">{{ `必传数量 (${classify.fileTotalNeedNum})` }}</span>
              </div>
              <div class="text-xs text-[rgba(0,0,0,0.6)] flex items-center">
                <a-tooltip title="暂无数据">
                  <span class="mr-16px">资料说明</span>
                </a-tooltip>
                <a-tooltip title="暂无数据">
                  <span>查看示例</span>
                </a-tooltip>
                <span v-if="classify.docLimitFlag !== '00' && classify.docLimitFlag !== '10'" class="inline-block w-[1px] h-[16px] bg-[#d4d6d9] mx-[12px]" />
                <span v-if="classify.docLimitFlag === '10'" class="text-[#576B95] cursor-pointer" @click="handleDeleteDoc(classify)"><VueIcon :icon="IconTongyongShanchuFont" /><span class="ml-[4px]">删除分类</span></span>
                <span v-if="classify.docLimitFlag === '01'" class="green-tag">出单必录</span>
                <span v-if="classify.docLimitFlag === '02'" class="blue-tag">出单可缓</span>
                <span v-if="classify.docLimitFlag === '03'" class="blue-tag">纸质归档</span>
              </div>
            </div>
            <div class="grid-container">
              <a-upload :multiple="true" :custom-request="() => {}" :before-upload="(file, fileList) => handleFileChange(file, fileList, classify.typeCode, classify.fileDetailList)" :show-upload-list="false">
                <div class="file-box flex justify-center items-center gap-x-[2px] text-[#606060]" :class="{ 'file-box-dragging': isDraggingItem }">
                  <VueIcon :icon="IconUploadFont" />
                  <span>上传文件</span>
                </div>
              </a-upload>
              <div v-for="file in classify.fileDetailList" :key="file.documentId" class="file-box" :draggable="true" @dragstart="dragStart(file, classify)" @dragend="dragEnd">
                <a-checkbox v-if="file.operType" v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(classify)" />
                <div class="h-full flex flex-col justify-center items-center">
                  <img v-if="file.thumbnail" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file, classify.fileDetailList)" />
                  <div v-else class="text-[50px]" @click="handleClickFile(file, classify.fileDetailList)">
                    <VueIcon :icon="getFileIcon(file.documentName)" />
                  </div>
                  <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]">{{ file.documentName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="fixed h-[56px] w-full bg-white bottom-0 footer-boxshadow">
        <div class="h-[56px] flex items-center justify-center gap-8px">
          <a-button type="primary" @click="() => (allDeleteModal = true)">删除</a-button>
          <a-button :disabled="debounceDisabled" :loading="loadingSubmit" type="primary" @click="submit">提交审核</a-button>
        </div>
      </div>
      <a-modal v-model:open="deleteModal" title="提醒" @ok="deleteDocType">
        <div>确认要将资料类型及其类型下所有资料删除？</div>
      </a-modal>
      <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleDeleteFile">
        <div>确认删除？</div>
      </a-modal>
      <ImageViewer v-if="viewList.length > 0" ref="viewRef" :list="viewList" :current="viewCurrent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { IconUploadFont, IconAddFont, IcPdfColor, IcWordColor, IcDianziqingdanColor, IcWenjianColor, IconErrorCircleFilledFont, IconTongyongShanchuFont } from '@pafe/icons-icore-agr-an';
import type { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import { Empty } from 'ant-design-vue';
import type { FileModule, UploadFile } from './ImageUpload';
import uploadImg from '@/assets/images/ic-upload.png';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { $postOnClient } from '@/composables/request';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import { debounce } from 'lodash-es';
import { useUploadFiles, type FileUploadParams } from '@/composables/useUploadFiles';

const { gateWay, service } = useRuntimeConfig().public || {};
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签

const router = useRouter();

const { bizNo, bizType, trackId } = defineProps<{
  bizNo: string; // 跟单编号
  bizType: string; // 跟单类型
  trackId?: string; // 附件信息修改跟踪id
}>();
const loading = ref<boolean>(false);
const loadingSubmit = ref<boolean>(false);

// 还需补充
const fileTypeMap = new Map([
  ['XLSX', IcDianziqingdanColor],
  ['XLS', IcDianziqingdanColor],
  ['DOC', IcWordColor],
  ['DOCX', IcWordColor],
  ['PDF', IcPdfColor],
]);

const totalCount = ref(0);
const docTypeValue = ref('');
const docTypeList = ref<{ label: string; value: string }[]>([]);
const documentGroupId = ref<string>('');

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const getDocTypeList = async () => {
  try {
    const fetchUrl = gateWay + service.administrate + '/attachment/documentType/list';
    const { data, code } = await $postOnClient(fetchUrl, {
      bizType,
      bizNo,
    });
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      docTypeList.value = data.map((item) => ({ label: item.parameterName, value: item.parameterCode }));
    }
  } catch (e) {
    console.log(e);
  }
};
getDocTypeList();

// 未分类文件
const noclassify = reactive<FileModule>({
  typeName: '未分类文件',
  typeCode: '',
  fileDetailList: [],
  checkedAll: false,
  indeterminate: false,
  docLimitFlag: '',
});
// 已分类列表
const classifyList = ref<Array<FileModule>>([]);

const getDocList = async () => {
  loading.value = true;
  const fetchUrl = gateWay + service.endorse + '/web/infomodify/attachment/query';
  try {
    const { code, data } = await $postOnClient(fetchUrl, { bizNo, bizType, idEvntDocInfoMdfyDataTrc: trackId });
    if (code === SUCCESS_CODE && data) {
      totalCount.value = data.fileTotalSize;
      classifyList.value = data.fileTypeList.map((item) => ({
        ...item,
        checkedAll: false,
        indeterminate: false,
      }));
      noclassify.fileDetailList = data.noTypeFileList?.[0]?.fileDetailList || [];
      documentGroupId.value = data.documentGroupId;
      loading.value = false;
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};
getDocList();

const handleAllCheckedChange = (e: CheckboxChangeEvent, target: FileModule) => {
  target.indeterminate = false;
  target.fileDetailList.forEach((el) => (el.selected = (e.target.checked && el.operType) || false));
};

const handleCheckedChange = (target: FileModule) => {
  target.checkedAll = target.fileDetailList.every((i) => i.selected);
  if (target.fileDetailList.some((i) => i.selected)) {
    target.indeterminate = !target.fileDetailList.every((i) => i.selected);
  } else {
    target.indeterminate = false;
  }
};
// 记录拖拽文件
const dragFile = ref<UploadFile>();
const moveFile = ref(false); // 文件拖拽移动中
// 记录待拖拽文件所属对象
const moduleSnapshot = ref<FileModule>();
const dragStart = (targetFile: UploadFile, fileModule: FileModule) => {
  moveFile.value = true;
  if (!targetFile?.operType) {
    return;
  }
  dragFile.value = targetFile;
  moduleSnapshot.value = fileModule;
};

const removeFile = async (file: UploadFile, fileList: UploadFile[]) => {
  const removeIndex = fileList.findIndex((item) => item.documentId === file.documentId);
  fileList.splice(removeIndex, 1);
};

const dragEnd = () => {
  moveFile.value = false;
};

const drop = (targetModule: FileModule) => {
  if (moduleSnapshot.value && dragFile.value) {
    const selectedList = moduleSnapshot.value?.fileDetailList.filter((i) => i.selected);
    if (selectedList.length > 1 && dragFile.value.selected) {
      selectedList.forEach((current) => {
        current.selected = false;
        targetModule.fileDetailList.push(current);
        removeFile(current, moduleSnapshot.value?.fileDetailList || []);
      });
    } else {
      targetModule.fileDetailList.push(dragFile.value);
      removeFile(dragFile.value, moduleSnapshot.value.fileDetailList);
    }
    handleCheckedChange(targetModule);
    moduleSnapshot.value.checkedAll = false;
    moduleSnapshot.value.indeterminate = false;
  }
};

const isDragging = ref(false); // 文件上传拖拽中

const handleDragEnter = () => {
  if (moveFile.value) {
    return;
  }
  isDragging.value = true;
};

const handleDragOver = () => {
  if (moveFile.value) {
    return;
  }
  isDragging.value = true;
};

const handleDragLeave = (e) => {
  // 确保只在鼠标真正离开容器时触发
  if (e.currentTarget.contains(e.relatedTarget)) return;
  isDragging.value = false;
};

const handleDrop = (e) => {
  isDragging.value = false;

  // 如果是从外部拖拽的文件（而不是内部元素拖拽）
  if (e.dataTransfer.files.length > 0 && !moveFile.value) {
    const files = Array.from(e.dataTransfer.files);

    // 验证和上传文件
    files.forEach((file) => {
      handleFileChange(file, files, noclassify.typeCode, noclassify.fileDetailList);
    });
  }

  // 重置状态
  moveFile.value = false;
};

const isDraggingItem = ref(false); // 文件上传拖拽中
const handleDragEnterItem = () => {
  if (moveFile.value) {
    return;
  }
  isDraggingItem.value = true;
};

const handleDragOverItem = () => {
  if (moveFile.value) {
    return;
  }
  isDraggingItem.value = true;
};

const handleDragLeaveItem = (e) => {
  // 确保只在鼠标真正离开容器时触发
  if (e.currentTarget.contains(e.relatedTarget)) return;
  isDraggingItem.value = false;
};

const handleDropItem = (e, classify) => {
  isDraggingItem.value = false;
  // 如果是从外部拖拽的文件（而不是内部元素拖拽）
  if (e.dataTransfer.files.length > 0 && !moveFile.value) {
    const files = Array.from(e.dataTransfer.files);

    // 验证和上传文件
    files.forEach((file) => {
      handleFileChange(file, files, classify.typeCode, classify.fileDetailList);
    });
  } else {
    drop(classify);
  }
  // 重置状态
  moveFile.value = false;
};

const acceptType = ['BMP', 'DIB', 'JPG', 'JPEG', 'JPE', 'JFIF', 'GIF', 'TIF', 'TIFF', 'PNG', 'PSD', 'PCX', 'EXIF', 'DOC', 'XLS', 'PDF', 'MSG', 'HTM', 'PPT', 'PPTX', 'RAR', 'ZIP', 'TXT', 'XLSX', 'DOCX'];
const MAX_SIZE = 10 * 1024 * 1024; // 10MB
// 文件验证
const validateFile = (file: File, fileOptions: FileUploadParams): boolean => {
  const fileExtension = file.name.split('.').pop()?.toUpperCase();

  if (!fileExtension || !acceptType.includes(fileExtension)) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }

  if (fileOptions.fileType === 'A23' && fileExtension !== 'PDF') {
    message.warning('文本保单只能上传PDF文件');
    return false;
  }

  if (file.size > MAX_SIZE) {
    message.warning(`${file.name} 大小超过10MB`);
    return false;
  }

  const isDuplicate = (list: UploadFile[]) => list.some((_file) => _file.documentName === file.name);
  if (isDuplicate(noclassify.fileDetailList || []) || classifyList.value.some((classify) => isDuplicate(classify.fileDetailList || []))) {
    message.warning(`${file.name}附件已存在，不能再上传`);
    return false;
  }

  return true;
};

const { upload, showFileUploadLoading, showFileUploadLoadingTips, uploadedFile } = useUploadFiles({ validate: validateFile });
const targetUploadList = ref<UploadFile>([]);

// 上传成功回调
watch(uploadedFile, (val) => {
  targetUploadList.value.unshift({ ...val, selected: false, operType: '1' });
  documentGroupId.value = val.documentGroupId;
});

const handleFileChange = async (file: File & { uid: string }, fileList: Array<File & { uid: string }>, targetType: string, targetList: UploadFile[]) => {
  // if (canUpload) {
  //   try {
  //     loading.value = true;
  //     const fetchUrl = `${gateWay}${service.administrate}/attachment/document/upload`;
  //     const formData = new FormData();
  //     formData.append('file', file);
  //     formData.append('bizNo', bizNo);
  //     formData.append('fileType', targetType);
  //     formData.append('bizType', bizType);
  //     formData.append('documentGroupId', documentGroupId.value);

  //     const { data, code, msg } = await $postOnClient(fetchUrl, formData);
  //     if (code === SUCCESS_CODE) {
  //       targetList.push({ ...data, selected: false, operType: '1' });
  //     } else {
  //       message.error(msg || '文件上传失败，请重试');
  //     }
  //     loading.value = false;
  //   } catch (e) {
  //     console.error('Upload error:', e);
  //     message.error('文件上传失败，请重试');
  //   } finally {
  //     loading.value = false;
  //   }
  // }
  // return false;
  targetUploadList.value = targetList;
  return upload(file, fileList, {
    bizNo,
    bizType: bizType,
    fileType: targetType,
    documentGroupId: documentGroupId.value || '',
  });
};

const getFileIcon = (fileName: string) => {
  const fileExtension = fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  return fileTypeMap.get(fileExtension) || IcWenjianColor;
};

const handleDeleteFile = async () => {
  const noclassifySelected = noclassify.fileDetailList.filter((item) => item.selected);
  let classifySelected: UploadFile[] = [];
  classifyList.value.forEach((classify) => {
    const selectedList = classify.fileDetailList.filter((item) => item.selected);
    classifySelected = [...classifySelected, ...selectedList];
  });
  if (noclassifySelected?.length === 0 && classifySelected?.length === 0) {
    message.error('请至少勾选1个要删除的文件');
  } else {
    const deleteList = [...noclassifySelected.map((k) => k.documentId), ...classifySelected.map((v) => v.documentId)];
    const url = gateWay + service.administrate + '/attachment/document/delete';
    try {
      const { code } = await $postOnClient(url, { bizType, bizNo, documentId: deleteList.join(',') });
      if (code === SUCCESS_CODE) {
        noclassify.fileDetailList = noclassify.fileDetailList.filter((item) => !item.selected);
        noclassify.checkedAll = false;
        noclassify.indeterminate = false;
        classifyList.value.forEach((classify) => {
          classify.fileDetailList = classify.fileDetailList.filter((item) => !item.selected);
          classify.checkedAll = false;
          classify.indeterminate = false;
        });
        allDeleteModal.value = false;
      } else {
        message.error('删除失败，请重试!');
      }
    } catch (e) {
      message.error('删除失败，请重试!');
      console.log(e);
    }
  }
};

const viewRef = ref(null);
const viewList = ref<
  {
    bucketName: string;
    uploadPath: string;
    thumbnail: string; // 缩略图url
    documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
    documentName?: string;
    id?: string;
  }[]
>([]);
const viewCurrent = ref(0);
const handleClickFile = async (file: UploadFile, fileList: UploadFile[]) => {
  // 如果点击图片，则预览，否则下载
  if (file.thumbnail) {
    viewCurrent.value = fileList.findIndex((k) => k.documentId === file.documentId);
    viewList.value = fileList.map((v) => ({
      bucketName: v.bucketName,
      uploadPath: v.uploadPath,
      thumbnail: v.thumbnail,
      documentFormat: v.documentFormat,
      documentName: v.documentName,
      id: v.documentId,
    }));
    if (viewRef.value) {
      viewRef.value?.openPreview();
    }
  } else {
    const { data } =
      (await $postOnClient('/api/iobs/getInIobsUrl', {
        iobsBucketName: file.bucketName,
        fileKey: file.uploadPath,
        storageTypeCode: file.storageType,
        fileName: file.documentName,
        downloadFlag: true,
      })) || {};
    if (data?.fileUrl) {
      window.open(data?.fileUrl);
    }
  }
};

const addDocType = () => {
  if (!docTypeValue.value) {
    message.warning('请选择要添加的资料类型');
    return;
  }
  const selected = docTypeList.value.find((doc) => doc.value === docTypeValue.value);
  const hasAdd = classifyList.value.some((item) => item.typeCode === selected?.value);
  if (selected && !hasAdd) {
    classifyList.value.push({ typeName: selected.label, fileDetailList: [], checkedAll: false, indeterminate: false, typeCode: selected.value, docLimitFlag: '10' });
  } else {
    message.warning('该类型文件已存在，请勿重新添加');
  }
};

const deleteModal = ref(false);
const waitDeleteDocType = ref<string>();
const handleDeleteDoc = async (target: FileModule) => {
  waitDeleteDocType.value = target.typeCode;
  deleteModal.value = true;
};
const allDeleteModal = ref(false);
const deleteDocType = async () => {
  const fetchurl = gateWay + service.administrate + '/attachment/documentGroup/deleteByDocType';
  try {
    const { code } = await $postOnClient(fetchurl, {
      bizNo,
      docType: waitDeleteDocType.value,
      bizType,
      documentGroupId: documentGroupId.value,
    });
    if (code === SUCCESS_CODE) {
      classifyList.value = classifyList.value.filter((classify) => classify.typeCode !== waitDeleteDocType.value);
    }
  } catch (e) {
    console.log(e);
  } finally {
    deleteModal.value = false;
  }
};

const saveList = async () => {
  const fetchUrl = gateWay + service.endorse + '/web/infomodify/attachment/submitApply';
  const fileList: Array<{ typeCode: string; documentId: string; typeName: string; operType: string }> = [];
  classifyList.value.forEach((classify) => {
    classify.fileDetailList.forEach((file) => {
      fileList.push({
        typeCode: classify.typeCode,
        documentId: file.documentId,
        typeName: classify.typeName,
        operType: file.operType,
      });
    });
  });
  loadingSubmit.value = true;
  try {
    const { code, msg } = await $postOnClient(fetchUrl, { policyNo: bizNo, bizType, idEvntDocInfoMdfyDataTrc: trackId, documentGroupId: documentGroupId.value, saveFileDetailVOS: fileList });
    if (code === SUCCESS_CODE) {
      message.success('附件已上传并提交审核，请到资料补传跟踪页面查看审核结果');
      // 关闭当前页面
      deletPageTabListItem('/fileModify'); // 关闭页签
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
      // 刷新页面
      // getDocList();
    } else {
      message.error(msg || '提交审核失败');
    }
    loadingSubmit.value = false;
    debounceDisabled.value = false;
  } catch (e) {
    console.log(e);
    loadingSubmit.value = false;
    debounceDisabled.value = false;
  }
};

const debounceDisabled = ref(false);
// 防抖提交审核
const debounceSaveList = debounce(saveList, 3000);

const submit = () => {
  if (noclassify.fileDetailList.length > 0) {
    message.error('存在未分类的附件信息，请调整。');
  } else {
    debounceDisabled.value = true;
    debounceSaveList();
  }
};
</script>

<style lang="less" scoped>
.container-height {
  height: calc(100vh - 120px);
  overflow-y: scroll;
}
.noclassify-height {
  max-height: calc(100% - 200px);
  overflow-y: auto;
  overflow-x: hidden;
}
.sub-title {
  position: relative;
  font-size: 14px;
  color: #404442;
  font-weight: 600;
  padding-left: 8px;

  &::before {
    position: absolute;
    left: 0px;
    top: 4px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
.upload-box {
  width: 279px;
  height: 140px;
  background: rgba(7, 193, 96, 0.05);
  border: 1px dashed rgba(7, 193, 96, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-box-dragging {
  transform: scale(1.05);
  border: 2px dashed rgba(7, 193, 96, 1);
  background: rgba(7, 193, 96, 0.1);
  box-shadow: 0 0 10px rgba(7, 193, 96, 0.3);
}

.file-box {
  cursor: pointer;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  width: 130px;
  height: 130px;
  position: relative;
  background: #ffffff;
}

.file-box-dragging {
  .text {
    transform: scale(1.05);
  }
  .txdicon {
    transform: scale(1.05);
  }
}

.classify-container {
  background: #fafafc;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  padding: 12px;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 默认是3列布局 */
  gap: 8px; /* 网格间距 */
  max-height: 580px;
  overflow-y: auto;
  overflow-x: hidden;
}
@media (min-width: 1920px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr); /* 1920及以上为4列布局 */
  }
}
.green-tag {
  background: rgba(7, 193, 96, 0.05);
  border: 1px solid rgba(7, 193, 96, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #07c160;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blue-tag {
  background: rgba(61, 126, 255, 0.05);
  border: 1px solid rgba(61, 126, 255, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #3d7eff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.orange-tag {
  background: rgba(255, 91, 0, 0.05);
  border: 1px solid rgba(255, 91, 0, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #ff5b00;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-boxshadow {
  box-shadow: 0px 2px 12px 0px rgba(212, 214, 217, 1);
}
</style>
