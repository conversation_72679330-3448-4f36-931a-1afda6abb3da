<template>
  <div class="m-14px space-y-16px">
    <div class="rounded">
      <div class="rounded bg-white flex-grow p-12px mb-12px">
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold mb-8px">审核处理</div>
        <a-form ref="formRef" :colon="false" :model="auditData">
          <a-form-item label="审核结论" name="auditFlag" required>
            <a-radio-group v-model:value="auditData.auditFlag" @change="handleRadioChange">
              <a-radio value="B4">同意</a-radio>
              <a-radio value="B7">不同意</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="审核意见" name="approveResultDesc" required>
            <a-textarea v-model:value="auditData.approveResultDesc" placeholder="请输入" />
          </a-form-item>
        </a-form>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
      <div class="rounded bg-white flex-grow p-12px container-height">
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold mb-8px">上传信息</div>
        <UploadFiles />
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold mb-8px">审核内容</div>
        <Attachment />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue/es/radio/interface';
import Attachment from '../auditDetail/components/Attachment.vue';
import UploadFiles from '../auditDetail/components/UploadFiles.vue';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签

const route = useRoute();
const router = useRouter();

const taskId = ref<string>('');

const formRef = ref();
const auditData = ref({
  approveResultDesc: '',
  auditFlag: '',
});

// 提交
const submit = async () => {
  await formRef?.value?.validate();
  const fetchUrl = gateWay + service.ums + '/underwriteCommonApproval/otherUdwProcess';
  const params = {
    udwProcess: {
      taskId: taskId.value,
      underwriteTypeCode: '4',
      voucherNo: route.query.idEvntDocInfoMdfyDataTrc,
      approveStatus: auditData.value.auditFlag,
      approveResultDesc: auditData.value.approveResultDesc,
      attachmentGroupNo: route.query?.attachmentGroupNo || '',
      createdBy: route.query?.createdBy || '',
      policyNo: route.query.bizNo,
      isSend: Number(route.query.isSend),
    },
  };
  try {
    const { code, msg } = await $postOnClient(fetchUrl, params);
    if (code === SUCCESS_CODE) {
      message.success(msg || '提交审核成功');
      // 关闭当前页面
      deletPageTabListItem('/auditProcessing'); // 关闭页签
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
    } else {
      message.error(msg || '提交审核失败');
    }
  } catch (e) {
    console.log(e);
  }
};

const handleRadioChange = (e: RadioChangeEvent) => {
  if (e?.target?.value === 'B4') {
    auditData.value.approveResultDesc = '同意';
  } else if (e?.target?.value === 'B7') {
    auditData.value.approveResultDesc = '不同意';
  }
};

// 获取任务号
const getTaskId = async () => {
  const fetchUrl = gateWay + service.ums + '/underwriteCommonApproval/getCurrentTaskInfo';
  try {
    const { code, data, msg } = await $getOnClient(fetchUrl, { voucherNo: route.query.idEvntDocInfoMdfyDataTrc, underwriteTypeCode: '4' });
    if (code === SUCCESS_CODE && data) {
      taskId.value = data.taskId;
    } else {
      message.error(msg || '获取任务号失败');
    }
  } catch (e) {
    console.log(e);
  }
};

watch(
  () => [route.query.bizNo, route.query.bizType, route.query.idEvntDocInfoMdfyDataTrc],
  () => {
    getTaskId();
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
.container-height {
  height: calc(100vh - 120px);
  overflow-y: scroll;
}
</style>
