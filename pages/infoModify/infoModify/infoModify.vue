<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row>
            <a-col span="10">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保单生成日期" name="insureDate" :label-col="{ style: { width: pxToRem(110) } }">
                <a-range-picker v-model:value="searchFormState.insureDate" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="0">
            <a-col span="10">
              <a-form-item label="保单号" name="policyNo">
                <a-input v-model:value.trim="searchFormState.policyNo" placeholder="请输入" @blur="handleBlurDisabled" />
              </a-form-item>
            </a-col>
            <a-col span="14">
              <a-form-item label="资料补交状态" name="docToSupply" :label-col="{ style: { width: pxToRem(110) } }">
                <a-radio-group v-model:value="searchFormState.docToSupply" :options="docToSupplyOptions" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="mb-16px flex justify-between">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
        <a-space>
          <AuthButton code="infoModification:infoModify:batchFeeModify" type="primary" @click="handleBatchFeeModify">批量费用修改</AuthButton>
          <AuthButton code="infoModification:infoModify:batchFeeModify" type="primary" @click="handleDisasterPrevention">防灾防损</AuthButton>
        </a-space>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" row-key="idPlySummaryInfo" :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'policyNo'">
            <div class="flex items-center">
              <CopyLink :text="text" @click="openDetail(text)" />
              <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'insuranceBeginDate'">
            {{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }}
          </template>
          <template v-if="['marketproductName', 'insuredName', 'departmentName', 'riskTypeName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <AuthButton code="infoModification:infoModify:attachmentModify" type="link" @click="modify(record.policyNo)">资料修改</AuthButton>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="openVisible" centered :width="pxToRem(450)">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div>该保单下存在未审核的资料，需先撤回未审核的资料，再继续补传，补传完成后一并重新提交审核</div>
      <template #footer>
        <a-button type="primary" @click="recall">撤回</a-button>
        <a-button @click="openVisible = false">取消</a-button>
      </template>
    </a-modal>
    <BatchFeeModifyModal v-if="batchFeeVisible" v-model:batch-fee-visible="batchFeeVisible" />
    <disasterPreventionModal v-if="disasterPreventionVisible" v-model:visible="disasterPreventionVisible" />
  </div>
</template>

<script lang="ts" setup>
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { SearchFormState, DataType } from './infoModify.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $getOnClient, $postOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import CopyLink from '@/components/ui/CopyLink.vue';
import AuthButton from '@/components/ui/AuthButton.vue';
import BatchFeeModifyModal from './components/batchFeeModifyModal.vue';
import disasterPreventionModal from './components/disasterPreventionModal.vue';

// 二次确认弹窗显示
const openVisible = ref<boolean>(false);
const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentName', fixed: 'left' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '产品名称', dataIndex: 'marketproductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '保费', dataIndex: 'totalActualPremium' },
  { title: '保单生成日期', dataIndex: 'createdDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
const router = useRouter();
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containLowDepartment: true, // 是否包含下级
  insureDate: [dayjs().subtract(1, 'month'), dayjs()], // 保单生成日期
  policyNo: '', // 保单号
  docToSupply: undefined, //资料补交状态(默认全部)
});
const docToSupplyOptions = [
  { label: '待补交', value: '0' },
  { label: '已补交', value: '1' },
  { label: '全部', value: undefined },
];
const { gateWay, service } = useRuntimeConfig().public || {};
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const getListReq = await usePost<DataType>(`${gateWay}${service.endorse}/web/infomodify/list`);
// 分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      policyNo: searchFormState.policyNo,
      departmentCode: searchFormState.departmentCode,
      containLowDepartment: searchFormState.containLowDepartment,
      inputStartDate: searchFormState.insureDate?.[0]?.format('YYYY-MM-DD') || '',
      inputEndDate: searchFormState.insureDate?.[1]?.format('YYYY-MM-DD') || '',
      docToSupply: searchFormState.docToSupply === undefined ? undefined : Number(searchFormState.docToSupply), // 全部传undefined
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value?.resetFields();
  searchForm.value?.clearValidate();
  disabled.value = false;
  refresh();
};
// 打开保单详情页
const openDetail = (policyNo: string) => {
  const path = {
    path: '/policyDetails',
    query: {
      policyNo: policyNo,
    },
  };
  router.push(path);
};
// 表格的select选择框
// type Key = string | number;
// const rowSelection: TableProps['rowSelection'] = {
//   onChange: (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => onSelectChange(selectedRowKeys, selectedRows),
//   getCheckboxProps: (record: insuranceTrackingType) => ({
//     disabled: false,
//     applyStatus: record.applyStatus,
//   }),
// };
// 表格选中ID
// const selectedRow = ref<insuranceTrackingType[]>([]);
// const selectedRowKey = ref<Key[]>([]);
// 选择数据
// const onSelectChange = (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => {
//   selectedRow.value = selectedRows;
//   selectedRowKey.value = selectedRowKeys;
// };

// 单号有值时其余搜索条件禁用,交互逻辑同投保跟踪
const disabled = ref<boolean>(false);
const handleBlurDisabled = async () => {
  if (searchFormState.policyNo) {
    disabled.value = true;
    searchFormState.insureDate = [];
    searchFormState.docToSupply = undefined;
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: searchFormState.policyNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      searchFormState.departmentCode = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    searchFormState.departmentCode = defaultDeptCode.value;
    searchFormState.insureDate = [dayjs().subtract(1, 'month'), dayjs()];
  }
};
const idEvntDocInfoMdfyDataTrc = ref<string>('');
const bizNo = ref<string>('');
// 资料修改
const modify = async (policyNo: string) => {
  const res = await $getOnClient<{ data: Record<string, string> }>(gateWay + service.endorse + '/web/infomodify/check', { policyNo });
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    if (res.data.data?.checkStatusCode === '2') {
      // 待审核
      openVisible.value = true;
      idEvntDocInfoMdfyDataTrc.value = res.data.data?.idEvntDocInfoMdfyDataTrc || '';
      bizNo.value = res.data.data?.policyNo || '';
    } else {
      router.push({
        path: '/fileModify',
        query: {
          bizNo: policyNo,
          idEvntDocInfoMdfyDataTrc: res.data.data?.idEvntDocInfoMdfyDataTrc,
        },
      });
    }
  } else {
    message.error(res?.msg);
  }
};
// 撤回
const recall = async () => {
  const params = {
    bizNo: bizNo.value,
    idEvntDocInfoMdfyDataTrc: idEvntDocInfoMdfyDataTrc.value,
  };
  const res = await $postOnClient<Record<string, string>>(gateWay + service.endorse + '/web/attachmentModify/trace/withdraw', params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    openVisible.value = false;
    message.success(res?.msg);
    search();
  } else {
    message.error(res?.msg);
  }
};

// 批量费用修改
const batchFeeVisible = ref(false);
const handleBatchFeeModify = () => {
  batchFeeVisible.value = true;
};

const disasterPreventionVisible = ref(false);
// 防灾防损
const handleDisasterPrevention = () => {
  disasterPreventionVisible.value = true;
};

const route = useRoute();
onMounted(() => {
  searchFormState.docToSupply = route.query.docToSupply === undefined ? undefined : String(route.query.docToSupply);
  if (route.query.startTime && route.query.endTime) {
    searchFormState.insureDate = [dayjs(String(route.query.startTime)), dayjs(String(route.query.endTime))];
  }
  search();
});
watch(
  () => route.query,
  (val) => {
    if (val.docToSupply) {
      searchFormState.docToSupply = val.docToSupply === undefined ? undefined : String(val.docToSupply);
      if (route.query.startTime && route.query.endTime) {
        searchFormState.insureDate = [dayjs(String(route.query.startTime)), dayjs(String(route.query.endTime))];
      }
    }
    if (val.bizNo) {
      searchFormState.policyNo = val.bizNo as string;
      handleBlurDisabled();
    }
    search();
  },
  { deep: true, immediate: true },
);
</script>
