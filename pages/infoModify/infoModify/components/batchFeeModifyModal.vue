<template>
  <a-modal v-model:open="batchFeeVisible" title="批量费用修改" centered :mask-closable="false" :width="pxToRem(800)" :body-style="{ paddingBottom: '0px', maxHeight: '600px' }">
    <div class="mb-[16px]">
      <div class="flex items-center mb-[8px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[2px]">上传修改内容</span>
        </div>
        <a-button type="link" style="padding-right: 0px" @click="handleDownloadTemplate">
          <template #icon>
            <VueIcon :icon="IconAttachmentFont" />
          </template>
          <span class="ml-[8px]">费用修改模板.xlsx</span>
        </a-button>
      </div>
      <div class="rounded-[4px] py-[14px] px-[16px] bg-[#F8F8F8]">
        <a-upload-dragger accept=".xlsx" name="file" :before-upload="(val) => beforeUpload(val, '1')" :max-count="1" :show-upload-list="false" :action="handleFileChange">
          <p class="text-[30px] mt-[0px] mb-[-4px]">
            <VueIcon :icon="IconTongyongShangchuanFont" />
          </p>
          <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
        </a-upload-dragger>
        <div v-if="currentModifyFile?.fileName" class="text-[12px] mt-[10px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px]">{{ currentModifyFile.fileName }}</span>
        </div>
      </div>
    </div>
    <div class="mb-[16px]">
      <div class="flex items-center mb-[8px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[2px]">上传修改证明材料</span>
        </div>
      </div>
      <div class="rounded-[4px] py-[14px] px-[16px] bg-[#F8F8F8]">
        <a-upload-dragger accept=".jpg,.png,.gif,.tif,.jpeg,.bmp,.doc,.docx,.xls,.pdf,.pptx,.ppt,.rar,.zip,.txt,.xlsx,.htm" name="file" :before-upload="(val) => beforeUpload(val, '2')" :max-count="1" :show-upload-list="false" :action="handleProofFileChange">
          <p class="text-[30px] mt-[0px] mb-[-4px]">
            <VueIcon :icon="IconTongyongShangchuanFont" />
          </p>
          <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
        </a-upload-dragger>
        <div v-if="currentProofFile?.fileName" class="text-[12px] mt-[10px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px]">{{ currentProofFile.fileName }}</span>
        </div>
      </div>
      <div class="flex items-baseline mt-[8px] text-[#0000008C]">
        <div class="flex-shrink-0">温馨提示：</div>
        <div>1. 整个附件大小不能超过4M；2. 附件图片格式为：JPG、PNG、GIF、TIF、BMP；3. 文档附件的格式为：DOC、DOCX、XLS、PDF、PPT、PPTX、RAR、ZIP、TXT、XLSX、HTM</div>
      </div>
    </div>
    <a-form ref="formReasonRef" :model="formReason" :colon="false" layout="vertical">
      <a-form-item name="modifyReason" :rules="[{ required: true, message: '请填写修改原因' }]">
        <template #label>
          <span class="text-[#0000008C]">填写修改原因</span>
        </template>
        <a-textarea v-model:value="formReason.modifyReason" placeholder="最长1200字" show-count :maxlength="1200" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="batchFeeVisible = false">取消</a-button>
      <!-- 确认修改 -->
      <a-button key="submit" :loading="loadingConfirm" type="primary" @click="handleConfirmModify">确定</a-button>
    </template>
  </a-modal>
  <!-- eoa审批 -->
  <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" eoa-type="T14" approval-chain-type="0" title="费用信息修改-EOA签报申请页" :eoa-title="`关于保单【${modifyInfo.policyNo || ''}】进行费用信息修改的申请`" :eoa-content="formReason.modifyReason" :department-code="modifyInfo.departmentCode" show-uploadbtn eoa-title-edit :attachment-list="attachmentList" :extern-params="externParams" @ok="handleEoaSignOff" />
  <!-- 文件格式不正确 -->
  <a-modal v-model:open="warningOpen" title="文件格式不正确" :width="pxToRem(450)" centered>
    <div class="flex items-baseline">
      <VueIcon class="text-[18px] text-[#f90] mr-[8px] translateY-4px" :icon="IconErrorCircleFilledFont" />
      <div>{{ warningText }}</div>
    </div>
    <template #footer>
      <a-button type="primary" @click="warningOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconTongyongShangchuanFont, IconErrorCircleFilledFont, IconAttachmentFont, IconPingtaijiekouFont } from '@pafe/icons-icore-agr-an';
import type { UploadFile } from 'ant-design-vue';
import { notification, Button } from 'ant-design-vue';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, downloadFile } from '@/utils/tools';

const { gateWay, service } = useRuntimeConfig().public || {};

const batchFeeVisible = defineModel<boolean>('batchFeeVisible', { default: false });

const formReason = ref({
  modifyReason: '', // 原因
});

const showApproval = ref(false);
const handleDownloadTemplate = async () => {
  try {
    const res = await $getOnClient(`${gateWay}${service.endorse}/web/infomodify/queryCostInfoModifyModelUrl`);
    const { msg = '', data: url } = res || {};
    if (url) {
      downloadFile(url as string);
    } else {
      message.error(msg);
    }
  } catch (err) {
    message.error(err as string);
  }
};

const downloadErrorInfo = (data: { errorMsg: string; errorFileKey: string }) => {
  const key = 'errorNotice';
  notification.error({
    message: '错误信息',
    description: () => {
      return h(
        'div',
        {
          style: {
            'white-space': 'pre-wrap',
            'word-wrap': 'break-word',
            'word-break': 'break-all',
          },
        },
        data.errorMsg || '',
      );
    },
    duration: null,
    key,
    btn: () =>
      h(
        Button,
        {
          type: 'link',
          size: 'small',
          onClick: async () => {
            const res = await $getOnClient(`${gateWay}${service.endorse}/web/infomodify/queryFileDownloadUrl`, { fileKey: data.errorFileKey });
            const { msg = '', data: url } = res as { msg: string; data: string };
            if (url) {
              downloadFile(url);
              if (key) {
                notification.close(key);
              }
            } else {
              message.error(msg);
            }
          },
        },
        { default: () => '下载完整信息' },
      ),
  });
};

interface ICurrentFile {
  fileKey: string;
  fileName: string;
  fileSize: string;
}

const currentModifyFile = ref<ICurrentFile>();
const handleFileChange = (file: File) => {
  const { name, size } = file;
  if (name && size) {
    currentModifyFile.value = { fileKey: '', fileName: '', fileSize: '0' };
    attachmentList.value[0] = { attachUrl: '', docoldName: '', fileSize: '0' };
    const formData = new FormData();
    formData.append('file', file);
    $postOnClient(`${gateWay}${service.endorse}/web/infomodify/uploadEdrCostInfoFile`, formData)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          currentModifyFile.value = res.data as ICurrentFile;
          const { uploadResult } = res.data as { uploadResult: boolean };
          if (!uploadResult) {
            downloadErrorInfo(res.data as { errorMsg: string; errorFileKey: string });
          } else {
            message.success(res.msg);
            notification.close('errorNotice');
          }
          attachmentList.value[0] = {
            attachUrl: currentModifyFile.value.fileKey,
            docoldName: currentModifyFile.value.fileName,
            fileSize: currentModifyFile.value.fileSize,
          };
        } else if (res) {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        message.error(err);
      });
  }
  return Promise.reject();
};

const currentProofFile = ref<ICurrentFile>({ fileKey: '', fileName: '', fileSize: '0' });
const handleProofFileChange = (file: File) => {
  const { name, size } = file;
  if (name && size) {
    currentProofFile.value = { fileKey: '', fileName: '', fileSize: '0' };
    attachmentList.value[1] = { attachUrl: '', docoldName: '', fileSize: '0' };
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileKey', '');
    $postOnClient(`${gateWay}${service.endorse}/web/infomodify/uploadFile`, formData)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE && res.data) {
          message.success(res.msg);
          currentProofFile.value = res.data as ICurrentFile;
          attachmentList.value[1] = {
            attachUrl: currentProofFile.value.fileKey,
            docoldName: currentProofFile.value.fileName,
            fileSize: currentProofFile.value.fileSize,
          };
        } else if (res) {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        message.error(err);
      });
  }
  return Promise.reject();
};
// 上传前检查格式
const warningOpen = ref(false);
const warningText = ref('');
const beforeUpload = (file: UploadFile, fileType: '1' | '2') => {
  if (fileType === '1') {
    const isExcel = /\.(xlsx)$/i.test(file.name);
    if (!isExcel) {
      warningText.value = `文件${file.name}格式不正确，请上传 xlsx 文件。`;
      warningOpen.value = true;
      return false;
    }
    return true;
  } else if (fileType === '2' && file?.size) {
    const flag = file.size / 1024 / 1024 < 4;
    const type = file.name.substr(file.name.lastIndexOf('.') + 1).toUpperCase();
    const allType = ['BMP', 'JPG', 'GIF', 'TIF', 'PNG', 'DOC', 'XLS', 'PDF', 'HTM', 'PPT', 'PPTX', 'RAR', 'ZIP', 'TXT', 'XLSX', 'DOCX'];
    if (!flag || !allType.includes(type)) {
      if (!allType.includes(type)) {
        warningText.value = `文件${file.name}格式不正确，请上传符合格式的文件。`;
      }
      if (!flag) {
        warningText.value = `文件${file.name}过大，请上传小于4M文件。`;
      }
      warningOpen.value = true;
      return false;
    }
    return true;
  } else {
    return false;
  }
};

const modifyInfo = ref({
  relationBusinessNo: '',
  policyNo: '',
  departmentCode: '',
});

const loadingConfirm = ref(false);
const formReasonRef = ref();
const externParams = ref();
const handleConfirmModify = () => {
  modifyInfo.value = { policyNo: '', departmentCode: '', relationBusinessNo: '' };
  formReasonRef.value.validate().then(() => {
    if (!currentModifyFile.value?.fileKey) {
      message.error('请上传修改内容');
      return;
    }
    if (!currentProofFile.value?.fileKey) {
      message.error('请上传修改证明材料');
      return;
    }
    const params = {
      fileKey: currentModifyFile.value.fileKey,
      addFileKey: currentProofFile.value.fileKey,
      modifyReason: formReason.value.modifyReason,
    };
    loadingConfirm.value = true;
    $postOnClient(`${gateWay}${service.endorse}/web/infomodify/submitEdrCostInfoFile`, params)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          showApproval.value = true;
          const { policyNo, departmentCode, batchId: relationBusinessNo, difDays } = res.data as { policyNo: string; departmentCode: string; batchId: string; difDays: number };
          modifyInfo.value = { policyNo, departmentCode, relationBusinessNo };
          externParams.value = { difDays };
        } else if (res) {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        message.error(err);
      })
      .finally(() => {
        loadingConfirm.value = false;
      });
  });
};

const attachmentList = ref([
  {
    attachUrl: '',
    docoldName: '',
    fileSize: '0',
  },
  {
    attachUrl: '',
    docoldName: '',
    fileSize: '0',
  },
]);
const handleEoaSignOff = (eoaData: Record<string, string>) => {
  const params = {
    endorseCreateEoaVO: {
      relationBusinessNo: modifyInfo.value.relationBusinessNo, // 批改申请单号
      eoaType: '019', // EOA类型
      eoaSubject: eoaData.eoaTitle, // 签报主题
      eoaBody: eoaData.eoaContent, // 签报内容
      documentGroupId: eoaData.documentGroupId, // 附件组ID
      departmentCode: modifyInfo.value.departmentCode, // 机构
      approveChainList: eoaData.approveChainList,
      productCode: '', // 产品编码传空
      attachmentList: attachmentList.value,
    },
  };
  $postOnClient(`${gateWay}${service.endorse}/eoa/createEoa`, params)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        message.success(res.msg);
        showApproval.value = false;
        batchFeeVisible.value = false;
      } else {
        message.error(res?.msg);
      }
    })
    .catch((err) => {
      message.error(err);
    }); // 创建eoa审批接口
};
</script>

<style lang="less" scoped>
.translateY-4px {
  transform: translateY(4px);
}
:deep(.ant-upload-wrapper .ant-upload-drag .ant-upload) {
  padding: 0;
}
</style>
