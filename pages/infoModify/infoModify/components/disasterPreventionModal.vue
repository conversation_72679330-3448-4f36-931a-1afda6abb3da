<template>
  <a-modal v-model:open="visible" title="防灾防损签报" centered :mask-closable="false" :width="pxToRem(800)" :body-style="{ paddingBottom: '0px', maxHeight: '600px' }">
    <div class="mb-[16px]">
      <div>主题</div>
      <a-input v-model:value="formState.title" disabled />
    </div>
    <div class="mb-[16px]">
      <div>内容</div>
      <a-textarea v-model:value="formState.content" auto-size />
    </div>
    <div class="mb-[16px]">
      <div class="flex items-center mb-[8px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[2px]">上传excel文件</span>
        </div>
        <a-button type="link" style="padding-right: 0px" @click="() => handleDownloadTemplate('excel')">
          <template #icon>
            <VueIcon :icon="IconAttachmentFont" />
          </template>
          <span class="ml-[8px]">下载excel模版</span>
        </a-button>
      </div>
      <div class="rounded-[4px] py-[14px] px-[16px] bg-[#F8F8F8]">
        <a-upload-dragger accept=".xls,.xlsx" name="file" :disabled="exUploadLoading" :before-upload="(val) => beforeUpload(val, '1')" :max-count="1" :show-upload-list="false" :action="handleFileChange">
          <a-spin :spinning="exUploadLoading">
            <p class="text-[30px] mt-[0px] mb-[-4px]">
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </p>
            <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
          </a-spin>
        </a-upload-dragger>
        <div v-if="excelFile?.fileName" class="text-[12px] mt-[10px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px]">{{ excelFile.fileName }}</span>
        </div>
      </div>
      <div class="flex items-baseline mt-[8px] text-[#0000008C]">
        <div class="flex-shrink-0">温馨提示：</div>
        <div>1. 整个附件大小不能超过20M；2. 文档附件的格式为：XLS、XLSX；</div>
      </div>
    </div>
    <div class="mb-[16px]">
      <div class="flex items-center mb-[8px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[2px]">上传word文件</span>
        </div>
        <a-button type="link" style="padding-right: 0px" @click="() => handleDownloadTemplate('word')">
          <template #icon>
            <VueIcon :icon="IconAttachmentFont" />
          </template>
          <span class="ml-[8px]">下载word模版</span>
        </a-button>
      </div>
      <div class="rounded-[4px] py-[14px] px-[16px] bg-[#F8F8F8]">
        <a-upload-dragger accept=".doc,.docx" name="file" :disabled="woUploadLoading" :before-upload="(val) => beforeUpload(val, '2')" :max-count="1" :show-upload-list="false" :action="handleProofFileChange">
          <a-spin :spinning="woUploadLoading">
            <p class="text-[30px] mt-[0px] mb-[-4px]">
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </p>
            <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
          </a-spin>
        </a-upload-dragger>
        <div v-if="wordFile?.fileName" class="text-[12px] mt-[10px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px]">{{ wordFile.fileName }}</span>
        </div>
      </div>
      <div class="flex items-baseline mt-[8px] text-[#0000008C]">
        <div class="flex-shrink-0">温馨提示：</div>
        <div>1. 整个附件大小不能超过20M；</div>
      </div>
    </div>
    <template #footer>
      <a-button @click="visible = false">取消</a-button>
      <!-- 确认修改 -->
      <a-button key="submit" :loading="loadingConfirm" type="primary" @click="handleConfirm">生成审批链</a-button>
    </template>
  </a-modal>
  <!-- eoa审批 -->
  <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" :eoa-type="eoaType" approval-chain-type="0" title="防灾防损-EOA签报申请页" :eoa-title="`关于保单【${modifyInfo.policyNo || ''}】防灾防损的申请`" :eoa-content="formState.content" :department-code="modifyInfo.departmentCode" show-uploadbtn eoa-title-edit :attachment-list="attachmentList" @ok="handleEoaSignOff" />
  <!-- 文件格式不正确 -->
  <a-modal v-model:open="warningOpen" title="文件格式不正确" :width="pxToRem(450)" centered>
    <div class="flex items-baseline">
      <VueIcon class="text-[18px] text-[#f90] mr-[8px] translateY-4px" :icon="IconErrorCircleFilledFont" />
      <div>{{ warningText }}</div>
    </div>
    <template #footer>
      <a-button type="primary" @click="warningOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconTongyongShangchuanFont, IconErrorCircleFilledFont, IconAttachmentFont, IconPingtaijiekouFont } from '@pafe/icons-icore-agr-an';
import type { UploadFile } from 'ant-design-vue';
import { notification, Button } from 'ant-design-vue';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, downloadFile } from '@/utils/tools';

const { gateWay, service } = useRuntimeConfig().public || {};

const visible = defineModel<boolean>('visible', { default: false });
const eoaType = ref('');
const exUploadLoading = ref(false);
const woUploadLoading = ref(false);
const formState = ref({
  title: '关于防灾防损费用申请', // 主题
  content: '领导好;\n***机构根据****拟对以下保单投放防灾防损费，现申请对此批保单防灾防损费用进行申请，请批示\n机构:\n场景:\n保单量:\n整体保费:\n防灾防损费用率:\n详细情况见附件。', // 内容
});

const showApproval = ref(false);
const handleDownloadTemplate = async (type: string) => {
  const url = type === 'word' ? '/web/infomodify/queryCalamityRateWordUrl' : '/web/infomodify/queryCalamityRateExcelModelUrl';
  try {
    const res = await $getOnClient(`${gateWay}${service.endorse}${url}`);
    const { msg = '', data } = res || {};
    if (data) {
      downloadFile(data as string);
    } else {
      message.error(msg);
    }
  } catch (err) {
    message.error(err as string);
  }
};

const downloadErrorInfo = (data: { errorMsg: string; errorFileKey: string }) => {
  const key = 'errorNotice';
  notification.error({
    message: '错误信息',
    description: () => {
      return h(
        'div',
        {
          style: {
            'white-space': 'pre-wrap',
            'word-wrap': 'break-word',
            'word-break': 'break-all',
          },
        },
        data.errorMsg || '',
      );
    },
    duration: null,
    key,
    btn: () =>
      h(
        Button,
        {
          type: 'link',
          size: 'small',
          onClick: async () => {
            const res = await $getOnClient(`${gateWay}${service.endorse}/web/infomodify/queryFileDownloadUrl`, { fileKey: data.errorFileKey });
            const { msg = '', data: url } = res as { msg: string; data: string };
            if (url) {
              downloadFile(url);
              if (key) {
                notification.close(key);
              }
            } else {
              message.error(msg);
            }
          },
        },
        { default: () => '下载完整信息' },
      ),
  });
};

interface ICurrentFile {
  fileKey: string;
  fileName: string;
  fileSize: string;
}

const excelFile = ref<ICurrentFile>();
const handleFileChange = (file: File) => {
  const { name, size } = file;
  if (name && size) {
    exUploadLoading.value = true;
    excelFile.value = { fileKey: '', fileName: '', fileSize: '0' };
    attachmentList.value[0] = { attachUrl: '', docoldName: '', fileSize: '0' };
    const formData = new FormData();
    formData.append('file', file);
    $postOnClient(`${gateWay}${service.endorse}/web/infomodify/uploadCalamityRateModifyFile`, formData)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          excelFile.value = res.data as ICurrentFile;
          const { uploadResult } = res.data as { uploadResult: boolean };
          if (!uploadResult) {
            downloadErrorInfo(res.data as { errorMsg: string; errorFileKey: string });
          } else {
            message.success(res.msg);
            notification.close('errorNotice');
          }
          attachmentList.value[0] = {
            attachUrl: excelFile.value.fileKey,
            docoldName: excelFile.value.fileName,
            fileSize: excelFile.value.fileSize,
          };
        } else if (res) {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        message.error(err);
      })
      .finally(() => {
        exUploadLoading.value = false;
      });
  }
  return Promise.reject();
};

const wordFile = ref<ICurrentFile>({ fileKey: '', fileName: '', fileSize: '0' });
const handleProofFileChange = (file: File) => {
  const { name, size } = file;
  if (name && size) {
    woUploadLoading.value = true;
    wordFile.value = { fileKey: '', fileName: '', fileSize: '0' };
    attachmentList.value[1] = { attachUrl: '', docoldName: '', fileSize: '0' };
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileKey', '');
    $postOnClient(`${gateWay}${service.endorse}/web/infomodify/uploadFile`, formData)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE && res.data) {
          message.success(res.msg);
          wordFile.value = res.data as ICurrentFile;
          attachmentList.value[1] = {
            attachUrl: wordFile.value.fileKey,
            docoldName: wordFile.value.fileName,
            fileSize: wordFile.value.fileSize,
          };
        } else if (res) {
          message.error(res.msg);
        }
      })
      .catch((err) => {
        message.error(err);
      })
      .finally(() => {
        woUploadLoading.value = false;
      });
  }
  return Promise.reject();
};
// 上传前检查格式
const warningOpen = ref(false);
const warningText = ref('');
const beforeUpload = (file: UploadFile, fileType: '1' | '2') => {
  if (fileType === '1') {
    const isExcel = /\.xls[x]?$/i.test(file.name);
    if (!isExcel) {
      warningText.value = `文件${file.name}格式不正确，请上传 xls或xlsx 文件。`;
      warningOpen.value = true;
      return false;
    }
    return true;
  }
  if (fileType === '2') {
    const flag = file?.size / 1024 / 1024 < 20;
    const isWord = /\.doc[x]?$/i.test(file.name);
    if (!isWord) {
      warningText.value = `文件${file.name}格式不正确，请上传 doc或docx 文件。`;
      warningOpen.value = true;
      return false;
    }
    if (!flag) {
      warningText.value = `文件${file.name}过大，请上传小于20M文件。`;
      warningOpen.value = true;
      return false;
    }
    return true;
  }
};

const modifyInfo = ref({
  relationBusinessNo: '',
  policyNo: '',
  departmentCode: '',
});

const loadingConfirm = ref(false);
const handleConfirm = () => {
  modifyInfo.value = { policyNo: '', departmentCode: '', relationBusinessNo: '' };
  if (!excelFile.value?.fileKey) {
    message.error('请上传execl文件');
    return;
  }
  if (!wordFile.value?.fileKey) {
    message.error('请上传word文件');
    return;
  }
  const params = {
    fileKey: excelFile.value.fileKey,
    addFileKey: wordFile.value.fileKey,
  };
  loadingConfirm.value = true;
  $postOnClient(`${gateWay}${service.endorse}/web/infomodify/submitCalamityRateModifyFile`, params)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        showApproval.value = true;
        const { policyNo, departmentCode, batchId: relationBusinessNo, eoaType: eoaTypeStr } = res.data as { policyNo: string; departmentCode: string; batchId: string; difDays: number; eoaType: string };
        modifyInfo.value = { policyNo, departmentCode, relationBusinessNo };
        eoaType.value = eoaTypeStr;
      } else if (res) {
        message.error(res.msg);
      }
    })
    .catch((err) => {
      message.error(err);
    })
    .finally(() => {
      loadingConfirm.value = false;
    });
};

const attachmentList = ref([
  {
    attachUrl: '',
    docoldName: '',
    fileSize: '0',
  },
  {
    attachUrl: '',
    docoldName: '',
    fileSize: '0',
  },
]);
const handleEoaSignOff = (eoaData: Record<string, string>) => {
  const params = {
    endorseCreateEoaVO: {
      relationBusinessNo: modifyInfo.value.relationBusinessNo, // 批改申请单号
      eoaType: '044', // EOA类型
      eoaSubject: eoaData.eoaTitle, // 签报主题
      eoaBody: eoaData.eoaContent, // 签报内容
      documentGroupId: eoaData.documentGroupId, // 附件组ID
      departmentCode: modifyInfo.value.departmentCode, // 机构
      approveChainList: eoaData.approveChainList,
      productCode: '', // 产品编码传空
      attachmentList: attachmentList.value,
    },
  };
  $postOnClient(`${gateWay}${service.endorse}/eoa/createEoa`, params)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        message.success(res.msg);
        showApproval.value = false;
        visible.value = false;
      } else {
        message.error(res?.msg);
      }
    })
    .catch((err) => {
      message.error(err);
    }); // 创建eoa审批接口
};
</script>

<style lang="less" scoped>
.translateY-4px {
  transform: translateY(4px);
}
:deep(.ant-upload-wrapper .ant-upload-drag .ant-upload) {
  padding: 0;
}
</style>
