<template>
  <!-- 投保组织者信息 -->
  <div v-if="contractModel?.baseInfo?.applyPolicyType === '2'">
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">投保组织者信息</div>
    <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">名称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.organizerName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.organizerName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件类型</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.certificateTypeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.certificateTypeChName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件号码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ organizerInfo?.certificateNo || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.certificateNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件有效期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ organizerInfo?.certificateIssueDate || '-' }} 至 {{ organizerInfo?.certificateValidDate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.certificateIssueDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">地址</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.address || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.address" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">邮政编码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ organizerInfo?.postalCode || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.postalCode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">移动电话</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ organizerInfo?.contactTelephone || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.contactTelephone" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>

      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">电子邮箱</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.email || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.email" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">经营范围</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.businessScope || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.belongOrganizationNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">行业</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.completeIndustryChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.professionName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">组织机构类型</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ organizerInfo?.organizationTypeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.organizerInfo?.organizationTypeChName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 被保险人信息 -->
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">被保险人信息</div>
    <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">客户属性</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.personnelTypeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.personnelType" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">姓名</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.name || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.name" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="证件类型" :value="insurantInfo?.certificateTypeChName" />
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件号码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ insurantInfo?.certificateNo || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.certificateNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件有效期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ insurantInfo?.certificateIssueDate || '-' }} 至 {{ insurantInfo?.certificateValidDate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.certificateIssueDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">地址</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.address || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.address" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">邮政编码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ insurantInfo?.postcode || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.postcode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">性别</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.sexCodeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.sexCode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">移动电话</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ insurantInfo?.mobileTelephone || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.mobileTelephone" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="电子邮箱" :value="insurantInfo?.email" />
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">贫困户标识</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.poorSymbolChName }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.poorSymbol" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="insurantInfo?.poorSymbol === '1' && contractModel?.baseInfo?.secDeptCode == '219'" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">贫困户细分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.poorDetailTypeCnName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.poorDetailType" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">企业归属</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.belongOrganizationNoChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.belongOrganizationNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">职业</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.professionName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.professionName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">农业主体类型</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.subjectNumberName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.subjectName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">国籍</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ insurantInfo?.nationalityZh || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.insurant?.nationality" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <!-- 非核保页面需要展示的字段 -->
      <template v-if="!isInsureProcess">
        <DescriptionsField label="投保人与被保人关系" :value="insurantInfo?.relationshipWithApplicantChName" />
        <template v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'">
          <DescriptionsField label="性别" :value="insurantInfo?.sexCodeChName" />
          <DescriptionsField label="国籍" :value="insurantInfo?.nationalityZh" />
          <DescriptionsField label="职业" :value="insurantInfo?.professionName" />
          <DescriptionsField label="年收入(元)" :value="insurantInfo?.superviseInfoList?.[0]?.yearlySalaries" />
          <DescriptionsField label="工作单位名称" :value="insurantInfo?.superviseInfoList?.[0]?.companyName" />
        </template>
        <template v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'">
          <DescriptionsField label="企业归属" :value="insurantInfo?.belongOrganizationNoChName" />
          <DescriptionsField label="行业" :value="insurantInfo?.completeIndustryChName" />
          <DescriptionsField label="经营范围" :value="insurantInfo?.superviseInfoList?.[0]?.businessScope" />
          <DescriptionsField label="国有属性" :value="insurantInfo?.superviseInfoList?.[0]?.enterpriseTypeChName" />
          <DescriptionsField label="注册资本金(元)" :value="insurantInfo?.superviseInfoList?.[0]?.registeredFund" />
        </template>
      </template>
    </div>
    <!-- 被保人信息团体需要展示以下模块 -->
    <div v-if="contractModel?.insurantInfoList[0]?.personnelType === '0' && !isInsureProcess">
      <template v-for="(item, index) in contractModel?.insurantInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
        <!-- 授权办理业务人员 -->
        <div v-if="item.companyRelationType === '3'">
          <a-divider />
          <div class="form-title">
            <span>授权办理业务人员</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 法定代办人/负责人 -->
        <div v-if="item.companyRelationType === '2'">
          <a-divider />
          <div class="form-title">
            <span>法定代办人/负责人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 控股股东/实际控制人 -->
        <div v-if="item.companyRelationType === '1'">
          <a-divider />
          <div class="form-title">
            <span>控股股东/实际控制人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
            <DescriptionsField label="地址" :value="item.address" />
          </div>
        </div>
        <!-- 受益所有人 -->
        <div v-if="item.companyRelationType === '4'">
          <a-divider />
          <div class="form-title">
            <span>受益所有人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
            <DescriptionsField label="地址" :value="item.address" />
            <DescriptionsField label="判定受益所有人方式" :value="item.benefitModeChName" />
            <DescriptionsField label="受益所有人持股数量或表决权占比（%）" :value="item.equityRatio" />
          </div>
        </div>
      </template>
    </div>
  </div>
  <a-divider />
  <!-- 投保人信息 -->
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] text-[14px]">投保人信息</div>
    <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">客户属性</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.personnelTypeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.personnelType" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">姓名</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.name }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.name" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}<a-button v-if="item.extendDesc" :href="item.extendDesc" target="_blank" size="small" class="ml-[8px]" type="primary">查看签报</a-button></div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="证件类型" :value="applicantInfo?.certificateTypeChName" />
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件号码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ applicantInfo?.certificateNo }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.certificateNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件有效期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ applicantInfo?.certificateIssueDate }} 至 {{ applicantInfo?.certificateValidDate }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.certificateIssueDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">地址</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.address }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.address" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">邮政编码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ applicantInfo?.postcode }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.postcode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">性别</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.sexCodeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.sexCode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">移动电话</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ applicantInfo?.mobileTelephone || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.mobileTelephone" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="电子邮箱" :value="applicantInfo?.email" />
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">企业归属</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.belongOrganizationNoChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.belongOrganizationNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">职业</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.professionName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.professionName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isForestry" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">林业主体类型</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.subjectNumberName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.subjectName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">国籍</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ applicantInfo?.nationalityZh || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.applicant?.nationality" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <!-- 非核保页面需要展示的字段 -->
      <template v-if="!isInsureProcess">
        <template v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'">
          <DescriptionsField label="性别" :value="applicantInfo?.sexCodeChName" />
          <DescriptionsField label="国籍" :value="applicantInfo?.nationalityZh" />
          <DescriptionsField label="职业" :value="applicantInfo?.professionName" />
          <DescriptionsField label="年收入(元)" :value="applicantInfo?.superviseInfoList?.[0]?.yearlySalaries" />
          <DescriptionsField label="工作单位名称" :value="applicantInfo?.superviseInfoList?.[0]?.companyName" />
        </template>
        <template v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'">
          <DescriptionsField label="企业归属" :value="applicantInfo?.belongOrganizationNoChName" />
          <DescriptionsField label="行业" :value="applicantInfo?.completeIndustryChName" />
          <DescriptionsField label="经营范围" :value="applicantInfo?.superviseInfoList?.[0]?.businessScope" />
          <DescriptionsField label="国有属性" :value="applicantInfo?.superviseInfoList?.[0]?.enterpriseTypeChName" />
          <DescriptionsField label="注册资本金(元)" :value="applicantInfo?.superviseInfoList?.[0]?.registeredFund" />
        </template>
      </template>
    </div>
    <!-- 投保人信息团体需要展示以下模块 -->
    <div v-if="contractModel?.applicantInfoList[0]?.personnelType === '0' && !isInsureProcess">
      <template v-for="(item, index) in contractModel?.applicantInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
        <!-- 授权办理业务人员 -->
        <div v-if="item.companyRelationType === '3'">
          <a-divider />
          <div class="form-title">
            <span>授权办理业务人员</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 法定代办人/负责人 -->
        <div v-if="item.companyRelationType === '2'">
          <a-divider />
          <div class="form-title">
            <span>法定代办人/负责人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 控股股东/实际控制人 -->
        <div v-if="item.companyRelationType === '1'">
          <a-divider />
          <div class="form-title">
            <span>控股股东/实际控制人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
            <DescriptionsField label="地址" :value="item.address" />
          </div>
        </div>
        <!-- 受益所有人 -->
        <div v-if="item.companyRelationType === '4'">
          <a-divider />
          <div class="form-title">
            <span>受益所有人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
            <DescriptionsField label="地址" :value="item.address" />
            <DescriptionsField label="判定受益所有人方式" :value="item.benefitModeChName" />
            <DescriptionsField label="受益所有人持股数量或表决权占比（%）" :value="item.equityRatio" />
          </div>
        </div>
      </template>
    </div>
  </div>
  <a-divider />
  <!-- 受益人信息 -->
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] text-[14px]">受益人信息</div>
    <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">客户属性</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.personnelTypeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.personnelType" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">姓名</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.name || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.name" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="证件类型" :value="beneficaryInfo?.certificateTypeChName" />
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件号码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ beneficaryInfo?.certificateNo || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.certificateNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">证件有效期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ beneficaryInfo?.certificateIssueDate || '-' }} 至 {{ beneficaryInfo?.certificateValidDate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.certificateIssueDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">地址</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.address || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.address" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">邮政编码</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ beneficaryInfo?.postcode || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.postcode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">性别</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.sexCodeChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.sexCode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">移动电话</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ beneficaryInfo?.mobileTelephone || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.mobileTelephone" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <DescriptionsField v-if="!isInsureProcess" label="电子邮箱" :value="beneficaryInfo?.email" />
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">企业归属</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.belongOrganizationNoChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.belongOrganizationNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">职业</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.professionName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.professionName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="isInsureProcess" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">国籍</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ beneficaryInfo?.nationalityZh || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.beneficary?.nationality" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <!-- 非核保页面需要展示的字段 -->
      <template v-if="!isInsureProcess">
        <template v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '1'">
          <DescriptionsField label="性别" :value="beneficaryInfo?.sexCodeChName" />
          <DescriptionsField label="国籍" :value="beneficaryInfo?.nationalityZh" />
          <DescriptionsField label="职业" :value="beneficaryInfo?.professionName" />
        </template>
        <template v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0'">
          <DescriptionsField label="企业归属" :value="beneficaryInfo?.belongOrganizationNoChName" />
          <DescriptionsField label="行业" :value="beneficaryInfo?.completeIndustryChName" />
          <DescriptionsField label="经营范围" :value="beneficaryInfo?.superviseInfoList?.[0]?.businessScope" />
        </template>
        <DescriptionsField label="受益人与被保人关系" :value="beneficaryInfo?.relationshipWithInsuredChName" />
      </template>
    </div>
    <!-- 受益人信息团体需要展示以下模块 -->
    <div v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0' && !isInsureProcess">
      <template v-for="(item, index) in contractModel?.beneficaryInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
        <!-- 授权办理业务人员 -->
        <div v-if="item.companyRelationType === '3'">
          <a-divider />
          <div class="form-title">
            <span>授权办理业务人员</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 法定代办人/负责人 -->
        <div v-if="item.companyRelationType === '2'">
          <a-divider />
          <div class="form-title">
            <span>法定代办人/负责人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
        <!-- 控股股东/实际控制人/受益所有人 -->
        <div v-if="item.companyRelationType === '1'">
          <a-divider />
          <div class="form-title">
            <span>控股股东/实际控制人/受益所有人</span>
          </div>
          <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px">
            <DescriptionsField label="名称" :value="item.name" />
            <DescriptionsField label="证件类型" :value="item.certificateTypeChName" />
            <DescriptionsField label="证件号码" :value="item.certificateNo" />
            <DescriptionsField label="证件有效起期" :value="item.certificateIssueDate" />
            <DescriptionsField label="证件有效止期" :value="item.certificateValidDate" />
          </div>
        </div>
      </template>
    </div>
  </div>
  <a-divider />
</template>
<script lang="ts" setup>
import type { ContractModelType, ErrorModelType } from '@/pages/review/insureProcess/insureProcess';
import DescriptionsField from './components/DescriptionsField.vue';

// 获取信息大对象
const { contractModel, errorDataInfo } = defineProps<{
  contractModel: ContractModelType; // 投保VO对象
  errorDataInfo?: ErrorModelType; // 核保错误信息对象
  isInsureProcess?: boolean; // 是否为核保页面，核保页面有部分字段不展示
}>();
// 投保人信息数据处理
const applicantInfo = computed(() => contractModel?.applicantInfoList[0]);
// 受益人
const beneficaryInfo = computed(() => contractModel?.beneficaryInfoList[0]);
// 被保人
const insurantInfo = computed(() => contractModel?.insurantInfoList[0]);
const organizerInfo = computed(() => contractModel?.organizerInfo);

// 是否为林业险
const isForestry = computed(() => {
  return contractModel.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode === 'C';
});
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
</script>

<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
</style>
