<template>
  <div v-for="(riskInfo, riskIndex) in contractModel?.riskGroupInfoList" :key="riskIndex">
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      标的信息 {{ contractModel?.riskGroupInfoList?.length < 2 ? '' : riskIndex + 1 }}
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.subjectInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.subjectInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <div class="grid grid-cols-3 gap-x-16px gap-y-12px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险数量</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.riskAgrInfo?.insuredNumber || '-' }}{{ riskInfo?.riskAgrInfo?.insuredUnitChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.insuredNumber" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <!-- 养殖险添加字段 start -->
      <template v-for="(item, idx) in riskInfo?.riskAgrInfo?.agrRiskAttribute" :key="item.bussinessKey">
        <div v-if="['priceAgreed', 'breedAmount', 'unitInsureCount'].includes(item.bussinessKey)" class="flex items-baseline">
          <div class="text-[rgba(0,0,0,0.60)] shrink-0">{{ item.bussinessType }}</div>
          <div class="pl-[10px]">
            <div class="text-[#333333] font-number">{{ (item?.bussinessValue || '-') + (riskInfo?.riskAgrInfo?.agrRiskAttribute?.[idx + 1]?.bussinessValueChName || riskInfo?.riskAgrInfo?.agrRiskAttribute?.[idx + 1]?.bussinessValue || '') }}</div>
          </div>
        </div>
        <template v-else-if="['breedUnit', 'priceAgreedUnit', 'unitInsureCountUnit'].includes(item.bussinessKey)" />
        <div v-else class="flex items-baseline">
          <div class="text-[rgba(0,0,0,0.60)] shrink-0">{{ item.bussinessType }}</div>
          <div class="pl-[10px]">
            <div class="text-[#333333] font-number">
              <span>{{ item.bussinessValueChName || item.bussinessValue || '-' }}</span>
              <span v-if="item.bussinessKey.indexOf('pigstyArea') > -1">平方米</span>
              <span v-if="item.bussinessKey.indexOf('carbonSinkPrice') > -1">元/吨</span>
              <span v-if="item.bussinessKey.indexOf('carbonSinkTargetRate') > -1">%</span>
              <span v-if="item.bussinessKey.indexOf('carbonSinkTargetValue') > -1">吨</span>
            </div>
            <template v-if="item.bussinessKey.indexOf('pigstyArea') > -1">
              <div v-for="(obj, index) in errorDataInfo?.riskInfo?.pigstyArea" :key="index" class="text-[12px] pt-[4px]" :class="colorText(obj.promptLevel)">{{ obj.promptInfo }}</div>
            </template>
            <template v-if="item.bussinessKey.indexOf('diseaseObservation') > -1">
              <div v-for="(obj, index) in errorDataInfo?.riskInfo?.diseaseObservation" :key="index" class="text-[12px] pt-[4px]" :class="colorText(obj.promptLevel)">{{ obj.promptInfo }}</div>
            </template>
          </div>
        </div>
      </template>
      <!-- 养殖险添加字段 end -->
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">参保农户数</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.riskAgrInfo?.farmersCount || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.farmersCount" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">标的名称明细</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ riskInfo?.combinedProductName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.riskTypeDetailName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">单位保险金额</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.planInfoList?.[0]?.unitInsuredAmount || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.unitInsuredAmount" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险费率（%）</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.planInfoList?.[0]?.expectPremiumRate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.insuredRate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">单位保费</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.planInfoList?.[0]?.unitPrimium || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.unitPremium" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险金额</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.riskAgrInfo?.insuredAmount || '-' }}元</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.insuredAmount" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保费金额</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ riskInfo?.riskAgrInfo?.premium || '-' }}元</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.premium" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">标的地址</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.riskAddressInfoList[0]?.address || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.riskInfo?.address" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <a-divider v-if="riskIndex < contractModel?.riskGroupInfoList.length - 1" />
  </div>
</template>
<script lang="ts" setup>
import type { ContractModelType, ErrorModelType } from '@/pages/review/insureProcess/insureProcess';
import { FujianFxtsColor } from '@pafe/icons-icore-agr-an';

// 获取信息大对象
const { contractModel, errorDataInfo, riskTipList } = defineProps<{
  contractModel: ContractModelType; // 投保VO对象
  errorDataInfo?: ErrorModelType; // 核保错误信息对象
  riskTipList: Record<string, string[]>;
}>();
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
</script>
<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
</style>
