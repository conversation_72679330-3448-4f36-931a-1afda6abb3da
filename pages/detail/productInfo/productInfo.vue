<template>
  <div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">项目来源</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ projectInfos?.isTenderBusinessChName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.tenderBusiness" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">项目名称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ projectInfos?.tenderBusinessName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.tenderBusinessName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="showCustomerAndRisk" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">客户类型</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ customerTypeObj(projectInfos?.customerType) }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.customerType" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <div v-if="showCustomerAndRisk" class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">客户简称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ projectInfos?.customerAbbrName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.customerAbbrName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="showCustomerAndRisk" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">风险程度（%）</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ projectInfos?.lossRate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.lossRate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div v-if="extendGroupInfo?.productFactoryPlanType === '05'" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">期货公司名称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ projectInfos?.futureCompanyName || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.projectInfo?.futureCompanyName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { ContractModelType, ErrorModelType } from '@/pages/review/insureProcess/insureProcess';
import { debounce } from 'lodash-es';
import { SUCCESS_CODE } from '~/utils/constants';
import { $post } from '~/utils/request';

const { gateWay, service } = useRuntimeConfig().public || {};
// 获取信息大对象
const { contractModel, errorDataInfo } = defineProps<{
  contractModel: ContractModelType; // 投保VO对象
  errorDataInfo?: ErrorModelType; // 核保错误信息对象
}>();
// 客户类型转换
// 客户类型 01-重要客户 02-潜力客户 03-集团客户 04-非三类客户
const customerTypeObj = (val: string) => {
  const subsidyTypeMap: Record<string, string> = {
    '01': '重要客户',
    '02': '潜力客户',
    '03': '集团客户',
    '04': '非三类客户',
  };
  return subsidyTypeMap[val] || '-';
};
// 项目信息
const projectInfos = computed(() => contractModel?.extendInfo);
const extendGroupInfo = computed(() => contractModel?.extendGroupInfo);
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};

// 默认不展示风险程度和客户类型
const showCustomerAndRisk = ref<boolean>(false);
// 获取隐藏客户字段开关
const getCustomerSwitch = async () => {
  const url = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const params = {
    switchCode: 'CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH',
    allowanceTypeCode: contractModel?.baseInfo?.govSubsidyType || '',
    agriculturalRiskObjectClassCode: contractModel?.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode || '',
    departmentNo: contractModel?.baseInfo?.departmentCode,
  };
  try {
    const res = await $post(url, params);
    if (res && res.code === SUCCESS_CODE) {
      showCustomerAndRisk.value = res.data;
    } else {
      showCustomerAndRisk.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};
// 根据机构及标的地址控制开关显隐客户类型及风险程度字段
const debounceGetCustomerSwitch = debounce(getCustomerSwitch, 1000);
watch([() => contractModel?.baseInfo?.govSubsidyType, () => contractModel?.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode, () => contractModel?.baseInfo?.departmentCode], () => {
  if (contractModel?.baseInfo?.departmentCode) {
    debounceGetCustomerSwitch();
  }
});
</script>
<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
</style>
