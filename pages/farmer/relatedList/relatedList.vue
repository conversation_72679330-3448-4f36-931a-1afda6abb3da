<template>
  <div class="p-14px space-y-16px">
    <div class="bg-white p-16px rounded-md">
      <div class="flex">
        <a-form :colon="false" class="flex-grow">
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item label="清单编号/名称">
              <a-input v-model:value="formState.farmerlistNo" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="标的地址" class="col-span-3">
              <RegionSelect v-model:province="address.province" v-model:city="address.city" v-model:county="address.county" v-model:town="address.town" v-model:village="address.village" :disabled="true" />
            </a-form-item>
          </div>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md">
      <div class="flex justify-between items-center mb-16px">
        <span class="text-font-primary text-xl font-bold leading-[32px]">查询结果</span>
        <a-button type="primary" @click="toCreateList">创建清单</a-button>
      </div>
      <a-table :data-source="dataSource" :columns="columns" :pagination="pagination" :loading="loading" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'farmerlistNo'">
            <CopyLink :text="text" @click="handleToInfo(record)" />
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="link" @click="clickLink(record)">关联</a-button>
          </template>
          <template v-if="['farmerlistName', 'productName', 'addressName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
        </template>
      </a-table>
      <!-- 关联清单确认 -->
      <a-modal v-model:open="confirmModal" title="确认关联" @ok="confirmPost">
        <a-spin :spinning="confirmLoading">
          <div>{{ `是否要和${selectRecord.farmerlistNo}，${selectRecord.farmerlistName}进行关联。 清单户数:${selectRecord.farmersCount}户，承保数量${selectRecord.insuredNumber}${selectRecord.insuranceNumsUnitName}。` }}</div>
        </a-spin>
      </a-modal>
      <!-- 关联失败处理 -->
      <a-modal v-model:open="failModal" title="关联失败">
        <div v-for="(item, index) in failReason" :key="index">
          <span v-if="failReason.length > 2 && index < failReason.length - 1">{{ index + 1 }}.</span>
          {{ item }}
        </div>
        <template #footer>
          <div class="space-x-8px">
            <a-button @click="() => (failModal = false)">取消</a-button>
            <a-button type="primary" @click="editList">修改清单</a-button>
            <a-button v-if="!(selectRecord.errorCount > 0)" type="primary" @click="editAccept">修改投保单</a-button>
          </div>
        </template>
      </a-modal>
    </div>
    <!-- 清单信息弹窗 -->
    <InfoModal v-model:visible="infoVisible" :item="rowItem" />
  </div>
</template>

<script setup lang="ts">
import { Form } from 'ant-design-vue';
import type { ColumnsType } from 'ant-design-vue/es/table';
import { get } from 'lodash-es';
import type { SourceType, FormState } from '../farmer.d';
import { usePagination } from '@/composables/usePagination';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import InfoModal from '../farmerList/modal/infoModal.vue';
import CopyLink from '@/components/ui/CopyLink.vue';

// 表头配置
const columns: ColumnsType<Record<string, string>> = [
  { title: '投保清单编号', dataIndex: 'farmerlistNo' },
  { title: '清单名称', dataIndex: 'farmerlistName' },
  { title: '产品名称', dataIndex: 'productName' },
  { title: '清单状态', dataIndex: 'farmerlistStatusDesc' },
  { title: '创建人员', dataIndex: 'createdBy' },
  { title: '导入条数', dataIndex: 'importCount' },
  { title: '承保户数', dataIndex: 'farmersCount' },
  { title: '承保数量', dataIndex: 'insuredNumber' },
  { title: '风险条数', dataIndex: 'riskCount' },
  { title: '错误条数', dataIndex: 'errorCount' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

const address = computed(() => ({
  province: route.query.province as string,
  city: route.query.city as string,
  county: route.query.county as string,
  town: route.query.town as string,
  village: route.query.village as string,
}));

// 响应式数据
const formState = reactive<FormState>({ farmerlistNo: '' });
const loading = ref(false); // 表格加载中
const dataSource = ref<SourceType[]>([]); // 表格数据
const infoVisible = ref(false);
const rowItem = ref({});
const handleToInfo = (record: Record<string, string>) => {
  rowItem.value = {
    farmerlistNo: record.farmerlistNo || '',
    farmerlistName: record.farmerlistName || '',
    farmerlistStatusDesc: record.farmerlistStatusDesc || '',
    address: record.addressName,
    createdBy: record.createdBy || '',
    importCount: record.importCount,
    riskCount: record.riskCount,
    errorCount: record.errorCount,
    importDate: record.importDate || '',
    oprRoleName: record.oprRoleName || '',
    departmentName: record.departmentName || '',
    departmentCode: record.departmentCode || '',
    productName: record.productName || '',
    applyPolicyNo: record.applyPolicyNo || '-',
    errorruleContents: record.errorruleContents || '-',
  };
  infoVisible.value = true;
};

const refresh = async () => {
  loading.value = true;
  try {
    const res = await $postOnClient<{ records: SourceType[] }>(`${gateWay}${service.accept}/web/applicationForm/pageFarmerListSummary`, {
      departmentCode: route.query.departmentCode, // 出单机构
      productCode: route.query.productCode, // 市场产品编码
      riskAddressCodeVillage: route.query.village,
      farmerlistNo: formState.farmerlistNo, // 清单编号支持模糊搜索
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    if (res && res.code === SUCCESS_CODE) {
      const { records = [] } = res.data || {};
      dataSource.value = records.map((record: SourceType) => {
        const { riskAddressNameProvince, riskAddressNameCity, riskAddressNameCounty, riskAddressNameTown, riskAddressNameVillage } = record;
        const addressName = [riskAddressNameProvince, riskAddressNameCity, riskAddressNameCounty, riskAddressNameTown, riskAddressNameVillage].reduce((acc, cur) => (cur ? acc + cur : acc), '');
        return {
          ...record,
          addressName,
        };
      });
      pagination.total = get(res, 'data.total', 0);
      pagination.current = get(res, 'data.current', 1);
      pagination.pageSize = get(res, 'data.size', 10);
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const submit = () => {
  refresh();
};

const reset = () => {
  resetFields();
  refresh();
};

const toCreateList = () => {
  router.push({
    path: '/farmerList',
    query: {
      departmentCode: route.query.departmentCode, // 出单机构
      productCode: route.query.productCode, // 市场产品编码
      province: route.query.province,
      city: route.query.city,
      county: route.query.county,
      town: route.query.town,
      village: route.query.village,
      openImport: 'Y',
    },
  });
};

const failModal = ref(false);
const failReason = ref<string[]>([]);
const selectRecord = ref();
const confirmModal = ref(false);
const { deletPageTabListItem } = inject('pageTab'); // 关闭页签
const confirmLoading = ref(false);
const confirmPost = () => {
  const fetchurl = gateWay + service.accept + '/web/applicationForm/linkFarmerList';
  confirmLoading.value = true;
  $postOnClient(fetchurl, {
    applyPolicyNo: route.query.applyPolicyNo,
    farmerlistNo: selectRecord.value.farmerlistNo,
  })
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        message.success(res.msg);
        router.push({
          path: '/insuranceFormFill',
          query: {
            applyPolicyNo: route.query.applyPolicyNo || '',
            from: 'relatedList',
          },
        });
        deletPageTabListItem('/relatedFarmerList'); // 关闭页签
      } else {
        failReason.value = res?.msg?.split(';') || [];
        failModal.value = true;
      }
    })
    .finally(() => {
      confirmLoading.value = false;
      confirmModal.value = false;
    });
};
// 打开关联清单确认弹窗
const clickLink = async (record: Record<string, string>) => {
  if (record.farmerlistStatus === '-3') {
    message.warning('该清单状态为执行失败，请点击清单编号检查错误日志。');
    return;
  }
  if (record.farmerlistStatus === '-1') {
    message.warning('该清单状态为执行中');
    return;
  }
  selectRecord.value = record;
  // const res = await $getOnClient(gateWay + service.farmer + '/farmerList/queryErrorRule', { farmerListNo: record.farmerlistNo });
  // if (res?.code === SUCCESS_CODE && Number(res?.data) > 0) {
  //   failReason.value = ['该清单存在错误信息，请前往“清单管理”模块修正后操作'];
  //   failModal.value = true;
  // } else {
  //   confirmModal.value = true;
  // }
  confirmModal.value = true;
};

// 修改清单
const editList = () => {
  failModal.value = false;
  router.push({
    path: '/editFarmerList',
    query: {
      id: selectRecord.value.farmerlistNo || '',
    },
  });
};

// 修改投保单
const editAccept = () => {
  failModal.value = false;
  router.push({
    path: '/insuranceFormFill',
    query: {
      applyPolicyNo: route.query.applyPolicyNo || '',
    },
  });
};

// 生命周期
onActivated(() => {
  refresh();
});

// hooks
const { gateWay, service } = useRuntimeConfig().public || {};
const route = useRoute();
const router = useRouter();
const { pagination } = usePagination(refresh);
const { resetFields } = Form.useForm(formState);
</script>
