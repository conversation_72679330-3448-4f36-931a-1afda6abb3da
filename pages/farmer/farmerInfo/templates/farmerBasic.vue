<template>
  <!-- 农户信息模板组件 -->
  <div class="grid grid-cols-3 gap-x-16px">
    <!-- 被保险人 -->
    <template v-if="farmerBasic.farmerName">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'farmerName', 'value']" :label-col="labelCol" :data="farmerBasic.farmerName" :extra-rules="[{ max: 100, trigger: 'change' }]">
        <a-input v-model:value="farmerBasic.farmerName.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 证件类型 -->
    <template v-if="farmerBasic.certificateType">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'certificateType', 'value']" :label-col="labelColLarge" :data="farmerBasic.certificateType" :options="farmer_certtype_unit_code">
        <a-select v-model:value="farmerBasic.certificateType.value" :options="farmer_certtype_unit_code" style="width: 100%" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 证件号码 -->
    <!-- // 身份证，统一社会信用代码限制18位，其他50 -->
    <template v-if="farmerBasic.certificateNo">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'certificateNo', 'value']" :label-col="labelCol" :data="farmerBasic.certificateNo" :extra-rules="[{ validator: IDValidator, trigger: 'change' }]">
        <a-input v-model:value="farmerBasic.certificateNo.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 证件有效起期 -->
    <template v-if="farmerBasic.certificateIssueDate">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'certificateIssueDate', 'value']" :label-col="labelCol" :data="farmerBasic.certificateIssueDate">
        <a-date-picker v-model:value="farmerBasic.certificateIssueDate.value" value-format="YYYY-MM-DD" :disabled-date="checkStartDate" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 证件有效止期 -->
    <template v-if="farmerBasic.certificateValidDate">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'certificateValidDate', 'value']" :label-col="labelColLarge" :data="farmerBasic.certificateValidDate">
        <a-date-picker v-model:value="farmerBasic.certificateValidDate.value" value-format="YYYY-MM-DD" :disabled-date="checkEndDate" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 手机号码 -->
    <template v-if="farmerBasic.mobileTelephone">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'mobileTelephone', 'value']" :label-col="labelCol" :extra-rules="[{ validator: phoneRule, trigger: 'change' }]" :data="farmerBasic.mobileTelephone">
        <a-input v-model:value="farmerBasic.mobileTelephone.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 被保险人地址 -->
    <template v-if="farmerBasic.insuredAddress">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'insuredAddress', 'value']" :label-col="labelCol" class="col-span-1" :data="farmerBasic.insuredAddress" :extra-rules="[{ max: 200, trigger: 'change' }]">
        <a-input v-model:value="farmerBasic.insuredAddress.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 是否建档立卡贫困户 -->
    <template v-if="farmerBasic.isPovertyRegistration">
      <proxyFormItem v-slot="{ clearStatus }" label="是否建档立卡贫困户" :name="['farmerBasic', 'isPovertyRegistration', 'value']" :label-col="labelColLarge" :data="farmerBasic.isPovertyRegistration" :options="isPovertyRegistrationOptions">
        <a-select v-model:value="farmerBasic.isPovertyRegistration.value" :options="isPovertyRegistrationOptions" allow-clear @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 银行名称 -->
    <template v-if="farmerBasic.bankName">
      <proxyFormItem v-slot="{ clearStatus }" :name="['farmerBasic', 'bankName', 'value']" :label-col="labelCol" :data="farmerBasic.bankName" :extra-rules="[{ max: 100, trigger: 'change' }]">
        <a-input v-model:value="farmerBasic.bankName.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
    <!-- 银行账号 -->
    <template v-if="farmerBasic.accountNo">
      <proxyFormItem
        v-slot="{ clearStatus }"
        :name="['farmerBasic', 'accountNo', 'value']"
        :label-col="labelCol"
        :data="farmerBasic.accountNo"
        :extra-rules="[
          { max: 30, trigger: 'change' },
          { pattern: /^\d{8,30}$/, message: '银行账号必须是8到30位数字', trigger: 'blur' },
        ]"
      >
        <a-input v-model:value="farmerBasic.accountNo.value" @change="clearStatus" />
      </proxyFormItem>
    </template>
  </div>
</template>

<script setup lang="ts">
import dayjs, { type Dayjs } from 'dayjs';
import type { Rule } from 'ant-design-vue/es/form/interface';
import { get } from 'lodash-es';
import proxyFormItem from '../components/proxyFormItem.vue';
import type { ItemOption } from '../../farmer.d';
import { pxToRem } from '@/utils/tools';
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { phoneRule, checkIDCardNo, isPassport, isSoldierCard, isDriveCard, isHM, checkHMT, checkHMResident, checkHMRP, checkTWP } from '@/utils/validators';

const farmerBasic = defineModel<{ [key: string]: ItemOption }>('farmerBasic', { default: {} });

// Define consistent label column width for all form items
const labelCol = { style: { width: pxToRem(108) } };
const labelColLarge = { style: { width: pxToRem(140) } };

const checkStartDate = (currentDate: Dayjs) => currentDate.isAfter(dayjs());

const checkEndDate = (currentDate: Dayjs) => currentDate.isBefore(dayjs());

const isPovertyRegistrationOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];
// 证件类型选择
const farmer_certtype_unit_code = ref([]);
// 证件类型校验映射
const personalCertificateValidators: {
  [key: string]: (rule: Rule, value: string) => Promise<void>;
} = {
  '01': checkIDCardNo,
  '02': isPassport,
  '03': isSoldierCard,
  '04': isHM,
  '05': isDriveCard,
  '06': checkHMT,
  '07': checkIDCardNo,
  '09': checkHMResident,
  '41': checkHMRP,
  '42': checkTWP,
  '99': (rule: Rule, value: string) => {
    if (/[\u4E00-\u9FA5]/g.test(value)) {
      return Promise.reject('不能输入中文');
    }
    return Promise.resolve();
  },
};
// 证件号码校验
const IDValidator = (rule: Rule, value: string) => {
  const { certificateType } = farmerBasic.value;
  if (!certificateType.value) {
    return Promise.reject('证件类别不能为空');
  }
  const validator = personalCertificateValidators[certificateType.value];
  if (validator) {
    return validator(rule, value);
  }
  return Promise.resolve();
};

onMounted(async () => {
  try {
    const res = await $post('/api/farmer/getSelect', ['farmer_certtype_unit_code']);
    if (res && res.code === SUCCESS_CODE) {
      farmer_certtype_unit_code.value = get(res, 'data.farmer_certtype_unit_code', []);
    } else {
      farmer_certtype_unit_code.value = [];
    }
  } catch {
    farmer_certtype_unit_code.value = [];
  }
});
</script>
