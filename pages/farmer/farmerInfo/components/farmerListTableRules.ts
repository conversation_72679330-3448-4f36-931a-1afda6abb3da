import type { CellData } from '@/components/ui/Vtable/vtable.d';

const stringIsEmpty = (val) => {
  return val === undefined || val === null || String(val).length === 0;
};

export const validStrMax = (num: number, require: boolean) => {
  const max = num;
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      if (!stringIsEmpty(val.value) && String(val.value).length <= max) {
        return true;
      } else {
        return false;
      }
    } else {
      if (stringIsEmpty(val.value) || String(val.value).length <= max) {
        return true;
      } else {
        return false;
      }
    }
  };
};

export const validStrRequire = (val: CellData) => {
  return !!(val.value && String(val.value).length);
};

export const validInsuredQuantity = (require: boolean) => {
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      if (stringIsEmpty(val.value)) {
        return { valid: false, message: '请录入投保数量' };
      } else if (!/^\d{1,9}(\.\d{0,2})?$/.test(val.value as string)) {
        return { valid: false, message: '仅支持输入数字，最多9位整数，2位小数' };
      }
      if (stringIsEmpty(val.unitValue)) {
        return { valid: false, message: '请录入投保数量单位' };
      }
      return true;
    } else {
      if (stringIsEmpty(val.value) || !/^\d{1,9}(\.\d{0,2})?$/.test(val.value as string)) {
        return { valid: false, message: '仅支持输入数字，最多9位整数，2位小数' };
      }
      return true;
    }
  };
};

export const validAnimalAge = (require: boolean) => {
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      return /^\d{1,3}$/.test(String(val.value));
    } else {
      return stringIsEmpty(val.value) || /^\d{1,3}$/.test(String(val.value));
    }
  };
};

export const validEarTagNo = (require: boolean) => {
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      if (!/^[a-zA-Z0-9]+$/.test(String(val.value))) {
        return { valid: false, message: '只能输入字母或者数字' };
      } else if (!stringIsEmpty(val.value) && String(val.value).length > 64) {
        return { valid: false, message: '只能输入不大于64为的数字' };
      }
      return true;
    } else {
      if (stringIsEmpty(val.value)) {
        return true;
      } else if (!stringIsEmpty(val.value) && !/^[a-zA-Z0-9]+$/.test(String(val.value))) {
        return { valid: false, message: '只能输入字母或者数字' };
      } else if (!stringIsEmpty(val.value) && String(val.value).length > 64) {
        return { valid: false, message: '只能输入不大于64为的数字' };
      }
      return true;
    }
  };
};

export const validUnitInsuredAmount = (require: boolean) => {
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      if (/^\d{1,9}(\.\d{0,6})?$/.test(String(val.value))) {
        return true;
      } else {
        return { valid: false, message: '最大录入9位整数，6位小数' };
      }
    } else {
      if (/^\d{1,9}(\.\d{0,6})?$/.test(String(val.value)) || stringIsEmpty(val.value)) {
        return true;
      } else {
        return { valid: false, message: '最大录入9位整数，6位小数' };
      }
    }
  };
};

export const validTreeAge = (require: boolean) => {
  const needRequire = require;
  return (val: CellData) => {
    if (needRequire) {
      if (/^\d{1,8}(\.\d{0,2})?$/.test(String(val.value))) {
        return true;
      } else {
        return { valid: false, message: '最大录入8位整数，2位小数' };
      }
    } else {
      if (/^\d{1,8}(\.\d{0,2})?$/.test(String(val.value)) || stringIsEmpty(val.value)) {
        return true;
      } else {
        return { valid: false, message: '最大录入8位整数，2位小数' };
      }
    }
  };
};
