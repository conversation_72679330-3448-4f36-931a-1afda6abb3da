<template>
  <div class="edit-farmer-list p-[14px]">
    <div class="p-[16px] bg-[white] rounded-[6px]">
      <div class="flex">
        <SearchForm v-model:form-state="formState" :expand="expand" :reset="formReset" :dept="dept" />
        <FormFold v-model="expand" class="ml-8px" />
      </div>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="p-[16px] bg-[white] rounded-[6px] mt-[14px]">
      <div class="table-title flex justify-between items-center">
        <div class="flex items-center space-x-[14px]">
          <span class="text-[16px] font-bold">查询结果</span>
          <span v-if="executeFlag" class="text-[rgba(0,0,0,0.55)] text-[14px]">数据处理中，请稍后查询</span>
        </div>
        <div class="space-x-8px">
          <AuthButton code="editAddedImport" @click="incrementalImport">增量导入</AuthButton>
          <AuthButton code="editBatchImport" @click="openModal">
            覆盖导入
            <a-tooltip>
              <template #title>
                <span>1、该操作会删除当前的全部数据，并保存本次上传的数据<br />2、该操作会重新同步爱农宝核身数据</span>
              </template>
              <VueIcon :icon="IconInfoCircleFont" />
            </a-tooltip>
          </AuthButton>
          <AuthButton code="editBatchDownload" :loading="exporting" @click="dataExport">批量下载清单</AuthButton>
          <AuthButton v-if="!viewDetail" code="editFarmerCreate" type="primary" @click="addFarmer">添加分户清单被保险人</AuthButton>
          <AuthButton code="editSyncSave" type="primary" :disabled="disabled" :loading="btnLoading" @click="pushAccept">同步保存数据</AuthButton>
          <a-button :loading="refreshLoading" @click="refreshIcpUserVerify">刷新核身率</a-button>
        </div>
      </div>
      <div class="mb-[14px] text-[rgba(0,0,0,.6)]">
        <div>清单编号：{{ route.query.id }}</div>
        <div class="pt-[8px]">
          清单户数：{{ tipState.farmersCount }}，错误户数：{{ tipState.errorCount }}，风险户数：{{ tipState.riskCount }}
          <span v-if="tipState?.eligibleVerifyFlag && tipState.farmerVerifyInfo" class="pl-[5px] text-[#07c160]">爱农宝核身率{{ tipState?.customerVerifyRate || '0' }}%（{{ tipState?.customerVerifyCount || '0' }}人/{{ tipState?.allCustomerCount || '0' }}人） </span>
          <span v-if="!tipState?.eligibleVerifyFlag && tipState.farmerVerifyInfo" class="pl-[5px] text-[#DB3939]">客户核身率{{ tipState?.customerVerifyRate || '0' }}%（{{ tipState?.customerVerifyCount || '0' }}人/{{ tipState?.allCustomerCount || '0' }}人），低于底线值{{ tipState?.umsVerifyRateX || '0' }}%无法投保。当{{ tipState?.umsVerifyRateX || '0' }}%≤核身率＜{{ tipState?.umsVerifyRate || '0' }}%时，可申请豁免。请引导客户完成爱农宝实名认证。</span>
        </div>
      </div>
      <a-table :data-source="dataSource" :columns="columns" :pagination="pagination" :bordered="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)" size="small" class="table-box">
        <template #bodyCell="{ column, index, record, text }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column.key === 'farmerName'">
            <div>{{ text }} <a-tag v-if="record.accurateBigFarmer === 'Y'" color="green">大户</a-tag></div>
          </template>
          <template v-if="column.key === 'insuranceNums'">
            <a-tooltip v-if="record.farmerRiskList?.length > 1" placement="top">
              <template #title>
                <div v-for="risk in record.farmerRiskList" :key="risk.riskCode">
                  {{ `${risk.riskName} ${risk.insuranceNums}${risk.insuranceNumsUnit}` }}
                </div>
              </template>
              <span>{{ text }}</span>
            </a-tooltip>
            <span v-else>{{ text }}</span>
          </template>
          <template v-if="column.key === 'statusList'">
            <div v-if="executeFlag">执行中</div>
            <div v-else class="flex">
              <template v-for="item in record.statusList" :key="item">
                <div v-if="item === '02'" class="flex items-center bg-[#FFECEB] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#DB3939] opacity-65" :icon="IconErrorCircleFilledFont" />
                  <span class="ml-[4px] text-[#DB3939]">错误</span>
                </div>
                <div v-else-if="item === '03'" class="flex items-center bg-[#FFF9EB] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#E6AD1C] opacity-65" :icon="IconErrorCircleFilledFont" />
                  <span class="ml-[4px] text-[#E6AD1C]">风险</span>
                </div>
                <div v-else class="flex items-center bg-[#F2FFF9] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#07C160] opacity-65" :icon="IconCheckCircleFilledFont" />
                  <span class="ml-[4px] text-[#07C160]">正确</span>
                </div>
              </template>
            </div>
          </template>
          <template v-if="column.key === 'operation'">
            <AuthButton v-if="!viewDetail" code="editFarmerEdit" type="link" size="small" :disabled="executeFlag" @click="handleEdit(record as EditFarmerList)">编辑</AuthButton>
            <AuthButton code="editFarmerView" type="link" size="small" :disabled="executeFlag" @click="handleView(record as EditFarmerList)">查看</AuthButton>
            <a-button v-if="!viewDetail && record.allowCheckMapFlag" type="link" size="small" @click="jump2Map(record as EditFarmerList)">地图</a-button>
            <AuthButton v-if="!viewDetail" code="editFarmerDel" type="link" size="small" :disabled="executeFlag" @click="handleDel(record as Record<string, string>)">删除</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
    <ImportModal v-if="modalVisible" v-model:visible="modalVisible" :list-info="listInfo" :farmerlist-status="tipState.farmerlistStatus" :template-list="templateList" :refresh="refresh" @get-push-status="getPushStatus" />
    <!-- 增量导入弹窗 -->
    <ImcrementalImportModal v-if="imcrementalImportVisible" v-model:visible="imcrementalImportVisible" :farmerlist-status="tipState.farmerlistStatus" :template-list="templateList" :refresh="refresh" :list-info="selectRecord" @get-push-status="getPushStatus" />
    <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleDeleteOk">
      <div>{{ text }}</div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { IconCheckCircleFilledFont, IconErrorCircleFilledFont, IconInfoCircleFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnProps } from 'ant-design-vue';
import type { SearchFormModel } from '../farmer.d';
import SearchForm from './components/SearchForm.vue';
import ImportModal from './components/ImportModal.vue';
import useUpdateList from './useUpdateList';
import ImcrementalImportModal from './components/incrementalImportModal.vue';
import FormFold from '@/components/ui/FormFold.vue';
import type { EditFarmerListResult, EditFarmerListReq, EditFarmerList } from '@/apiTypes/farmer/farmerList';
import { usePost, $postOnClient, $getOnClient } from '@/composables/request';
import { $get } from '@/utils/request';
import { usePagination } from '@/composables/usePagination';
import { message } from '@/components/ui/Message';
import { pxToRem } from '@/utils/tools';
import { SUCCESS_CODE } from '@/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';

const getListReq = await usePost<EditFarmerListResult, EditFarmerListReq>('/gateway/icore-agr-an.farmer/farmerList/getFarmerListByPage');
const deleteFarmerListReq = await usePost('/gateway/icore-agr-an.farmer/farmerList/deleteFarmerList');

const route = useRoute();
const expand = ref(false);
const formState = reactive<SearchFormModel>({
  farmerlistNo: undefined,
  farmerName: '',
  certificateNo: undefined,
  mobileTelephone: undefined,
  plantAddressCodeProvince: '',
  plantAddressCodeCity: '',
  plantAddressCodeCounty: '',
  plantAddressCodeTown: '',
  plantAddressCodeVillage: '',
  riskCode: '', // 标的
});
const formReset = ref(0);

const columns: TableColumnProps[] = [
  { title: '序号', dataIndex: 'index', key: 'index' },
  { title: '分户被保险人', dataIndex: 'farmerName', key: 'farmerName' },
  { title: '证件号码', dataIndex: 'certificateNo', key: 'certificateNo' },
  { title: '标的名称', dataIndex: 'riskName', key: 'riskName' },
  { title: '保险数量', dataIndex: 'insuranceNums', key: 'insuranceNums' },
  { title: '联系电话', dataIndex: 'mobileTelephone', key: 'mobileTelephone' },
  { title: '状态', dataIndex: 'statusList', key: 'statusList' },
  { title: '操作', width: pxToRem(200), dataIndex: 'operation', key: 'operation', fixed: 'right' },
];

// route.query.viewType为1是查看，其他是编辑
const viewDetail = computed(() => String(route.query.viewType) === '1');

const dept = computed(() => (route.query.dept as string) || '');

const refresh = async () => {
  await nextTick();
  getList();
  fetchTip();
};

const dataSource = ref<EditFarmerList[]>([]);
const { pagination } = usePagination(refresh);

const getList = async () => {
  const params = {
    farmerlistNo: route.query.id as string,
    farmerName: formState.farmerName || '',
    certificateNo: formState.certificateNo || '',
    mobileTelephone: formState.mobileTelephone || '',
    // plantAddressCodeVillage: formState.plantAddressCodeVillage || '',
    // plantAddressCodeProvince: formState.plantAddressCodeProvince || '',
    // plantAddressCodeCity: formState.plantAddressCodeCity || '',
    // plantAddressCodeCounty: formState.plantAddressCodeCounty || '',
    // plantAddressCodeTown: formState.plantAddressCodeTown || '',
    riskCode: formState.riskCode || '',
    pageSize: pagination.pageSize || 10,
    pageNum: pagination.current || 1,
  };
  try {
    const res = await getListReq.fetchData(params);
    if (res && res.code === '000000') {
      dataSource.value = res?.data?.records;
      pagination.current = res?.data?.current;
      pagination.total = res?.data?.total;
      pagination.pageSize = res?.data?.size;
    } else {
      message.error(res?.msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

const tipState = reactive({
  farmersCount: 0,
  errorCount: 0,
  riskCount: 0,
  applyPolicyNo: '',
  farmerlistStatus: '',
  allCustomerCount: '', // 爱农宝总数
  customerVerifyCount: '', // 爱农宝核身数
  customerVerifyRate: '', // 核身率
  eligibleVerifyFlag: false, // 符合核身标识：true为符合，false为不符合
  farmerVerifyInfo: '',
  umsVerifyRate: '',
  umsVerifyRateX: '',
});
const ruleInfo = ref();
const fetchTip = async () => {
  try {
    const url = '/gateway/icore-agr-an.farmer/farmerList/getFarmerListSummaryDetail';
    const formData = new FormData();
    formData.append('farmerListNo', route.query.id);
    const res = await $postOnClient(url, formData);
    if (res && res.code === SUCCESS_CODE) {
      tipState.farmersCount = res.data.farmersCount;
      tipState.errorCount = res.data.errorCount;
      tipState.riskCount = res.data.riskCount;
      tipState.farmerlistStatus = res?.data?.farmerlistStatus;
      ruleInfo.value = res?.data;
    }
  } catch (e) {
    console.log(e);
  }
  try {
    const res = await $get('/gateway/icore-agr-an.farmer/farmerList/queryFarmerVerifyInfoByFarmerListNo', {
      farmerListNo: route.query.id,
    });

    if (res?.code === SUCCESS_CODE) {
      tipState.allCustomerCount = res?.data?.allCustomerCount;
      tipState.customerVerifyCount = res?.data?.customerVerifyCount;
      tipState.customerVerifyRate = res?.data?.customerVerifyRate;
      tipState.eligibleVerifyFlag = res?.data?.eligibleVerifyFlag;
      tipState.farmerVerifyInfo = res?.data || '';
      tipState.umsVerifyRate = res?.data?.umsVerifyRate;
      tipState.umsVerifyRateX = res?.data?.umsVerifyRateX;
    }
  } catch (e) {
    console.log(e);
  }
};

const executeFlag = computed(() => {
  if (dataSource.value?.length > 0) {
    return dataSource.value[0].executeFlag;
  } else {
    return false;
  }
});

const reset = () => {
  formState.farmerName = '';
  formState.certificateNo = '';
  formState.mobileTelephone = '';
  formReset.value = formReset.value + 1;
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const handleEdit = (record: EditFarmerList) => {
  router.push({
    path: '/farmerInfo',
    query: {
      mode: 'edit',
      farmerListNo: route.query.id,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
    },
  });
};
const handleView = (record: EditFarmerList) => {
  router.push({
    path: '/farmerInfo',
    query: {
      mode: 'view',
      farmerListNo: route.query.id,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
    },
  });
};
const editFarmerObj = ref<Record<string, string>>({
  farmerName: '',
  customCode: '',
  farmerlistNo: '',
  idFarmerlistCustomInfo: '',
});
const text = ref<string>('');
const allDeleteModal = ref<boolean>(false);
// 查询是否验标
const queryFarmerCheckResult = async () => {
  try {
    const res = await $postOnClient('/gateway/icore-agr-an.farmer/web/farmer/queryFarmerCheckResult', { applyPolicyNo: tipState.applyPolicyNo, customCode: editFarmerObj.value.customCode });
    if (res?.code === SUCCESS_CODE) {
      if (res?.data === true) {
        allDeleteModal.value = true;
        text.value = `该农户已完成验标，是否确认删除?`;
      } else {
        allDeleteModal.value = true;
        text.value = `是否确认删除分户被保险人【${editFarmerObj.value.farmerName}】`;
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 删除点击确定
const handleDeleteOk = async () => {
  try {
    const res = await deleteFarmerListReq.fetchData({
      farmerlistNo: editFarmerObj.value.farmerlistNo,
      idFarmerlistCustomInfo: editFarmerObj.value.idFarmerlistCustomInfo,
    });
    if (res?.code === '000000') {
      message.success('删除成功');
      allDeleteModal.value = false;
      refresh();
    } else {
      message.error(res?.msg || '删除失败');
    }
  } catch (err) {
    console.log(err);
  }
};

// 点击删除，如果未验标，则直接删除
const handleDel = (record: Record<string, string>) => {
  editFarmerObj.value = record;
  if (tipState.applyPolicyNo !== null && editFarmerObj.value.customCode !== null) {
    queryFarmerCheckResult();
  } else {
    allDeleteModal.value = true;
    text.value = `是否确认删除分户被保险人【${editFarmerObj.value.farmerName}】`;
  }
};

const { modalVisible, listInfo, openModal, dataExport, exporting } = useUpdateList();

const router = useRouter();
// 添加分户被保险人
const addFarmer = () => {
  router.push({
    path: '/farmerInfo',
    query: {
      mode: 'add',
      farmerListNo: route.query.id,
    },
  });
};
// 增量导入
const imcrementalImportVisible = ref<boolean>(false);
const selectRecord = ref();
const incrementalImport = () => {
  selectRecord.value = {
    farmerlistNo: route.query?.id,
    farmerlistName: route.query?.farmerlistName,
  };
  imcrementalImportVisible.value = true;
};
// 查询同步数据按钮是否可点击 0无更新1待推送2推送中
const disabled = ref<boolean>(true);
const getPushStatus = async () => {
  try {
    const fetchUrl = `${gateWay}${service.farmer}/farmerList/getPushStatus`;
    const res = await $postOnClient(fetchUrl, { farmerlistNo: route.query.id });
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      disabled.value = ['1'].includes(res.data as string) ? false : true;
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 同步数据提交
const btnLoading = ref<boolean>(false);
const pushAccept = async () => {
  try {
    btnLoading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/farmerList/pushAccept`;
    const res = await $postOnClient(fetchUrl, { farmerlistNo: route.query.id });
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      message.success('同步成功');
      refresh();
      getPushStatus();
    } else {
      message.error(msg);
    }
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
await getList();
// await fetchTip();
const templateList = ref<File[]>([]); // 模版清单
const productChange = () => {
  $postOnClient<File[], { productCode: string; businessType: string; productVersion: string; templateType: string; departmentCode: string }>(`/gateway${service.farmer}/templateInfo/queryTemplateInfo`, {
    productCode: ruleInfo.value?.productCode,
    businessType: '01', // 投保清单模版固定传01
    productVersion: ruleInfo.value?.productVersion,
    templateType: '0',
    departmentNo: ruleInfo.value?.departmentCode,
  }).then((res) => {
    if (res && res.data) {
      templateList.value = res.data;
    }
  });
};
const refreshLoading = ref(false);

const refreshIcpUserVerify = async () => {
  try {
    refreshLoading.value = true;
    await $getOnClient('/gateway/icore-agr-an.farmer/farmerList/refreshIcpUserVerify', { farmerListNo: route.query.id });
    await fetchTip();
    await getList();
  } catch (error) {
    console.log(error);
  } finally {
    refreshLoading.value = false;
  }
};

// 跳转地图
const jump2Map = ({ farmerlistNo: farmerListNo, idFarmerlistCustomInfo }) => {
  router.push({
    path: 'landEdit',
    query: {
      farmerListNo,
      idFarmerlistCustomInfo,
      mode: 'editFarmer',
    },
  });
};

onMounted(async () => {
  getPushStatus();
  await fetchTip();
  productChange();
});

onActivated(async () => {
  // 重置筛选条件
  reset();
  pagination.current = 1;
  refresh();
  getPushStatus();
  await fetchTip();
  productChange();
});
</script>
