type SimpleCombinationType<T, K> = T | K;

type RangeValue = [string, string];

export interface FormState {
  farmerlistNo: string;
}

export interface SourceType {
  farmerlistNo: string;
  farmerlistName: string;
  productName: string;
  farmerlistStatusDesc: string;
  createdBy: string;
  importCount: string;
  insuredFamily: string;
  insuredAccount: string;
  riskCount: string;
  riskAddressNameProvince: string;
  riskAddressNameCity: string;
  riskAddressNameCounty: string;
  riskAddressNameTown: string;
  riskAddressNameVillage: string;
}

export interface SearchFormState {
  insureDepartmentNo: string; // 机构编码
  containChildDepart?: boolean; // 是否包含下级机构
  riskObjectCompleteAddress: string; // 地址
  agriculturalRiskObjectDetailCode: string; // 标的类型
  productVersionNo: string; // 产品
  insureDate: RangeValue; // 保险起止期
  publicDate: RangeValue; // 公示起止期
  publicityStatusCode: string[]; // 公示状态
  applyPublicityMethodCode: string[]; // 公示方式
  complaintStatusCode: string[]; // 异议状态
  // publicResult: string[]; // 公示结果
  voucherNo: string; // 业务号
  voucherType: string[]; // 业务类型
}

export interface AddressType {
  province: string | undefined; // 省
  provinceName: string; // 省
  city: string | undefined; // 市
  cityName: string; // 市
  county: string | undefined; // 区
  countyName: string; // 区
  town: string | undefined; // 县
  townName: string; // 县
  village: string | undefined; // 村
  villageName: string; // 村
}

export interface SearchFormModel {
  farmerlistNo?: string;
  farmerName: string;
  certificateNo?: string;
  mobileTelephone?: string;
  plantAddressCodeProvince: string;
  plantAddressCodeCity: string;
  plantAddressCodeCounty: string;
  plantAddressCodeTown: string;
  plantAddressCodeVillage: string;
  riskCode: string;
}

export interface UpdateFileType {
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}

export type PageMode = 'add' | 'edit' | 'view';

export interface ItemOption {
  value: string;
  unitValue: string;
  status: string;
  statusTips: string;
  statusValue: string;
  require: boolean;
  show: boolean;
  label: string;
}

export interface RefMapType {
  [key: string]: {
    value: unknown;
  };
}

export interface TargetRecordType {
  [key: string]: {
    value: string;
    status: string;
    statusTips: string;
    tag: unknown[];
  };
}

type TaskStatus = 1 | 2 | 3; // 1排队(加入任务队列);2执行(在生成文件);3完成(可下载文件)

export interface TaskDataListObj {
  type: number | string;
  typeName: string;
  name: string;
  time: string;
  status: TaskStatus;
  statusText: string;
  idEvntFileTask: string;
}

export type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';

export interface FarmerInfo {
  departmentCode: string;
  containChildDepart: boolean;
  farmerlistNo: string;
  farmerlistName: string;
  farmerlistStatus: string[];
  oprRoleCode: string[];
  productCode: string;
  riskCode?: string;
  policyYear?: string;
  quarter?: string;
  riskAddressCodeCity?: string;
  riskAddressCodeCounty?: string;
  riskAddressCodeProvince?: string;
  riskAddressCodeTown?: string;
  riskAddressCodeVillage?: string;
  applyPolicyNo?: string;
}

export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
export interface SelectOptions {
  label: string;
  value: string;
  disabled?: boolean;
  children?: SelectOptions[];
  isLeaf?: boolean;
}
export interface UploadFile {
  documentName: string;
  documentId: string;
  thumbnail: string; // 缩略图，上传图片会返回
  documentFormat: string;
  uploadPath: string;
  selected: boolean;
  bucketName: string;
  storageType: string;
  documentType: string;
  documentGroupItemsId: string;
  fileUrl?: string;
  fileKey?: string;
}

export interface PublicityImagesType {
  fileKey: string;
  fileUrl: string;
  documentId: string;
}
