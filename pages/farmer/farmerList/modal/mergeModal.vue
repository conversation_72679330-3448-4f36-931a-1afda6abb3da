<template>
  <a-modal v-model:open="visible" title="合并清单" centered @ok="mergerList">
    <div class="flex items-center space-x-[8px]">
      <div class="whitespace-nowrap">清单名称:</div>
      <a-input v-model:value="mergeListName" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const props = defineProps<{ selectedRowKeys: string[]; finish: () => void }>();
const visible = defineModel<boolean>('visible', { required: true, default: false });
const { gateWay, service } = useRuntimeConfig().public || {};

const mergeListName = ref(''); // 合并后的清单名称
// 合并清单
const mergerList = () => {
  if (mergeListName.value.length > 0) {
    const fetchurl = gateWay + service.farmer + '/farmerList/combineFarmerList';
    $postOnClient(fetchurl, { farmerListNos: props.selectedRowKeys, farmerListName: mergeListName.value }).then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        message.success(res.data as string);
      } else {
        message.error(res?.msg || '');
      }
      props.finish();
    });
  } else {
    message.warning('合并名称不能为空');
  }
};
</script>
