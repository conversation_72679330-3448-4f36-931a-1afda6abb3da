<template>
  <a-modal v-model:open="visible" title="清单制作" :width="pxToRem(800)" centered :confirm-loading="confirmLoading" :mask-closable="false" @ok="handleOk">
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="1" tab="附件导入">
        <div class="mb-[18px]">
          <div class="flex items-center mb-[9px]">
            <VueIcon :icon="IconJichuxinxiFont" />
            <span class="ml-[3px]">基础信息</span>
          </div>
          <div class="bg-gray-100 rounded p-14px overflow-x-auto">
            <a-form ref="formRef" :model="baseInfo" :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
              <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, message: '请选择机构' }]" :label-col="{ style: { width: pxToRem(80) } }">
                <department-search :dept-code="baseInfo.departmentCode" @change-dept-code="changeDeptCode" />
              </a-form-item>
              <a-form-item name="productCode" :rules="[{ required: true, message: '请选择产品' }]">
                <template #label>
                  <div class="flex items-center space-x-[4px]">
                    <span>产品</span>
                    <a-popover title="险种信息">
                      <template #content>
                        <div v-for="item in planList" :key="item.planCode">
                          <span>{{ item.planCode }}{{ item.planChineseName }}</span>
                          <span class="ml-8px cursor-pointer" @click="copyText(item.planCode)">
                            <VueIcon :icon="IconFuzhiFont" />
                          </span>
                        </div>
                        <div v-if="planList.length === 0">暂无</div>
                      </template>
                      <span class="text-[rgba(0,0,0,0.55)]">
                        <VueIcon :icon="IconInfoCircleFilledFont" />
                      </span>
                    </a-popover>
                  </div>
                </template>
                <ProductSelect v-model:value="baseInfo.productCode" :disabled="lockParams" :department-code="baseInfo.departmentCode" :encode-key="riskCode" @product-change="productChange" />
              </a-form-item>
              <a-form-item label="标的地址" name="villageCode" :required="true" :rules="[{ required: true, message: '请补充标的地址' }]">
                <RegionSelect v-model:province="baseInfo.provinceCode" v-model:city="baseInfo.cityCode" v-model:county="baseInfo.countyCode" v-model:town="baseInfo.townCode" v-model:village="baseInfo.villageCode" :disabled="lockParams" :init-change="true" @change-selected="regionChange" />
                <div class="mt-[12px]">
                  <a-input v-model:value="wholeAddress" placeholder="请输入" />
                </div>
              </a-form-item>
              <a-form-item label="清单名称" name="farmerListName">
                <a-input v-model:value="baseInfo.farmerListName" placeholder="附件上传后自动生成" />
              </a-form-item>
              <a-form-item label="是否从共" name="fromCommon" :rules="[{ required: true, message: '请选择是是否从共' }]">
                <a-radio-group v-model:value="baseInfo.fromCommon">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </div>
        </div>
        <div class="mb-[15px]">
          <div class="flex items-center justify-between mb-[9px]">
            <div class="flex items-center">
              <VueIcon :icon="IconPingtaijiekouFont" />
              <span class="ml-[3px]">清单文件</span>
            </div>
            <div class="text-[#576B95] flex items-center justify-end space-x-14px list-file-right">
              <div v-if="templateList.length" class="flex items-center">
                <a-upload :multiple="false" :show-upload-list="false" :before-upload="beforeUpload" :action="handleOneTableDataImport">
                  <a-button type="link" ghost :loading="oneTableUploading">
                    <template #icon>
                      <VueIcon :icon="IconTongyongShangchuanFont" />
                    </template>
                    <span class="ml-[4px]">一表数据导入</span>
                  </a-button>
                </a-upload>
                <a-tooltip>
                  <template #title>
                    <span>导入一表数据后，系统自动将一表数据导入到清单模板中，清单模板中其他信息仍需补充</span>
                  </template>
                  <span>
                    <VueIcon :icon="IconInfoCircleFilledFont" />
                  </span>
                </a-tooltip>
              </div>
              <div v-for="temp in templateList" :key="temp.templateFileKey" class="cursor-pointer" @click="downloadFileByKey(temp.templateFileKey, '', temp.templateName)">
                <VueIcon :icon="IconTongyongXiazaiFont" />
                {{ temp.templateName }}
              </div>
            </div>
          </div>
          <div class="bg-gray-100 rounded p-14px">
            <a-upload :multiple="false" :show-upload-list="false" :action="handleFileChange" :disabled="disabledUploadButton" @change="handleFileChange">
              <a-button type="primary" ghost :disabled="disabledUploadButton" :loading="uploading">
                <template #icon>
                  <VueIcon :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[2px]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-if="currentFile" class="text-12px text-[#4E6085] mt-[10px]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFileByKey(currentFile.fileKey)">{{ currentFile.fileName }}({{ currentFile.fileSize }})</span>
              <a-button type="link" @click="removeFile">删除</a-button>
            </div>
          </div>
        </div>
        <div>
          <div class="font-semibold text-[#444444]">温馨提示：</div>
          <div class="text-[12px]">1.模板下载：必须录入产品名称，才能下载对应的投保清单模板；</div>
          <div class="text-[12px]">2.附件上传：点击附件上传之前，请先选择<span class="font-semibold text-[#444444]">标的地址</span>，建立投保清单归属的投保区域；</div>
          <div class="text-[12px]">3.删除：发现清单上传错误后，可以直接点击删除后，重新上传；</div>
          <div class="text-[12px]">4.清单名称命名：标的地址+产品，例如：<span class="font-semibold text-[#444444]">建立投保清单归属的投保区域；</span></div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="按图制作">
        <div class="bg-gray-100 rounded p-14px">
          <a-form ref="formRef2" :model="baseInfo" :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
            <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, message: '请选择机构' }]" :label-col="{ style: { width: pxToRem(80) } }">
              <department-search :dept-code="baseInfo.departmentCode" @change-dept-code="changeDeptCode" />
            </a-form-item>
            <a-form-item name="productCode" :rules="[{ required: true, message: '请选择产品' }]">
              <template #label>
                <div class="flex items-center space-x-[4px]">
                  <span>产品</span>
                  <a-popover title="险种信息">
                    <template #content>
                      <div v-for="item in planList" :key="item.planCode">
                        <span>{{ item.planCode }}{{ item.planChineseName }}</span>
                        <span class="ml-8px cursor-pointer" @click="copyText(item.planCode + item.planChineseName)">
                          <VueIcon :icon="IconFuzhiFont" />
                        </span>
                      </div>
                      <div v-if="planList.length === 0">暂无</div>
                    </template>
                    <span class="text-[rgba(0,0,0,0.55)]">
                      <VueIcon :icon="IconInfoCircleFilledFont" />
                    </span>
                  </a-popover>
                </div>
              </template>
              <ProductSelect v-model:value="baseInfo.productCode" :disabled="lockParams" :department-code="baseInfo.departmentCode" :encode-key="riskCode" @product-change="productChange" />
            </a-form-item>
            <a-form-item label="标的地址" name="villageCode" :rules="[{ required: true, message: '请补充标的地址' }]">
              <RegionSelect v-model:province="baseInfo.provinceCode" v-model:city="baseInfo.cityCode" v-model:county="baseInfo.countyCode" v-model:town="baseInfo.townCode" v-model:village="baseInfo.villageCode" :disabled="lockParams" :init-change="true" @change-selected="regionChange" />
            </a-form-item>
            <a-form-item label="模版" name="templateId" :rules="[{ required: true, message: '请选择模版' }]">
              <a-radio-group v-model:value="baseInfo.templateId">
                <a-radio v-for="temp in templateList" :key="temp.templateId" :style="radioStyle" :value="temp.templateId">
                  <span class="text-[#576B95]" @click.stop="downloadFileByKey(temp.templateFileKey)">
                    <VueIcon :icon="IconTongyongXiazaiFont" class="mr-[4px]" />
                    {{ temp.templateName }}</span
                  >
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="是否从共" name="fromCommon" :rules="[{ required: true, message: '请选择是否从共' }]">
              <a-radio-group v-model:value="baseInfo.fromCommon">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, IconPingtaijiekouFont, IconTongyongXiazaiFont, IconTongyongShangchuanFont, IconAttachmentFont, IconInfoCircleFilledFont, IconFuzhiFont } from '@pafe/icons-icore-agr-an';
import { notification, Button } from 'ant-design-vue';
import { get, set } from 'lodash-es';
import type { RegionType } from '../../farmer.d';
import { pxToRem, downloadFile, copyText, downloadBlob } from '@/utils/tools';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { message } from '@/components/ui/Message';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';

const rejectDepartmentChange = ref(false);
const activeKey = ref('1');
const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  defaultDepartmentCode: string;
  defaultProduct: string;
  riskCode?: string;
  refresh: () => void;
}>();
const templateList = ref<File[]>([]); // 模版清单
// 当前上传清单附件
const currentFile = ref<{
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}>();

const lockParams = ref(false); // 上传清单后锁定入参
const route = useRoute();

const { service } = useRuntimeConfig().public || {};

const formRef = ref();
const formRef2 = ref();
// 删除附件
const removeFile = async () => {
  await $getOnClient(`/gateway${service.farmer}/templateInfo/deleteFile`, {
    fileKey: currentFile.value?.fileKey || '',
  });
  currentFile.value = undefined;
  if (lockParams.value) {
    lockParams.value = false;
  }
};

// 产品和地址选择后才可以上传附件
const disabledUploadButton = computed(() => !(baseInfo.productCode && baseInfo.villageCode));
// 附件导入表单
const baseInfo = reactive({
  departmentCode: props.defaultDepartmentCode,
  farmerListName: '',
  productCode: '',
  productVersion: '',
  productName: '',
  provinceCode: '',
  cityCode: '',
  countyCode: '',
  townCode: '',
  villageCode: '',
  provinceName: '',
  cityName: '',
  countyName: '',
  townName: '',
  villageName: '',
  templateId: '',
  fromCommon: '0',
});

const wholeAddress = computed(() => baseInfo.provinceName + baseInfo.cityName + baseInfo.countyName + baseInfo.townName + baseInfo.villageName);

const planList = ref<{ planCode: string; planChineseName: string }[]>([]);

const getPlanInfo = async (productCode: string, productVersion: string) => {
  const res = await $getOnClient('/api/common/getPlanListByProductCode', { productCode, productVersion });
  if (res && Array.isArray(res.data)) {
    planList.value = res.data;
  }
};

// 机构赋值
const changeDeptCode = (val: string) => {
  // 上传附件后修改了机构要解除锁定
  if (lockParams.value) {
    lockParams.value = false;
    baseInfo.farmerListName = '';
  }
  baseInfo.departmentCode = val;
};

const productChange = (value: string, option = { version: '', label: '' }) => {
  planList.value = [];
  baseInfo.productVersion = option.version;
  baseInfo.productName = option.label;
  getPlanInfo(value, option.version);
  $postOnClient<File[], { productCode: string; businessType: string; productVersion: string; templateType: string; departmentCode: string }>(`/gateway${service.farmer}/templateInfo/queryTemplateInfo`, {
    productCode: value,
    businessType: '01', // 投保清单模版固定传01
    productVersion: option.version,
    templateType: '0',
    departmentNo: baseInfo.departmentCode,
  }).then((res) => {
    if (res && res.data) {
      templateList.value = res.data;
    }
  });
};

const regionChange = (selected: { value: string; label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    baseInfo[`${type}Name`] = selected?.label || '';
  }
};

watch(
  () => props.defaultProduct,
  (product) => {
    baseInfo.productCode = product;
  },
  { immediate: true, once: true },
);

watchEffect(() => {
  const openImport = route.query.openImport;
  if (openImport === 'Y') {
    rejectDepartmentChange.value = true;
    baseInfo.provinceCode = route.query.province as string;
    baseInfo.cityCode = route.query.city as string;
    baseInfo.countyCode = route.query.county as string;
    baseInfo.townCode = route.query.town as string;
    baseInfo.villageCode = route.query.village as string;
  }
});

const beforeUpload = (file: File) => {
  const isExcel = /\.(xlsx|xls)$/i.test(file.name);
  if (!isExcel) {
    message.error(`文件${file.name}格式不正确，请上传 xlsx或xls 文件。`);
    return false;
  }
  return true;
};

const oneTableUploading = ref(false);
const handleOneTableDataImport = (file: File) => {
  oneTableUploading.value = true;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('templateId', templateList?.value[0]?.templateBranchesId);
  $postOnClient(`/gateway${service.farmer}/file/convertExport`, formData, {
    onResponse({ response }) {
      if (response._data instanceof Blob) {
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          const fileName = match ? decodeURI(match[1]) : '';
          downloadBlob(fileData, fileName, fileType);
        }
      } else {
        const { code, data, msg = '' } = response._data;
        if (code === SUCCESS_CODE) {
          message.success(data);
        } else if (msg) {
          message.error(msg);
        }
      }
    },
  }).finally(() => {
    oneTableUploading.value = false;
  });
  return Promise.reject();
};

const downloadFileByKey = async (fileKey: string, key: string, fileName?: string) => {
  const params = {
    fileKey,
    value: wholeAddress.value,
    fileName,
  };

  if (fileName && wholeAddress.value) {
    // 如果有fileName，走queryAndExportFile逻辑
    $postOnClient(`/gateway${service.farmer}/templateInfo/queryAndExportFile`, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } else {
    // 如果没有fileName，走queryFileDownloadUrl逻辑
    const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
    const { msg = '' } = res || {};
    const url = get(res, 'data.url');
    if (url) {
      downloadFile(url);
      notification.close(key);
    } else {
      message.error(msg);
    }
  }
};

const confirmLoading = ref(false);

const fileImport = () => {
  formRef.value.validate().then(async () => {
    if (currentFile.value) {
      confirmLoading.value = true;
      const res = await $postOnClient(`/gateway${service.farmer}/file/excelUpload`, {
        position: '1',
        applyParam: {
          fileKey: currentFile.value?.fileKey || '',
          wholeAddress: wholeAddress.value,
          ...baseInfo,
        },
      });
      confirmLoading.value = false;
      if (res?.code === SUCCESS_CODE) {
        visible.value = false;
        props.refresh();
      } else {
        message.error(res?.msg || '');
      }
    } else {
      message.warning('请先上传清单文件!');
    }
  });
};
const router = useRouter();
const mapCreate = () => {
  formRef2.value?.validate().then(() => {
    visible.value = false;
    // 跳转到地图
    router.push({
      path: '/landToList',
      query: {
        templateId: baseInfo.templateId,
        productCode: baseInfo.productCode, // 产品编码
        productName: baseInfo.productName, // 产品名称
        productVersion: baseInfo.productVersion, // 产品版本号
        provinceCode: baseInfo.provinceCode,
        cityCode: baseInfo.cityCode,
        countyCode: baseInfo.countyCode,
        townCode: baseInfo.townCode,
        villageCode: baseInfo.villageCode,
        addressName: wholeAddress.value,
        departmentCode: baseInfo.departmentCode,
        fromCommon: baseInfo.fromCommon,
      },
    });
  });
};

const handleOk = () => {
  if (activeKey.value === '1') {
    fileImport();
  } else {
    mapCreate();
  }
};

const uploading = ref(false);

const handleFileChange = (file: File) => {
  if (!disabledUploadButton.value) {
    uploading.value = true;
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productCode', baseInfo.productCode);
    formData.append('businessType', '01');
    formData.append('addressName', `${baseInfo.provinceName}.${baseInfo.cityName}.${baseInfo.countyName}.${baseInfo.townName}.${baseInfo.villageName}`);
    formData.append('addressCode', `${baseInfo.provinceCode}.${baseInfo.cityCode}.${baseInfo.countyCode}.${baseInfo.townCode}.${baseInfo.villageCode}`);
    formData.append('productVersion', baseInfo.productVersion);
    formData.append('departmentCode', baseInfo.departmentCode);
    formData.append('templateIdStr', templateList.value.map((v) => v.templateId).join(','));
    $postOnClient(`/gateway${service.farmer}/templateInfo/uploadFarmerListFile`, formData)
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          const uploadResult = get(res, 'data.uploadResult');
          if (uploadResult) {
            set(currentFile, 'value', res.data);
            if (!baseInfo.farmerListName) {
              baseInfo.farmerListName = currentFile.value?.farmerListName || '';
            }
            baseInfo.templateId = currentFile.value?.templateId || '';
            if (!lockParams.value) {
              lockParams.value = true;
            }
          } else {
            const key = 'errorNotice';
            notification.error({
              message: '错误信息',
              description: () => {
                return h(
                  'div',
                  {
                    style: {
                      'white-space': 'pre-wrap',
                      'word-wrap': 'break-word',
                      'word-break': 'break-all',
                    },
                  },
                  get(res, 'data.errorMsg'),
                );
              },
              duration: null,
              key,
              btn: () =>
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    onClick: () => {
                      downloadFileByKey(get(res, 'data.fileKey', ''), key);
                    },
                  },
                  { default: () => '下载完整信息' },
                ),
            });
          }
        } else {
          message.error(get(res, 'msg', ''));
        }
      })
      .finally(() => {
        uploading.value = false;
      });
    return Promise.reject();
  }
};
interface addressType {
  city: string;
  county: string;
  province: string;
}
// 机构变动，回填标的地址
const refreshAddressByDepartment = async () => {
  const addressRes = await $getOnClient(`/gateway${service.administrate}/public/getAddressByDepartmentCode`, { departmentCode: baseInfo.departmentCode });
  if (addressRes && addressRes.code === SUCCESS_CODE) {
    if (addressRes?.data) {
      const { city, county, province } = addressRes?.data as addressType;
      baseInfo.cityCode = city;
      baseInfo.countyCode = county;
      baseInfo.provinceCode = province;
    } else {
      baseInfo.cityCode = '';
      baseInfo.countyCode = '';
      baseInfo.provinceCode = '';
    }
    baseInfo.townCode = '';
    baseInfo.villageCode = '';
  }
};

watch(
  () => baseInfo.departmentCode,
  () => {
    if (rejectDepartmentChange.value) {
      rejectDepartmentChange.value = false;
    } else {
      refreshAddressByDepartment();
    }
  },
  { immediate: true },
);
</script>
<style lang="less" scoped>
.list-file-right {
  :deep(.ant-btn) {
    padding-right: 0px;
  }
}
</style>
