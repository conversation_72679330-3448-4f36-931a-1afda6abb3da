<template>
  <div class="p-14px space-y-14px">
    <div class="bg-white p-16px rounded-md">
      <div class="flex">
        <a-form :colon="false" class="flex-grow">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="机构" name="departmentCode" v-bind="validateInfos.departmentCode" :label-col="{ style: { width: pxToRem(80) } }">
                <department-search v-model:contain-child-depart="formData.containChildDepart" :dept-code="formData.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="清单编号/名称" name="farmerlistNo" :label-col="{ style: { width: pxToRem(110) } }">
                <a-input v-model:value="formData.farmerlistNo" placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="投保单号" name="applyPolicyNo">
                <a-input v-model:value.trim="formData.applyPolicyNo" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col>
              <a-form-item label="状态" name="farmerlistStatus" v-bind="validateInfos.farmerlistStatus" :label-col="{ style: { width: pxToRem(80) } }">
                <check-box-group v-model:checked-list="formData.farmerlistStatus" :options="statusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="24">
              <a-form-item label="标的地址" :label-col="{ style: { width: pxToRem(80) } }">
                <region-select v-model:province="formData.riskAddressCodeProvince" v-model:city="formData.riskAddressCodeCity" v-model:county="formData.riskAddressCodeCounty" v-model:town="formData.riskAddressCodeTown" v-model:village="formData.riskAddressCodeVillage" class="w-[70%]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="24">
              <a-form-item label="标的" name="riskCode" :label-col="{ style: { width: pxToRem(80) } }" v-bind="validateInfos.riskCode">
                <RiskCodeSelect v-model:value="formData.riskCode" :department-code="formData.departmentCode" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="8">
              <a-form-item label="产品" name="productCode" :label-col="{ style: { width: pxToRem(80) } }">
                <ProductSelect v-model:value="formData.productCode" :department-code="formData.departmentCode" :encode-key="formData.riskCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="投保批次" name="listStatus" :label-col="{ style: { width: pxToRem(110) } }">
                <quater-select v-model:year-value="formData.policyYear" v-model:quater-value="formData.quarter" />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-row v-show="expand">
            <a-col :span="10">
              <a-form-item label="操作人员类型" name="oprRoleCode" v-bind="validateInfos.oprRoleCode" :label-col="{ style: { width: pxToRem(110) } }">
                <check-box-group v-model:checked-list="formData.oprRoleCode" :options="oprRoleOptions" />
              </a-form-item>
            </a-col>
          </a-row> -->
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md space-y-16px farmerlist-table">
      <div class="flex justify-between align-center">
        <div class="flex items-center space-x-[12px]">
          <span class="text-[#404442] text-xl font-bold">查询结果</span>
          <span v-if="selectedRowKeys.length > 0" class="text-[rgba(0,0,0,0.55)] text-[14px]">已选择{{ selectedRowKeys.length }}条</span>
          <AuthButton code="farmerExportList" type="link" @click="openDownLoad">导出清单列表<VueIcon :icon="IconChevronRightDoubleFont" /></AuthButton>
        </div>
        <a-space :gutter="8">
          <AuthButton code="farmerMerge" @click="clickMergeButton">清单合并</AuthButton>
          <AuthButton code="farmerDelete" @click="clickDeleteButton">清单删除</AuthButton>
          <AuthButton :loading="batchExportLoading" code="farmerExport" @click="exportList">清单导出</AuthButton>
          <AuthButton type="primary" code="farmerImport" @click="openImportModal">清单制作</AuthButton>
          <AuthButton type="primary" code="landDownload" @click="jump2LandQuery">地块图表下载</AuthButton>
        </a-space>
      </div>
      <a-table :columns="columns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" row-key="farmerlistNo" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)" class="table-box">
        <template #headerCell="{ column, title }">
          <template v-if="column.dataIndex === 'riskCount'">
            <span class="mr-[4px]">{{ title }}</span>
            <a-tooltip>
              <template #title>
                <span>存在作业风险，需慎重</span>
              </template>
              <span class="text-[rgba(0,0,0,0.55)]">
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </span>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'errorCount'">
            <span class="mr-[4px]">{{ title }}</span>
            <a-tooltip>
              <template #title>
                <span>存在错误，必处理</span>
              </template>
              <span class="text-[rgba(0,0,0,0.55)]">
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </span>
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'farmerlistNo'">
            <div class="flex">
              <CopyLink :text="text" @click="handleToInfo(record)" />
              <a-tag v-if="record.doublePrecisionFlag" color="green">双</a-tag>
            </div>
            <!-- <div class="group/item">
              <span class="text-[#3A77F0] cursor-pointer" @click="handleToInfo(record)">{{ text }}</span>
              <span class="cursor-pointer invisible group-hover/item:visible" @click="copyText(text)">
                <VueIcon :icon="IconFuzhiFont" />
              </span>
            </div> -->
          </template>
          <template v-if="column.dataIndex === 'riskCount'">
            <p v-if="text === 0 || text === '0'" class="m-0">{{ text }}</p>
            <p v-else type="text" class="text-[#E6AD1C] cursor-pointer m-0" @click="redirectTo('/dangerFarmerList', { id: record.farmerlistNo })">{{ text }}</p>
          </template>
          <template v-if="column.dataIndex === 'errorCount'">
            <p v-if="text === 0 || text === '0'" class="m-0">{{ text }}</p>
            <p v-else class="text-[#DB3939] cursor-pointer m-0" @click="redirectTo('/errorFarmerList', { id: record.farmerlistNo })">{{ text }}</p>
          </template>
          <template v-if="['farmerlistName', 'productName', 'addressName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <!-- <a-button type="link" size="small" @click="landMap(record)">地图</a-button> -->
            <div class="flex items-center justify-start">
              <a-button v-for="button in record.buttonAuthList.slice(0, 3)" :key="button.code" type="link" size="small" @click="clickEvent(record, button.code)">{{ button.name }}</a-button>
              <a-dropdown v-if="record.buttonAuthList.length > 3">
                <a-button type="link" size="small">
                  <VueIcon :icon="IconBiaogeGengduoFont" />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-for="button in record.buttonAuthList.slice(3)" :key="button.code" @click="clickEvent(record, button.code)">
                      <span class="text-xs text-font-second">{{ button.name }}</span>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 合并清单弹窗 -->
    <MergeModal v-if="mergeVisible" v-model:visible="mergeVisible" :selected-row-keys="selectedRowKeys" :finish="mergeFinish" />
    <!-- 清单导入弹窗 -->
    <ImportModal v-if="listImportVisible" v-model:visible="listImportVisible" :default-department-code="formData.departmentCode" :risk-code="formData.riskCode" :default-product="formData.productCode" :refresh="submit" />
    <!-- 增量导入弹窗 -->
    <ImcrementalImportModal v-if="imcrementalImportVisible" v-model:visible="imcrementalImportVisible" :list-info="selectRecord" />
    <!-- 清单信息弹窗 -->
    <InfoModal v-model:visible="infoVisible" :item="rowItem" />
    <!-- 导出清单弹窗 -->
    <DownLoadModal v-model:open="downLoadVisible" />
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="openVisible" :width="pxToRem(450)" @ok="handleConfirmOk">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div>清单信息修改，需要重新发起投保告知，发起公示，提交验标</div>
    </a-modal>
    <!-- 取消确认 -->
    <a-modal v-model:open="cancelVisible" :width="pxToRem(450)" @ok="cancelLink">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div>{{ `该清单已关联投保单${cancleItem.applyPolicyNo}，继续操作会取消当前关联关系，是否继续操作` }}</div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { Form } from 'ant-design-vue';
import { IconBiaogeGengduoFont, IconInfoCircleFilledFont, IconChevronRightDoubleFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { set, debounce } from 'lodash-es';
import type { FarmerInfo } from '../farmer.d';
import ImcrementalImportModal from './modal/incrementalImportModal.vue';
import InfoModal from './modal/infoModal.vue';
import DownLoadModal from './modal/downLoadModal.vue';
import ImportModal from './modal/importModal.vue';
import MergeModal from './modal/mergeModal.vue';
import { columns } from './columns';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import FormFold from '@/components/ui/FormFold.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import QuaterSelect from '@/components/selector/QuaterSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import AuthButton from '@/components/ui/AuthButton.vue';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { useGet, usePost, $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import type { InsuranceFarmerList, InsuranceFarmerListRes } from '@/apiTypes/farmer/farmerList';
import { useRowSelect } from '@/composables/useRowSelect';
import { useUserStore } from '@/stores/useUserStore';
import { message } from '@/components/ui/Message';
import CopyLink from '@/components/ui/CopyLink.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

const route = useRoute();
const router = useRouter();

const { userInfo } = useUserStore();
const statusOptions = ref([]); // 清单状态
// const oprRoleOptions = ref([]); // 操作人员类型

const expand = ref(false);
const dataSource = ref<InsuranceFarmerList[]>([]);
const loading = ref(false);
// 二次提示弹窗
const openVisible = ref<boolean>(false);

const formData = reactive<FarmerInfo>({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  farmerlistNo: '',
  farmerlistName: '',
  farmerlistStatus: ['-1', '0', '1'],
  oprRoleCode: ['1', '2'],
  productCode: '',
  riskCode: undefined,
  policyYear: undefined,
  quarter: undefined,
  riskAddressCodeCity: undefined,
  riskAddressCodeCounty: undefined,
  riskAddressCodeProvince: undefined,
  riskAddressCodeTown: undefined,
  riskAddressCodeVillage: undefined,
  applyPolicyNo: '',
});

const formRules = reactive({
  departmentCode: [{ required: true, message: '请选择机构' }],
  farmerlistStatus: [{ required: true, message: '请选择状态' }],
  oprRoleCode: [{ required: true, message: '请选择人员类别' }],
});

const mergeVisible = ref(false); // 合并弹窗
const clickMergeButton = () => {
  if (selectedRowKeys.value.length > 1) {
    mergeVisible.value = true;
  } else {
    message.warning('请至少选择2条及以上的投保清单');
  }
};
// 合并成功
const mergeFinish = () => {
  clearSelect();
  mergeVisible.value = false;
  submit();
};

// 删除清单
const clickDeleteButton = () => {
  if (selectedRowKeys.value.length > 0) {
    Modal.confirm({
      title: '提醒',
      content: '是否删除当前选中清单',
      async onOk() {
        $postOnClient(`${gateWay}${service.farmer}/farmerList/deleteList`, { farmerListNos: selectedRowKeys.value }).then((res) => {
          if (res && res.code === SUCCESS_CODE) {
            message.success(res.msg);
            clearSelect();
            submit();
          } else {
            message.error(res?.msg || '');
          }
        });
      },
    });
  } else {
    message.warning('请至少选择1条投保清单');
  }
};

// 清单确认
// const confirmList = (record: Record<string, string>) => {
//   $postOnClient(`${gateWay}${service.farmer}/farmerList/confirmFarmerNo`, { farmerListNos: [record.farmerlistNo] }).then((res) => {
//     if (res && res.code === SUCCESS_CODE) {
//       message.success((res?.data || '') as string);
//       submit();
//     } else {
//       message.error(res?.msg || '');
//     }
//   });
// };

// 地图
const landMap = (record: Record<string, string>) => {
  if (record.importCount > 50000) {
    message.warning('清单关联的地块数据超过50000条，地图功能升级中，暂不能使用');
  } else {
    router.push({
      path: 'landEdit',
      query: {
        farmerListNo: record.farmerlistNo,
        addressName: record.addressName,
      },
    });
  }
};

const { resetFields, validate, validateInfos } = Form.useForm(formData, formRules);
const imcrementalImportVisible = ref(false); // 增量导入弹窗

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  selectedRowKeys.value = [];
  refresh();
};

const resetForm = () => {
  resetFields();
  refresh();
};

// 获取清单状态筛选项
const { fetchData: getListStatus } = await useGet('/api/farmer/listFarmerListStatus');
// 获取清单状态筛选项
// const { fetchData: getListOprRoleCode } = await useGet('/api/farmer/listOprRoleCode');
// 获取表格
const { fetchData: getTable } = await usePost<InsuranceFarmerListRes>('/api/farmer/pageFarmerListSummary');
// 增量导入
const selectRecord = ref();
// const incrementalImport = (record: Record<string, string>) => {
//   selectRecord.value = record;
//   imcrementalImportVisible.value = true;
// };

interface farmerType {
  id: string;
  farmerlistName: string;
  riskCount: string;
  errorCount: string;
  importCount: string;
}
// 点击编辑时，为已关联数据则提示
const obj = ref<farmerType>({ id: '', farmerlistName: '', riskCount: '', errorCount: '', importCount: '' });
const handleEdit = ({ farmerlistNo, farmerlistName, farmerlistStatus }: Record<string, string>) => {
  obj.value = {
    id: farmerlistNo,
    farmerlistName,
    // riskCount,
    // errorCount,
    // importCount: farmersCount,
  };
  if (farmerlistStatus === '2') {
    openVisible.value = true;
  } else {
    handleConfirmOk();
  }
};
// 二次弹窗确认 跳转编辑也
const handleConfirmOk = () => {
  openVisible.value = false;
  redirectTo('/editFarmerList', obj.value);
};

const redirectTo = (path: string, query: Record<string, string>) => {
  router.push({ path, query });
};

const handleView = ({ farmerlistNo, departmentCode, farmerlistName }: Record<string, string>) => {
  redirectTo('/editFarmerList', {
    id: farmerlistNo || '',
    farmerlistName,
    viewType: '1',
    dept: departmentCode,
  });
};

const infoVisible = ref(false);
const rowItem = ref({});
const handleToInfo = (record: Record<string, string>) => {
  rowItem.value = {
    farmerlistNo: record.farmerlistNo || '',
    farmerlistName: record.farmerlistName || '',
    farmerlistStatusDesc: record.farmerlistStatusDesc || '',
    address: record.addressName,
    createdBy: record.createdBy || '',
    importCount: record.importCount,
    riskCount: record.riskCount,
    errorCount: record.errorCount,
    importDate: record.importDate || '',
    oprRoleName: record.oprRoleName || '',
    departmentName: record.departmentName || '',
    departmentCode: record.departmentCode || '',
    productName: record.productName || '',
    applyPolicyNo: record.applyPolicyNo || '-',
    errorruleContents: record.errorruleContents || '-',
  };
  infoVisible.value = true;
};

// 机构赋值
const changeDeptCode = (val: string) => {
  formData.departmentCode = val;
};

// Create a debounced version of the refresh function
const debouncedRefresh = debounce(() => {
  validate().then(() => {
    loading.value = true;
    getTable({
      ...formData,
      farmerlistNo: formData.farmerlistNo.trim(),
      farmerlistName: formData.farmerlistName.trim(),
      containChildDepart: formData.containChildDepart ? '1' : '0',
      farmerlistStatus: formData.farmerlistStatus.join(','),
      oprRoleCode: formData.oprRoleCode.join(','),
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    })
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          const { records, total = 0, current = 1, size = 10 } = res.data;
          dataSource.value = records;
          pagination.total = total;
          pagination.current = current;
          pagination.pageSize = size;
        } else {
          dataSource.value = [];
          pagination.total = 0;
          pagination.current = 1;
          pagination.pageSize = 10;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
}, 500); // 300ms debounce time

// Update the original refresh function to use the debounced version
const refresh = () => {
  debouncedRefresh();
};

// 取消关联
const cancelLink = async () => {
  cancelVisible.value = false;

  try {
    const fetchUrl = gateWay + service.accept + '/web/applicationForm/cancelLinkFarmerList';
    const res = await $postOnClient(fetchUrl, { applyPolicyNo: cancleItem.value.applyPolicyNo, farmerlistNo: cancleItem.value.farmerlistNo });
    if (res && res.code === SUCCESS_CODE) {
      message.success('取消成功');
      refresh();
    } else {
      message.success(res.msg || '取消失败');
    }
  } catch (e) {
    console.log(e);
  }
};

const cancelVisible = ref(false);
const cancleItem = ref();
const handleCancel = (record: Record<string, string>) => {
  cancelVisible.value = true;
  cancleItem.value = record;
};

const clickEvent = async (record: Record<string, string>, type: string) => {
  const eventMap = {
    farmerEdit: handleEdit, // 编辑
    farmerView: handleView, // 查看
    farmerDisassociation: handleCancel, // 取消关联
    // farmerConfirm: confirmList, // 清单确认
    // incrementalImport: incrementalImport, // 增量导入
    farmerExportList: landMap, // 地图
  };
  const eventFn = eventMap[type as keyof typeof eventMap];
  if (eventFn) {
    if (type === 'farmerExportList') {
      const res = await $getOnClient(`${gateWay}${service.farmer}/dictSelect/getLandVersionNum`, { farmerListNo: record.farmerlistNo });
      if (res?.code === SUCCESS_CODE && res?.data !== '3.0') {
        message.error('双精准2.0地图功能升级中，暂不支持使用');
      } else {
        eventFn(record);
      }
    } else {
      eventFn(record);
    }
  }
};

const { pagination } = usePagination(refresh);
const { selectedRowKeys, onSelectChange, clearSelect } = useRowSelect(pagination);

// 导出
const batchExportLoading = ref(false);
const exportList = async () => {
  // 检查有没有勾选某行数据
  if (selectedRowKeys.value && selectedRowKeys.value.length) {
    batchExportLoading.value = true;
    const query = {
      position: '1',
      applyParamVO: {
        farmerListNos: selectedRowKeys.value,
      },
    };
    try {
      await $postOnClient(`${gateWay}${service.farmer}/file/excelExport`, query, {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
          selectedRowKeys.value = [];
          batchExportLoading.value = false;
        },
      });
    } catch (err) {
      batchExportLoading.value = false;
      console.log(err);
    }
  } else {
    message.warning('请至少选择1条及以上的投保清单');
  }
};

const listImportVisible = ref(false); // 导入清单弹窗
const openImportModal = () => {
  listImportVisible.value = true;
};

const downLoadVisible = ref(false);
const openDownLoad = () => {
  downLoadVisible.value = true;
};

// 初次刷新
// 既然relatedNext都会调用，就无需这个判断了
// watch(
//   () => formData.departmentCode,
//   () => {
//     refresh();
//   },
//   { once: true, immediate: true },
// );
// 任务中心跳转过来带入投保单号
watch(
  () => route.query,
  (val) => {
    if (val && val.bizNo) {
      formData.applyPolicyNo = val.bizNo as string;
      refresh();
    }
  },
  { deep: true, immediate: true },
);

// 关联清单跳转过来
const relatedNext = () => {
  if (route.query.openImport === 'Y') {
    formData.departmentCode = route.query.departmentCode as string;
    formData.productCode = route.query.productCode as string;
    formData.riskAddressCodeProvince = route.query.province as string;
    formData.riskAddressCodeCity = route.query.city as string;
    formData.riskAddressCodeTown = route.query.town as string;
    formData.riskAddressCodeCounty = route.query.county as string;
    formData.riskAddressCodeVillage = route.query.village as string;
    openImportModal();
  }
  // 按图制作跳回来
  if (route.query.farmerlistNo) {
    formData.farmerlistNo = route.query.farmerlistNo as string;
  }
  // 不管从哪里进来都刷新一些列表，编辑完用户，返回错误列表，又通过页签切换，数据没有刷新
  refresh();
};

const jump2LandQuery = () => {
  router.push({
    path: '/landQuery',
  });
};

onActivated(() => {
  relatedNext();
});

onDeactivated(() => {
  route.query.openImport = 'N';
});

onMounted(() => {
  relatedNext();
  initOption();
});

// onBeforeMount(() => {
//   map.instance.destroy();
// });

// 初始化选择项
const initOption = () => {
  getListStatus().then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      set(statusOptions, 'value', res?.data || []);
    }
  });
  // getListOprRoleCode().then((res) => {
  //   if (res && res.code === SUCCESS_CODE) {
  //     set(oprRoleOptions, 'value', res?.data || []);
  //   }
  // });
};
</script>

<style lang="less" scoped>
.farmerlist-table {
  :deep(.ant-table-wrapper .ant-table-cell-fix-right) {
    right: -1px !important;
  }
}
</style>
