<template>
  <div class="p-14px space-y-14px">
    <a-spin :spinning="searchLoading" wrapper-class-name="search-spin-wrapper-css">
      <div class="bg-white p-16px rounded-md">
        <div class="flex">
          <a-form ref="formRef" :model="formData" :colon="false" :disabled="disabled" class="flex-grow">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="机构" name="departmentNo" :rules="[{ required: true, message: '请选择机构' }]" :label-col="{ style: { width: pxToRem(80) } }">
                  <department-search v-model:contain-child-depart="formData.containChildDepart" v-model:loading="searchLoading" :dept-code="formData.departmentNo" :show-child-depart="true" @change-dept-code="changeDeptCode" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产品" name="productCode" :label-col="{ style: { width: pxToRem(80) } }">
                  <ProductSelect v-model:value="formData.productCode" :department-code="formData.departmentNo" :encode-key="formData.riskType" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="标的" name="riskType" :label-col="{ style: { width: pxToRem(80) } }">
                  <RiskCodeSelect v-model:value="formData.riskType" :disabled="disabled" :department-code="formData.departmentNo" :show-search="false" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="任务分类" name="taskCategoryCode" :label-col="{ style: { width: pxToRem(80) } }">
                  <a-cascader v-model:value="formData.taskCategoryCode" class="w-full" change-on-select :options="categoryOpt" @change="handleCategoryChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="保单号/投保单号" name="bizNo" :label-col="{ style: { width: pxToRem(110) } }">
                  <a-input v-model:value.trim="formData.bizNo" :disabled="false" placeholder="请输入保单号或投保单号" allow-clear @blur="handleBizNoChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="任务紧急程度" name="taskLevel" :label-col="{ style: { width: pxToRem(120) } }">
                  <check-box-group v-model:checked-list="formData.taskLevel" :options="levelOpt" :disabled="disabled" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand">
              <a-col>
                <a-form-item label="任务进展" name="progress" :label-col="{ style: { width: pxToRem(80) } }">
                  <check-box-group v-model:checked-list="formData.progress" :options="progressOpt" :disabled="disabled" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand">
              <a-col>
                <a-form-item label="任务状态" name="taskStatus" :label-col="{ style: { width: pxToRem(80) } }">
                  <check-box-group v-model:checked-list="formData.taskStatus" :options="statusOpt" :disabled="disabled" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <div class="w-[50px]">
            <form-fold v-model="expand" />
          </div>
        </div>
        <div class="flex justify-center items-center space-x-8px">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" ghost @click="submit">查询</a-button>
        </div>
      </div>
    </a-spin>
    <div class="bg-white p-16px rounded-md space-y-16px taskList-table relative">
      <div class="flex justify-between align-center">
        <div class="flex items-center space-x-[12px]">
          <span class="text-[#404442] text-xl font-bold">查询结果</span>
        </div>
      </div>
      <a-table :columns="columns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" row-key="taskListNo" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)" class="table-box">
        <template #headerCell="{ column, title }">
          <template v-if="column.dataIndex === 'taskName'">
            <span class="mr-[4px]">{{ title }}</span>
            <a-tooltip title="任务完成后T+1刷新任务状态">
              <span class="text-[rgba(0,0,0,0.55)]">
                <VueIcon :icon="IconHelpCircleFilledFont" />
              </span>
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'label'">
            <div class="rounded-[12px] bg-[#EAF1FF] w-[48px] leading-[24px] text-center">
              <!-- 合规显示一个合字，其他显示两个字 -->
              {{ _find(lebalOpt, { value: text })?.label.slice(0, _find(lebalOpt, { value: text })?.label === '合规' ? 1 : 2) || '-' }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'bizNo'">
            <CopyLink :text="text" />
          </template>
          <template v-if="column.dataIndex === 'taskStatus'">
            <span :class="`rounded-[12px] bg-[${operationMap[record.taskStatusCode]?.bg}] p-[6px] text-center`">
              <VueIcon :icon="operationMap[record.taskStatusCode]?.icon" :class="`text-[${operationMap[record.taskStatusCode]?.color}] mr-[5px]`" />
              <span :class="`text-[${operationMap[record.taskStatusCode]?.color}]`">{{ text }}</span>
            </span>
          </template>
          <template v-if="column.dataIndex === 'progress'">
            <span v-if="record.progressCode" :class="`rounded-[12px] bg-[${progressMap[record.progressCode].bg}] text-[${progressMap[record.progressCode].color}] px-[8px] py-[4px]`">{{ text }}</span>
            <span v-else>-</span>
          </template>
          <template v-if="column.dataIndex === 'dueDateDay'">
            <span :class="{ 'text-[#FF5B00]': record.progressCode === 'overdue' }">{{ text }}天</span>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <div class="flex items-center justify-center space-x-[4px]">
              <a-button v-for="(item, index) in operationMap[record.taskStatusCode]?.list" :key="index" type="link" size="small" @click="handleClick(record, item.txt)">{{ item.txt }}</a-button>
            </div>
          </template>
        </template>
      </a-table>
      <span class="text-[#000] opacity-55 absolute bottom-[30px]">总任务{{ taskNum.total }}个，其中紧急任务{{ taskNum.emergency }}个，常规任务{{ taskNum.normal }}个。</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { debounce, find as _find } from 'lodash-es';
import type { taskInfo, taskListRes, taskList, optType } from '../task';
import { columns, TaskNameCode } from './config';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import FormFold from '@/components/ui/FormFold.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { pxToRem } from '@/utils/tools';
import { usePost, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import { useUserStore } from '@/stores/useUserStore';
import { IconErrorCircleFilledFont, IconCheckCircleFilledFont, IconHelpCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import CopyLink from '@/components/ui/CopyLink.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const router = useRouter();
const { userInfo } = useUserStore();
const disabled = ref(false);
const categoryOpt = ref([]);
const levelOpt = ref([]);
const progressOpt = ref([]);
const statusOpt = ref([]); // 清单状态
const lebalOpt = ref<optType[]>([]);

const expand = ref(false);
const dataSource = ref<taskList[]>([]);
const loading = ref(false);
const searchLoading = ref(false);

const formData = reactive<taskInfo>({
  departmentNo: defaultDeptCode.value,
  containChildDepart: true,
  bizNo: '',
  productCode: '',
  riskType: undefined,
  taskCategoryCode: [],
  taskNameCode: [],
  taskLevel: [],
  progress: [],
  taskStatus: [],
});
const taskNum = reactive({
  total: 0,
  normal: 0,
  emergency: 0,
});

const formRef = ref();

const handleBizNoChange = async () => {
  if (formData.bizNo) {
    disabled.value = true;
    formData.productCode = '';
    formData.riskType = undefined;
    formData.taskCategoryCode = [];
    formData.taskNameCode = [];
    formData.taskLevel = [];
    formData.progress = [];
    formData.taskStatus = [];
    formData.taskNameCode = [];
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: formData.bizNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      formData.departmentNo = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    formData.departmentNo = defaultDeptCode.value;
  }
};

const handleCategoryChange = (val) => {
  // 清空时重置为空数组
  if (!val) {
    formData.taskCategoryCode = [];
  }
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const resetForm = () => {
  formRef.value?.resetFields();
  formData.taskNameCode = [];
  refresh();
};

// 获取清单状态筛选项
const { fetchData: getcheckOpt } = await usePost<optType[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`);
// 获取表格
const { fetchData: getTable } = await usePost<taskListRes>(`${gateWay}${service.administrate}/taskCenter/list`);
// 获取任务统计
const { fetchData: getTaskTotal } = await usePost<{ normal: number; total: number; emergency: number }>(`${gateWay}${service.administrate}/taskCenter/countGroupByLevel`);

// 操作map
const operationMap: { [key: string]: { list: { txt: string }[]; icon: IconDefinition; bg: string; color: string } } = {
  '1': { list: [{ txt: '处理' }], icon: IconErrorCircleFilledFont, bg: '#FFF9EB', color: '#E6AD1C' },
  '2': { list: [{ txt: '查看' }, { txt: '重新发起' }], icon: IconErrorCircleFilledFont, bg: '#FFEEE5', color: '#FF5B00' },
  '3': { list: [{ txt: '查看' }], icon: IconErrorCircleFilledFont, bg: '#EAF1FF', color: '#576B95' },
  '4': { list: [{ txt: '查看' }], icon: IconCheckCircleFilledFont, bg: '#F2FFF9', color: '#07C160' },
};

const progressMap: { [key: string]: { bg: string; color: string } } = {
  overdue: { bg: '#FFEEE5', color: '#FF5B00' },
  deadLine: { bg: '#FFF9EB', color: '#E6AD1C' },
  normal: { bg: '#EAF1FF', color: '#576B95' },
};

// 机构赋值
const changeDeptCode = (val: string) => {
  formData.departmentNo = val;
};

const debouncedRefresh = debounce(() => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    const params = {
      ...formData,
      taskNameCode: formData.taskCategoryCode.length > 1 ? [formData.taskCategoryCode[1]] : [],
      taskCategoryCode: formData.taskCategoryCode.length > 1 ? [formData.taskCategoryCode[0]] : formData.taskCategoryCode,
    };
    getTaskTotal(params).then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        taskNum.total = res.data.total;
        taskNum.normal = res.data.normal;
        taskNum.emergency = res.data.emergency;
      }
    });
    getTable({ ...params, pageNum: pagination.current, pageSize: pagination.pageSize })
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          const { records, total = 0, current = 1, size = 10 } = res.data;
          dataSource.value = records;
          pagination.total = total;
          pagination.current = current;
          pagination.pageSize = size;
        } else {
          dataSource.value = [];
          pagination.total = 0;
          pagination.current = 1;
          pagination.pageSize = 10;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
}, 500);

const refresh = () => {
  debouncedRefresh();
};

const handleClick = (row: Record<string, string>, btnTxt: string) => {
  const urlMap: { [key in TaskNameCode]: string } = {
    [TaskNameCode.postCheckAdjustment]: '/queryCheckTask',
    [TaskNameCode.farmerAuthConfirm]: btnTxt === '处理' ? '/endorseApply' : '/endorseTracking',
    [TaskNameCode.supplementingDoc]: '/infoModify',
    [TaskNameCode.verificationRate4ANB]: '/farmerList',
    [TaskNameCode.postCheckModify]: '/queryCheckTask',
  };
  const taskNameCode = row.taskNameCode as TaskNameCode; // 确保 taskNameCode 是枚举类型
  if (urlMap[taskNameCode]) {
    router.push({
      path: urlMap[taskNameCode],
      // 跳转分户清单要传投保单号其余都可以传保单号
      query: { bizNo: taskNameCode === TaskNameCode.verificationRate4ANB ? row.applyPolicyNo : row.bizNo },
    });
  }
};

const { pagination } = usePagination(refresh);

// 初次刷新
watch(
  () => formData.departmentNo,
  () => {
    refresh();
  },
  { once: true, immediate: true },
);

onMounted(() => {
  initOption();
});

// 初始化选择项
const initOption = () => {
  const typeMap: { [key: string]: Ref<optType[], unknown> } = {
    taskCenterCategory: categoryOpt,
    taskCenterLevel: levelOpt,
    taskCenterProgress: progressOpt,
    taskCenterStatus: statusOpt,
    taskCenterLabel: lebalOpt,
  };

  getcheckOpt(['taskCenterCategory', 'taskCenterLevel', 'taskCenterProgress', 'taskCenterStatus', 'taskCenterLabel']).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      res.data.forEach((item) => {
        if (item.value in typeMap) {
          typeMap[item.value].value = item.children;
        }
      });
      levelOpt.value.forEach((item: { value: string }) => formData.taskLevel.push(item.value));
      progressOpt.value.forEach((item: { value: string }) => formData.progress.push(item.value));
      statusOpt.value.forEach((item: { value: string }) => formData.taskStatus.push(item.value));
    }
  });
};
</script>

<style lang="less" scoped>
.taskList-table {
  :deep(.ant-table-wrapper .ant-table-cell-fix-right) {
    right: -1px !important;
  }
}
</style>
