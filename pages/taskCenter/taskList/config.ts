import type { ColumnsType } from 'ant-design-vue/es/table';
import { pxToRem } from '@/utils/tools';

export const columns: ColumnsType<Record<string, string>> = [
  { title: '标签', dataIndex: 'label' },
  { title: '单号', dataIndex: 'bizNo' },
  { title: '任务分类', align: 'center', dataIndex: 'taskCategory' },
  { title: '任务名', align: 'center', dataIndex: 'taskName' },
  { title: '任务状态', align: 'center', dataIndex: 'taskStatus' },
  { title: '进展', align: 'center', dataIndex: 'progress' },
  { title: '到期时间', align: 'center', dataIndex: 'dueDateDay' },
  { title: '操作', align: 'center', fixed: 'right', dataIndex: 'operation', width: pxToRem(150) },
];

export enum TaskNameCode {
  postCheckAdjustment = 'postCheckAdjustment', // 验标后补
  farmerAuthConfirm = 'farmerAuthConfirm', // 农户验真后补
  supplementingDoc = 'supplementingDoc', // 资料补缴
  verificationRate4ANB = 'verificationRate4ANB', // 爱农宝核身率后补
  postCheckModify = 'postCheckModify', // 保单验标修改
}
