<template>
  <a-modal v-model:open="visible" title="更改退款信息" :width="pxToRem(950)" centered :closable="false" :mask-closable="false" @ok="handleOk">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">通知单信息</span>
      </div>
    </div>
    <div class="bg-gray-100 rounded p-14px box-border mb-[13px]">
      <div class="flex">
        <p class="m-0 w-1/2 shrink-0 box-border">
          <span>通知单金额：</span>
          <span class="text-[#262626]">{{ premiumObj.adjustedFlag === '1' ? premiumObj.adjustedAmount : premiumObj.actualPremium }}</span>
        </p>
        <p class="m-0 w-1/2 shrink-0 box-border">
          <span>扣税后金额：</span>
          <span class="text-[#262626]">{{ premiumObj.adjustedFlag === '1' ? premiumObj.excludeTaxAdjustedAmount : premiumObj.excludeAddedTaxPremium }}</span>
        </p>
      </div>
    </div>
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="SolutionfontFont" />
        <span class="ml-[3px]">收款人银行信息</span>
      </div>
    </div>
    <div class="bg-gray-100 rounded p-14px box-border">
      <a-form ref="formStateRef" label-align="left" :label-col="{ style: { width: pxToRem(150) } }" :model="formState">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="收款人名称" name="paymentPersonName" :rules="[{ required: true, message: '请输入收款人名称' }]">
              <a-input v-model:value.trim="formState.paymentPersonName" :maxlength="50" placeholder="请输入收款人名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="收付途径" name="paymentPathSwitch" :rules="[{ required: true, message: '请选择收付途径' }]">
              <a-radio-group v-model:value="formState.paymentPathSwitch" @change="handleChangePaymentPathSwitch(formState.paymentPathSwitch)">
                <a-radio value="0">公司柜面</a-radio>
                <a-radio value="1">银行转账</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <template v-if="formState.paymentPathSwitch === '1'">
          <!-- 银行转账信息 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="银行类型" name="bankAccountAttribute" :rules="[{ required: true, message: '请选择收付途径' }]">
                <a-radio-group v-model:value="formState.bankAccountAttribute">
                  <a-radio value="1">个人账号</a-radio>
                  <a-radio value="0">公司账号</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="收付方式" name="paymentPath" required>
                <a-radio-group v-model:value="formState.paymentPath">
                  <a-radio value="02">实时</a-radio>
                  <a-radio value="03">批量</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col v-if="formState.bankAccountAttribute === '1'" :span="12">
              <a-form-item label="身份证" name="certificateNo" :rules="[{ required: formState.bankAccountAttribute === '1', message: '请输入身份证' }, { validator: checkIDCardNo }]">
                <a-input v-model:value.trim="formState.certificateNo" allow-clear placeholder="请输入身份证" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="开户银行所属省份" name="bankProvinceCode" :rules="[{ required: true, message: '请选择省份' }]">
                <a-select v-model:value="formState.bankProvinceCode" :options="provinceOptions" show-search allow-clear placeholder="请选择省份" :filter-option="filterOption" @change="handleProvince" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="开户银行所属城市" name="bankCityCode" :rules="[{ required: true, message: '请选择城市' }]">
                <a-select v-model:value="formState.bankCityCode" :disabled="!formState.bankProvinceCode" allow-clear :options="cityOptions" :filter-option="filterOption" show-search placeholder="请选择城市" @change="handleClearBankCity" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="开户银行" name="bankHeadquartersCode" :rules="[{ required: true, message: '请选择开户行' }]">
                <a-select v-model:value="formState.bankHeadquartersCode" :disabled="!formState.bankCityCode" allow-clear :options="bankOption" show-search :filter-option="filterOption" placeholder="请选择开户行" @change="handleBankChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="开户银行名称" name="bankCode" :rules="[{ required: true, message: '请选择开户行名称' }]">
                <a-select v-model:value="formState.bankCode" :disabled="!(formState.bankHeadquartersCode && formState.bankCityCode && formState.bankProvinceCode)" show-search allow-clear :filter-option="filterOption" :options="bankNameOption" placeholder="请选择开户行名称" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="银行账号" name="bankAccountNo" :rules="[{ required: true, message: '请输入银行账号' }]">
                <a-input v-model:value.trim="formState.bankAccountNo" allow-clear placeholder="请输入银行账号" />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel"> 取消 </a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleOk"> 确认 </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, SolutionfontFont } from '@pafe/icons-icore-agr-an';
import { cloneDeep } from 'lodash-es';
import type { OptionType, PremiumObjType, IFormState } from '../paymentManage';
import { pxToRem } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '~/composables/request';
import { checkIDCardNo } from '@/utils/validators';

const { gateWay, service } = useRuntimeConfig().public || {};
const visible = defineModel<boolean>('visible', { required: true, default: false });
const premiumObj = defineModel<PremiumObjType>('premiumObj', { default: {} });
const confirmLoading = defineModel<boolean>('confirmLoading', { default: false });
const emit = defineEmits(['handleOk', 'handleCancel']);
const formState = ref<IFormState>({
  bankAccountAttribute: '1', // 领款人帐号类型(0 团体，1个人)
  bankAccountNo: '', // 收款人账号
  bankCode: undefined, // 收款人开户行编码(支行)
  bankHeadquartersCode: undefined, // 收款人开户银行编码(总行)
  paymentPathSwitch: '0', // 0-公司柜面 1-银行转账
  paymentPath: '02', // 收款途径 03-银行转帐 批量收付 02 实时支付-->实时收付
  paymentPersonName: '', // 收款人姓名
  bankProvinceCode: undefined, // 省份
  bankCityCode: undefined, // 市
  certificateNo: '', // 身份证
});
const provinceOptions = ref<OptionType[]>([]);
const cityOptions = ref<OptionType[]>([]);

// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option?.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 获取全国所有省份信息
const queryBankProvinceList = async () => {
  try {
    const res = await $getOnClient(`${gateWay}${service.accept}/trans/base/queryBankProvinceList`);
    provinceOptions.value = (res?.data as OptionType[])?.map((item) => {
      return {
        value: item.provinceCode,
        label: item.province,
      };
    });
  } catch (error) {
    console.log(error);
  }
};
// 获取全国所有城市信息
const queryBankCityList = async () => {
  try {
    const res = await $getOnClient(`${gateWay}${service.accept}/trans/base/queryBankCityList`, { bankProvinceCode: formState.value.bankProvinceCode });
    cityOptions.value = (res?.data as OptionType[])?.map((item) => {
      return {
        value: (item.cityCode || '') + item.city,
        label: item.city,
        keys: item.cityCode,
      };
    });
  } catch (error) {
    console.log(error);
  }
};
// 开户行
const bankOption = ref<OptionType[]>([]);
// 获取全部银行大类
const queryBankTypeList = async () => {
  try {
    const res = await $getOnClient(`${gateWay}${service.accept}/trans/base/queryBankTypeList`);
    bankOption.value = (res?.data as OptionType[])?.map((item) => {
      return {
        value: item.bankTypeCode,
        label: item.bankTypeName,
      };
    });
  } catch (error) {
    console.log(error);
  }
};
const bankNameOption = ref<OptionType[]>([]);
// 通过省市、银行大类查询具体支行信息
const queryBankCodeListByTypeAndCity = async () => {
  try {
    const { bankProvinceCode, bankHeadquartersCode } = formState.value;
    const code = cloneDeep(cityOptions.value)?.filter((item) => item.value === formState.value.bankCityCode)?.[0].keys; // 处理value值
    const res = await $postOnClient(`${gateWay}${service.accept}/trans/base/queryBankCodeListByTypeAndCity`, { bankProvinceCode, bankCityCode: code, bankTypeCode: bankHeadquartersCode });
    bankNameOption.value = (res?.data as OptionType[])?.map((item) => {
      return {
        value: (item.bankCode || '') + item.bankName,
        label: item.bankName,
        keys: item.bankCode,
      };
    });
  } catch (error) {
    console.log(error);
  }
};
// 当省市，银行大类都有值时，调用开户银行名称接口
const handleBankChange = () => {
  const { bankProvinceCode, bankCityCode, bankHeadquartersCode } = formState.value;
  if (bankProvinceCode && bankCityCode && bankHeadquartersCode) {
    queryBankCodeListByTypeAndCity();
    formState.value.bankCode = undefined;
  } else {
    formState.value.bankCode = undefined;
  }
};
// 省份选择
const handleProvince = () => {
  formState.value.bankCityCode = undefined;
  formState.value.bankHeadquartersCode = undefined;
  formState.value.bankCode = undefined;
  queryBankCityList();
};
// 选择市
const handleClearBankCity = () => {
  formState.value.bankHeadquartersCode = undefined;
  formState.value.bankCode = undefined;
};
const formStateRef = ref();
const handleOk = async () => {
  if (formState.value.bankAccountAttribute === '0') formStateRef.value.clearValidate('bankAccountAttribute');
  await formStateRef.value.validate();
  confirmLoading.value = true;
  // 如果名称不一样，则接口仅校验
  const flag = premiumObj.value.name?.find((item) => item !== formState.value.paymentPersonName);
  if (flag) {
    premiumObj.value.refundCheckFlag = 'Y';
  } else {
    premiumObj.value.refundCheckFlag = 'N';
  }
  // 如果为公司柜面则清空银行类的信息
  if (formState.value.paymentPathSwitch === '0') {
    formState.value.paymentPath = '01'; // 公司柜面
    formState.value.bankAccountNo = '';
    formState.value.bankAccountNo = ''; // 收款人账号
    formState.value.bankCode = undefined; // 收款人开户行编码(支行)
    formState.value.bankHeadquartersCode = undefined; // 收款人开户银行编码(总行)
    formState.value.bankProvinceCode = undefined; // 省份
    formState.value.bankCityCode = undefined; // 市
    formState.value.certificateNo = ''; // 身份证
  } else {
    premiumObj.value.bankHeadquartersName = cloneDeep(bankOption.value)?.filter((item) => item.value === formState.value.bankHeadquartersCode)?.[0]?.label;
    premiumObj.value.bankName = cloneDeep(bankNameOption.value)?.filter((item) => item.value === formState.value.bankCode)?.[0]?.label;
  }
  // 如果为公司账号，则清空身份证
  if (formState.value.bankAccountAttribute === '0') {
    formState.value.certificateNo = '';
  }
  const _bankCode = cloneDeep(bankNameOption.value)?.filter((item) => item.value === formState.value.bankCode)?.[0]?.keys; // 处理value值
  const formParams = cloneDeep(formState.value as Partial<IFormState>);
  delete formParams.paymentPathSwitch;
  premiumObj.value = { ...premiumObj.value, ...formParams, bankCode: _bankCode };
  emit('handleOk');
};
const handleCancel = () => {
  visible.value = false;
  emit('handleCancel');
};
watch(premiumObj, (val: PremiumObjType) => {
  formState.value.paymentPersonName = val.paymentPersonName || '';
});
watch(visible, (val) => {
  if (val) {
    formState.value = {
      bankAccountAttribute: '1', // 领款人帐号类型(0 团体，1个人)
      bankAccountNo: '', // 收款人账号
      bankCode: undefined, // 收款人开户行编码(支行)
      bankHeadquartersCode: undefined, // 收款人开户银行编码(总行)
      paymentPathSwitch: '0',
      paymentPath: '02', // 收款途径 03-银行转帐 批量收付 02 实时支付-->实时收付
      paymentPersonName: '', // 收款人姓名
      bankProvinceCode: undefined, // 省份
      bankCityCode: undefined, // 市
      certificateNo: '', // 身份证
    };
  }
});

const handleChangePaymentPathSwitch = (val: string) => {
  if (val === '1') {
    formState.value.paymentPath = '02'; // 银行转账默认实时支付
  }
};

onMounted(() => {
  queryBankProvinceList();
  queryBankTypeList();
});
</script>
