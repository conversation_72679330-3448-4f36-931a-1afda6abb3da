<template>
  <div class="mt-14px mb-9px space-y-16px pl-[16px]">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="formRef" :colon="false" :model="formState" class="flex-grow" :label-col="{ style: { width: pxToRem(100) } }" autocomplete="off">
          <a-row :gutter="16">
            <a-form-item label="开单类型">
              <a-radio-group v-model:value="formState.documentType">
                <a-radio value="1">见费</a-radio>
                <a-radio value="0">非见费</a-radio>
                <a-radio value="2">全选</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, message: '不能为空', trigger: 'change' }]">
                <department-search v-model:contain-child-depart="formState.containChildDepart" :dept-code="formState.departmentCode as string" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="保险起期" name="dateValue">
                <a-range-picker v-model:value="formState.dateValue" :disabled="disabledInsured" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="被保险人" name="personName">
                <a-input v-model:value="formState.personName" :disabled="disabledInsured" placeholder="可输入被保险人名称，不支持模糊搜索" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="单号" name="documentNo" :rules="[{ validator: docNoRule }]">
                <notice-type-no v-model:document-type="formState.documentNoType" v-model:document-no="formState.documentNo" @dep-code-change="depCodeChange" @blur="noticeNoBlur" />
              </a-form-item>
            </a-col>
            <a-col :span="14">
              <a-form-item label="标的类型" name="lastRiskType">
                <RiskCodeSelect v-model:value="formState.lastRiskType" :department-code="formState.departmentCode as string" :show-search="false" :disabled="disabledInsured" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="mt-[-10px]">
            <a-form-item label="保费来源" name="payerType" :rules="[{ required: true, message: '至少选择一个', trigger: 'change' }]">
              <check-box-group v-model:checked-list="formState.payerType" :options="SOURCEPREMIUM_OPTIONS" />
            </a-form-item>
          </a-row>
          <a-row :gutter="16">
            <a-form-item label="通知单状态" name="noticeStatus" :rules="[{ required: true, message: '至少选择一个', trigger: 'change' }]">
              <check-box-group v-model:checked-list="formState.noticeStatus" :options="NOTICESTATUS_OPTIONS" />
            </a-form-item>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm"> 重置 </a-button>
        <a-button type="primary" ghost @click="handleQuery"> 查询 </a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-10px">
        <div class="flex">
          <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
          <span class="px-[5px] pt-[2px]">|</span>
          <div class="inline-block min-w-5 text-[#333] pt-[2px]">
            合计保费：<span class="text-[rgba(163,0,20,0.9)]">{{ tableForm.totalAmount }}</span>
          </div>
        </div>
        <div class="table-operate text-[14px] space-x-[8px]">
          <a-form ref="formDataRef" class="inline-block w-12" :model="tableForm" autocomplete="off">
            <a-form-item label="通知单客户名称" name="customerName" :rules="[{ required: true, message: '不能为空' }]">
              <a-input v-model:value="tableForm.customerName" placeholder="请输入" />
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="notice-query-table">
        <a-table
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onSelect: onSelectChange,
            onSelectAll: onSelectAllChange,
          }"
          row-key="idApplyPayInfo"
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :scroll="{ x: 'max-content' }"
          :pagination="pagination"
          class="table-box"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="['customName', 'insurantName'].includes(column.dataIndex as string)">
              <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
                <div class="max-w-[260px] table-ellipsis-multiline">{{ text }}</div>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'insureDepartmentName'">{{ record.insureDepartmentName }}</template>
            <template v-if="column.dataIndex === 'voucherNo'">
              <div class="flex items-center">
                {{ text }}
                <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] ml-[6px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'noticeStatusName'">
              <div v-if="record.noticeStatus === '00'" class="text-amber-300 bg-[rgba(230,173,28,1)">
                <VueIcon class="text-[rgba(230,173,28,1)] text-[12px] group-hover:text-[#07C160]" :icon="IconErrorCircleFilledFont" />
                {{ text }}
              </div>
              <div v-else-if="record.noticeStatus === '51'" class="text-current">
                <VueIcon class="text-[rgba(87,107,149,1))] text-[12px]" :icon="IconCheckCircleFilledFont" />
                {{ text }}
              </div>
              <div v-else-if="record.noticeStatus === '71' || record.noticeStatus === '72'" class="text-green-500">
                <VueIcon class="text-[rgba(7,193,96,1)] text-[12px]" :icon="IconCheckCircleFilledFont" />
                {{ text }}
              </div>
              <div v-else-if="record.noticeStatus === '74'" class="text-red-500">
                <VueIcon class="text-red-500 text-[12px]" :icon="IconCloseCircleFilledFont" />
                {{ text }}
              </div>
              <div v-else class="text-green-500">
                <VueIcon class="text-[rgba(7,193,96,1)] text-[12px]" :icon="IconCheckCircleFilledFont" />
                {{ text }}
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'termAmount'">
              <div v-if="record?.adjustedFlag === '1'">
                <span class="text-red-500 mr-[6px]"> {{ text }} </span>
                <a-popover placement="right">
                  <template #title>
                    <span>调整后的开单金额 {{ record?.adjustedAmount }}</span>
                  </template>
                  <VueIcon :icon="IconHelpCircleFilledFont" />
                </a-popover>
              </div>
              <span v-else> {{ text }} </span>
            </template>
            <template v-else-if="column.dataIndex === 'isPolicyBeforePayfee'">
              <span v-if="text === '1'">见费</span>
              <span v-if="text === '0'">非见费</span>
            </template>
            <template v-else-if="column.dataIndex === 'antiMoneyInfo'">
              {{ record.paymentPersonRelationType ? '已填' : '未填' }}
            </template>
            <template v-else-if="column.dataIndex === 'operate'">
              <AuthButton code="feeBilling" type="link" :disabled="createDisabledFn(record as ApplyPayInfo)" @click="createOrder(record as ApplyPayInfo)">开单</AuthButton>
              <a-button type="link" :disabled="payDisabledFn(record as ApplyPayInfo)" @click="handlePayment(record as ApplyPayInfo)">缴费</a-button>
              <a-button type="link" :disabled="repDisabledFn(record as ApplyPayInfo)" @click="repealNotice(record as ApplyPayInfo)">撤单</a-button>
            </template>
          </template>
        </a-table>
        <div class="footer-wrapper text-[14px] space-x-[8px] text-center mt-[9px]">
          <AuthButton code="feePrintNotice" type="primary" @click="handlePayment()"> 打印通知单 </AuthButton>
          <AuthButton code="feeMergeBilling" type="primary" @click="mergeNotice"> 合并开单 </AuthButton>
          <AuthButton code="feeAdjustBillingAmount" type="primary" :disabled="!showAdjustBtn" @click="adjustAmount"> 调整开单金额 </AuthButton>
          <!-- <AuthButton code="feeMergePay" type="primary"> 合并缴费 </AuthButton> -->
          <AuthButton code="feeRevokeBilling" type="primary" @click="repealNotice('batch')"> 批量撤单 </AuthButton>
          <a-button type="primary" @click="payerModifyVisible = true"> 付款人名称修改 </a-button>
          <!-- <AuthButton code="feeICPPay" type="primary"> 爱农宝支付 </AuthButton> -->
          <AuthButton code="feeBillingBatch" type="primary" @click="open = true"> 批量开单 </AuthButton>
          <a-button type="primary" @click="toANBPayCode"> 爱农宝支付码 </a-button>
          <a-button type="primary" :disabled="antiMoneyModifyDisabled" @click="modifyAntiMoneyInfo"> 反洗钱信息修改 </a-button>
        </div>
        <!-- 财务缴费台跳转 TODO 等后端排期改为从接口拿url的方式 -->
        <form id="noticeForm" target="_blank" style="display: none" :action="noticeFormData.onlinePayUrl" method="post" accept-charset="GBK" onsubmit="document.charset='&quot;GBK&quot;'">
          <input name="amount" :value="noticeFormData.amount" />
          <input name="businessNo" :value="noticeFormData.businessNo" />
          <input name="businessTranChnl" :value="noticeFormData.businessTranChnl" />
          <input name="businessTranCode" :value="noticeFormData.businessTranCode" />
          <input name="businessType" :value="noticeFormData.businessType" />
          <input name="certPubKey" :value="noticeFormData.certPubKey" />
          <input name="currencyNo" :value="noticeFormData.currencyNo" />
          <input name="customerName" :value="noticeFormData.customerName" />
          <input name="dataSource" :value="noticeFormData.dataSource" />
          <input name="documentNo" :value="noticeFormData.documentNo" />
          <input name="onlinePayUrl" :value="noticeFormData.onlinePayUrl" />
          <input name="regionCode" :value="noticeFormData.regionCode" />
          <input name="signData" :value="noticeFormData.signData" />
          <input name="tellerNo" :value="noticeFormData.tellerNo" />
        </form>
      </div>
    </div>
    <!-- 开单成功 - 弹框  -->
    <notice-success-modal v-model:visible="successVisible" @handle-ok="handlePrintNotice" />
    <!-- 批量开单 - 弹框 -->
    <bulk-billing-modal v-model:visible="open" />
    <!-- 调整开单金额 - 弹框 -->
    <adjust-amount-modal v-model:visible="openAdjustModal" :table-data="selectData" @handle-ok="adjustSubmit" />
    <!-- 付款人名称修改 -->
    <payer-name-modify-modal v-model:visible="payerModifyVisible" />
    <!-- 反洗钱信息修改 -->
    <anti-money-modify-modal v-model:visible="antiMoneyModifyVisible" :select-data="selectData[0]" @handle-query="handleQuery" />
    <!-- 退费 -->
    <notice-modal ref="noticeModalRef" v-model:visible="noticeVisible" v-model:premium-obj="premiumObj" v-model:confirm-loading="confirmLoading" @handle-ok="handleCreateRefundNotice" @handle-cancel="() => (loading = false)" />
    <!-- 审批链签报 -->
    <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" :show-uploadbtn="false" title="开单通知- EOA签报申请" eoa-type="T01" :department-code="formState.departmentCode" :show-text="true" @ok="handleSubmit">
      <template #content>
        <LookApproval :premium-obj="premiumObj" :eoa-title="eoaTitle" />
      </template>
    </CommonApprovalModal>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import dayjs from 'dayjs';
import { IconCheckCircleFilledFont, IconErrorCircleFilledFont, IconHelpCircleFilledFont, IconCloseCircleFilledFont } from '@pafe/icons-icore-agr-an';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { $postOnClient, usePost, useGet } from '@/composables/request';
import BulkBillingModal from '@/pages/paymentManage/paymentManage/components/BulkBillingModal.vue';
import { NOTICESTATUS_OPTIONS, SOURCEPREMIUM_OPTIONS, columns } from '@/pages/paymentManage/paymentManage/constants';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import type { ApplyPayInfo, FormState, NoticeFormDataType, QueryApplyPayInfoListRes, FormDataType, ApplyPayInfoDetail, NoticeInfo, PremiumObjType, EoaData } from '@/pages/paymentManage/paymentManage/paymentManage.d';
import { useUserStore } from '@/stores/useUserStore';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import { usePagination } from '@/composables/usePagination';
import NoticeSuccessModal from '@/pages/paymentManage/paymentManage/components/NoticeSuccessModal.vue';
import AdjustAmountModal from '@/pages/paymentManage/paymentManage/components/AdjustAmountModal.vue';
import NoticeTypeNo from '@/pages/paymentManage/paymentManage/components/NoticeTypeNo.vue';
import NoticeModal from '@/pages/paymentManage/paymentManage/components/NoticeModal.vue';
import PayerNameModifyModal from '~/pages/paymentManage/paymentManage/components/PayerNameModifyModal.vue';
import AntiMoneyModifyModal from '~/pages/paymentManage/paymentManage/components/AntiMoneyModifyModal.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import AuthButton from '@/components/ui/AuthButton.vue';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import LookApproval from '@/pages/paymentManage/paymentManage/components/LookApproval.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const { userInfo } = useUserStore();
const route = useRoute();

// 是否展示付款人名称修改弹窗
const payerModifyVisible = ref<boolean>(false);
// 是否展示反洗钱信息修改弹窗
const antiMoneyModifyVisible = ref<boolean>(false);
// 筛选条件表单ref
const formRef = ref();
// 开单表单ref
const formDataRef = ref();
// 查询条件
const formState = reactive<FormState>({});
// 跳转财务缴费
const noticeFormData = ref<NoticeFormDataType>({});
// 表格数据源
const tableData = ref<ApplyPayInfo[]>([]);
// 选中的keys
const selectedRowKeys = ref<Array<string | number>>([]);
// 是否展示开单成功弹窗
const successVisible = ref<boolean>(false);
// 当前需要打印的通知单数据
const currentPayInfo = ref<ApplyPayInfoDetail>();
// 合计保费和通知单客户名称
const tableForm = reactive<FormDataType>({
  customerName: '',
  totalAmount: 0,
});
// 签报
const showApproval = ref<boolean>(false);
// 开单弹窗
const noticeVisible = ref<boolean>(false);
// 表格选中
const selectData = ref<ApplyPayInfo[]>([]);
const loading = ref<boolean>(false);
// 是否展示调整金额弹窗
const openAdjustModal = ref<boolean>(false);
// 是否使用新缴费方式
const useNewPay = ref<boolean>(false);
// 机构 change
const changeDeptCode = (val: string) => {
  formState.departmentCode = val;
  formRef?.value?.validate(['departmentCode']);
};
const resetForm = () => {
  initQueryData();
};
const resetSelected = () => {
  selectedRowKeys.value = [];
  selectData.value = [];
};
// 开单
const createDisabledFn = (record: ApplyPayInfo): boolean => {
  return !['00', '82', '74'].includes(record.noticeStatus);
};
// 缴费
const payDisabledFn = (record: ApplyPayInfo): boolean => {
  return !['51'].includes(record.noticeStatus) || (record.termAmount.includes('-') && record.noticeStatus === '51');
};
// 撤单
const repDisabledFn = (record: ApplyPayInfo): boolean => {
  return !['51', '52', '75', '74'].includes(record.noticeStatus);
};

// 单号关联机构编码
const depCodeChange = (code: string) => {
  if (code) {
    changeDeptCode(code);
  }
};
// 选中单个，支持跨页选中
const onSelectChange = (record: ApplyPayInfo, selected: boolean) => {
  // 选中单个
  if (selected) {
    selectedRowKeys.value.push(record?.idApplyPayInfo as string);
    selectData.value.push(record);
  } else {
    const filterRowKeys = selectedRowKeys.value.filter((item) => item !== record.idApplyPayInfo);
    selectedRowKeys.value = filterRowKeys;
    const filterSelectData = selectData.value.filter((item) => item.idApplyPayInfo !== record.idApplyPayInfo);
    selectData.value = filterSelectData;
  }
  calculateTotalTermAmount(selectData.value);
};
// 单页全选，支持跨页选中
const onSelectAllChange = (selected: boolean, selectedRows: ApplyPayInfo[], changeRows: ApplyPayInfo[]) => {
  // 选中全选
  if (selected) {
    changeRows.forEach((item) => {
      selectedRowKeys.value.push(item?.idApplyPayInfo as string);
      selectData.value.push(item);
    });
  } else {
    // 取消全选
    const selectedRowIds = changeRows.map((item) => item.idApplyPayInfo);
    // 从 selectedRowKeys 中过滤掉 selectedRowIds 中包含的 id
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => !selectedRowIds.includes(item as string));
    // 从 selectData 中过滤掉 selectedRows 中包含的记录
    selectData.value = selectData.value.filter((item) => !selectedRowIds.includes(item.idApplyPayInfo));
  }
  calculateTotalTermAmount(selectData.value);
};

// 选择的时候回显合计保费和通知单客户名称
const calculateTotalTermAmount = (selectData: ApplyPayInfo[]) => {
  if (selectData?.length < 1) {
    tableForm.totalAmount = 0;
    tableForm.customerName = '';
    return;
  }
  const total = selectData.reduce((total, item) => total + (parseFloat(item.termAmount) || 0), 0);
  tableForm.totalAmount = total.toFixed(2);
  const firstCustomName = selectData[0].customName;
  const allSame = selectData.every((item) => item.customName === firstCustomName);
  tableForm.customerName = allSame ? firstCustomName : '';
};

// 清除选中
const clearSelect = () => {
  selectedRowKeys.value = [];
  selectData.value = [];
  tableForm.totalAmount = 0;
  tableForm.customerName = '';
};

// 初始化查询数据
const initQueryData = async () => {
  formState.documentType = '2'; // 开单类型
  formState.lastRiskType = ''; // 标的类型
  formState.payerType = ['1', '2', '3', '4', '5', '6'];
  formState.noticeStatus = ['00', '54', '51', '62', '71', '72', '73', '74', '82'];
  formState.dateValue = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().add(1, 'month').format('YYYY-MM-DD')]; // 保险起期
  formState.departmentCode = userInfo?.deptList[0] || '';
  formState.containChildDepart = true;
  formState.documentNoType = (route.query.applyPolicyNo as string) ? '1' : '';
  formState.documentNo = (route.query.applyPolicyNo as string) || '';
  formState.personName = '';
  disabledInsured.value = false;
};

onMounted(async () => {
  initQueryData();
  getParamValueSwitch();
  if (route.query.applyPolicyNo) {
    await noticeNoBlur();
    await handleQuery();
  }
});
// 批量开单
const open = ref<boolean>(false);
// 退费弹窗数据集合
const premiumObj = ref<PremiumObjType>({
  actualPremium: '',
  excludeAddedTaxPremium: '',
  paymentPersonName: '',
  refundDocumentInfoList: [],
  name: [],
  refundCheckFlag: 'N',
  clientName: '',
  adjustedFlag: '',
  adjustedAmount: '',
  excludeTaxAdjustedAmount: '',
});
// 单个开单
const createOrder = async (record: ApplyPayInfo) => {
  const { customName, premiumSourceCode, termNo, endorseNo, voucherNo } = record;
  loading.value = true;
  // 如果金额小于0，则显示退费弹窗
  if (parseFloat(record.termAmount) < 0) {
    premiumObj.value = {
      adjustedFlag: record.adjustedFlag || '',
      adjustedAmount: (record.adjustedAmount as string) || '',
      excludeTaxAdjustedAmount: record.excludeTaxAdjustedAmount || '',
      actualPremium: record.termAmount?.toString(),
      excludeAddedTaxPremium: record.excludeAddedTaxPremium?.toString() || '',
      paymentPersonName: record.customName || '',
      name: [record.customName],
      clientName: record.customName,
      refundCheckFlag: 'N',
      refundDocumentInfoList: [
        {
          ...record,
          documentNo: endorseNo || voucherNo, // 如果有保单号就取保单号，没有则取投保单号
          payerType: premiumSourceCode,
          actualPremium: record.termAmount?.toString(),
        },
      ],
    };
    noticeVisible.value = true;
  } else {
    const params = {
      clientName: tableForm.customerName || customName,
      documentInfoList: [
        {
          documentNo: voucherNo || endorseNo,
          payerType: premiumSourceCode,
          termNo,
        },
      ],
    };
    const res = await createNoticeAPI(params);
    if (res?.code === SUCCESS_CODE) {
      const { noticeNo, documentInfoList } = res.data;
      currentPayInfo.value = {
        noticeNo,
        clientName: documentInfoList?.[0]?.clientName,
        policyNo: documentInfoList?.[0]?.documentNo,
        collectPayType: '00', // 收付标志 目前只有00这一种 后续会增加
      };
      successVisible.value = true;
      getTableData();
      loading.value = false;
    } else {
      message.warning(res?.msg);
      clearSelect();
      loading.value = false;
    }
  }
};

// 开单成功，打印通知单
const handlePrintNotice = () => {
  if (useNewPay.value) {
    handleGetPaymentUrl(currentPayInfo.value);
    return;
  }
  getNoticePrintParamsAPI(currentPayInfo.value).then((res) => {
    if (res?.code === SUCCESS_CODE && res?.data) {
      const { data } = res || {};
      noticeFormData.value = data;
      // 财务缴费台跳转
      nextTick(() => {
        const form = document.getElementById('noticeForm') as HTMLFormElement;
        form?.submit();
        getTableData();
      });
    }
  });
};

// 点击撤单
const repealNotice = (row: ApplyPayInfo | string) => {
  // 批量撤单
  if (row === 'batch') {
    // 批量撤单之前校验
    if (selectData?.value?.length < 1) {
      return message.error('请勾选至少一条记录');
    }
    for (let i = 0; i < selectData.value.length; i++) {
      if (selectData.value[i].noticeStatus !== '51') {
        return message.error(`${selectData.value[i].noticeStatusName}的通知单不能撤单`);
      }
    }
    batchCancelOrder();
  } else {
    // 撤单
    cancelOrder(row as ApplyPayInfo);
  }
};
// 批量撤单
const batchCancelOrder = async () => {
  const noticeDocumentList = selectData.value.map((item: ApplyPayInfo) => ({
    noticeNo: item.noticeNo,
    documentNo: item.voucherNo,
  }));
  const data = {
    noticeDocumentList, // 通知单号
  };
  try {
    const res = await batchRepealNoticeAPI(data);
    if (res?.code === SUCCESS_CODE) {
      message.success(res?.msg);
      getTableData();
      resetSelected();
    } else {
      message.error(res?.msg);
    }
  } catch (e) {
    console.log(e);
  }
};

// 撤单
const cancelOrder = async (row: ApplyPayInfo) => {
  const { noticeNo, voucherNo } = row || {};
  const data = {
    noticeNo,
    documentNo: voucherNo,
  };
  const res = await repealNoticeAPI(data);
  if (res?.code === SUCCESS_CODE) {
    getTableData();
    message.success(res.msg);
  } else {
    message.warning(res?.msg);
  }
};

// 缴费
const handlePayment = async (record?: ApplyPayInfo) => {
  if (!record) {
    if (selectData?.value?.length < 1) {
      message.error('请勾选至少一条记录');
      return;
    }
    // 校验通知单状态 只有已开单状态才能打印通知单
    const invalidStatus = selectData.value.find((item) => item.noticeStatus !== '51');
    if (invalidStatus) {
      message.error(`${invalidStatus.noticeStatusName}状态的通知单不能打印`);
      return;
    }
    const firstNoticeNo = selectData.value?.[0].noticeNo;
    const allSame = selectData.value.every((item) => item?.noticeNo === firstNoticeNo);
    if (firstNoticeNo && !allSame) {
      message.error('只能打印同一个通知单号');
      return;
    }
  }
  const { noticeNo, customName, applyPolicyNo } = record ? record : selectData.value[0];
  const data = {
    noticeNo, // 通知单号
    clientName: customName, // 缴费人
    policyNo: applyPolicyNo, // 单证号
    collectPayType: '00', // 收付款类型
  };
  if (useNewPay.value) {
    handleGetPaymentUrl(data);
    return;
  }
  const res = await getNoticePrintParamsAPI(data);
  if (res?.code === SUCCESS_CODE && res?.data) {
    const { data } = res;
    noticeFormData.value = data || {};
    // 财务缴费台跳转
    nextTick(() => {
      const form = document.getElementById('noticeForm') as HTMLFormElement;
      form?.submit();
    });
  }
  getTableData();
};

// 列表查询
const getTableData = async () => {
  await formRef?.value?.validate();
  const { dateValue, documentType, departmentCode, containChildDepart, documentNo, documentNoType, personName, lastRiskType, payerType, noticeStatus } = formState;
  const { customerName } = tableForm;
  const params = {
    isPolicyBeforePayfee: documentType,
    insureDepartmentNo: departmentCode,
    containLower: containChildDepart,
    insuranceBeginDate: dateValue?.[0],
    insuranceEndDate: dateValue?.[1],
    voucherTypeCode: documentNoType,
    voucherNos: documentNo ? documentNo.split(',').filter((i) => !!i) : [],
    insurantName: personName,
    riskType: lastRiskType,
    premiumSourceCodes: payerType,
    noticeStatus,
    customName: customerName,
    // 分页
    pageSize: pagination.pageSize,
    pageNum: pagination.current,
  };
  loading.value = true;
  try {
    const res = await queryApplyPayInfoListAPI(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records, total = 0, current = 1, size = 10 } = res.data;
      tableData.value = records;
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      tableData.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
      message.error(res?.msg || '请求失败');
    }
  } catch (e) {
    console.log(e);
  }
  loading.value = false;
};
// 切换页码的时候需要清空客户名称
const pageChange = () => {
  tableForm.customerName = '';
  getTableData();
};
// 列表分页
const { pagination } = usePagination(pageChange);

// 点击查询
const handleQuery = async () => {
  formRef.value.validate().then(() => {
    pagination.current = 1;
    pagination.pageSize = 10;
    clearSelect();
    getTableData();
  });
};

// 单号失去焦点
const noticeNoBlur = async () => {
  await formRef.value.validate(['documentNo']);
  setDisabledDate();
  tableForm.customerName = '';
};

// 只根据机构和单号查询，其他查询条件置灰且不参与查询
const setDisabledDate = () => {
  const { documentNo } = formState;
  if (documentNo) {
    disabledInsured.value = true;
    formState.personName = '';
    // 修改这里，确保类型匹配
    formState.dateValue = ['', '']; // 或者使用其他合适的值
  } else {
    disabledInsured.value = false;
    formState.dateValue = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  }
};

// 置灰条件
const disabledInsured = ref(false);
// 合并开单
const mergeNotice = async () => {
  // 如果支付状态是失败，则不允许开单
  const _index = (selectData.value || [])?.findIndex((item) => item.noticeStatus === '74');
  if (_index > -1) {
    message.warning('开单数据存在支付失败订单，不能开单');
    return;
  }
  const total = (selectData.value || [])?.reduce((total, item) => total + (parseFloat(item.termAmount) || 0), 0);
  const premium = (selectData.value || [])?.reduce((total, item) => total + (parseFloat(item.excludeAddedTaxPremium) || 0), 0);
  const nameList: string[] = [];
  (selectData.value || [])?.forEach((item: { customName: string }) => {
    nameList.push(item.customName);
  });
  if (selectData.value.length === 0) {
    message.warning('请选择需要开单的数据');
  } else if (selectData.value.length === 1) {
    message.error('一个单无法合并开单');
  } else {
    await formDataRef.value?.validate();
    if (total < 0) {
      // 如果金额为负数，则打开退费弹窗
      // 如果金额小于0，则显示退费弹窗
      premiumObj.value = {
        actualPremium: total?.toFixed(2)?.toString(),
        excludeAddedTaxPremium: premium?.toFixed(2)?.toString() || '',
        paymentPersonName: tableForm.customerName,
        clientName: tableForm.customerName,
        name: nameList,
        refundCheckFlag: 'N',
        refundDocumentInfoList: selectData.value.map((item: ApplyPayInfo) => ({ ...item, documentNo: item?.endorseNo || item.voucherNo, payerType: item.premiumSourceCode, termNo: item.termNo, actualPremium: item.termAmount })),
      };
      noticeVisible.value = true;
    } else {
      const data = {
        clientName: tableForm.customerName,
        documentInfoList: selectData.value.map((item: ApplyPayInfo) => ({ documentNo: item?.voucherNo, payerType: item.premiumSourceCode, termNo: item.termNo })),
      };
      const res = await createNoticeAPI(data);
      if (res?.code === SUCCESS_CODE) {
        const { noticeNo, documentInfoList } = res.data;
        currentPayInfo.value = {
          noticeNo,
          clientName: documentInfoList?.[0]?.clientName,
          policyNo: documentInfoList?.[0]?.documentNo,
          collectPayType: '00', // 收付标志 目前只有00这一种 后续会增加
        };
        successVisible.value = true;
        getTableData();
        resetSelected();
      } else {
        message.error(res?.msg);
      }
    }
  }
};
type ResultCodeType = {
  resultCode: string;
};
const confirmLoading = ref<boolean>(false);
const eoaTitle = ref<string>(''); // 签报主题
const eoaBodyStr = ref<string>(''); // 签报内容
const changeObjStr = (obj: Record<string, string>) => {
  const arr = Object.entries(obj).map(([key, value]) => `${key}${value}`);
  let str = '';
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    str += item + '&nbsp;&nbsp;&nbsp;&nbsp;';
    if (i % 3 === 2) {
      str += '<br/>';
    }
  }
  return str;
};
// 新退款信息数据组装
const newPremiumStr = computed(() => {
  const { paymentPersonName, paymentPathName, bankAccountAttributeName, certificateNo, bankName, bankAccountNo, actualPremium, bankHeadquartersName } = premiumObj.value;
  const obj: Record<string, string> = {
    '收款人名称：': paymentPersonName || '',
    '支付方式：': paymentPathName || '',
    '账号类型：': bankAccountAttributeName || '',
    '身份证：': certificateNo || '',
    '保费变化：': actualPremium || '',
    '开户银行：': bankHeadquartersName || '',
    '开户银行名称：': bankName || '',
    '银行账号：': bankAccountNo || '',
  };
  return `新退款信息：<br/>${changeObjStr(obj)}`;
});
// 旧退款信息数据组装
const oldPremiumStr = computed(() => {
  const { clientName, refundDocumentInfoList, excludeAddedTaxPremium } = premiumObj.value;
  const obj: Record<string, string> = {
    '收款人名称：': clientName || '-',
    '支付方式：': refundDocumentInfoList[0]?.paymentPathName || '',
    '账号类型：': '-',
    '身份证：': '-',
    '保费变化：': excludeAddedTaxPremium || '',
    '开户银行：': '-',
    '开户银行名称：': '-',
    '银行账号：': '-',
  };
  return `原退款信息：<br/>${changeObjStr(obj)}`;
});
// 退费
const handleCreateRefundNotice = async () => {
  try {
    // actualPremium为通知单金额，但后端需要这个字段传税后金额，所以这里转换
    const res = await $postOnClient(`${gateWay}${service.accept}/notice/one/createRefundNotice`, { ...premiumObj.value, actualPremium: premiumObj.value.excludeAddedTaxPremium });
    if (res?.code === SUCCESS_CODE) {
      // 如果客户名称不相同，则跳转签报
      if (premiumObj.value.refundCheckFlag === 'Y' && (res?.data as ResultCodeType)?.resultCode === 'Y') {
        eoaTitle.value = `关于【${premiumObj.value.refundDocumentInfoList[0].documentNo}】等退费信息的更改申请`;
        premiumObj.value.paymentPathName = premiumObj.value.paymentPath === '01' ? '公司柜面' : '银行转帐';
        premiumObj.value.bankAccountAttributeName = premiumObj.value.bankAccountAttribute === '1' ? '个人账号' : '公司账号';
        showApproval.value = true;
        eoaBodyStr.value = `涉及的保单/批单：<br/>【${premiumObj.value.refundDocumentInfoList[0]?.documentNo}】<br/>${oldPremiumStr.value}<br/>${newPremiumStr.value}`;
        noticeVisible.value = false;
      } else if ((res?.data as ResultCodeType)?.resultCode === 'N') {
        message.warning((res?.data as { msg: string })?.msg || res?.msg || ''); // 该银行不支持实时支付，提醒用户
      } else {
        message.success('退费申请成功');
        getTableData();
        noticeVisible.value = false;
      }
    } else {
      message.error(res?.msg);
      noticeVisible.value = false;
    }
    confirmLoading.value = false;
    loading.value = false;
  } catch (error) {
    console.log(error);
    confirmLoading.value = false;
    noticeVisible.value = false;
  }
};

// 是否禁用调整开单金额按钮
const showAdjustBtn = computed(() => {
  // 判断是不是我司主承且都是待开单 1
  let isDisabled = false;
  if (selectData.value.length > 0) {
    isDisabled = selectData.value.every((item: ApplyPayInfo) => item?.isCoinsuranceFullPay === '1' && item.noticeStatus === '00');
  }
  return isDisabled;
});
const adjustAmount = () => {
  openAdjustModal.value = true;
};

// 调整开单金额
const adjustSubmit = async (type: string) => {
  const params = selectData.value.map((item) => {
    return {
      documentNo: item.voucherNo,
      adjustedFlag: type,
      termNo: item.termNo,
    };
  });
  const res = await updateIsCoinsuranceFullpay(params);
  if (res?.code === SUCCESS_CODE) {
    message.success(res?.msg || '');
    openAdjustModal.value = false;
    getTableData();
  } else {
    message.error(res?.msg || '');
  }
};
// 校验单号的规则
const docNoRule = async (_rule: Rule, value: string): Promise<void> => {
  // 如果传入的值为空，则直接返回
  if (!value) return;
  // 定义单号的前缀对应关系
  const prefixes: { [key: string]: string } = { 5: '1', 3: '5', 7: '4', 1: '3' };
  // 获取单号类型，根据首字符来确定
  let noType: string = prefixes[value[0]] || '2';
  // 特殊情况下调整单号类型 7到8位为58的是通知单号
  if (['58'].includes(value.substring(6, 8))) {
    noType = '2';
  }
  // 将确定的单号类型保存到表单状态中
  formState.documentNoType = noType;
  // 拆分输入的单号，并验证每个单号的格式
  const invalidItems: string[] = value
    .replace(/，/g, ',')
    .split(',')
    .map((item) => item.trim()) // 去除每个单号的首尾空白
    .filter((item) => {
      // 用正则表达式检查单号是否为 20 位数字
      const req = /^\d{20}$/;
      return (
        !req.test(item) || // 格式不符合
        (noType === '2' && !['58'].includes(item.substring(6, 8)))
      ); // 当类型为 2 时，第 7 和第 8 位必须为58
    });
  // 如果存在不符合规范的单号，返回拒绝的 Promise，并提示格式错误
  if (invalidItems.length) {
    return Promise.reject(invalidItems.join(', ') + ' 格式不符合规范');
  }
  // 如果没有错误，返回成功的 Promise
  return Promise.resolve();
};
// 反洗钱按钮可点击条件：选中数据为1条，并且保费来源是农户，状态为非已支付，金额为负数
const antiMoneyModifyDisabled = computed(() => {
  if (selectData.value.length === 1 && selectData.value[0].noticeStatus !== '73' && selectData.value[0].premiumSourceCode === '5' && selectData.value[0].termAmount < 0) {
    return false;
  }
  return true;
});
// 反洗钱信息修改
const modifyAntiMoneyInfo = () => {
  antiMoneyModifyVisible.value = true;
};
// 签报
const handleSubmit = async (eoaData: EoaData) => {
  try {
    const res = await $postOnClient(`${gateWay}${service.accept}/accept/eoa/createEoa`, {
      relationBusinessNo: premiumObj.value.refundDocumentInfoList[0].documentNo,
      eoaType: '007',
      eoaSubject: eoaTitle.value,
      eoaBody: eoaBodyStr.value,
      documentGroupId: eoaData.documentGroupId,
      departmentCode: formState.departmentCode,
      approveChainList: eoaData.approveChainList,
      businessData: premiumObj.value,
      attachmentList: eoaData.fileInfos,
    });
    if (res?.code === SUCCESS_CODE) {
      showApproval.value = false;
      message.success('发起签报成功');
      getTableData();
    } else {
      message.error(res?.msg || '发起签报失败');
    }
  } catch (error) {
    console.log(error);
  }
};

// 跳转爱农宝支付码
const toANBPayCode = async () => {
  if (!selectData.value.length) return message.error('请勾选至少一条记录');
  if (selectData.value.length > 1) return message.error('只能选中同一个通知单号进行支付');
  if (!selectData.value[0].noticeNo) return message.error('请开单后再进行支付');
  try {
    let res = await getIcpPayUrl({ noticeNo: selectData.value[0].noticeNo });
    res = typeof res === 'string' ? JSON.parse(res) : res;
    if (res?.code === SUCCESS_CODE && typeof res?.data === 'string') {
      window.open(res.data);
    } else {
      message.error(res?.msg);
    }
  } catch (err) {
    console.log(err);
  }
};

// 查询缴费方式开关
const getParamValueSwitch = async () => {
  try {
    const res = await getParamValue();
    if (res?.code === SUCCESS_CODE) {
      useNewPay.value = [null, 'Y', '1'].includes(res?.data);
    } else {
      message.error(res?.msg);
    }
  } catch (err) {
    console.log(err);
  }
};

// 新方式缴费
const handleGetPaymentUrl = async (data: ApplyPayInfoDetail | undefined) => {
  try {
    const res = await getPaymentUrl(data);
    if (res?.code === SUCCESS_CODE) {
      if (res?.data?.cpc?.pay_url) {
        window.open(res.data.cpc.pay_url);
      } else {
        message.error(res?.data?.cpc?.result_msg);
      }
    } else {
      message.error(res?.msg);
    }
  } catch (err) {
    console.log(err);
  }
};

const gatewayAcceptURL = gateWay + service.accept;
const gatewayAdministrateURL = gateWay + service.administrate;
// 查询
const { fetchData: queryApplyPayInfoListAPI } = await usePost<QueryApplyPayInfoListRes>(`${gatewayAcceptURL}/applyPayInfo/queryApplyPayInfoList`);
// 开单
const { fetchData: createNoticeAPI } = await usePost<NoticeInfo>(`${gatewayAcceptURL}/notice/one/createNotice`);
// 撤单
const { fetchData: repealNoticeAPI } = await usePost(`${gatewayAcceptURL}/notice/one/repealNotice`);
// 财务缴费台跳转
const { fetchData: getNoticePrintParamsAPI } = await usePost<NoticeFormDataType>(`${gatewayAcceptURL}/notice/getNoticePrintParams`);
// 批量开单
// const { fetchData: batchCreateNoticeAPI } = await usePost(`${gatewayAcceptURL}/notice/batch/createNotice`);
// 批量撤单
const { fetchData: batchRepealNoticeAPI } = await usePost(`${gatewayAcceptURL}/notice/batch/repealNotice`);
// 调整开单金额
const { fetchData: updateIsCoinsuranceFullpay } = await usePost(`${gatewayAcceptURL}/notice/updateIsCoinsuranceFullpay`);
// 获取爱农宝支付码地址
const { fetchData: getIcpPayUrl } = await useGet(`${gatewayAcceptURL}/notice/getIcpPayUrl`);
// 查询缴费方式开关
const { fetchData: getParamValue } = await usePost<string>(`${gatewayAdministrateURL}/public/getParamValue?paramKey=USE_NEW_PAYMENT_API`);
// 获取缴费地址
const { fetchData: getPaymentUrl } = await usePost(`${gatewayAcceptURL}/notice/getPaymentUrl`);
</script>

<style lang="less" scoped>
.footer-wrapper {
  z-index: 100;
  height: 50px;
  background: #fff;
  position: sticky;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
}
</style>
