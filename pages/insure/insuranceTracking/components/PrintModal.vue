<template>
  <a-modal v-model:open="visible" :after-close="afterClose" title="打印信息" :width="pxToRem(600)" ok-text="打印" :mask-closable="false" centered @ok="handleOk">
    <div class="mb-[18px]">
      <div class="bg-gray-100 rounded p-14px box-border mb-8px">
        <a-form ref="formRef" :model="formState" autocomplete="off" :colon="false" required label-align="left" :label-col="{ style: { width: pxToRem(90) } }">
          <a-form-item label="单证大类">
            <a-radio-group v-model:value="modeLarge" :style="{ marginBottom: '8px' }" @change="handleChangeGroup">
              <a-radio-button v-for="(item, index) in panes" :key="index" :value="item.key" :disabled="selectObj.applyStatus !== undefined && selectObj.applyStatus !== 'B5' && ['2', '3'].includes(item.key)">
                {{ item.title }}
              </a-radio-button>
            </a-radio-group>
          </a-form-item>
          <!-- 单证大类等于保单-1 -->
          <div v-if="modeLarge === '1'">
            <a-form-item label="单证小类">
              <a-radio-group v-model:value="modeSmall" :style="{ marginBottom: '8px' }" @change="getOptions">
                <a-radio-button v-for="(item, index) in panesSmall" :key="index" :value="item.key" :disabled="selectObj.applyStatus !== undefined && selectObj.applyStatus !== 'B5' && ['1', '2', '3'].includes(item.key)">
                  {{ item.title }}
                </a-radio-button>
              </a-radio-group>
            </a-form-item>
            <!-- 单证小类等于格式保单-1 -->
            <div v-show="modeSmall === '1'">
              <a-form-item label="单证模版" name="templateValue" :rules="[{ required: true, trigger: 'change', message: '请选择单证模版' }]">
                <a-select ref="select" v-model:value="formState.templateValue" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'templateName' }" @change="handleChange" />
              </a-form-item>
              <a-form-item label="印刷号" :required="modeSmall === '1'" name="printNo">
                <a-input v-model:value="formState.printNo" @change="handleChange" />
              </a-form-item>
            </div>
            <!-- 单证小类等于文本保单-2 -->
            <div v-show="modeSmall === '2'">
              <a-form-item label="保单附件">
                <a-row :gutter="16">
                  <a-col :span="16">
                    <a-input v-model:value="formState.documentName" :disabled="true" @change="handleChange" />
                  </a-col>
                  <a-col :span="4">
                    <a-button type="primary" @click="downloadPolicyFile">下载</a-button>
                  </a-col>
                </a-row>
              </a-form-item>
              <p v-if="resultMessage" class="text-[red]">{{ resultMessage }}</p>
            </div>
            <!-- 单证小类等于保单抄件-3 -->
            <div v-show="modeSmall === '3'">
              <a-form-item label="单证模版" name="templateValue" :rules="[{ required: true, trigger: 'change', message: '请选择单证模版' }]">
                <a-select ref="select" v-model:value="formState.templateValue" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'templateName' }" @change="handleChange" />
              </a-form-item>
              <a-form-item label="电子保单章">
                <a-switch v-model:checked="checked" checked-value="1" un-checked-value="0" />
              </a-form-item>
            </div>
          </div>
          <!-- 单证大类等于保险凭证-2 -->
          <div v-if="modeLarge === '2'">
            <a-form-item label="单证模版" :rules="[{ required: true, trigger: 'change', message: '请选择单证模版' }]">
              <a-select ref="select" v-model:value="formState.templateValue" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'templateName' }" @change="handleChange" />
            </a-form-item>
            <a-form-item v-if="isNp" label="印刷号清单" name="printNoFile" :rules="[{ required: true, message: '不能为空' }]">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-input v-model:value="formState.printNoFile" :disabled="true" @change="handleChange" />
                </a-col>
                <a-col :span="3">
                  <a-upload v-model:file-list="fileList" accept=".csv" :show-upload-list="false" :before-upload="() => false" @change="handleUploadChange">
                    <a-button type="primary">上传</a-button>
                  </a-upload>
                </a-col>
                <a-col :span="2">
                  <a-button type="link" @click="exportNpTemplate">下载清单模版</a-button>
                </a-col>
              </a-row>
            </a-form-item>
            <p v-if="isNp" class="text-[red] m-0">销号状态：{{ cancelStatus[cancelFlag] }}</p>
            <p v-if="isNp && cancelFlag === 'N'" class="text-[red] m-0">{{ cancelMsg }}</p>
            <p v-if="isNp" class="text-[red] m-0">本次需要销号 {{ needCancelCount }} 个</p>
          </div>
          <!-- 单证大类等于发票-3 -->
          <div v-show="modeLarge === '3'" />
          <!-- 单证大类等于批单-4 -->
          <div v-if="modeLarge === '4'">
            <a-form-item label="单证小类">
              <a-radio-group v-model:value="modeSmall" :style="{ marginBottom: '8px' }" @change="getOptions">
                <a-radio-button value="1">批单</a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="单证模版" name="templateValue" :rules="[{ required: true, trigger: 'change', message: '请选择单证模版' }]">
              <a-select ref="select" v-model:value="formState.templateValue" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'templateName' }" />
            </a-form-item>
            <a-form-item label="印刷号" :required="true" name="printNo">
              <a-input v-model:value="formState.printNo" />
            </a-form-item>
            <a-form-item label="给予对象" name="printItem" :rules="[{ required: true, message: '请选择给予对象' }]">
              <a-select ref="select" v-model:value="formState.printItem" :options="printItemOptions" />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel">{{ cancelText }}</a-button>
      <a-button type="primary" @click="handleOk">打印</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import type { insuranceTrackingType } from '../insuranceTracking.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { $getOnClient, $postOnClient, useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};
const visible = defineModel<boolean>('visible', { required: true, default: false });
const formState = ref({
  templateValue: '', // 单证模版
  printNo: '', // 印刷号
  documentName: '', // 保单附件名称
  printNoFile: '', // 保险凭证：印刷号清单
  printItem: '01', // 给予对象
});
const formRef = ref();
const props = withDefaults(
  defineProps<{
    selectObj: insuranceTrackingType;
    type: string; // 投保跟踪传'01', 最新保单、批改跟踪传'02'
    isEndorse?: boolean; // 大类是否显示批改
  }>(),
  {},
);
// 单证大类 key
const modeLarge = ref<string>('1');
// 单证小类 key
const modeSmall = ref<string>('1');
// 电子保单章
const checked = ref<string>('0');
// 单证大类 tab
const panes = ref<{ title: string; key: string }[]>([
  { title: props.type === '01' ? '保单' : '最新保单', key: '1' },
  { title: '保险凭证', key: '2' },
  { title: '发票', key: '3' },
]);
// 单证小类 tab
const panesSmall = ref<{ title: string; key: string; templateType?: string }[]>([
  { title: '格式保单', key: '1' },
  { title: '文本保单', key: '2' },
  { title: '保单抄件', key: '3' },
  { title: '农户清单', key: '4' },
]);
const printItemOptions = [
  { label: '业务联', value: '01' },
  { label: '客户联', value: '02' },
  { label: '银行联', value: '03' },
];
// 单证模版选项
const templateOptions = ref<Record<string, string>[]>([]);
const cancelText = ref<string>('取消');
const handleChange = () => {};
type DataUrl = {
  url: string;
};
// 打印发票跳转页面-目前只支持大类中的发票功能
const handleOk = async () => {
  if (modeLarge.value === '3') {
    const fetchUrl = gateWay + service.accept + '/invoice/sso/url';
    const { currencyCode, insureDepartmentNo, voucherNo, departmentCode, policyNo } = props.selectObj as insuranceTrackingType;
    const params = {
      issueDeptModel: insureDepartmentNo || departmentCode,
      currencyCode,
      policyNum: voucherNo || policyNo,
    };
    $postOnClient(fetchUrl, params).then((res) => {
      const { url = '' } = (res?.data as DataUrl) || {};
      if (url) {
        visible.value = false;
        window.open(url);
      }
    });
  } else if (modeLarge.value === '1' && modeSmall.value === '4') {
    // 农户清单下载
    if (props.type === '01') {
      // 投保
      const fetchUrl = gateWay + service.accept + '/applyFarmer/exportFarmerSignFile';
      const params = {
        applyPolicyNo: props.selectObj.applyPolicyNo,
      };
      const res = await $getOnClient<string>(fetchUrl, params);
      if (res && res?.code === SUCCESS_CODE && res?.data) {
        window.open(res?.data);
        visible.value = false;
        // const { uploadPath, storageType, bucketName } = res.data;
        // const iobsRes = await $postOnClient<{ fileUrl: string }>('/api/iobs/getInIobsUrl', { fileKey: uploadPath, storageTypeCode: storageType, iobsBucketName: bucketName });
        // if (iobsRes?.code === SUCCESS_CODE) {
        //   window.open(iobsRes?.data?.fileUrl);
        //   visible.value = false;
        // }
      } else {
        message.warning(res?.msg);
      }
    } else if (props.type === '02') {
      // 02 最新保单和批改跟踪
      downloadFarmerList(props.selectObj.policyNo || '');
    }
  } else if (modeLarge.value === '1' && modeSmall.value === '1') {
    // 格式保单下载
    downloadPolicy('1');
  } else if (modeLarge.value === '1' && modeSmall.value === '3') {
    // 保单抄件下载
    downloadPolicy('3');
  } else if (modeLarge.value === '1' && modeSmall.value === '2') {
    // 文本保单打印
    if (resultMessage.value) {
      message.warning(resultMessage.value);
    } else {
      textPolicyPrint();
    }
  } else if (modeLarge.value === '2') {
    // 保险凭证
    checkCancelStatus(); // 查询销号状态
  } else if (modeLarge.value === '4') {
    // 批改
    printEdr();
  } else {
    message.warning('功能开发中...');
  }
};
// 最新保单、批改跟踪 的 农户清单下载
const downloadFarmerList = async (policyNo: string) => {
  const fetchUrl = gateWay + service.document + '/print/getApplyFarmerUrl';
  const params = {
    taskType: '02', // 02是分户清单
    businessNo: policyNo,
    systemType: props.type, // 01 承保 , 02 批改
  };
  const res = await $postOnClient(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res.data) {
    urlDate.value = res.data as string;
    // 后端直接返回url了
    window.open(urlDate.value);
    // downloadFile();
  } else {
    message.warning('获取农户清单数据失败');
  }
};
// 文本保单打印
const textPolicyPrint = async () => {
  const fetchUrl = gateWay + service.document + '/print/textPolicyOASSeal';
  const params = {
    policyNo: props.selectObj.policyNo,
    systemType: props.type, // 01 投保 , 02 批改和最新保单
  };
  const res = await $getOnClient(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE) {
    message.success('已成功，请到印章系统完成后续操作');
    visible.value = false;
  } else {
    message.warning(res?.msg);
  }
};
// 格式保单和保单抄单下载
const downloadPolicy = async (type: string) => {
  try {
    await formRef.value.validate();
    const fetchUrl = gateWay + service.document + '/print/policy';
    const params = {
      businessNo: props.selectObj.policyNo,
      printRequestForm: {
        isSigned: type === '1' ? '0' : checked.value, // 是否加签名印章（0:否 1:是） 对应电子保单章
        electron: false, // 是否电子保单
        isForCopy: type === '1' ? '0' : '1', // 抄件打印（0:否 1:是）
        printType: formState.value.templateValue,
        systemType: props.type, // 01 承保 , 02 批改
        eleApply: true, // 电子投保单
        printNo: formState.value.printNo, // 印刷号
        documentNo: props.selectObj.policyNo, // 单号
        endorseNo: props.selectObj.policyNo, // 批单号
      },
    };
    const res = await $postOnClient<{ fileUrl: string }>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      window.open(res?.data?.fileUrl);
      visible.value = false;
    } else {
      message.warning(res?.msg);
    }
  } catch (e) {
    console.log(e);
  }
};
// 选择单证大类
const handleChangeGroup = async () => {
  formRef.value?.resetFields();
  if (modeLarge.value === '2') {
    // 保险凭证
    getTemplate('10');
    cancelText.value = isNp.value ? '销号' : '取消';
  } else if (modeLarge.value === '1') {
    // 保单
    getTemplate('01');
    cancelText.value = '取消';
  } else if (modeLarge.value === '4') {
    // 批改
    getTemplate('08');
    cancelText.value = '取消';
  } else {
    cancelText.value = '取消';
  }
};
// 点击取消或者消户
const handleCancel = () => {
  if (modeLarge.value === '2' && isNp.value) {
    // 保险凭证 非保卡需要销号
    certificateCancel();
  } else {
    visible.value = false;
  }
};
// 销号
const certificateCancel = async () => {
  await formRef.value.validate();
  const file = fileInfo.value;
  const formData = new FormData();
  formData.append('file', file as unknown as Blob);
  formData.append('policyNo', props.selectObj.policyNo || '');
  formData.append('systemType', props.type); // 01 承保 , 02 批改
  const uploadUrl = gateWay + service.document + '/print/certificateCancel';
  try {
    const res = await $postOnClient(uploadUrl, formData);
    const { code, msg } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      cancelFlag.value = 'H';
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 点击单证小类
const getOptions = async () => {
  formRef.value?.resetFields();
  // 格式保单和保单抄件获取单证模版
  if (modeSmall.value === '1' || modeSmall.value === '3') {
    getTemplate('01');
  }
  // 文本保单获取保单附件
  if (modeSmall.value === '2') {
    getPolicyFile();
  }
  formRef.value?.clearValidate();
};
watch(
  () => props.selectObj.applyStatus,
  () => {
    if (props.selectObj.applyStatus && props.selectObj.applyStatus !== 'B5') {
      modeLarge.value = '1';
      modeSmall.value = '4';
    } else {
      modeLarge.value = '1';
      modeSmall.value = '1';
    }
  },
);
// 文本保单提示信息
const resultMessage = ref<string>('');
// 文本保单：获取保单附件
const getPolicyFile = async () => {
  resultMessage.value = '';
  const fetchUrl = gateWay + service.document + '/print/getDocumentUrlAESByPolicyNo';
  const params = {
    policyNo: props.selectObj.policyNo,
  };
  const res = await $getOnClient<{ resultCode: string; resultMessage: string; data: Record<string, string>[] }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    formState.value.documentName = res.data?.data?.[0]?.documentName || '';
    if (res.data?.resultCode === 'N') {
      resultMessage.value = res.data?.resultMessage;
    }
  } else {
    message.warning(res?.msg);
  }
};
// 文本保单附件下载
const downloadPolicyFile = async () => {
  const fetchUrl = gateWay + service.document + '/print/downloadTextPolicy';
  const params = {
    policyNo: props.selectObj.policyNo,
  };
  try {
    await $getOnClient(fetchUrl, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('导出成功');
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } catch (error) {
    console.log(error);
  }
};
// 保险凭证：销号提示
const cancelFlag = ref<string>('');
const isNp = ref<boolean>(false);
const needCancelCount = ref<number>();
const cancelMsg = ref<string>('');
// 销号状态 W-未销号, Y-已经销号, N-销号失败, H-销号中
const cancelStatus: Record<string, string> = {
  W: '未销号',
  Y: '已经销号',
  N: '销号失败',
  H: '销号中',
};
// 获取单证模版
const getOptionsReq = await useGet<{ printTemple: Record<string, string>[]; cancel: string; isNp: boolean; needCancelCount: number; cancelMsg: string; msg: string }>(`${gateWay}${service.document}/print/queryPrintTemplate`);
const getTemplate = async (type: string) => {
  if (modeLarge.value === '2') {
    getCancelStatus();
  }
  formState.value.templateValue = '';
  templateOptions.value = [];
  try {
    const params = {
      templetType: type,
      departmentCode: props.selectObj.insureDepartmentNo,
      policyNo: props.selectObj.policyNo,
    };
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      templateOptions.value = res.data?.printTemple || [];
      formState.value.templateValue = res.data?.printTemple?.[0]?.templateName || '';
      if (modeLarge.value === '2') {
        isNp.value = res.data?.isNp;
        cancelText.value = isNp.value ? '销号' : '取消';
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 保险凭证：获取单证模版时 查询销号状态
const getCancelStatus = async () => {
  const fetchUrl = gateWay + service.document + `/print/queryCancelStatus?policyNo=${props.selectObj.policyNo}`;
  const params = {
    policyNo: props.selectObj.policyNo,
  };
  const res = await $postOnClient<{ cancel: string; message: string; needCancelCount: number }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res.data) {
    cancelFlag.value = res.data?.cancel || '';
    cancelMsg.value = res.data.cancel === 'N' ? res.data.message : '';
    needCancelCount.value = res.data.needCancelCount;
  } else {
    message.warning(res?.msg);
  }
};
// 保险凭证：下载清单模版
const exportNpTemplate = async () => {
  const downloadUrl = gateWay + service.document + '/print/exportNpTemplate';
  try {
    await $postOnClient(
      downloadUrl,
      {},
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('下载成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
  } catch (error) {
    console.log(error);
  }
};
// 保险凭证：印刷号清单
const fileList = ref([]);
const fileInfo = ref({});
// 保险凭证：上传印刷号清单
const handleUploadChange = async (e: { file: File; fileList: UploadListType }) => {
  const { file } = e;
  fileInfo.value = file;
  formState.value.printNoFile = file.name;
};
// 保险凭证：查询销号状态
const checkCancelStatus = async () => {
  if (isNp.value) {
    // true需要查询销号状态，false不用查询销号状态销号
    const fetchUrl = gateWay + service.document + `/print/queryCancelStatus?policyNo=${props.selectObj.policyNo}`;
    const params = {
      policyNo: props.selectObj.policyNo,
    };
    const res = await $postOnClient<{ cancel: string; message: string }>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res.data) {
      cancelFlag.value = res.data.cancel; // 销号
      cancelMsg.value = res.data.cancel === 'N' ? res.data.message : '';
      if (cancelFlag.value !== 'Y') {
        if (cancelFlag.value === 'W') {
          message.warning('请先完成销号再打印');
        } else if (cancelFlag.value === 'H') {
          message.warning('正在销号中，请稍后再打印');
        } else if (cancelFlag.value === 'N') {
          message.warning(res.data.message);
        }
        return;
      }
      toPrintCertificate(props.selectObj.policyNo as string);
    } else {
      message.warning(res?.msg);
    }
  } else {
    toPrintCertificate(props.selectObj.policyNo as string);
  }
};
const urlDate = ref<string>('');
// 获取打印凭证url参数
const toPrintCertificate = async (policyNo: string) => {
  const fetchUrl = gateWay + service.document + '/print/getApplyFarmerUrl';
  const params = {
    taskType: '01', // 01是凭证
    businessNo: policyNo,
    systemType: props.type, // 01 承保 , 02 批改
  };
  const res = await $postOnClient(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res.data) {
    urlDate.value = res.data as string;
    // 后端直接返回url了
    window.open(urlDate.value);
    // downloadFile();
  } else {
    message.warning('获取凭证打印数据失败');
  }
};
// 打印凭证
// const downloadFile = () => {
//   const url = gateWay + service.document + `/print/downloadCertificatePrint?url=${urlDate.value}`;
//   window.open(url);
// };
// 批改：批单打印
const printEdr = async () => {
  try {
    await formRef.value.validate();
    const fetchUrl = gateWay + service.document + '/print/printEdr';
    const params = {
      businessNo: props.selectObj.endorseNo,
      printRequestForm: {
        printType: formState.value.templateValue,
        systemType: props.type, // 01 承保 , 02 批改
        printNo: formState.value.printNo, // 印刷号
        printItem: formState.value.printItem, // 给予对象
      },
    };
    const res = await $postOnClient<{ fileUrl: string }>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      window.open(res?.data?.fileUrl);
      visible.value = false;
    } else {
      message.warning(res?.msg);
    }
  } catch (e) {
    console.log(e);
  }
};
watch(visible, (val) => {
  // 批改跟踪页面大类增加批改
  const hasEndorse = panes.value.findIndex((item) => item.title === '批改');
  if (props.isEndorse && hasEndorse === -1) {
    panes.value.unshift({ title: '批改', key: '4' });
    modeLarge.value = '4';
  }
  const type = modeSmall.value === '1' || modeSmall.value === '3';
  // 单证大类为保单，单证小类为格式保单，调接口获取单证模版
  if (val && modeLarge.value === '1' && type) {
    getTemplate('01');
  }
  // 单证大类为保险凭证，调接口获取单证模版
  if (val && modeLarge.value === '2') {
    getTemplate('10');
  }
  // 单证大类为批改
  if (val && modeLarge.value === '4') {
    getTemplate('08');
  }
  // 单证大类为保单，单证小类为文本保单，调接口获取保单附件
  if (val && modeLarge.value === '1' && modeSmall.value === '2') {
    getPolicyFile();
  }
});
// 关闭弹窗清空数据和校验结果
const afterClose = () => {
  formRef.value.resetFields();
  formRef.value.clearValidate();
};
</script>
