<template>
  <ClientOnly>
    <div class="p-14px space-y-14px">
      <a-spin :spinning="btnLoading" wrapper-class-name="search-spin-wrapper-css">
        <div class="bg-white p-16px rounded-md">
          <div class="flex">
            <a-form :colon="false" class="flex-grow">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="机构" :label-col="{ style: { width: pxToRem(80) } }" name="departmentCode" v-bind="validateInfos.departmentCode">
                    <department-search v-model:contain-child-depart="formData.containChildDepart" v-model:loading="btnLoading" :dept-code="formData.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="创建日期">
                    <a-range-picker v-model:value="formData.createdDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="disabled" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="被保险人">
                    <a-input v-model:value.trim="formData.insurantNames" placeholder="请输入" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-form-item label="标的" :label-col="{ style: { width: pxToRem(80) } }">
                    <RiskCodeSelect v-model:value="formData.riskType" :department-code="formData.departmentCode" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="14">
                  <a-form-item label="单号" name="voucherNo" :label-col="{ style: { width: pxToRem(80) } }">
                    <a-input-group>
                      <a-row :gutter="8">
                        <a-col :span="5">
                          <a-select v-model:value="formData.voucherType" :options="voucherTypeList" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                        </a-col>
                        <a-col :span="19">
                          <a-input v-model:value.trim="formData.voucherNo" placeholder="请输入" allow-clear @blur="handleBlurDisabled" />
                        </a-col>
                      </a-row>
                    </a-input-group>
                  </a-form-item>
                </a-col>
                <a-col :span="10">
                  <a-form-item label="单证状态">
                    <a-select v-model:value="formData.applyStatus" :options="processStatusOption" mode="multiple" allow-clear placeholder="请选择" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-show="expand" :gutter="16">
                <a-col :span="8">
                  <a-form-item label="产品" :label-col="{ style: { width: pxToRem(80) } }">
                    <ProductSelect v-model:value="formData.marketProductCode" :department-code="formData.departmentCode" :encode-key="formData.riskType" module="accept" :disabled="disabled" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="补贴类型">
                    <a-select v-model:value="formData.govSubsidyType" :options="subsidyTypeOptions" placeholder="请选择" allow-clear :disabled="disabled" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="是否属于续保" :label-col="{ style: { width: pxToRem(90) } }">
                    <a-select v-model:value="formData.renewalType" allow-clear :disabled="disabled">
                      <a-select-option value="1">续保</a-select-option>
                      <a-select-option value="0">新保</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-show="expand" :gutter="16">
                <a-col :span="8">
                  <a-form-item label="保险起期" :label-col="{ style: { width: pxToRem(80) } }">
                    <a-range-picker v-model:value="formData.insuranceBeginDateRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-show="expand">
                <a-col :span="24">
                  <a-form-item label="标的地址" :label-col="{ style: { width: pxToRem(80) } }">
                    <region-select v-model:province="formData.riskProvince" v-model:city="formData.riskCity" v-model:county="formData.riskCounty" v-model:town="formData.riskTown" v-model:village="formData.riskVillage" class="w-[70%]" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
            <div class="w-[50px]">
              <form-fold v-model="expand" />
            </div>
          </div>
          <div class="flex justify-center items-center space-x-8px">
            <a-button @click="resetForm">重置</a-button>
            <a-button type="primary" ghost @click="submit">查询</a-button>
          </div>
        </div>
      </a-spin>
      <div class="bg-white p-16px rounded-md">
        <div class="mb-16px flex justify-between">
          <div class="flex items-center space-x-[12px]">
            <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">查询结果</span>
            <span v-if="selectedRow?.length > 0" class="text-[rgba(0,0,0,0.55)] text-[14px]">已选择{{ selectedRow?.length || 0 }}条</span>
          </div>
          <div>
            <a-button class="ml-[12px]" type="primary" @click="handlePrint">打印</a-button>
            <a-button v-if="showOneToOneBtn" class="ml-[12px]" type="primary" @click="handlePrintOne">一户一保打印</a-button>
            <a-button v-if="showOneToOneBtn" class="ml-[12px]" type="primary" @click="handlePrintFile">凭证文件下载</a-button>
            <a-button class="ml-[12px]" type="primary" @click="downloadTemplate">导出模版</a-button>
          </div>
        </div>
        <a-spin :spinning="tableLoading">
          <a-table :columns="columns" :data-source="dataSource" :pagination="pagination" :scroll="{ x: 'max-content' }" :loading="loading" row-key="idAplyProcessTraceSummaryInfo" :row-selection="rowSelection" :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)" class="table-box">
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex === 'voucherNo'">
                <div>
                  <span class="mr-[4px]">单证号</span>
                  <a-tooltip>
                    <template #title>
                      <div class="mb-[2px]">
                        <a-tag color="red">保</a-tag>
                        <span>保单号</span>
                      </div>
                      <div class="mb-[2px]">
                        <a-tag color="orange">投</a-tag>
                        <span>投保单号</span>
                      </div>
                      <div class="mb-[2px]">
                        <a-tag color="blue">自助</a-tag>
                        <span>自助投保单号</span>
                      </div>
                    </template>
                    <VueIcon :icon="IconInfoCircleFilledFont" />
                  </a-tooltip>
                </div>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <div>
                  <span class="mr-[4px]">操作</span>
                  <a-tooltip>
                    <template #title>
                      <span>修改投保申请信息会影响验标要求，请谨慎操作</span>
                    </template>
                    <VueIcon :icon="IconInfoCircleFilledFont" />
                  </a-tooltip>
                </div>
              </template>
            </template>
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.key === 'status' && text">
                <div v-if="text !== '4'">
                  <a-tag :bordered="false" :color="tagMap[text].color">
                    <template #icon>
                      <VueIcon :icon="tagMap[text].icon" />
                    </template>
                    {{ tagMap[text].text }}
                  </a-tag>
                </div>
                <div v-else></div>
              </template>
              <!-- 单证号 -->
              <template v-if="column.dataIndex === 'voucherNo'">
                <div class="flex items-center">
                  <a-tag v-if="record.documentTypeCode === '1'" color="orange">投</a-tag>
                  <a-tag v-else-if="record.documentTypeCode === '2'" color="red">保</a-tag>
                  <CopyLink :text="record.voucherNo" @click="openApplyDetail(record.applyPolicyNo)" />
                  <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
                  <a-tag v-if="record.doublePrecisionFlag" color="green">双</a-tag>
                </div>
              </template>
              <template v-if="column.dataIndex === 'applyApplyFormNo'">
                <CopyLink v-if="text" :text="text" @click="goToInsuranceApply(text)" />
              </template>
              <template v-if="column.dataIndex === 'applyNotificationStatusCode'">
                <a-popover placement="rightTop" overlay-class-name="no-arrow-popover process-popover">
                  <template #content>
                    <div class="text-xs flex flex-col space-y-[4px] mr-[-8px]">
                      <div class="text-[rgba(0,0,0,0.55)]">进度提示</div>
                      <a-tag :bordered="false" :color="record.applyFormCompleteStatusCode === '1' ? 'success' : 'default'">
                        <span class="ml-[4px]">投保信息</span>
                        <template #icon>
                          <VueIcon :icon="record.applyFormCompleteStatusCode === '1' ? IconCheckCircleFilledFont : IconTimeCircleFilledFont" />
                        </template>
                      </a-tag>
                      <a-tag :bordered="false" :color="record.matchRuleFlag === '1' ? 'success' : 'default'">
                        <span class="ml-[4px]">承保资料</span>
                        <template #icon>
                          <VueIcon :icon="record.matchRuleFlag === '1' ? IconCheckCircleFilledFont : IconTimeCircleFilledFont" />
                        </template>
                      </a-tag>
                      <a-tag :bordered="false" :color="record.applyNotificationStatusCode === '1' ? 'success' : 'default'">
                        <span class="ml-[4px]">投保告知</span>
                        <template #icon>
                          <VueIcon :icon="record.applyNotificationStatusCode === '1' ? IconCheckCircleFilledFont : IconTimeCircleFilledFont" />
                        </template>
                      </a-tag>
                      <a-tag :bordered="false" :color="record.checkRiskobjStatusCode === '1' ? 'success' : 'default'">
                        <span class="ml-[4px]">验标</span>
                        <template #icon>
                          <VueIcon :icon="record.checkRiskobjStatusCode === '1' ? IconCheckCircleFilledFont : IconTimeCircleFilledFont" />
                        </template>
                      </a-tag>
                      <a-tag :bordered="false" :color="['2', '3'].includes(record.publicityStatusCode) ? 'success' : 'default'">
                        <span class="ml-[4px]">投保公示</span>
                        <template #icon>
                          <VueIcon :icon="['2', '3'].includes(record.publicityStatusCode) ? IconCheckCircleFilledFont : IconTimeCircleFilledFont" />
                        </template>
                      </a-tag>
                    </div>
                  </template>
                  <a-progress :percent="calcProgress(record)" :steps="5" :show-info="false" />
                </a-popover>
              </template>
              <template v-if="['marketProductName', 'insuredName', 'departmentNoAndName'].includes(column.dataIndex as string)">
                <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
                  <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
                </a-tooltip>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <div class="flex items-center">
                  <AuthButton code="insureEdit" type="link" :disabled="record.dataSource === 'icore_agr_icp_self' || applyDisabled(record.applyStatus)" @click="handleInsuranceFormFill(record)">编辑</AuthButton>
                  <a-button type="link" @click="handleInsuranceFormcopy(record)">复制</a-button>
                  <AuthButton code="insureDel" type="link" size="small" :disabled="record.dataSource === 'icore_agr_icp_self' || deleteDisabled(record.applyStatus)" @click="handleDelete(record.idAplyProcessTraceSummaryInfo)">删除</AuthButton>
                  <AuthButton code="insureRevoke" type="link" size="small">
                    <a-popover placement="bottom" overlay-class-name="no-arrow-popover">
                      <template #content>
                        <div class="text-xs text-[rgba(0,0,0,0.55)] space-y-[4px]">
                          <a-button type="link" size="small" :disabled="record.dataSource === 'icore_agr_icp_self' || !revocationDisabled(record.applyStatus)" @click="handleRevocation(record.applyPolicyNo)">撤回</a-button>
                        </div>
                      </template>
                      <VueIcon :icon="IconBiaogeGengduoFont" />
                    </a-popover>
                  </AuthButton>
                </div>
              </template>
            </template>
          </a-table>
        </a-spin>
        <PrintModal v-model:visible="printVisible" type="01" :select-obj="selectObj" />
        <!-- 确认删除弹窗 -->
        <a-modal v-model:open="open" :width="pxToRem(450)" :centered="true" @ok="handleDeleteOk">
          <div>
            <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
            <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
          </div>
          <div class="mt-[10px]">是否确认删除</div>
        </a-modal>
        <OneToOnePrintModal v-model:visible="onToOnePrintVisible" :select-obj-arr="selectObjArr" :sid="sid" />
        <FilePrintModal v-model:visible="filePrintVisible" />
        <TemplateDownload v-model:visible="downloadTemplateVisible" />
      </div>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
import { IconInfoCircleFilledFont, IconCheckCircleFilledFont, IconTimeCircleFilledFont, IconBiaogeGengduoFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import Form from 'ant-design-vue/es/form/Form';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { insuranceTrackingType, formDataType, dataType, ChildrenType, OptionsType } from './insuranceTracking.d';
import PrintModal from './components/PrintModal.vue';
import OneToOnePrintModal from './components/OneToOnePrintModal.vue';
import FilePrintModal from './components/FilePrintModal.vue';
import TemplateDownload from './components/TemplateDownload.vue';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import FormFold from '@/components/ui/FormFold.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { pxToRem } from '@/utils/tools';
import { $post, usePost, $getOnClient, $postOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';
import CopyLink from '@/components/ui/CopyLink.vue';

const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 打印信息弹窗
const printVisible = ref<boolean>(false);
// 一户一凭证弹窗
const onToOnePrintVisible = ref<boolean>(false);
// 凭证文件下载
const filePrintVisible = ref<boolean>(false);
// 导出模版弹窗
const downloadTemplateVisible = ref<boolean>(false);
// 验标状态option
const insrCheckStatusOptions = ref<OptionsType[]>([]);
// 单证类型option
const voucherTypeList = ref<OptionsType[]>([]);

const calcProgress = (record: Record<string, string>) => {
  if (record) {
    const { applyFormCompleteStatusCode, applyNotificationStatusCode, checkRiskobjStatusCode, publicityStatusCode, matchRuleFlag } = record;
    return ((Number(applyFormCompleteStatusCode !== '1' ? 0 : 1) + Number(applyNotificationStatusCode !== '1' ? 0 : 1) + Number(checkRiskobjStatusCode !== '1' ? 0 : 1) + Number(!['2', '3'].includes(publicityStatusCode) ? 0 : 1) + Number(matchRuleFlag !== '1' ? 0 : 1)) / 5) * 100;
  }
  return 0;
};

const tagMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
  0: { color: 'warning', text: '待公示', icon: IconErrorCircleFilledFont },
  1: { color: 'processing', text: '公示中', icon: IconErrorCircleFilledFont },
  2: { color: 'success', text: '公示完成', icon: IconCheckCircleFilledFont },
  3: { color: 'default', text: '无需公示', icon: IconErrorCircleFilledFont },
  5: { color: 'orange', text: '需重新公示', icon: IconErrorCircleFilledFont },
};

// 表单数据
const formData = reactive<formDataType>({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  riskProvince: undefined,
  riskCity: undefined,
  riskCounty: undefined,
  riskTown: undefined,
  riskVillage: undefined,
  riskType: '',
  marketProductCode: '',
  insrCheckStatus: [],
  associatedListFlag: ['1', '0'],
  insurantNames: '',
  govSubsidyType: undefined,
  quoteEndTime: '',
  createdDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  renewalType: '',
  voucherType: undefined,
  voucherNo: '',
  applyStatus: [],
  insuranceBeginDateRange: ['', ''],
  insuranceEndDate: '',
});

const formRules = reactive({
  departmentCode: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  associatedListFlag: [
    {
      required: true,
      message: '请选择是否有投保清单',
    },
  ],
});

const { resetFields, validateInfos, validate } = Form.useForm(formData, formRules);
const btnLoading = ref<boolean>(false);
const tableLoading = ref<boolean>(false);

/** 业务号有值时其余搜索条件禁用
 * 测试-龙伟文说的需求
 * 1.改成根据业务号去适配单证类型
 * 2.录入投保申请号就变成类型就是投保单申请
 * 3.PG开头的就是投保申请单，5开头的是投保单，1开头的是保单
 * 4.然后不用置灰清空单证类 **/
// 单号输入后调接口获取单号类型和机构,禁用其他组件
const disabled = ref<boolean>(false);
const handleBlurDisabled = async () => {
  if (formData.voucherNo) {
    disabled.value = true;
    formData.riskProvince = '';
    formData.riskCity = '';
    formData.riskCounty = '';
    formData.riskTown = '';
    formData.riskVillage = '';
    formData.riskType = '';
    formData.marketProductCode = '';
    formData.insrCheckStatus = [];
    formData.insurantNames = '';
    formData.govSubsidyType = '';
    formData.quoteEndTime = '';
    formData.createdDate = [];
    formData.renewalType = '';
    formData.applyStatus = [];
    formData.insuranceBeginDateRange = [];
    formData.insuranceEndDate = '';
    btnLoading.value = true;
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: formData.voucherNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      const isBizType = voucherTypeList.value.findIndex((item) => item.value === res.data?.bizType);
      if (isBizType !== -1) {
        formData.voucherType = res.data?.bizType;
      }
      formData.departmentCode = res.data?.insureDepartmentNo || '';
    }
    btnLoading.value = false;
  } else {
    disabled.value = false;
    formData.voucherType = undefined;
    formData.departmentCode = defaultDeptCode.value;
    btnLoading.value = false;
  }
};
const expand = ref(false);

const columns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentNoAndName', fixed: 'left' },
  { title: '单证号', dataIndex: 'voucherNo' },
  { title: '投保申请单号', dataIndex: 'applyApplyFormNo' },
  { title: '产品名称', dataIndex: 'marketProductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '保险起期', dataIndex: 'insuranceBeginDate' },
  { title: '投保状态', dataIndex: 'applyStatusDesc' },
  { title: '中保信公示状态', dataIndex: 'zbxPublicityStatusCode', key: 'status' },
  { title: '我司公示状态', dataIndex: 'paOfflinePublicityStatusCode', key: 'status' },
  { title: '保费', dataIndex: 'premium' },
  { title: '投保单进度', dataIndex: 'applyNotificationStatusCode' },
  { title: '出单员', dataIndex: 'createdBy' },
  { title: '清单编号', dataIndex: 'farmersListBatchNo' },
  { title: '操作', dataIndex: 'action', fixed: 'right' },
];
// 列表数据
const dataSource = ref<insuranceTrackingType[]>([]);
// 选择机构
const changeDeptCode = (newVal: string) => {
  formData.departmentCode = newVal;
};
// 点击打印获取需要传给后端的数据
const selectObj = ref<insuranceTrackingType>({
  currencyCode: '',
  insureDepartmentNo: '',
  voucherNo: '',
  applyPolicyNo: '',
  applyStatus: '',
  applyApplyFormNo: '',
});
// 点击打印按钮
const handlePrint = () => {
  if (!selectedRowKey.value.length) {
    message.warning('请选择一条记录');
  } else {
    if (selectedRowKey.value?.length >= 2) {
      message.warning('只能选择一条记录进行打印');
    } else {
      printVisible.value = true;
      selectObj.value = selectedRow.value?.[0];
    }
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
const selectObjArr = ref<insuranceTrackingType[]>([]);
const policyNos = ref<string[]>([]);
// 同批次打印任务id
const sid = ref<string>('');
// 合并导出接口
const mergeUrl = gateWay + service.document + '/print/mergePolicy';
const mergeReq = await usePost<string>(mergeUrl);
const getMergePolicy = async () => {
  const params = {
    policyNos: policyNos.value,
  };
  try {
    sid.value = '';
    const res = await mergeReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      sid.value = res.data || '';
      onToOnePrintVisible.value = true;
      selectObjArr.value = selectedRow.value;
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 点击一户一保打印
const handlePrintOne = () => {
  if (!selectedRowKey.value.length) {
    message.warning('请选择至少一条记录');
  } else {
    const isPolicyNo = ref<boolean>(true); // 是否生成了保单
    selectObjArr.value = [];
    policyNos.value = [];
    selectedRow.value.forEach((item) => {
      if (!item.policyNo) {
        isPolicyNo.value = false;
      } else {
        policyNos.value.push(item.policyNo);
      }
    });
    if (!isPolicyNo.value) {
      message.error('没有生成对应的保单号，不能打印');
    } else {
      getMergePolicy();
    }
  }
};
// 凭证文件下载
const handlePrintFile = () => {
  filePrintVisible.value = true;
};
const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  validate().then(() => {
    getListData();
  });
};

const subsidyTypeOptions = ref<OptionsType[]>([]);
const processStatusOption = ref<OptionsType[]>([]);
const initOption = async () => {
  const url = gateWay + service.administrate + '/parmBaseConstantConf/getParmBaseConstantConf';
  const res = await $post(url, ['subsidyType', 'agrAnVourcherType', 'agrAnCheckRiskobjStatus']);
  const { data = [] } = res || {};
  const originData = (data as ChildrenType[])?.find((item: OptionsType) => item.value === 'subsidyType');
  if (originData && originData.children) {
    subsidyTypeOptions.value = originData.children.map((item: OptionsType) => ({
      value: item.value,
      label: item.label,
    }));
  }
  // 验标状态获取
  const item = (data as ChildrenType[])?.find((item: OptionsType) => item.value === 'agrAnCheckRiskobjStatus');
  if (item && item.children) {
    insrCheckStatusOptions.value = item.children.map((item: OptionsType) => {
      formData.insrCheckStatus.push(item.value);
      return {
        value: item.value,
        label: item.label,
      };
    });
  }
  // 单证类型
  const findOne = (data as ChildrenType[])?.find((item: OptionsType) => item.value === 'agrAnVourcherType');
  if (findOne && findOne.children) {
    voucherTypeList.value = findOne.children.map((child: OptionsType) => ({
      value: child.value,
      label: child.label,
    }));
  }
};
initOption();

// 列表接口
const loading = ref<boolean>(false);
const url = gateWay + service.accept + '/agr/voucher/queryVoucherSummaryInfos';
const queryVoucherSummaryInfos = await usePost(url);
const getListData = async () => {
  loading.value = true;
  const params = {
    ...formData,
    applyStatus: formData.applyStatus || [],
    containChildDepart: formData.containChildDepart ? '1' : '0',
    startCreatedDate: formData.createdDate?.[0] || '',
    endCreatedDate: formData.createdDate?.[1] || '',
    startInsuranceBeginDate: formData.insuranceBeginDateRange?.[0] || '',
    endInsuranceBeginDate: formData.insuranceBeginDateRange?.[1] || '',
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };
  try {
    const res = await queryVoucherSummaryInfos.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = (res?.data as dataType) || {};
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
      message.error(res?.msg || '');
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
// 编辑按钮不可修改状态
const applyDisabled = (type: string) => ['A3', 'A4', 'B5', 'B4', 'B7', 'B3', 'B6', 'B21'].includes(type);
// 删除按钮禁用状态
const deleteDisabled = (type: string) => ['A1', 'A3', 'A4', 'B3', 'B21', 'B4', 'B5', 'B6', 'B7'].includes(type);
// 撤回按钮禁用状态
const revocationDisabled = (type: string) => ['B3', 'B6', 'B21'].includes(type);
const router = useRouter();
// 修改投保单-跳转到投保申请第二屏
const handleInsuranceFormFill = (record: Record<string, string>) => {
  if (['B1', 'B2', 'B8'].includes(record.applyStatus)) {
    router.push({
      path: '/insuranceFormFill',
      query: {
        applyPolicyNo: record.applyPolicyNo || '',
      },
    });
  } else {
    handleInsuranceApply(record.applyApplyFormNo, record?.applyPolicyNo);
  }
};
// 复制投保单-跳转到投保申请第一屏
const handleInsuranceFormcopy = async (record: Record<string, string>) => {
  // todos: 按钮未展示，待调用接口、获取applyApplyFormNo、applyPolicyNo
  const fetchUrl = gateWay + service.accept + '/web/applicationForm/copyApplyContract';
  tableLoading.value = true;
  const res = await $getOnClient<{ newPreRiskNo: string; newApplyPolicyNo: string }>(fetchUrl, { preRiskNo: record.applyApplyFormNo, applyPolicyNo: record.applyPolicyNo });
  tableLoading.value = false;
  const { code, msg = '' } = res || {};
  if (code === SUCCESS_CODE) {
    const { newPreRiskNo, newApplyPolicyNo } = res?.data || {};
    handleInsuranceApply(newPreRiskNo, newApplyPolicyNo);
  } else {
    message.error(msg);
  }
};
// 修改投保申请-跳转到投保申请第一屏
const handleInsuranceApply = (id: string, applyPolicyNo: string) => {
  router.push({
    path: '/insuranceApply',
    query: {
      id,
      applyPolicyNo: applyPolicyNo,
    },
  });
};
// 删除
const open = ref<boolean>(false);
const idAplyProcessTraceSummaryInfo = ref<string>('');
const handleDelete = (id: string) => {
  open.value = true;
  idAplyProcessTraceSummaryInfo.value = id;
};
// 确认删除
const handleDeleteOk = async () => {
  try {
    const fetchUrl = gateWay + service.accept + '/agr/voucher/deleteVoucherSummaryInfo';
    const res = await $getOnClient(fetchUrl, { idAplyProcessTraceSummaryInfo: idAplyProcessTraceSummaryInfo.value });
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('删除成功');
      open.value = false;
      getListData();
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 重置
const resetForm = () => {
  resetFields();
  getListData();
  disabled.value = false;
};
// 表格的select选择框，如果状态不为B5则禁用选项
type Key = string | number;
const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => onSelectChange(selectedRowKeys, selectedRows),
  getCheckboxProps: (record: insuranceTrackingType) => ({
    disabled: false,
    applyStatus: record.applyStatus,
  }),
};
// 表格选中ID
const selectedRow = ref<insuranceTrackingType[]>([]);
const selectedRowKey = ref<Key[]>([]);
// 选择数据
const onSelectChange = (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => {
  selectedRow.value = selectedRows;
  selectedRowKey.value = selectedRowKeys;
};
// 点击撤回
const handleRevocation = async (applyPolicyNo: string) => {
  try {
    const fetchUrl = gateWay + service.accept + '/web/applicationForm/withdrawApplyPolicy';
    const res = await $postOnClient(fetchUrl, { applyPolicyNo });
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('撤回成功');
      getListData();
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
const goToInsuranceApply = (id: string) => {
  router.push({
    path: '/insuranceApplyDetail',
    query: {
      id,
      origin: 'insuranceTracking', // 跳转标识
    },
  });
};
// 打开投保单详情页
const openApplyDetail = (applyPolicyNo: string) => {
  const path = {
    path: '/applyPolicyDetails',
    query: {
      applyPolicyNo: applyPolicyNo,
    },
  };
  router.push(path);
};
// 一对一打印按钮权限控制，234机构的出单员才能显示
const showOneToOneBtn = computed(() => formData.departmentCode.includes('234'));
// 导出模版
const downloadTemplate = () => {
  downloadTemplateVisible.value = true;
};

const getVoucherType = async () => {
  const fetchUrl = gateWay + service.accept + '/agr/voucher/queryApplyStatusList';
  const res = await $getOnClient<OptionsType[]>(fetchUrl, { voucherType: formData.voucherType });
  if (res && res.code === SUCCESS_CODE) {
    formData.applyStatus = [];
    processStatusOption.value = res.data || [];
  }
};

watch(
  () => formData.voucherType,
  () => {
    getVoucherType();
  },
);
const route = useRoute();
const { pagination } = usePagination(getListData);
onMounted(() => {
  // 首页投保单待申请核保 跳转 投保跟踪
  if (route.query.fromHome === '1') {
    formData.applyStatus = ['B1', 'B2', 'B8'];
    if (route.query.startTime && route.query.endTime) {
      formData.createdDate = [route.query.startTime, route.query.endTime];
    }
  }
  getListData();
  getVoucherType();
});
watch(
  // 首页投保单待申请核保 跳转 投保跟踪
  () => route.query.fromHome,
  () => {
    if (route.query.fromHome === '1') {
      formData.applyStatus = ['B1', 'B2', 'B8'];
      if (route.query.startTime && route.query.endTime) {
        formData.createdDate = [route.query.startTime, route.query.endTime];
      }
      getListData();
    }
  },
);
</script>
<style lang="less" scoped>
.ant-tag-orange {
  color: #ff5b00;
  background: #ffeee5;
}
.ant-tag-default {
  color: rgba(0, 0, 0, 0.4);
  background: #f2f3f5;
}
</style>
