<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <!-- 表单录入 -->
      <div class="flex-1 bg-white border-right-line">
        <div class="h-[76px] w-ful bg-image-url">
          <span class="text-[24px] text-[#00190c] leading-[76px] ml-[16px]">自助投保配置</span>
        </div>
        <div class="bg-white mx-14px mb-[14px] px-[24px] pt-[16px] pb-[6px] rounded form-area">
          <a-spin :spinning="pageLoading">
            <a-form ref="formRef" :colon="false" :label-col="{ style: { width: pxToRem(115) } }" :model="formState">
              <!-- 基本信息 -->
              <div class="grid grid-cols-3 gap-x-16px bottom-line">
                <a-form-item label="出单机构" :name="['baseInfo', 'departmentCode']" :rules="[{ required: true, message: '请选择出单机构' }]">
                  <department-search :dept-code="formState.baseInfo.departmentCode" :disabled="isEdit" @change-dept-code="changeDeptCode" />
                </a-form-item>
                <a-form-item class="col-span-2" label="是否共展" :name="['saleInfo', 'developFlg']">
                  <a-radio-group v-model:value="formState.saleInfo.developFlg" disabled>
                    <a-radio value="N">否</a-radio>
                    <a-radio value="Y">是</a-radio>
                  </a-radio-group>
                </a-form-item>
                <!-- 业务员 -->
                <EmployeeInfo v-model="formState.saleInfo.employeeInfoList" :department-code="formState.baseInfo.departmentCode" :develop-flg="formState.saleInfo.developFlg" :validate-fields="formRef?.validateFields" :disabled="isEdit" />
                <a-form-item label="渠道来源" :name="['saleInfo', 'channelSourceCode']" :rules="[{ required: true, message: '请选择渠道来源' }]">
                  <a-select v-model:value="formState.saleInfo.channelSourceCode" :options="channelSourceList" disabled allow-clear show-search option-filter-prop="label" placeholder="请选择" @change="changeChannelSource" />
                </a-form-item>
                <a-form-item label="渠道来源细分" :name="['saleInfo', 'channelSourceDetailCode']" :rules="[{ required: true, message: '请选择渠道来源细分' }]">
                  <a-select v-model:value="formState.saleInfo.channelSourceDetailCode" :options="channelSourceDetailList" allow-clear show-search option-filter-prop="label" placeholder="请选择" :disabled="isEdit" />
                </a-form-item>
                <a-form-item label="业务员UM" :name="['baseInfo', 'inputBy']" :rules="[{ required: true, message: '请选择渠道来源细分' }]">
                  <a-input v-model:value="formState.baseInfo.inputBy" :disabled="isEdit" />
                </a-form-item>
                <a-form-item label="协保员ID" class="col-span-3" name="assisterInfoList" :rules="[{ required: true, validator: checkId }]">
                  <div class="flex items-center">
                    <a-select v-model:value="assisterInfoList" class="w-full" mode="multiple" :filter-option="filterOption" :max-tag-count="8" :disabled="isEdit" @select="handleSelect" @change="(value, option) => handleChange(value as Array<string>)">
                      <!-- 最多选择8个农保id 选择无就不能再选择其他id -->
                      <a-select-option v-for="item in assistIds" :key="item.assisterId" :value="item.assisterId" :disabled="(assisterInfoList.length >= 8 && assisterInfoList.findIndex((assister: string) => assister === item.assisterId) === -1) || (assisterInfoList.includes('无') && item.assisterId !== '无')">
                        {{ item.assisterId }}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-form-item>
              </div>
              <div class="form-title">项目信息</div>
              <div class="grid grid-cols-2 gap-x-16px bottom-line">
                <a-form-item label="项目来源" :name="['extendInfo', 'isTenderBusiness']" :rules="[{ required: true, message: '请选择项目来源' }]">
                  <a-select v-model:value="formState.extendInfo.isTenderBusiness" placeholder="请选择" :options="projectSourceList" allow-clear :disabled="isEdit" @change="changeIsTenderBusiness" />
                </a-form-item>
                <a-form-item label="项目名称" :name="['extendInfo', 'tenderBusinessNo']" :rules="[{ required: true, message: '请选择项目名称' }]">
                  <a-select v-model:value="formState.extendInfo.tenderBusinessNo" placeholder="请选择" :options="projectNameList" allow-clear show-search :filter-option="false" :default-active-first-option="false" :loading="searchProjectLoading" :disabled="isEdit" @focus="resetTenderProjectList" @search="debounceSearchProject" @change="changeTenderBusiness" />
                </a-form-item>
                <a-form-item v-if="showCustomerAndRisk" label="客户类型" :name="['baseInfo', 'customerType']" :rules="[{ required: true, message: '请选择客户类型' }]">
                  <a-select v-model:value="formState.baseInfo.customerType" disabled placeholder="请选择" :options="customerTypeList" allow-clear show-search option-filter-prop="label" />
                </a-form-item>
                <a-form-item
                  v-if="showCustomerAndRisk"
                  label="风险程度"
                  validate-first
                  :name="['extendInfo', 'lossRate']"
                  :rules="[
                    { required: true, message: '请输入风险程度' },
                    { pattern: /^\d{1,4}(\.\d{1,4})?$/, message: '输入数字且整数位不超过4位，小数位不超过4位', trigger: 'change' },
                  ]"
                >
                  <a-input v-model:value.trim="formState.extendInfo.lossRate" placeholder="请输入" allow-clear :disabled="isEdit">
                    <template #addonAfter>
                      <div class="w-[32px]">%</div>
                    </template>
                  </a-input>
                </a-form-item>
                <a-form-item label="农业用地分类" :name="['extendInfo', 'agrLandClassification']" :rules="[{ required: true, message: '请选择农业用地分类' }]">
                  <a-select v-model:value="formState.extendInfo.agrLandClassification" placeholder="请选择" :options="unitCodeList" :disabled="isEdit" />
                </a-form-item>
              </div>
              <div class="form-title">客户信息</div>
              <div class="grid grid-cols-6 gap-x-16px bottom-line">
                <a-form-item label="">
                  <a-checkbox v-model:checked="sameInsuredPersons" disabled>投/被/受信息一致</a-checkbox>
                </a-form-item>
                <a-form-item label="">
                  <a-radio v-model:checked="radioValue" disabled>个人</a-radio>
                </a-form-item>
              </div>
              <div class="grid grid-cols-3 gap-x-16px bottom-line">
                <a-form-item label="贫困户标识" :name="['insurantInfoList', 0, 'poorSymbol']">
                  <a-select v-model:value="formState.insurantInfoList[0].poorSymbol" disabled :options="poorHouseList" />
                </a-form-item>
                <a-form-item label="农户主体类型" :name="['insurantInfoList', 0, 'subjectNumber']">
                  <a-select v-model:value="formState.insurantInfoList[0].subjectNumber" disabled :options="farmerMainList" />
                </a-form-item>
                <a-form-item label="参保农户数" :name="['riskGroupInfoList', 0, 'riskAgrInfo', 'farmersCount']">
                  <a-input v-model:value="formState.riskGroupInfoList[0].riskAgrInfo.farmersCount" suffix="户" disabled />
                </a-form-item>
                <a-form-item label="农户自缴减免系数" :name="['riskGroupInfoList', 0, 'riskAgrInfo', 'reductionCoefficient']">
                  <a-input v-model:value="formState.riskGroupInfoList[0].riskAgrInfo.reductionCoefficient" disabled />
                </a-form-item>
                <a-form-item label="农户自缴是否代缴" :name="['riskGroupInfoList', 0, 'riskAgrInfo', 'substitute']">
                  <a-select v-model:value="formState.riskGroupInfoList[0].riskAgrInfo.substitute" disabled>
                    <a-select-option value="Y">是</a-select-option>
                    <a-select-option value="N">否</a-select-option>
                  </a-select>
                </a-form-item>
              </div>
              <div class="form-title">产品信息</div>
              <div class="grid grid-cols-3 gap-x-16px bottom-line">
                <a-form-item class="col-span-2" label="产品名称" :name="['baseInfo', 'productCode']" :rules="[{ required: true, message: '请选择产品名称' }]">
                  <product-select ref="productRef" v-model:value="formState.baseInfo.productCode" :department-code="formState.baseInfo.departmentCode" module="accept" :agent-agreement-no="formState.saleInfo.agentInfoList[0].agentAgreementNo" :channel-source-code="formState.saleInfo.channelSourceCode" :disabled="isEdit" @product-change="changeProduct" />
                </a-form-item>
                <a-form-item label="补贴类型" :name="['baseInfo', 'govSubsidyType']">
                  <a-select v-model:value="formState.baseInfo.govSubsidyType" placeholder="请选择" :options="subsidyTypeList" disabled />
                </a-form-item>
                <a-form-item label="标的" :name="['baseInfo', 'riskCode']" class="col-span-3" :rules="[{ required: true, message: '请选择标的' }]">
                  <a-form-item-rest>
                    <RiskCodeSelect ref="riskCodeRef" disabled :default-value="defaultRisk" :department-code="formState.baseInfo.departmentCode" :show-search="false" />
                  </a-form-item-rest>
                </a-form-item>
                <a-form-item label="标的地址" :name="['riskAddressInfoList', 0, 'address']" class="col-span-3" :rules="[{ validator: validateAddress }]">
                  <div class="flex">
                    <a-form-item-rest>
                      <RegionSelect v-model:province="formState.riskAddressInfoList[0].province" v-model:city="formState.riskAddressInfoList[0].city" v-model:county="formState.riskAddressInfoList[0].county" v-model:town="formState.riskAddressInfoList[0].town" v-model:village="formState.riskAddressInfoList[0].village" class="grow mr-8px w-[70%]" :disabled="isEdit" @change-selected="changeRiskAddress" />
                    </a-form-item-rest>
                    <div class="w-[30%]">
                      <a-input v-model:value.trim="formState.riskAddressInfoList[0].address" placeholder="请输入" allow-clear :disabled="isEdit" />
                    </div>
                  </div>
                </a-form-item>
              </div>
              <div class="grid grid-cols-3 gap-x-16px bottom-line">
                <a-form-item required label="保险起期" :name="['baseInfo', 'insuranceBeginDate']" :rules="[{ required: true, message: '请选择保险起期' }]">
                  <a-date-picker v-model:value="formState.baseInfo.insuranceBeginDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="isEdit" />
                </a-form-item>
                <a-form-item required label="保险止期" :name="['baseInfo', 'insuranceEndDate']" :rules="[{ required: true, message: '请选择保险止期' }]">
                  <a-date-picker v-model:value="formState.baseInfo.insuranceEndDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="isEdit" />
                </a-form-item>
                <a-form-item label="年限系数" :name="['baseInfo', 'shortTimeCoefficient']">
                  <a-input v-model:value="formState.baseInfo.shortTimeCoefficient" disabled />
                </a-form-item>
                <a-form-item label="单位保险金额" :name="['riskGroupInfoList', 0, 'planInfoList', 0, 'unitInsuredAmount']" required>
                  <a-input-number v-model:value="formState.riskGroupInfoList[0].planInfoList[0].unitInsuredAmount" :disabled="isEdit" @change="handleAmountChange">
                    <template #addonAfter>
                      <div class="w-[32px]">元</div>
                    </template>
                  </a-input-number>
                </a-form-item>
                <a-form-item label="费率" :name="['riskGroupInfoList', 0, 'planInfoList', 0, 'expectPremiumRate']" required>
                  <a-input-number v-model:value="formState.riskGroupInfoList[0].planInfoList[0].expectPremiumRate" :disabled="isEdit" @change="handleRateChange">
                    <template #addonAfter>
                      <div class="w-[32px]">%</div>
                    </template>
                  </a-input-number>
                </a-form-item>
                <a-form-item label="单位保费" :name="['riskGroupInfoList', 0, 'planInfoList', 0, 'unitPrimium']" required>
                  <a-input-number v-model:value="formState.riskGroupInfoList[0].planInfoList[0].unitPrimium" :min="0">
                    <template #addonAfter>
                      <div class="w-[32px]">元</div>
                    </template>
                  </a-input-number>
                </a-form-item>
                <a-form-item label="单位" :name="['riskGroupInfoList', 0, 'riskAgrInfo', 'insuredUnit']" required>
                  <a-select v-model:value="formState.riskGroupInfoList[0].riskAgrInfo.insuredUnit" :options="unitInsureCountOptions" :disabled="isEdit" />
                </a-form-item>
              </div>
              <div class="form-title">保险方案</div>
              <div class="grid grid-cols-3 gap-x-16px">
                <a-form-item label="是否共保" :name="['baseInfo', 'coinsuranceMark']" :rules="[{ required: true, message: '请选择是否共保' }]">
                  <a-radio-group v-model:value="formState.baseInfo.coinsuranceMark" disabled @change="changeCoinsuranceMark">
                    <a-radio value="1">是</a-radio>
                    <a-radio value="0">否</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="应收责任人" :name="['baseInfo', 'premiumOwner']" required>
                  <a-input v-model:value="formState.baseInfo.premiumOwner" :disabled="isEdit" />
                </a-form-item>
                <a-form-item label="争议处理方式">
                  <a-select v-model:value="formState.baseInfo.disputedSettleMode" disabled :options="disputeTypeList" />
                </a-form-item>
              </div>
              <FeePlan ref="feeInfoRef" v-model:pay-info-list="formState.payInfoList" class="mb-[12px" :disabled="isEdit" :gov-subsidy-type="formState.baseInfo.govSubsidyType" />
              <div class="form-title">费用信息</div>
              <CostInfo ref="costInfoRef" v-model:cost-info="formState.costInfo" :base-info="formState.baseInfo" :business-source-code="formState.saleInfo?.businessSourceCode" :disabled="isEdit" />
            </a-form>
            <div class="form-title">免赔信息</div>
            <DisclaimInfo ref="disclaimRef" v-model:disclaim-info="formState.noclaimInfoList" v-model:base-info="formState.baseInfo" :risk-group-info-list="formState.riskGroupInfoList" :disabled="isEdit" />
            <div class="form-title">特别约定</div>
            <SpecialSet v-model="formState.specialPromiseList" :department-code="formState.baseInfo?.departmentCode" :market-product-code="formState.baseInfo?.productCode" :disabled="isEdit" />
          </a-spin>
        </div>
      </div>
    </main>
    <footer v-show="!isEdit" class="footer-wrapper space-x-8px">
      <a-button type="primary" :loading="saveLoading" @click="validateAllForms">确定</a-button>
      <a-button @click="closeCurrentPage">取消</a-button>
    </footer>
  </div>
</template>

<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue';
import { debounce } from 'lodash-es';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import type { Rule } from 'ant-design-vue/es/form';
import type { RawValueType } from 'ant-design-vue/es/vc-tree-select/TreeSelect';
import type { LabelInValueType } from 'ant-design-vue/es/vc-select/Select';
import type { AssisterInfo } from '../insuranceFormFill/insuranceFormFill';
import type { FormState } from './selfInsurance';
import EmployeeInfo from './components/EmployeeInfo.vue';
import FeePlan from './components/FeePlan.vue';
import CostInfo from './components/CostInfo.vue';
import DisclaimInfo from './components/DisclaimInfo.vue';
import SpecialSet from './components/SpecialSet.vue';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import { multiply, pxToRem } from '@/utils/tools';
import { $postOnClient, $getOnClient, usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { useUserStore } from '@/stores/useUserStore';
import { $post } from '@/utils/request';

const scrollWrapper = ref();
const { gateWay, service } = useRuntimeConfig().public || {};
const route = useRoute();
const { userInfo } = useUserStore();
const { deletPageTabListItem } = inject('pageTab'); // 关闭页签

const router = useRouter();
// 农保id options
const assistIds = ref<Array<AssisterInfo>>([
  {
    assisterId: '无',
    idCardNo: '',
    umCode: '',
  },
]);

const defaultRisk = ref('');

const sameInsuredPersons = ref<boolean>(true);
const radioValue = ref<boolean>(true);
const formRef = ref();
const riskCodeRef = ref();
const productRef = ref();
// 是否需要机构联动标的地址
const updateRiskAddress = ref<boolean>(true);
// 是否需要联动支付信息
const updatePayInfo = ref<boolean>(true);
// 是否需要联动费用信息
const updateCostInfo = ref<boolean>(true);
const formState = reactive<FormState>({
  baseInfo: {
    departmentCode: '',
    productCode: '',
    productName: '',
    govSubsidyType: '', // 补贴类型，产品带出
    coinsuranceMark: '0', // 是否共保
    acceptInsuranceFlag: '', // 是否主承
    customerType: '', // 客户类型
    riskCode: '',
    riskLevel: '',
    insuranceEndDate: '', // 保险止期
    insuranceBeginDate: '', // 保险起期
    disputedSettleMode: '1', // 争议处理方式
    premiumOwner: '', // 应收责任人
    shortTimeCoefficient: '1', // 年限系数
    assisterInfoList: [], // 协保员id数组
    productVersion: '', // 产品版本
    inputBy: '', // 业务员UM
  },
  saleInfo: {
    developFlg: 'N', // 是否共展
    channelSourceCode: '', // 渠道来源
    channelSourceDetailCode: '', // 渠道来源细分
    isReportTuanjin: '',
    businessOpportunitySource: '', // 商机来源
    tuanjinProjectNo: '',
    presenterCode: '', // 主介绍人code
    presenterName: '', // 主介绍人name
    employeeInfoList: [{ employeeCode: '', employeeName: '', employeeProfCertifNo: '', commisionScale: '', mainEmployeeFlag: '1' }],
    agentInfoList: [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }],
    brokerInfoList: [{ brokerCode: '', brokerName: '', agencySaleName: '', agencySaleProfCertifNo: '' }],
  },
  riskAddressInfoList: [{ province: '', provinceName: '', city: '', cityName: '', town: '', townName: '', county: '', countyName: '', village: '', villageName: '', address: '' }],
  extendInfo: {
    lossRate: '', // 风险程度
    tenderBusinessName: '', // 项目名称
    tenderBusinessNo: '', // 项目名称-code
    isTenderBusiness: '1', // 项目来源
    agrLandClassification: '', // 农业用地分类
  },
  insurantInfoList: [
    {
      sameInsuredPersons: '1',
      personnelType: '1',
      poorSymbol: '0',
      subjectNumber: '00',
    },
  ],
  riskGroupInfoList: [
    {
      combinedProductCode: '',
      riskAgrInfo: {
        farmersCount: '1',
        reductionCoefficient: '0',
        substitute: 'N',
        insuredUnit: '',
      },
      planInfoList: [
        {
          unitInsuredAmount: '',
          expectPremiumRate: '',
          centralFinance: '',
          cityFinance: '',
          countyFinance: '',
          farmersFinance: '',
          otherFinance: '',
          provincialFinance: '',
          unitPrimium: '',
        },
      ],
    },
  ],
  costInfo: {
    totalSumFeeLimit: '',
    performanceValue1Default: '',
    managementFees: '',
    assisterCharge: '',
    calamitySecurityRate: '',
    commissionBrokerChargeProportion: '',
  },
  payInfoList: [],
  noclaimInfoList: [],
  specialPromiseList: [],
});
// 协保员id value
const assisterInfoList = ref<Array<string>>([]);
// 请求基础数据：项目来源、客户类型、补贴类型
const projectSourceList = ref<Array<SelectOptions>>([]);
const subsidyTypeList = ref<Array<SelectOptions>>([]); // 补贴类型关联产品
const customerTypeList = ref<Array<SelectOptions>>([]);
const disputeTypeList = ref<Array<SelectOptions>>([]); // 争议处理方式
const farmerMainList = ref<Array<SelectOptions>>([]); // 农业主体类型
const poorHouseList = ref<Array<SelectOptions>>([]); // 凭困户标识
const unitCodeList = ref<Array<SelectOptions>>([]); // 农业用地分类
const unitInsureCountOptions = ref<Array<SelectOptions>>([]); // 单位
const mainEmployee = computed(() => formState.saleInfo.employeeInfoList.find((employee: { mainEmployeeFlag: string }) => employee.mainEmployeeFlag === '1')?.employeeCode);

// 主业务员变动影响渠道
watchEffect(async () => {
  if (mainEmployee.value) {
    try {
      const res = await $getOnClient('/api/common/getChannelSourceList', { employeeCode: mainEmployee.value });
      if (res && res.code === '000000' && Array.isArray(res.data)) {
        channelSourceList.value = res.data;
        // 渠道来源如果有农渠 则为农渠 没有则提示不能出单
        const includeAgrChannel = channelSourceList.value.some((item) => item.value === 'N');
        formState.saleInfo.channelSourceCode = includeAgrChannel ? 'N' : '';
        // 过滤掉非直接业务的渠道细分
        channelSourceDetailList.value = channelSourceList.value.find((item) => item.value === 'N')?.children?.filter((item) => item.value.split(',')?.[1] === '1') as SelectOptions[];
      } else {
        channelSourceList.value = [];
        formState.saleInfo.channelSourceCode = '';
      }
    } catch (err) {
      console.log(err);
    }
  }
});

// 默认不展示
const showCustomerAndRisk = ref<boolean>(false);
// 获取隐藏客户字段开关
const getCustomerSwitch = async () => {
  const url = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const params = {
    switchCode: 'CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH',
    allowanceTypeCode: formState?.baseInfo?.govSubsidyType || '',
    agriculturalRiskObjectClassCode: formState?.baseInfo?.riskCode?.[0] || '',
    departmentNo: formState?.baseInfo?.departmentCode,
  };
  try {
    const res = await $post(url, params);
    if (res && res.code === SUCCESS_CODE) {
      showCustomerAndRisk.value = res.data;
    } else {
      showCustomerAndRisk.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};
// 根据机构及标的地址控制开关显隐客户类型及风险程度字段
const debounceGetCustomerSwitch = debounce(getCustomerSwitch, 1000);
watch([() => formState?.baseInfo?.govSubsidyType, () => formState?.baseInfo?.riskCode, () => formState?.baseInfo?.departmentCode], () => {
  if (formState?.baseInfo.departmentCode) {
    debounceGetCustomerSwitch();
  }
});

// 单位保险金额改变
const handleAmountChange = (value) => {
  const expectPremiumRate = formState.riskGroupInfoList[0].planInfoList[0].expectPremiumRate;
  const realRate = isNaN(parseFloat(expectPremiumRate as string)) ? 0 : parseFloat(expectPremiumRate as string) / 100;
  if (expectPremiumRate && value) {
    // 单位保费=单位保额*费率
    formState.riskGroupInfoList[0].planInfoList[0].unitPrimium = Number(multiply(String(realRate), String(value || 0))).toFixed(6);
  } else {
    formState.riskGroupInfoList[0].planInfoList[0].unitPrimium = 0;
  }
};

// 费率改变
const handleRateChange = (value) => {
  const unitInsuredAmount = formState.riskGroupInfoList[0].planInfoList[0].unitInsuredAmount;
  const realRate = isNaN(parseFloat(value as string)) ? 0 : parseFloat(value as string) / 100;
  if (unitInsuredAmount && value) {
    // 单位保费=单位保额*费率
    formState.riskGroupInfoList[0].planInfoList[0].unitPrimium = Number(multiply(String(unitInsuredAmount), String(realRate || 0))).toFixed(6);
  } else {
    formState.riskGroupInfoList[0].planInfoList[0].unitPrimium = 0;
  }
};

// 查询产品费用上限
watchEffect(async () => {
  if (formState?.baseInfo?.departmentCode && formState?.baseInfo?.productCode && formState?.baseInfo?.productVersion) {
    const params = {
      departmentCode: formState.baseInfo.departmentCode,
      productCode: formState.baseInfo.productCode,
      productVersion: formState.baseInfo.productVersion,
    };
    const res = await $postOnClient(`${gateWay}${service.accept}/applyPayInfo/qeyCostLimitInfo`, params);
    if (res && res.code === '000000') {
      formState.costInfo = {
        ...res.data,
        assisterCharge: updateCostInfo.value ? '0' : formState.costInfo.assisterCharge,
        commissionBrokerChargeProportion: updateCostInfo.value ? '0' : formState.costInfo.commissionBrokerChargeProportion,
        managementFees: updateCostInfo.value ? '0' : formState.costInfo.managementFees,
        calamitySecurityRate: updateCostInfo.value ? '0' : formState.costInfo.calamitySecurityRate,
        performanceValue1Default: updateCostInfo.value ? '0' : formState.costInfo.performanceValue1Default,
      };
      updateCostInfo.value = true;
    }
  }
});

const filterOption = (input: string, option: { value: string }) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 共保id选择无需要提示
const handleSelect = (value: RawValueType | LabelInValueType) => {
  if (value === '无') {
    assisterInfoList.value = ['无'];
    formState.baseInfo.assisterInfoList = [
      {
        assisterId: '无',
        idCardNo: '',
        umCode: '',
      },
    ];
    message.info('选择“无”将无法录入并支付协办费(共保非主承除外)!');
  }
};
// 农保id校验
const checkId = (_rule: Rule) => {
  if (assisterInfoList?.value?.length < 1) {
    return Promise.reject('农保ID不能为空');
  }
  return Promise.resolve();
};
// 协保员id改变
const handleChange = (value: Array<string>) => {
  const results: Array<AssisterInfo> = [];
  if (Array.isArray(value)) {
    assistIds.value.forEach((item: AssisterInfo) => {
      if (value?.includes(item.assisterId) && item.assisterId !== '无') {
        results.push(item);
      }
    });
  }
  formState.baseInfo.assisterInfoList = results;
};
// 获取农保id
const queryAssistListReq = await usePost<AssisterInfo[]>(`${gateWay}${service.accept}/saleInfo/queryAssistList`);
// 获取农保id数据
const getAssistIdData = async () => {
  const params = {
    um: userInfo.umCode,
    departmentNo: formState.baseInfo.departmentCode,
  };
  const res = await queryAssistListReq.fetchData(params);
  if (res?.code === SUCCESS_CODE) {
    assistIds.value = [
      {
        assisterId: '无',
        idCardNo: '',
        umCode: '',
      },
      ...res.data,
    ];
  }
};
const getBaseInfo = async () => {
  try {
    const res = await $post<{ projectSourceList: Array<SelectOptions>; customerTypeList: Array<SelectOptions>; subsidyTypeList: Array<SelectOptions>; disputeTypeList: Array<SelectOptions>; farmerMainList: Array<SelectOptions>; poorHouseList: Array<SelectOptions>; unitCodeList: Array<SelectOptions> }>('/api/common/getParmBaseConstantConf', ['customerType', 'isTenderBusiness', 'subsidyType', 'disputeType', 'farmerMain', 'poorHouse', 'farmer_land_unit_code']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      projectSourceList.value = res.data.projectSourceList || [];
      // formState.saleInfo.isTenderBusiness = '1';
      customerTypeList.value = res.data.customerTypeList || [];
      // 默认为非三类客户 不可更改
      formState.baseInfo.customerType = '04';
      subsidyTypeList.value = res.data.subsidyTypeList || [];
      disputeTypeList.value = res.data.disputeTypeList || [];
      farmerMainList.value = res.data.farmerMainList || [];
      poorHouseList.value = res.data.poorHouseList || [];
      unitCodeList.value = res.data.unitCodeList || [];
    } else {
      projectSourceList.value = [];
      customerTypeList.value = [];
      subsidyTypeList.value = [];
      farmerMainList.value = [];
      poorHouseList.value = [];
      unitCodeList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
const getUnitData = async () => {
  const params = {
    productCode: formState.baseInfo.productCode,
    productVersion: formState.baseInfo.productVersion,
  };
  const res = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryRiskNumberUnitList`, params);
  if (res && res?.code === SUCCESS_CODE) {
    unitInsureCountOptions.value = res.data || [];
  } else {
    unitInsureCountOptions.value = [];
  }
};
// 页面一进入就查询基础数据
getBaseInfo();
const pageLoading = ref(false);
// 选择机构
const changeDeptCode = (value: string) => {
  formState.baseInfo.departmentCode = value;
  defaultRisk.value = '';
};
watch(
  () => formState.baseInfo.departmentCode,
  async () => {
    if (formState.baseInfo.departmentCode) {
      getAssistIdData();
      try {
        // 机构变动，回填标的地址
        if (updateRiskAddress.value) {
          formState.extendInfo.tenderBusinessNo = ''; // 修改机构项目名称清空
          assisterInfoList.value = []; // 修改机构清空协保员
          const addressRes = await $getOnClient<{ province: string; provinceName: string; city: string; cityName: string; town: string; townName: string; county: string; countyName: string; village: string; villageName: string; address: string }>(`${gateWay}${service.administrate}/public/getAddressByDepartmentCode`, { departmentCode: formState.baseInfo.departmentCode });
          if (addressRes && addressRes.code === SUCCESS_CODE) {
            if (addressRes?.data) {
              formState.riskAddressInfoList = [addressRes.data];
              formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);
            } else {
              formState.riskAddressInfoList = [{ province: '', provinceName: '', city: '', cityName: '', town: '', townName: '', county: '', countyName: '', village: '', villageName: '', address: '' }];
            }
          }
        }
        updateRiskAddress.value = true;
      } catch (err) {
        console.log('获取地址失败', err);
      }
    }
  },
);
watch(
  () => formState.saleInfo.employeeInfoList,
  async () => {
    if (formState.saleInfo.employeeInfoList?.[0]?.employeeCode) {
      const res = await $getOnClient<SelectOptions[]>(`${gateWay}${service.accept}/saleInfo/getEmployeeDetail`, { employeeCode: formState.saleInfo.employeeInfoList?.[0]?.employeeCode });
      if (res && res?.code === SUCCESS_CODE) {
        formState.baseInfo.inputBy = res.data.umCode;
      } else {
        formState.baseInfo.inputBy = '';
      }
    }
  },
  {
    deep: true,
  },
);
// 选择渠道来源
const channelSourceList = ref<Array<SelectOptions>>([]);
const channelSourceDetailList = ref<Array<SelectOptions>>([]);
// 选择渠道来源
const changeChannelSource = (_: SelectValue, option: DefaultOptionType) => {
  formState.saleInfo.channelSourceCode = option?.value as string;
  channelSourceDetailList.value = option?.children.filter((item) => item.value.split(',')?.[1] === '1') as SelectOptions[];
};
// 回填渠道来源细分
watch(channelSourceDetailList, (val) => {
  // 渠道来源细分数组为空则清空选中项
  if (val?.length === 0) {
    formState.saleInfo.channelSourceDetailCode = '';
  } else {
    // 当前选中值在列表中存在则值不变，否则默认选第一项
    formState.saleInfo.channelSourceDetailCode = formState.saleInfo.channelSourceDetailCode && val.some((item) => item.value === formState.saleInfo.channelSourceDetailCode) ? formState.saleInfo.channelSourceDetailCode : val?.[0]?.value;
  }
});
// 初始数据
const getPayInfoList = (govSubsidyType: string) => {
  const defaultPayers = ['5', '6'];
  const subsidizedPayers = ['1', '2', '3', '4', '5', '6'];

  return (govSubsidyType === '3' ? defaultPayers : subsidizedPayers).map((type) => ({
    payerType: type,
    premiumFinance: '',
    paymentPersonName: '',
    otherPayerTypeDesc: '',
  }));
};
// 选择产品名称
const changeProduct = async (value: string, option: DefaultOptionType) => {
  formState.baseInfo.productCode = option?.value as string;
  formState.baseInfo.productName = option?.realLabel || '';
  // formState.baseInfo.govSubsidyType = option?.subsidyType || '';
  formState.baseInfo.productVersion = option?.version || '';
  //  5级标的有值，则不回显标的
  formState.baseInfo.riskCode = option?.targetType || '';
  formState.riskGroupInfoList[0].combinedProductCode = option?.targetType || '';
  defaultRisk.value = formState.baseInfo.riskCode;
  // formRef.value.validateFields([['baseInfo', 'riskCode']]);
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const data = {
    productCode: option?.value,
    version: option?.version,
  };
  const res = await $postOnClient<{ subsidyType: string }>(url, data);
  if (res && res.code === SUCCESS_CODE) {
    if (updatePayInfo.value) {
      formState.payInfoList = getPayInfoList(res.data.subsidyType);
      // 重置特约、费用、免赔
      formState.noclaimInfoList = [];
      formState.specialPromiseList = [];
      formState.baseInfo.govSubsidyType = res.data.subsidyType || '';
      const planInfoList = res.data?.planList.filter((item) => item?.isMain === '1');
      formState.riskGroupInfoList[0].planInfoList = planInfoList || [];
    }
    updatePayInfo.value = true;
  }
  getUnitData();
};
type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';
// 选择标的地址
const changeRiskAddress = async (option: { label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    formState.riskAddressInfoList[0][`${type}Name`] = option?.label || '';
  }

  // 拼接地址时过滤掉undefined/null值，避免出现NaN
  const addressParts = [formState.riskAddressInfoList[0]?.provinceName, formState.riskAddressInfoList[0]?.cityName, formState.riskAddressInfoList[0]?.countyName, formState.riskAddressInfoList[0]?.townName, formState.riskAddressInfoList[0]?.villageName].filter(Boolean);

  formState.riskAddressInfoList[0].address = addressParts.join('') || '';

  formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);

  if (['county'].includes(type)) {
    const url = gateWay + service.accept + '/policy/insuranceRegionValid';
    const res = await $postOnClient<{ subsidyType: string }>(url, formState);
    if (res && res.code !== SUCCESS_CODE) {
      message.info(res.msg || '');
    }
  }
};
// 修复出单机构校验问题
watch(
  () => formState.baseInfo.departmentCode,
  () => {
    formRef?.value?.validate([['baseInfo', 'departmentCode']]);
  },
);
watch(
  () => formState.saleInfo.isReportTuanjin,
  (val) => {
    if (val !== 'Y') {
      formState.saleInfo.businessOpportunitySource = '';
      formState.saleInfo.tuanjinProjectNo = '';
    }
  },
);
// 查询商机来源
const businessSourceList = ref<SelectOptions[]>([]);
const allBusinessSourceList = ref<SelectOptions[]>([]); // 存储全量商机来源（第一页），用来搜索后给下拉列表重新赋值
const searchBusinessLoading = ref(false);
watchEffect(async () => {
  if (formState.saleInfo.isReportTuanjin === 'Y' && formState.baseInfo.departmentCode) {
    searchBusinessLoading.value = true;
    try {
      const fetchUrl = gateWay + service.accept + '/saleInfo/queryBizSourceList';
      const res = await $getOnClient(fetchUrl);
      searchBusinessLoading.value = false;
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        businessSourceList.value = res.data;
        allBusinessSourceList.value = res.data;
      } else {
        businessSourceList.value = [];
        allBusinessSourceList.value = [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      searchBusinessLoading.value = false;
    }
  }
});

// 根据项目来源和机构编码，查询项目名称
const projectNameList = ref<Array<SelectOptions>>([]);
const allProjectNameList = ref<Array<SelectOptions>>([]); // 存储全量项目名称（第一页），用来搜索后给下拉列表重新赋值
const searchProjectLoading = ref(false);

watchEffect(async () => {
  if (formState.baseInfo.departmentCode && formState.extendInfo.isTenderBusiness !== '') {
    try {
      searchProjectLoading.value = true;
      const res = await $postOnClient('/api/accept/queryTenderProjectList', {
        departmentCode: formState.baseInfo.departmentCode,
        keyword: '',
        pageNum: 1,
        pageSize: 50,
        tenderSource: formState.extendInfo.isTenderBusiness,
      });
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        projectNameList.value = res.data;
        allProjectNameList.value = res.data;
      } else {
        projectNameList.value = [];
        allProjectNameList.value = [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      searchProjectLoading.value = false;
    }
  }
});
// 改变项目来源
const changeIsTenderBusiness = () => {
  formState.saleInfo.tenderBusinessNo = '';
};
// 根据关键字远程搜索项目名称
const handleSearchProject = async (keyword: string) => {
  if (keyword && formState.baseInfo.departmentCode && formState.saleInfo.isTenderBusiness !== '') {
    try {
      searchProjectLoading.value = true;
      const res = await $postOnClient('/api/accept/queryTenderProjectList', {
        departmentCode: formState.baseInfo.departmentCode,
        keyword,
        pageNum: 1,
        pageSize: 50,
        tenderSource: formState.saleInfo.isTenderBusiness,
      });
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        projectNameList.value = res.data;
      } else {
        projectNameList.value = [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      searchProjectLoading.value = false;
    }
  }
};
const debounceSearchProject = debounce(handleSearchProject, 500);

// 聚焦时项目名称的下拉选项重新赋值为全量项目名称（第一页）
const resetTenderProjectList = () => {
  projectNameList.value = JSON.parse(JSON.stringify(allProjectNameList.value));
};

// 选择项目名称
const changeTenderBusiness = (value: SelectValue, option: DefaultOptionType) => {
  formState.extendInfo.tenderBusinessNo = option?.value as string;
  formState.extendInfo.tenderBusinessName = option?.label || '';
};

// 选择共保后，将是否主承默认值改为‘1’
const changeCoinsuranceMark = (e: RadioChangeEvent) => {
  formState.baseInfo.acceptInsuranceFlag = e?.target?.value === '1' ? '1' : '';
};
// 标的地址校验
const validateAddress = (_rule: Rule, value: string) => {
  if (!formState.riskAddressInfoList[0].province) {
    return Promise.reject('请选择地址');
  } else if (!value) {
    return Promise.reject('请输入地址');
  }
  return Promise.resolve();
};

// 关闭当前页面
const closeCurrentPage = () => {
  router.push('/selfInsuranceQuery');
  deletPageTabListItem('/selfInsurance'); // 关闭页签
};

const costInfoRef = ref();
const disclaimRef = ref();
const feeInfoRef = ref();
const getValidateArr = () => {
  const validateItems = [costInfoRef.value?.validate(), disclaimRef.value?.validate(), feeInfoRef.value?.validate()];
  return validateItems;
};
const validateAllForms = async () => {
  const results = await Promise.all(getValidateArr());
  const isValid = results.every((result) => result.valid);
  if (isValid) {
    // 所有表单校验通过
    save();
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 暂存
const saveLoading = ref(false);
const save = async () => {
  if (!formState.baseInfo.departmentCode) {
    message.error('请选择出单机构');
  } else {
    saveLoading.value = true;
    try {
      const params = {
        ...formState,
        applicantInfoList: [
          {
            sameInsuredPersons: '1',
          },
        ],
      };
      if (route?.query?.templateId) {
        params.templateId = route.query.templateId;
      }
      const res = await $postOnClient(gateWay + service.accept + '/web/mobile/market/saveApplyTemplate', params);
      if (res?.code === SUCCESS_CODE) {
        message.success(res?.msg || '保存成功');
        closeCurrentPage();
      } else {
        message.error(res?.msg || '');
      }
    } catch (err) {
      console.log(err);
    } finally {
      saveLoading.value = false;
    }
  }
};

// 是否禁用
const isEdit = computed(() => {
  return route.query.type === 'look';
});
// 有投保申请单号则查询详情
const queryRiskDetail = async () => {
  const templateId = route.query.templateId;
  if (templateId) {
    pageLoading.value = true;
    try {
      const res = await $postOnClient<FormState>(gateWay + service.accept + '/web/mobile/market/queryProductTemplate', { templateId });
      if (res?.code !== SUCCESS_CODE || !res.data) {
        message.error(res?.msg || '查询详情失败，请稍后再试');
        return;
      }
      const { baseInfo, saleInfo, riskAddressInfoList, extendInfo, costInfo, riskGroupInfoList, noclaimInfoList, payInfoList, specialPromiseList } = res.data.contractVO;
      formState.baseInfo = baseInfo;
      assisterInfoList.value = formState?.baseInfo?.assisterInfoList?.map((item) => item.assisterId) || [''];
      formState.saleInfo = {
        ...saleInfo,
        agentInfoList: saleInfo.agentInfoList.length < 1 ? [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }] : saleInfo.agentInfoList,
      };
      formState.riskAddressInfoList = riskAddressInfoList || [{}];
      formState.extendInfo = extendInfo;
      formState.costInfo = costInfo;
      formState.riskGroupInfoList = riskGroupInfoList;
      formState.noclaimInfoList = noclaimInfoList;
      formState.specialPromiseList = specialPromiseList;
      formState.payInfoList = payInfoList;
      defaultRisk.value = baseInfo.riskCode;
      // 免赔数据处理
      if (formState.noclaimInfoList?.length > 0) {
        formState.noclaimInfoList = formState.noclaimInfoList.map((item: { noclaimItem: string; planCode: string; dutyCode: string; noclaimItemArr: string[] }) => {
          item.noclaimItemArr = [];
          if (item.noclaimItem) {
            item.noclaimItemArr.push(item.noclaimItem);
            if (item.planCode) {
              item.noclaimItemArr.push(item.planCode);
              if (item.dutyCode) {
                item.noclaimItemArr.push(item.dutyCode);
              }
            }
          }
          return item;
        });
      }
      updateCostInfo.value = false;
      updateRiskAddress.value = false;
      updatePayInfo.value = false;
    } catch (err) {
      console.log(err);
    } finally {
      pageLoading.value = false;
    }
  }
};
onMounted(() => {
  queryRiskDetail();
});
onActivated(() => {
  queryRiskDetail();
});
</script>

<style scoped lang="less">
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;
  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
