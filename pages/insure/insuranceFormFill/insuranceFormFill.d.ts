interface RiskAgrInfo {
  insuredNumber: string; // 保险数量
  insuredUnit: string; // 保险数量单位
  insuredUnitChName: string;
  insuredAmount: string; // 保险金额
  premium: string; // 保费金额
  farmersCount: string; // 参保农户数
  planAttributeSameMain: string; // 保费来源是否与主险一致
  reductionCoefficient: string; // 减免系数
  substitute: string; // 农户保费是否代缴 Y：是，N：否
  planConfigId: string; // 保险方案配置ID（保险方案配置表ID）
  fundingSource: string; // 代缴保费资金来源 '财政补贴'-'01','扶贫资金'-'02','社会捐赠'-'03','公司代缴'-'04','组合'-'05'
  riskAddressInfo: {
    province: string; // 标的地址-省
    city: string; // 标的地址-市
    county: string; // 标的地址-县
    town: string; // 标的地址-城镇
    village: string; // 标的地址-村
    address: string; // 完整地址
  };
  agrRiskAttribute: {
    bussinessType: string; // label
    bussinessKey: string; // keyword
    bussinessValue: string; // value
    notRequiredFlag: boolean; // 是否必填
    notShowFlag: boolean; // 是否显示
  }[];
}

export interface RiskGroupInfo {
  combinedProductCode: string; // 标的明细
  combinedProductName: string; // 标的名称
  riskAgrInfo: RiskAgrInfo;
  planInfoList: Array<PlanInfoListItem>;
}

export interface RiskAddressInfo {
  province: string; // 省
  city: string; // 市
  county: string; // 区
  town: string; // 镇
  village: string; // 村
  address: string; // 完整地址
}
// 费用信息页面
export interface CostInfoType {
  [key: string]: string;
  isPolicyBeforePayfee: string; // 财务标识,
  assisterCharge: string; // 协办费
  commissionBrokerChargeProportion: string; // 手续费/经纪费
  managementFees: string; // 工作经费
  calamitySecurityRate: string; // 防灾防损费
  coinsuranceInsureFeeRatio: string; // 共保出单费
  performanceValue1Default: string; // 农险补贴
  totalSumFeeLimit: string; //  string; // 总费用之和上限值
  assisterChargeLimit: string; // 协办费上限值
  commissionBrokerChargeProportionLimit: string; // 手续费/经纪费上限
  managementFeesLimit: string; // 工作经费上限-  // 管理费(业务员的工作经费比例
  calamitySecurityRateLimit: string; // 防灾防损费上限值
  performanceValue1DefaultLimit: string; // 农险补贴上限值
  coinsuranceInsureFeeRatioLimit: string; // 共保出单费上限值
}
// 免赔信息
export interface DisclaimInfo {
  noclaimItem: string; // 免赔项目
  noclaimItemArr: string[]; // 免赔项目
  planCode: string; // 险种编码
  dutyCode: string; // 责任编码
  noclaimType: string; // 免陪类别
  noclaimRate: string; // 免赔率
  noclaimAmount: string; // 免赔额
}
// 免赔信息-保险方案信息
export interface RiskGroupInfoType {
  agriculturalRiskObjectClassName: string;
  agriculturalRiskObjectClassCode: string;
  riskAgrInfo: Record<string, string>;
  planInfoList: Record<string, object[]>[];
  combinedProductName: string; // 标的名称
  combinedProductCode: string; // 标的明细
}

// 免赔信息-险种对象类型
export interface OptionsItemType {
  planCode?: string;
  planName?: string;
}
// 免赔信息-责任对象类型
export interface dutyItemType {
  dutyName?: string;
  dutyCode?: string;
}
// 免赔信息-责任对象类型
export interface OptionsType extends OptionsItemType {
  dutyInfoList?: Record<string, dutyItemType[]>[];
}
// 免赔信息-责任选项类型
export interface dutyOptionType {
  label: string;
  value: string;
}
// 免赔信息-责任选项类型
export interface PlantItemType extends dutyOptionType {
  children: dutyOptionType[];
}

// 收费计划
export interface payInfo {
  branchBankOptions: Record<string, string>[]; // 银行分支选项
  bankCode: string; // 银行分支code
  bankHeadquartersCode: string; // 银行总行code
  bankDetail: string; // 银行明细
  bankAccountNo: string; // 银行帐号
  termNo: string; // 期次
  payerType: string; // 补贴类型
  paymentPersonName: string; // 付款人名称
  paymentPath: string; // 收付途径
  actualPremium: string; // 应收保费
  paymentPeriod: [string, string]; // 缴费起止期
  paymentBeginDate: string; // 缴费起期
  paymentEndDate: string; // 缴费止期
  bankAttribute: string; // 账户类型
  certificateNo: string; // 身份证
  receivableDate: string; // 预计收回日期
  otherPayerTypeDesc: string; // 备注
  noclaimRate: string; // 免赔率
  noclaimAmount: string; // 免赔额
  typeDisabled?: boolean; // 是否新增行
  flag?: boolean; // 判断是否为新增行
}

// 协办员信息
export interface AssisterInfo {
  assisterId: string; // 协办员id
  idCardNo: string; // 身份证号
  umCode: string; // um号码
  assisterNameAndUm: string; // 协保员姓名
}
// 基本信息
export interface BaseInfo {
  assisterInfoList: Array<AssisterInfo>; // 协办员列表
  insuranceBeginDate: string; // 保险起期
  insuranceEndDate: string; // 保险止期
  disputedSettleMode: string; // 争议处理方式 1:诉讼 2: 仲裁
  arbitralDepartment?: string; // 仲裁机构
  shortTimeCoefficient: string | number; // 年限系数
  timeRange: string; // 起止时间
  departmentCode: string; // 机构编码
  applyPolicyNo: string; // 保单号
  totalActualPremium: string; // 整单总保费
  deductionDesc: string; // 免赔描述
  premiumOwner: string; // 责任人
  coinsuranceMark: string; // 共保标志
  insuranceEndDate: string; // 保险止期
  govSubsidyType: string; // 补贴类型
  acceptInsuranceFlag: string; // 是否主承
  productCode: string; // 产品编码
}

// 基本信息-》倒签签报内容
export interface BackSignContent {
  relationBusinessNo?: string; // 签报编号
  eoaSubject?: string; // 事项名称
  riskName?: string; // 涉及险种
  projectFee?: string; // 预估总保费
  date?: [string, string]; // 保险起期范围
  eoaBody?: string; // 保险起期前置原因
  eoaNo?: string; // 签报编号
}

// 共保信息-》内部共保签报内容
export interface CoinsuranceContent {
  relationBusinessNo?: string; // 签报编号
  eoaSubject?: string; // 事项名称
  insuranceType?: string; // 涉及险种
  projectFee?: string; // 预估总保费
  date?: [string, string]; // 保险起期范围
  eoaBody?: string; // 保险起期前置原因
  checked?: boolean; // 逐单签报
}

export interface SpecialPromiseItemx {
  num: string;
  type: string;
  value: string;
}
export interface SpecialPromise {
  // 特约信息
  promiseCode?: string;
  promiseDesc?: string;
  promiseType?: string;
  promiseControlList?: SpecialPromiseItemx[];
  sortIndex?: string;
}

// 费用信息页面-基础信息
export interface BaseInfoType {
  // 农保id
  assisterInfoList: string[];
  // 是否共保-0非-1是
  coinsuranceMark: string;
  // 政策类型
  govSubsidyType: string;
  // 保单号
  applyPolicyNo: string;
  // 机构编码
  departmentCode: string;
  // 总保费
  totalActualPremium: string;
  acceptInsuranceFlag: string; // 是否主承
  productCode: string; // 产品编码
  productName?: string; // 产品名称
}
// 费用信息页面-是否主承
interface ListType {
  acceptInsuranceFlag: string;
}
// 费用信息页面-共保信息
export interface CoinsuranceInfoType {
  // 共保类型-0系统外共保-1系统内共保-2系统内外共保
  innerCoinsuranceMark: string;
  // 共保列表
  coinsuranceDetailList: ListType[];
}
// 发起公示
export interface PublicityData {
  [key: string]: string | boolean;
  publicityType: string;
  publicityDays: string;
  beginDate: string;
  endDate: string;
  requiredFlag: string;
  checked: boolean;
  publicityMethodDesc: string;
}
export interface PublicityInfoType {
  reqId: string;
  code?: string;
  msg?: string;
  data?: PublicityData[];
}
// 投保告知
export interface InsuranceNoticeType {
  informSignType?: string;
  directFlag?: boolean;
}
// 责任
export interface DutyInfoListItem {
  dutyCode: string; // 责任编码
  dutyName: string; // 责任名称
}

// 方案
export interface PlanInfoListItem {
  expectPremiumRate?: string; // 费率
  isMain?: string; // 是否主险,是否主险 1-主险 0-附加险
  planCode?: string; // 险种编码
  planEnglishName?: string; // 英文险种名称
  planName?: string; // 险种名称
  unitInsuredAmount?: string; // 单位保险金额
  unitPrimium?: string; // 单位保费
  centralFinance?: string; // 中央来源比例
  cityFinance?: string; // 市财政来源
  countyFinance?: string; // 区县财政来源
  farmersFinance?: string; // 农户来源
  otherFinance?: string; // 其他来源
  provincialFinance?: string; // 省财政来源
  totalActualPremium?: string; // 实交保费合计,保险方案页面叫做 复核保费
  totalAgreePremium?: string; // 应交保费合计,保险方案页面叫做 减免后保费金额
  totalInsuredAmount?: string; // 保单保险金额,保险方案页面叫做 保险金额
  totalStandardPremium?: string; // 减免前保单保费金额,保险方案页面叫做 基准保费金额
  eachCompensationMaxAmount?: string; // 每次赔偿限额
  feesInfoSameFlag?: string; // 附加险是否与主险一致
  dutyInfoList?: Array<DutyInfoListItem>; // 责任list
}
// 方案配置信息
export interface PlanRiskAgrInfo {
  farmersCount: string; // 农险相关参数：参保户数
  fundingSource: string; // 代缴保费资金来源 【fundingSource】 '财政补贴'-'01','扶贫资金'-'02','社会捐赠'-'03','公司代缴'-'04','组合'-'05'
  insuredNumber: string; // 保险数量
  planAttributeSameMain: string; // 保费来源是否与主线一致
  reductionCoefficient: string; // 减免系数
  substitute: string; // 农户保费是否代缴 Y：是，N：否
  planConfigId?: string; // 保险方案配置ID（保险方案配置表ID）
}
// 保险方案详情
export interface InsurancePlanDetail {
  insuranceProposalName: string; // 保险方案名称
  marketProductName: string; // 产品名称
  departmentName: string; // 机构名称
  planAttributeSameMain: string; // 是否与主险一致
  planList: Array<PlanInfoListItem>; // 方案产品数据
  id: string; // 方案id
  fileInfos: Array<FileInfo>; // 文件列表
}

// 保额总计
export interface SumRiskGroupAmount {
  totalInsuredAmount: string; // 合计保单保险金额
  totalStandardPremium: string; // 合计减免前保单保费金额
  totalAgreePremium: string; // 合计应交保费
  totalActualPremium: string; // 合计实交保费
}

// 文件
export interface FileInfo {
  fileName: string; // 文件名称
  fileType: string; // 文件类型
  fileKey: string; // 文件id
}

// 附加险下拉选项
export interface PlanInfo {
  planName: string; // 险种名称
  planCode: string; // 险种id
  dutyInfoList?: Array<DutyInfoListItem>; // 责任列表
}

// 共展业务员
export interface employeeInfo {
  calamitySecurityRate: string;
  coDevelopDeptCode: string;
  commisionScale: string;
  employeeCode: string; // 业务员code
  employeeName: string; // 业务员名称
  mainEmployeeFlag: string; // 是否为主业务员
  managementFees: string;
  saleInfoId: string;
  signSaleFlag: string;
  spWorkRate: string;
}

export interface SaleInfo {
  developFlg?: string; // 是否共展 Y是 N 否
  departmentCode?: string; // 机构编码
  departmentName?: string; // 机构名称
  baseInfoId?: string; // 基本信息id
  businessSourceCode?: string;
  businessSourceDetailCode?: string;
  channelSourceCode?: string;
  channelSourceDetailCode?: string;
  employeeInfoList?: Array<employeeInfo>; // 共展业务员列表
  preRiskEmployeeInfoList?: Array<employeeInfo>; // 预设共展业务员列表
}

// 签报eoadata
export interface EoaData {
  documentGroupId: string;
  eoaTitle?: string; // 签报主题
  eoaContent?: string; // 签报内容
  fileInfos?: { fileKey: string; fileName: string; fileType: string }[]; // 签报附件
  approvalChain?: string; // 跳转eoa审批链
  templateChainKey?: string; // 跳转eoa审批链key
  approveChainList: EoaChainsType[]; // 审批链
}
interface EoaChainsType {
  flowOwnerNameList: string[];
  handType: string;
  flowOwnerTypeList: string[] | OptionType[];
  seq: number;
  flowOwnerOptions: OptionType[][];
  flowOwner: (OptionType | null)[];
}
// 审批人选项
interface OptionType {
  um: string;
  name: string;
  option?: OptionType;
}

// 提交核保返回校验信息
export interface ExceptionPayload {
  exceptionPayload: {
    signDataList: Record<string, string>[];
    formPayloadList: FormPayload[];
  };
}

export interface FormPayload {
  code: string; // 提示code
  msg: string; // 提示信息
  type: string; // 提示类型
}
interface ExtendsType {
  showWindowType: string;
  showReInformWindowFlag: string;
}
export interface SignData {
  signType: string; // 签报类型
  signDesc: string; // 签报类型说明
  signStatus: string; // 签报状态
  signStatusDesc: string; // 签报状态说明
  eoaNo: string; // 签报号
  eoaDetailUrl: string; // 签报详情url
}

interface PayloadType {
  relatedTransactionFlag: boolean;
  relatedTransactionMsg: string;
}
export interface RelatedTransaction {
  payload: PayloadType;
}

export interface RuleData {
  signDataList: Record<string, string>[];
  relatedTransactionFlag: boolean;
}

export interface PublicityStatusType {
  publicityMethodCode: string; // 公示状态
  publicityMethodDesc: string; // 公示状态描叙
  publicityStatusCode: string; // 公示方式
  publicityStatusDesc: string; // 公示方式描叙
}

export enum PublicStyleCode {
  ZBX = 'ZBX', // 中保信公示状态
  LOCAL_PF = 'LOCAL_PF', // 地方平台公示状态：
  PA_ONLINE = 'PA_ONLINE', // 爱农宝线上公示状态：
  PA_OFFLINE = 'PA_OFFLINE', // 我司线下公示状态：
}
