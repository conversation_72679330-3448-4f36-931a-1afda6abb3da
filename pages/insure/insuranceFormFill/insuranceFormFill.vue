<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <!-- 表单录入 -->
      <!-- <a-spin :spinning="loading"> -->
      <div class="flex-1 bg-white border-right-line">
        <!-- <div class="h-[76px] w-ful bg-image-url"> -->
        <div class="h-[76px] w-ful bg-[url('@/assets/images/content-head.png')]">
          <span class="text-[24px] text-[#00190c] leading-[76px] ml-[16px]">投保单信息</span>
        </div>
        <a-spin :spinning="loading">
          <a-form :colon="false">
            <div class="p-16px space-y-8px">
              <InfoGroup id="policy-info" title="投保单信息">
                <div class="policy-info">
                  <div class="flex justify-between">
                    <div class="flex-1 text-[#333333] text-[14px] flex items-end">
                      <div class="flex flex-wrap">
                        <span>投保单号：{{ route.query.applyPolicyNo }}</span>
                        <span class="mx-[24px]">产品名称：{{ contractModel.baseInfo?.productName }}</span>
                        <span>验标任务号：{{ contractModel.extendInfo?.insrCheckNo }}</span>
                      </div>
                    </div>
                    <div class="flex-shrink-0 ml-[24px] space-x-8px">
                      <a-button type="primary" @click="goToLink">添加清单</a-button>
                      <a-button v-if="hasLink" type="primary" @click="editList">修改清单</a-button>
                    </div>
                  </div>
                  <div v-if="hasLink" class="text-[#333333] text-[14px]">
                    <div class="space-x-[36px]">
                      <span>清单编号：{{ contractModel.baseInfo?.farmerlistNo }}</span>
                      <span>导入户数：{{ contractModel.baseInfo?.farmerlistFarmerNum }}户</span>
                      <span>承保数量：{{ contractModel.baseInfo?.farmerlistRiskNum }} {{ contractModel.baseInfo?.unitName }}</span>
                      <span>验真率：{{ contractModel.baseInfo?.verifyRate }}%</span>
                      <span v-if="contractModel?.insurantInfoList?.[0]?.personnelType === '1'">
                        爱农宝核身率：{{ voucherSummaryInfo?.customerVerifyRate }}%
                        <a-tooltip v-if="identifySignData" placement="right">
                          <template #title>
                            <span>{{ statusItem.tip }}</span>
                          </template>
                          <span :class="identifyStyle(statusItem)" @click="handleJumpEoaDetailOrApproval()">
                            <VueIcon v-if="statusItem.icon" class="text-[12px] mr-[3px]" :icon="statusItem.icon" />
                            <img v-else-if="statusItem.svg" class="mr-[3px] translate-y-[2px]" :src="statusItem.svg" />
                            <span>{{ statusItem.label }}</span>
                          </span>
                        </a-tooltip>
                        <a-button v-if="['-1', '3'].includes(identifySignData?.signStatus)" :loading="loadingReStart" type="link" class="re-start-btn" @click="handleJumpEoaDetailOrApproval('reStart')">重新发起</a-button>
                      </span>
                    </div>
                    <div v-if="voucherSummaryInfo.farmerInsureDupNumber && voucherSummaryInfo.farmerInsureDupNumber > 0" class="text-[#F03E3E]">
                      <VueIcon class="text-[14px] text-[#F03E3E] mr-[3px]" :icon="IconErrorCircleFilledFont" />
                      分户清单存在 {{ voucherSummaryInfo.farmerInsureDupNumber }} 名被保险人 {{ voucherSummaryInfo.marketProductName }}险种 {{ voucherSummaryInfo.insuranceBeginDate + '至' + voucherSummaryInfo.insuranceEndDate }} 保险期间内重叠，重复率 {{ voucherSummaryInfo.farmerInsureDupRadio }} %，可能存在重复投保，可点击“ <span class="underline cursor-pointer" @click="downloadInfo">下载重复信息</span>”
                    </div>
                  </div>
                  <div v-else class="text-[#F03E3E]">
                    <VueIcon class="text-[14px] text-[#F03E3E] mr-[3px]" :icon="IconErrorCircleFilledFont" />
                    未添加清单，请先添加清单再操作!
                  </div>
                  <!-- 风险评估编号 -->
                  <div v-if="contractModel?.baseInfo?.riskAsmtNo">
                    <span>风险评估编号：</span>
                    <span class="text-[#576B95] cursor-pointer font-number" @click="goRiskAssessDetail">{{ contractModel?.baseInfo?.riskAsmtNo }}</span>
                  </div>
                  <!-- 公示状态 -->
                  <div class="text-[14px] space-x-[36px]">
                    <span v-for="(item, index) in publicityList" :key="index">
                      <span>{{ publicStyleMap[item.publicityMethodCode as PublicStyleCode] }}</span>
                      <span :class="`text-[${publicityStatusMap[item.publicityStatusCode]?.color}]`">
                        <a-tooltip placement="bottomLeft" color="#fff" :overlay-inner-style="{ width: '500px' }">
                          <VueIcon class="mr-[3px]" :icon="publicityStatusMap[item.publicityStatusCode]?.icon" />
                          <template #title>
                            <div class="text-[#333333] p-[10px]">
                              <div class="mb-[17px]">修改如下信息需要重新公示，请谨慎操作</div>
                              <div class="form-title"><span class="text-[rgba(0,0,0,0.60)] text-[15px]">中银保信公示</span></div>
                              <div>投保单：投保人[姓名、证件号、手机号]，被保险人[名称、地址]，标的地址[省、市、区、镇、村、地址明细]，保险起期、止期，险种单位保额，投保标的单位</div>
                              <div class="mb-[16px]">农户清单：农户、证件号、手机号、银行卡号、投保数量</div>
                              <div class="form-title"><span class="text-[rgba(0,0,0,0.60)] text-[15px]">爱农宝公示</span></div>
                              <div>投保单：投保人[姓名]，组织者[姓名]，五级标的、投保险种[编码、单位保额、费率、单位保费、保费各来源比例]</div>
                              <div>农户清单：农户、证件号、手机号、银行卡号、投保数量</div>
                            </div>
                          </template>
                        </a-tooltip>
                        <span>{{ item.publicityStatusDesc }}</span>
                        <!-- <span class="ml-[12px]">原因：修改了投保单**字段，需要重新发起公示，请注意</span> -->
                      </span>
                    </span>
                  </div>
                </div>
              </InfoGroup>
              <InfoGroup id="basic-info" title="基础信息">
                <BaseInfo ref="baseInfoRef" v-model:data="contractModel.baseInfo" v-model:address-list="contractModel.riskAddressInfoList" :risk-group-info-list="contractModel.riskGroupInfoList" :sign-data-list="signDataList" :allow-insurance-date-past-flag="allowInsuranceDatePastFlag" :is-accept-insurance="isAcceptInsurance" @get-eoa-data="getEoaData" />
              </InfoGroup>
              <InfoGroup id="customer-info" title="客户信息">
                <CustomerInfo
                  ref="customerInfoRef"
                  v-model:organizer-info="contractModel.organizerInfo"
                  v-model:insurant-info-list="contractModel.insurantInfoList"
                  v-model:applicant-info-list="contractModel.applicantInfoList"
                  v-model:beneficary-info-list="contractModel.beneficaryInfoList"
                  class="customer-info"
                  :insurance-way="contractModel?.baseInfo?.applyPolicyType"
                  :insurant-filed-disabled="insurantFiledDisabledComputed"
                  :is-anti-money="isAntiMoney"
                  :show-related-approval="showRelatedApproval"
                  :total-actual-premium="contractModel?.sumRiskGroupAmount?.totalActualPremium"
                  :apply-policy-no="contractModel?.baseInfo?.applyPolicyNo"
                  :combined-product-name="contractModel?.riskGroupInfoList?.[0]?.combinedProductName"
                  :insure-type="contractModel?.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode"
                  :product-name="contractModel?.baseInfo?.productName"
                  :product-code="contractModel?.baseInfo?.productCode"
                  :department-code="contractModel?.baseInfo?.departmentCode"
                  :sec-dept-code="contractModel?.baseInfo?.secDeptCode"
                  :sign-data-list="signDataList"
                  @get-eoa-data="getEoaData"
                />
              </InfoGroup>
              <InfoGroup id="target-info" title="标的信息">
                <PlantRisk ref="riskFormRef" v-model:data="contractModel.riskGroupInfoList" v-model:address-list="contractModel.riskAddressInfoList" :department-code="contractModel.baseInfo?.departmentCode" :product-code="contractModel.baseInfo?.productCode" :product-version="contractModel.baseInfo?.productVersion" :init-risk-code="initRiskCode" @change-amount="changeAmount" />
              </InfoGroup>
              <InfoGroup id="insurance-plan" title="保险方案">
                <InsurancePlan ref="planFormRef" v-model:data="contractModel.riskGroupInfoList" :product-code="contractModel.baseInfo?.productCode" :product-version="contractModel.baseInfo?.productVersion" :department-code="contractModel.baseInfo?.departmentCode" :short-time-coefficient="contractModel.baseInfo.shortTimeCoefficient" :gov-subsidy-type="contractModel.baseInfo.govSubsidyType" :sum-risk-group-amount="contractModel.sumRiskGroupAmount" :insured-number="contractModel.baseInfo?.insuredNumber" :farmers-count="contractModel.baseInfo?.farmersCount" :insured-unit="contractModel.riskGroupInfoList?.[0]?.riskAgrInfo?.insuredUnit" />
              </InfoGroup>
              <InfoGroup v-if="contractModel.payInfoList.length > 0" id="fee-plan" title="收费计划">
                <FeePlan ref="payInfoRef" v-model:pay-info-list="contractModel.payInfoList" v-model:base-info="contractModel.baseInfo" v-model:payer-type-sum-of-pay-info="contractModel.payerTypeSumOfPayInfo" :inner-coinsurance-mark="contractModel.coinsuranceInfo?.innerCoinsuranceMark" :combined-product-code="contractModel.riskGroupInfoList?.[0]?.combinedProductCode" />
              </InfoGroup>
              <InfoGroup id="disclaimers-info" title="免赔信息">
                <DisclaimInfo ref="disclaimRef" v-model:disclaim-info="contractModel.noclaimInfoList" v-model:base-info="contractModel.baseInfo" :risk-group-info-list="contractModel.riskGroupInfoList" :sum-risk-group-amount="contractModel.sumRiskGroupAmount" />
              </InfoGroup>
              <InfoGroup id="special-appoint" title="特别约定">
                <SpecialSet v-model="contractModel.specialPromiseList" :department-code="contractModel.baseInfo?.departmentCode" :market-product-code="contractModel.baseInfo?.productCode" />
              </InfoGroup>
              <InfoGroup id="fee-info" title="费用信息">
                <CostInfo ref="feeInfoRef" v-model:cost-info="contractModel.costInfo" :base-info="contractModel.baseInfo" :coinsurance-info="contractModel.coinsuranceInfo" :business-source-code="contractModel.saleInfo?.businessSourceCode" :sign-data-list="signDataList" :combined-product-code="contractModel.riskGroupInfoList?.[0]?.combinedProductCode" :total-actual-premium="contractModel?.payerTypeSumOfPayInfo?.payerType5" :is-accept-insurance="isAcceptInsurance" @get-eoa-data="getEoaData" />
              </InfoGroup>
              <InfoGroup v-if="contractModel.baseInfo.coinsuranceMark === '1'" id="co-insurance" title="共保信息">
                <CoInsuranceInfo ref="coInsuranceRef" v-model="contractModel.coinsuranceInfo" :department-code="contractModel.baseInfo.departmentCode" :accept-insurance-flag="contractModel.baseInfo.acceptInsuranceFlag" :total-actual-premium="contractModel.baseInfo?.totalActualPremium" :apply-policy-no="contractModel.baseInfo?.applyPolicyNo" :insurance-begin-date="contractModel.baseInfo?.insuranceBeginDate" :product-code="contractModel.baseInfo?.productCode" :sale-info="contractModel.saleInfo" :sign-data-list="signDataList" @get-eoa-data="getEoaData" />
              </InfoGroup>
            </div>
          </a-form>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 发起公示弹窗 -->
    <PublicityInfo ref="publicInfoRef" v-model:visible="publicVisible" v-model:public-loading="publicLoading" v-model:list-data="listData" @handle-public-ok="handlePublicOk" />
    <!-- 投保告知 -->
    <InsuranceInform v-model:visible="informVisible" v-model:extend-info="contractModel.extendInfo" v-model:inform-loading="informLoading" :show-window-type="showWindowTypes" @handle-get-inform-ok="handleGetInformOk" />
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" @click="goLastStep">上一步</a-button>
      <a-button type="primary" @click="saveData(true)">保存</a-button>
      <a-button type="primary" @click="imageUpload">影像上传</a-button>
      <a-button type="primary" @click="validateAllForms">保费计算</a-button>
      <a-button type="primary" @click="submitCheck">去验标</a-button>
      <a-button v-if="!isAcceptInsurance" type="primary" @click="informChange">投保告知</a-button>
      <a-button type="primary" @click="publicChange">发起公示</a-button>
      <a-button type="primary" @click="submitApproval">提交核保</a-button>
    </footer>
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="openVisible" :width="pxToRem(450)" :ok-text="openType === '7' ? '申请豁免签报' : '确认'" @ok="handleConfirmOk" @cancel="handleConfirmCancel">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">{{ openType === '7' ? '爱农宝核身率豁免申请' : '提醒' }}</span>
      </div>
      <div class="text-[14px] text-[#333333] mb-[10px]" v-html="confirmText" />
    </a-modal>
    <!-- 验标弹窗 -->
    <a-modal v-model:open="resultVisible" :closable="false" :width="pxToRem(400)">
      <div class="flex flex-col items-center">
        <VueIcon :icon="IconCheckCircleFilledFont" class="text-[40px] text-[#07C160]" />
        <div class="mt-[18px] break-all">
          {{ '请前往移动端完成验标操作，验标任务号:' + contractModel.extendInfo?.insrCheckNo }}
          <a-tooltip placement="top">
            <template #title>
              <span>复制</span>
            </template>
            <VueIcon :icon="IconFuzhiFont" @click="copyText(contractModel.extendInfo?.insrCheckNo)" />
          </a-tooltip>
        </div>
      </div>
      <template #footer>
        <a-button type="primary" @click="resultVisible = false">我知道了</a-button>
        <!-- <a-button type="primary" @click="goSubmitCheck">去pc验标</a-button> -->
      </template>
    </a-modal>
    <!-- 错误信息弹窗 勿删 -->
    <!-- <a-modal v-model:open="errorVisible" :width="pxToRem(480)" :centered="true" :style="{ 'max-height': pxToRem(300), 'overflow': 'auto' }" @ok="errorVisible = false">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#f03e3e]" :icon="IconCloseCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">温馨提示</span>
      </div>
      <div v-for="(item, i) in confirmErrText" :key="i"> {{ item }} </div>
      <template #footer>
        <a-space>
          <a-button type="primary" @click="errorVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal> -->
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="showToRefund" :closable="false" :width="pxToRem(450)" ok-text="去退费" :keyboard="false" :mask-closable="false" @ok="handleToRefund" @cancel="showToRefund = false">
      <div class="text-[14px] text-[#333333] mb-[10px]" v-html="refundText" />
    </a-modal>
    <CommonApprovalModal v-if="showIdentityApproval" v-model:open="showIdentityApproval" title="爱农宝核身率豁免申请" eoa-type="T22" :department-code="contractModel?.baseInfo?.departmentCode" :total-actual-premium="contractModel?.sumRiskGroupAmount?.totalActualPremium" :extern-params="approvalExternParams" @ok="submitIdentityApproval">
      <template #content>
        <a-form ref="identifyRateRef" :model="identifyRateForm" :colon="false">
          <a-form-item label="签报主题" name="eoaTitle" required :label-col="{ style: { width: '130px' } }">
            <a-input v-model:value="identifyRateForm.eoaTitle" placeholder="请输入" />
          </a-form-item>
          <a-form-item label="签报内容" name="eoaContent" :label-col="{ style: { width: '130px' } }">
            <a-textarea v-model:value="identifyRateForm.eoaContent" autosize disabled />
          </a-form-item>
          <a-form-item label="申请原因" name="eoaReason" required :label-col="{ style: { width: '130px' } }">
            <a-input v-model:value="identifyRateForm.eoaReason" :maxlength="200" show-count />
          </a-form-item>
        </a-form>
      </template>
    </CommonApprovalModal>
  </div>
</template>

<script setup lang="ts">
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { IconErrorCircleFilledFont, IconCheckCircleFilledFont, IconFuzhiFont } from '@pafe/icons-icore-agr-an';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import BaseInfo from './components/BaseInfo.vue';
import CustomerInfo from './components/customerInfo/CustomerInfo.vue';
import PlantRisk from './components/PlantRisk.vue';
import InsurancePlan from './components/InsurancePlan.vue';
import FeePlan from './components/FeePlan.vue';
import DisclaimInfo from './components/DisclaimInfo.vue';
import SpecialSet from './components/specialSet.vue';
import PublicityInfo from './components/PublicityInfo.vue';
import InsuranceInform from './components/InsuranceInform.vue';
import type { PublicityInfoType, PublicityData, ExceptionPayload, FormPayload, ExtendsType, SignData, RuleData, EoaData, PublicityStatusType, PublicStyleCode } from './insuranceFormFill';
import type { CustomerInfo as TypeCustomerInfo } from './components/customerInfo/customerInfo';
// 共保模块组件
import CoInsuranceInfo from './components/coinsuranceInfo/CoInsuranceInfo.vue';
import CostInfo from './components/CostInfo.vue';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { pxToRem, copyText, downloadBlob, generateUUID } from '@/utils/tools';
import { $getOnClient, $postOnClient, $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { useUserStore } from '@/stores/useUserStore';
import infoCircle from '@/assets/images/info-circle.svg';

const REFUND_CODE = 'B0B0015'; // 去退费码

const { gateWay, service } = useRuntimeConfig().public || {};
const { userInfo } = useUserStore();
const publicVisible = ref<boolean>(false); // 发起公示弹窗
const informVisible = ref<boolean>(false); // 投保告知弹窗
const scrollWrapper = ref();
const getContainer = () => scrollWrapper.value || window;
// 关闭页签
const { deletPageTabListItem, pageTabList } = inject('pageTab');
// 二次确认弹窗描述
const confirmText = ref<string>('');
// 二次确认弹窗显示隐藏
const openVisible = ref<boolean>(false);
// 验标弹窗
const resultVisible = ref<boolean>(false);
// 二次确认弹窗共用type：1-投保告知，2-修改清单，3-添加清单，4-发起公示二次确认，5-关联方发起签报提示 6-疑似个人二次确认提示 7-爱农宝核身率发起签报
const openType = ref<string>('');
// 错误信息弹窗显示隐藏
// const errorVisible = ref<boolean>(false);
// 错误信息描述
// const confirmErrText = ref<string[]>([]);
// 核保校验数据
const formPayloadList = ref<FormPayload[]>([]);
// 是否触发反洗钱
const isAntiMoney = ref<boolean>(false);
// 签报数据
const signDataList = ref<SignData[]>([]);
// 爱农宝核身率豁免签报ref
const identifyRateRef = ref();
// 爱农宝核身率豁免签报表单ref
const identifyRateForm = ref({
  eoaTitle: '',
  eoaContent: '',
  eoaReason: '',
  fontBody: '',
  endBody: '',
  reasonBody: '',
});
// 爱农宝核身率签报弹窗开关
const showIdentityApproval = ref<boolean>(false);
const route = useRoute();
const router = useRouter();

const initRiskCode = ref('');

// 公示状态
const publicityStatusMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
  '0': { color: '#E6AD1C', text: '待公示', icon: IconErrorCircleFilledFont },
  '1': { color: '#576B95', text: '公示中', icon: IconErrorCircleFilledFont },
  '2': { color: '#07C160', text: '公示完成', icon: IconCheckCircleFilledFont },
  '5': { color: '#FF5B00', text: '需重新公示', icon: IconErrorCircleFilledFont },
};

// 公示方式
const publicStyleMap: { [key in PublicStyleCode]: string } = {
  ZBX: '中保信公示状态：',
  LOCAL_PF: '地方平台公示状态：',
  PA_ONLINE: '爱农宝线上公示状态：',
  PA_OFFLINE: '我司线下公示状态：',
};

// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#policy-info', title: '保单信息' },
  { key: '2', href: '#basic-info', title: '基础信息' },
  { key: '3', href: '#customer-info', title: '客户信息' },
  { key: '4', href: '#target-info', title: '标的信息' },
  { key: '5', href: '#insurance-plan', title: '保险方案' },
  { key: '6', href: '#fee-plan', title: '收费计划' },
  { key: '7', href: '#disclaimers-info', title: '免赔信息' },
  { key: '8', href: '#special-appoint', title: '特别约定' },
  { key: '9', href: '#fee-info', title: '费用信息' },
]);

// 爱农宝核身率签报
const identifySignMsgMap = {
  '-1': { label: '豁免失败', color: '#FF5B00', bg: '#FFEEE5', icon: IconErrorCircleFilledFont, tip: '豁免审批退回，点击可跳转eoa签报' },
  '1': { label: '豁免审批中', color: '#576B95', bg: '#EAF1FF', svg: infoCircle, tip: '豁免审批中，点击可跳转eoa签报' },
  '2': { label: '豁免成功', color: '#07C160', bg: '#F2FFF9', icon: IconCheckCircleFilledFont, tip: '豁免审成功，点击可跳转eoa签报' },
  '3': { label: '豁免失败', color: '#FF5B00', bg: '#FFEEE5', icon: IconErrorCircleFilledFont, tip: '豁免审批退回，点击可跳转eoa签报' },
};

const identifyStyle = (statusItem: { color: string; bg: string }) => {
  return `text-[${statusItem.color}] rounded-[12px] text-[12px] px-[10px] py-[4px] cursor-pointer bg-[${statusItem.bg}]`;
};

// 爱农宝核身率豁免签报信息
const identifySignData = computed(() => {
  return signDataList?.value.filter((item) => item?.signType === '039')?.[0];
});
const statusItem = computed(() => identifySignMsgMap?.[identifySignData.value?.signStatus as '-1']);

const initContractModel = (data) => {
  return {
    ...data,
    baseInfo: data.baseInfo || {}, // 基础信息
    extendInfo: data.extendInfo || {},
    organizerInfo: data.organizerInfo || {}, // 组织者
    applicantInfoList: data.applicantInfoList || [], // 投保人
    insurantInfoList: data.insurantInfoList || [], // 被保人
    beneficaryInfoList: data.beneficaryInfoList || [], // 收益人
    riskAddressInfoList: data.riskAddressInfoList || [], // 标的
    riskGroupInfoList: data.riskGroupInfoList || [], // 标的
    sumRiskGroupAmount: data.sumRiskGroupAmount || {}, // 保费计划合计
    coinsuranceInfo: data.coinsuranceInfo || {}, // 共保
    noclaimInfoList: data.noclaimInfoList || [], // 免赔
    payInfoList: data.payInfoList || [], // 收费计划
    costInfo: data.costInfo || {}, // 费用信息
    specialPromiseList: data.specialPromiseList || [], // 特约
    saleInfo: data.saleInfo || {},
    payerTypeSumOfPayInfo: data.payerTypeSumOfPayInfo || {}, // 收费按类别汇总用于校验
  };
};

// !!!类型晚点补
const contractModel = ref(initContractModel({}));

const loading = ref(false);
// 是否需要置灰被保人名称、证件类型、证件号码、联系电话(用于清单关联回显)
// const insurantFiledDisabledComputed = computed(() => contractModel.value.baseInfo.farmerlistNo?.length > 0 && contractModel.value.baseInfo.farmersCount === 1);
const insurantFiledDisabledComputed = false;

const customerInfoRef = ref();
const riskFormRef = ref();
const baseInfoRef = ref();
const disclaimRef = ref();
const payInfoRef = ref();
const feeInfoRef = ref();
const planFormRef = ref();
const coInsuranceRef = ref();
const getValidateArr = (validatePayInfo: boolean, isSubmitApproval?: boolean) => {
  const validateItems = [
    riskFormRef.value?.validate(),
    baseInfoRef.value?.validate(),
    customerInfoRef.value?.validate(),
    planFormRef.value?.validate(),
    disclaimRef.value?.validate(),
    feeInfoRef.value?.validate(),
    // 其他子组件的校验方法
  ];
  if (payInfoRef.value && validatePayInfo) {
    // 保费计算和提交核保
    if (isSubmitApproval) {
      // 提交核保时，收费计划校验
      validateItems.push(payInfoRef.value?.validate());
    } else {
      // 保费计算时，收费计划校验
      validateItems.push(payInfoRef.value?.validateCal());
    }
  }
  // 暂时注释
  if (coInsuranceRef?.value) {
    validateItems.push(coInsuranceRef.value?.validate());
  }
  return validateItems;
};
const clearValidateArr = async () => {
  const validateItems = [riskFormRef.value?.clearValidate(), baseInfoRef.value?.clearValidate(), customerInfoRef.value?.clearValidate(), planFormRef.value?.clearValidate()];
  await Promise.all(validateItems);
};
const validateAllForms = async () => {
  console.log(contractModel, 'contractModel');
  const results = await Promise.all(getValidateArr(true));
  const isValid = results.every((result) => result.valid);
  if (isValid) {
    // 所有表单校验通过
    premiumCompute();
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 林业险碳汇触发自动补全金额
const changeAmount = (value: number) => {
  planFormRef?.value.changeAmount(value);
};

// 提交时投保人模块的值
const transCustomerInfo = (customerData: Array<TypeCustomerInfo>, insurantData: Array<TypeCustomerInfo>) => {
  // 如果sameInsuredPersons为1，代表和被保险人数据一致，需要把被保人的数据填充并返回
  const isSameInsuredPersons = customerData?.[0]?.sameInsuredPersons !== '0';
  const sourceData = isSameInsuredPersons ? { ...insurantData?.[0], sameInsuredPersons: '1', id: customerData?.[0]?.id, subjectNumber: customerData?.[0]?.subjectNumber } : customerData?.[0];
  // 如果有投保人是否书面声明字段，说明是投保人，需要回填isConfirm字段
  if (customerData?.[0]?.isConfirm) {
    sourceData.isConfirm = customerData[0].isConfirm;
  }
  return [sourceData];
};
// 提交的VO数据
const submitData = computed(() => {
  const tempData = {
    ...contractModel.value,
    beneficaryInfoList: transCustomerInfo(contractModel.value.beneficaryInfoList, contractModel.value.insurantInfoList),
    applicantInfoList: transCustomerInfo(contractModel.value.applicantInfoList, contractModel.value.insurantInfoList),
    riskGroupInfoList: planFormRef.value.transformData(contractModel.value.riskGroupInfoList),
  };
  // 共保数据处理
  if (coInsuranceRef?.value) {
    tempData.coinsuranceInfo = coInsuranceRef.value.transformData(contractModel.value.coinsuranceInfo);
  }
  // 提交前数据处理
  return tempData;
});

// 是从共出单
const isAcceptInsurance = computed(() => {
  if (coInsuranceRef?.value) {
    return contractModel.value?.baseInfo?.acceptInsuranceFlag === '0' && coInsuranceRef.value?.nonMainVerifySwitch;
  } else {
    return false;
  }
});

// 暂存
const saveData = async (showMsg?: boolean) => {
  loading.value = true;
  const params = submitData.value;
  // 切换tab触发保存需多传个参数用于后端判断
  if (!showMsg) {
    params.tabAutoOpFlag = 'Y';
  }
  try {
    const res = await $postOnClient(`${gateWay}${service.accept}/web/applicationForm/saveContract`, params);
    if (res && res.code === SUCCESS_CODE) {
      if (showMsg) {
        message.success(res?.msg || '保存成功');
      }
    } else {
      if (showMsg) {
        message.error(res?.msg || '操作失败');
      }
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
  }
};

// 跳转风险评估页面
const goRiskAssessDetail = () => {
  router.push({
    path: '/riskAssessDetail',
    query: { riskAsmtNo: contractModel.value?.baseInfo?.riskAsmtNo },
  });
};

// 关联投保单清单
const goToLink = () => {
  const address = contractModel.value.riskAddressInfoList[0] || {};
  if (hasLink.value) {
    openVisible.value = true;
    openType.value = '3';
    confirmText.value = `该投保单已添加清单${contractModel.value.baseInfo.farmerlistNo}，继续操作会取消当前关联关系，是否继续操作`;
  } else {
    router.push({
      path: '/relatedFarmerList',
      query: {
        applyPolicyNo: route.query.applyPolicyNo,
        departmentCode: contractModel.value.baseInfo.departmentCode,
        productCode: contractModel.value.baseInfo.productCode,
        province: address.province,
        city: address.city,
        county: address.county,
        town: address.town,
        village: address.village,
      },
    });
  }
};
// 判断关联交易签报按钮是否展示
const showRelatedApproval = ref<boolean>(false);
// 保费计算
const premiumCompute = async () => {
  loading.value = true;
  const fetchUrl = gateWay + service.accept + '/web/applicationForm/applyPremiumCalc';
  const res = await $postOnClient<{ message?: string; ruleData?: RuleData }>(fetchUrl, {
    ...submitData.value,
  });
  loading.value = false;
  const { code, data, msg = '' } = res || {};
  if (code === SUCCESS_CODE) {
    // 如果data里面有message,也要提示
    if (data?.message) {
      confirmText.value = data?.message;
      openType.value = '';
      openVisible.value = true;
    }
    message.success(msg);
    isAntiMoney.value = false;
    getInit();
    // relatedTransactionFlag为true，显示关联交易签报发起按钮
    if (data?.ruleData?.relatedTransactionFlag) {
      showRelatedApproval.value = true;
    } else {
      showRelatedApproval.value = false;
    }
  } else if (code === REFUND_CODE) {
    goToRefund(msg);
  } else {
    // 错误提示
    message.error(msg);
  }
};
// 接口返回错误信息提示-勿删
// const handleHint = (msg: string) => {
//   errorVisible.value = true;
//   confirmErrText.value = msg?.split(';');
// };
// 上一步
const goLastStep = () => {
  router.push({
    path: '/insuranceApply',
    query: {
      id: contractModel.value.extendInfo.preRiskNo,
      applyPolicyNo: route.query.applyPolicyNo, // 跳转标识
    },
  });
};
// 获取允许保险起期前置开关
const allowInsuranceDatePastFlag = ref<boolean>(false);
const getSwitchResult = async () => {
  const fetchUrl = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const res = await $postOnClient<boolean>(fetchUrl, {
    switchCode: 'ALLOW_INSURANCE_DATE_PAST_SIGN',
    agriculturalRiskObjectDetailCode: contractModel.value.riskGroupInfoList?.[0]?.combinedProductCode || '',
    allowanceTypeCode: contractModel.value?.baseInfo?.govSubsidyType || '',
    departmentNo: contractModel.value?.baseInfo?.departmentCode || '',
    coinsuranceFlag: contractModel.value?.baseInfo?.coinsuranceMark || '',
    mainInsureFlag: contractModel.value?.baseInfo?.acceptInsuranceFlag || '',
  });
  if (res && res.code === SUCCESS_CODE) {
    allowInsuranceDatePastFlag.value = res.data;
  }
};
const voucherSummaryInfo = ref({
  farmerInsureDupNumber: undefined,
  marketProductName: undefined,
  farmerInsureDupRadio: undefined,
  insuranceBeginDate: '',
  insuranceEndDate: '',
  customerVerifyRate: '',
  departmentName: '',
});

const approvalExternParams = ref({});

const publicityDetail = ref<PublicityStatusType[]>([]);
// 需重新公示状态列表
const publicityList = computed(() => {
  return publicityDetail?.value.filter((item) => !['3', '4'].includes(item?.publicityStatusCode) && item.publicityStatusCode);
});

const getInit = async () => {
  loading.value = true;
  const res = await $getOnClient(`${gateWay}${service.accept}/web/applicationForm/getContractInfo`, { applyPolicyNo: route.query.applyPolicyNo });
  loading.value = false;
  if (res && res.code === SUCCESS_CODE) {
    voucherSummaryInfo.value = res.data.voucherSummaryInfoVO || {};
    contractModel.value = initContractModel(res.data.contract || {});
    publicityDetail.value = res.data.publicityDetailVOS || [];
    if (coInsuranceRef?.value) {
      // 共保初始化数据
      coInsuranceRef.value?.initData();
    }
    initRiskCode.value = res.data.contract.riskGroupInfoList[0].combinedProductCode;
    // 免赔信息：免赔项目级联选择器回显数据处理
    if (contractModel.value.noclaimInfoList?.length > 0) {
      contractModel.value.noclaimInfoList = contractModel.value.noclaimInfoList.map((item: { noclaimItem: string; planCode: string; dutyCode: string; noclaimItemArr: string[] }) => {
        item.noclaimItemArr = [];
        if (item.noclaimItem) {
          item.noclaimItemArr.push(item.noclaimItem);
          if (item.planCode) {
            item.noclaimItemArr.push(item.planCode);
            if (item.dutyCode) {
              item.noclaimItemArr.push(item.dutyCode);
            }
          }
        }
        return item;
      });
    }
    if (contractModel.value.payInfoList?.length > 0) {
      // 缴费起至时间处理为数组
      contractModel.value.payInfoList.forEach((item: { paymentBeginDate: string | Dayjs }) => {
        const today = dayjs(new Date()).format('YYYY-MM-DD');
        item.paymentBeginDate = item.paymentBeginDate || dayjs(today);
      });
      // payInfoList有数据，右侧锚点显示收费计划
      const index = anchorItems.value.findIndex((item) => item.href === '#fee-plan');
      if (index === -1) {
        anchorItems.value.splice(5, 0, {
          key: '6',
          href: '#fee-plan',
          title: '收费计划',
        });
      }
    } else {
      // payInfoList没数据，右侧锚点不显示收费计划
      anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
    }
    // 如果共保信息存在，右侧大纲显示共保信息
    const hasCoinsurance = anchorItems.value.findIndex((item) => item.href === '#co-insurance') === -1;
    if (contractModel.value.baseInfo.coinsuranceMark === '1' && hasCoinsurance) {
      anchorItems.value.push({ key: '10', href: '#co-insurance', title: '共保信息' });
      // 共保数据初始化uuid 用于共保删除功能
      contractModel.value.coinsuranceInfo.coinsuranceDetailList.forEach((item) => {
        item.uuid = generateUUID();
      });
    }
    if (contractModel.value?.payInfoList) {
      for (const [index, item] of contractModel.value.payInfoList.entries()) {
        if (item.bankHeadquartersCode) {
          await payInfoRef.value?.getBankBranchList(item.bankHeadquartersCode, index);
        }
      }
    }
    // 获取允许保险起期前置开关
    getSwitchResult();
    // 清单关联成功清除校验
    if (route?.query?.from === 'relatedList') {
      clearValidateArr();
    }
    approvalExternParams.value = {
      riskType: contractModel.value.riskGroupInfoList?.[0]?.combinedProductCode || '',
      applyPolicyTypes: [contractModel.value?.baseInfo?.applyPolicyType || ''],
      subsidyTypes: [contractModel.value?.baseInfo?.govSubsidyType || ''],
    };
  }
};

// 获取eoa签报数据
const getEoaData = async () => {
  const res = await $getOnClient<SignData[]>(`${gateWay}${service.accept}/accept/eoa/queryApplyPolicySignDataList`, { applyPolicyNo: route.query.applyPolicyNo });
  if (res && res.code === SUCCESS_CODE) {
    signDataList.value = res.data || [];
  }
};
const isReset = () => {
  contractModel.value.baseInfo.farmerlistNo = '';
  contractModel.value.baseInfo.verifyRate = '';
};
// 修改清单
const editList = () => {
  openVisible.value = true;
  openType.value = '2';
  confirmText.value = `修改清单可能会影响验标要求，且投保单需要重新签字/公示，请谨慎操作`;
};

// 是否已关联
const hasLink = computed(() => contractModel.value.baseInfo?.farmerlistNo?.length > 0);

// 公示列表
const listData = ref<PublicityData[]>([]);
interface SystemType {
  [key: string]: string;
}
// 公示类型
const publicityTypeC = ref<SystemType>({
  ZBX: '中保信公示',
  LOCAL_PF: '四川省保险平台',
  PA_ONLINE: '公司线上（爱农宝）',
  PA_OFFLINE: '公司线下',
});
// 提交验标
const submitCheck = () => {
  resultVisible.value = true;
};
// const goSubmitCheck = () => {
//   resultVisible.value = false;
//   router.push({
//     path: '/inspectionInfo',
//     query: {
//       applyPolicyNo: route.query.applyPolicyNo,
//     },
//   });
// };
// 发起公示-初始化调用
const publicChange = async () => {
  const results = await Promise.all(getValidateArr(false));
  const isValid = results.every((result) => result.valid);
  if (contractModel.value.baseInfo?.status === 'B1') {
    message.warning('请先保费计算');
  } else if (!contractModel.value.baseInfo?.farmerlistNo || contractModel.value.baseInfo?.farmerlistNo === null) {
    message.warning('未添加清单，请先添加清单再发起公示');
    // 滚动到清单模块
    const farmerDiv = document.querySelector('.policy-info');
    farmerDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  } else if (isValid) {
    // 所有表单校验通过
    try {
      // 判断发起公示是否二次发起，false-需要二次弹窗提示,true直接通过
      const res = await $postOnClient(gateWay + service.accept + '/publicity/validatePublicity', { ...submitData.value });
      if (res?.code === SUCCESS_CODE) {
        if (!res?.data) {
          confirmText.value = '已发起公示，是否重新发起公示？';
          openVisible.value = true;
          openType.value = '4';
        } else {
          commitPublicity();
        }
      }
    } catch (error) {
      console.log(error);
    }
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
// 获取发起公示列表
const commitPublicity = async () => {
  try {
    const fetchUrl = gateWay + service.accept + '/publicity/getPublicityList';
    const res = await $getOnClient(fetchUrl, { applyPolicyNo: route.query.applyPolicyNo });
    const { code, data, msg = '' } = res || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      listData.value = data?.map((item: PublicityData) => {
        return {
          ...item,
          publicityName: publicityTypeC.value[item.publicityType],
          checked: item.requiredFlag === 'Y' ? true : false,
        };
      });
      publicVisible.value = true;
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 发起公示提交
const publicLoading = ref<boolean>(false);
const handlePublicOk = async (publicityList: PublicityInfoType[]) => {
  try {
    publicLoading.value = true;
    const params = {
      contractVO: { ...submitData.value },
      publicityList,
    };
    const fetchUrl = `/gateway${service.accept}/publicity/submitPublicity`;
    const res = await $postOnClient<ExceptionPayload>(fetchUrl, params);
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      publicVisible.value = false;
      message.success(msg || '');
      getInit();
    } else {
      // 错误提示
      message.error(msg || '');
    }
    publicLoading.value = false;
  } catch (error) {
    console.log(error);
    publicLoading.value = false;
  }
};
// 电子化接口请求
const showWindowTypes = ref<string>('');
const queryIsSwitch = async () => {
  try {
    const fetchUrl = gateWay + service.accept + '/web/applicationForm/apply/inform/electronInfo';
    $postOnClient(fetchUrl, submitData.value).then((res) => {
      const { code, msg = '', data } = res || {};
      if (code === SUCCESS_CODE) {
        const { showWindowType, showReInformWindowFlag } = (data as ExtendsType) || {};
        showWindowTypes.value = showWindowType || '';
        // Y-弹起二次弹窗
        if (showReInformWindowFlag === 'Y') {
          confirmText.value = '已发送投保告知短信，是否重新进行告知流程？';
          openVisible.value = true;
          openType.value = '1';
        } else {
          // 是否显示弹窗，showWindowType=0不显示直接调接口，否则1-显示电子签名+纸质签名，2-显示电子签名+纸质签名+验证码发送，不同场景显示不同选项
          if (showWindowType === '0') {
            handleGetInformOk();
          } else {
            informVisible.value = true;
          }
        }
      } else {
        // 错误提示
        message.error(msg);
      }
    });
  } catch (error) {
    console.log(error);
  }
};
// 投保告知,电子化-直接调用提交接口，否则选择再提交
const informChange = async () => {
  const results = await Promise.all(getValidateArr(false));
  const isValid = results.every((result) => result.valid);
  if (contractModel.value.baseInfo?.status === 'B1') {
    message.warning('请先保费计算');
  } else if (isValid) {
    // 所有表单校验通过
    // 调用电子化接口
    queryIsSwitch();
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
const publicInfoRef = ref();
// 二次确认弹窗点击确认按钮
const handleConfirmOk = async () => {
  if (openType.value === '1') {
    openVisible.value = false;
    // 是否显示弹窗，showWindowType=0不显示直接调接口，否则1-显示电子签名+纸质签名，2-显示电子签名+纸质签名+验证码发送，不同场景显示不同选项
    if (showWindowTypes.value === '0') {
      handleGetInformOk();
    } else {
      informVisible.value = true;
    }
  } else if (openType.value === '2') {
    router.push({
      path: '/editFarmerList',
      query: {
        id: contractModel.value.baseInfo.farmerlistNo || '',
      },
    });
  } else if (openType.value === '3') {
    try {
      const fetchUrl = gateWay + service.accept + '/web/applicationForm/cancelLinkFarmerList';
      $postOnClient(fetchUrl, {
        applyPolicyNo: route.query.applyPolicyNo,
        farmerlistNo: contractModel.value.baseInfo.farmerlistNo,
      }).then((res) => {
        const { code, msg = '' } = res || {};
        if (code === SUCCESS_CODE) {
          isReset();
          getInit();
          const address = contractModel.value.riskAddressInfoList[0] || {};
          router.push({
            path: '/relatedFarmerList',
            query: {
              applyPolicyNo: route.query.applyPolicyNo,
              departmentCode: contractModel.value.baseInfo.departmentCode,
              productCode: contractModel.value.baseInfo.productCode,
              province: address.province,
              city: address.city,
              county: address.county,
              town: address.town,
              village: address.village,
            },
          });
        } else {
          message.error(msg);
        }
      });
    } catch (error) {
      console.log(error);
    }
  } else if (openType.value === '4') {
    // 发起公示二次确认
    commitPublicity();
  } else if (openType.value === '5') {
    // 关联方签报二次确认
  } else if (openType.value === '6') {
    // 疑似个人二次确认
    const codeList = formPayloadList.value.map((item) => item.code);
    // 被保人从团体改为个人
    if (codeList.includes('individualSuspectedByTheInsured')) {
      resetPersonnelInfo(contractModel.value.insurantInfoList[0]);
    }
    // 投保人从团体改为个人
    if (codeList.includes('theApplicantIsSuspectedToBeAnIndividual')) {
      resetPersonnelInfo(contractModel.value.applicantInfoList[0]);
    }
    // 受益人从团体改为个人
    if (codeList.includes('theBeneficiaryIsSuspectedToBeAnIndividual')) {
      resetPersonnelInfo(contractModel.value.beneficaryInfoList[0]);
    }
  } else if (openType.value === '7') {
    // 爱农宝核身率豁免签报
    showIdentityApproval.value = true;
  }
  openType.value = '';
  openVisible.value = false;
};

// 重置投被受信息 团体改为个人
const resetPersonnelInfo = (infoList: TypeCustomerInfo) => {
  infoList.personnelType = '1';
  infoList.relationshipWithInsured = '';
  infoList.certifyResult = '';
  infoList.certificateType = '';
  infoList.superviseInfoList[0].superviseExtendList = [];
};
// 二次确认点击取消按钮
const handleConfirmCancel = () => {
  openVisible.value = false;
  if (openType.value === '6') {
    submitApproval();
  }
};
// 投保告知提交
const informLoading = ref<boolean>(false);
const handleGetInformOk = async () => {
  try {
    informLoading.value = true;
    const params = {
      ...submitData.value,
    };
    const fetchUrl = gateWay + service.accept + '/web/applicationForm/apply/inform';
    $postOnClient(fetchUrl, params)
      .then((res) => {
        const { code, msg = '' } = res || {};
        if (code === SUCCESS_CODE) {
          informVisible.value = false;
          message.success(msg);
          getInit();
        } else {
          // 错误提示
          message.error(msg);
        }
        informLoading.value = false;
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        informLoading.value = false;
      });
  } catch (error) {
    console.log(error);
    informLoading.value = false;
  }
};
/**
 * 校验识别为关联方清单
 */
// const signFlag = ref<boolean>(true);
// provide('signFlag', signFlag.value);
// const checkSign = () => {
//   message.warning('投保人属于公司关联方，请提前完成签报申请！');
//   signFlag.value = true;
// };

// 提交核保
const submitApproval = async () => {
  const results = await Promise.all(getValidateArr(true, true));
  const isValid = results.every((result) => result.valid);
  if (isValid) {
    // 所有表单校验通过
    const params = {
      ...submitData.value,
      formPayloadList: formPayloadList.value,
    };
    loading.value = true;
    try {
      const fetchUrl = gateWay + service.accept + '/web/applicationForm/submitContact';
      const res = await $postOnClient<ExceptionPayload>(fetchUrl, params);
      const tipsCode = ['PEC1001', 'PEE1001']; // 触发二次确认提示场景、触发签报场景
      if (res) {
        if (tipsCode.includes(res.code)) {
          if (res?.data?.exceptionPayload?.formPayloadList?.length > 0) {
            const tipsMsg = res?.data?.exceptionPayload?.formPayloadList.map((item, index) => `${index + 1}、${item.msg}`).join(';  ');
            message.error(tipsMsg);
            formPayloadList.value = res?.data?.exceptionPayload?.formPayloadList || [];
          }
          // signDataList存在代表有签报未完成
          if (res.data?.exceptionPayload?.signDataList?.length > 0) {
            // 关联交易签报
            const relatedItem = res.data?.exceptionPayload?.signDataList?.find((item) => item.signType === '033');
            // 爱农宝核身率签报
            const identifyRateItem = res.data?.exceptionPayload?.signDataList?.find((item) => item.signType === '039');
            // 关联方交易签报未完成，需要弹出提示并且显示发起签报按钮
            if (relatedItem && ['0', '1'].includes(relatedItem.signStatus)) {
              confirmText.value = relatedItem.signStatusDesc || '';
              openVisible.value = true;
              openType.value = '5';
              // signStatus为0 需要显示发起签报的按钮
              showRelatedApproval.value = relatedItem.signStatus === '0';
              // 0是未发起 3是不通过
            } else if (identifyRateItem && ['0', '3'].includes(identifyRateItem.signStatus)) {
              // 爱农宝核身率签报未完成，需要弹出提示并展示发起签报弹窗
              openVisible.value = true;
              const { farmerVerifyInfo } = identifyRateItem;
              confirmText.value = `本单爱农宝核身率小于${farmerVerifyInfo.umsVerifyRate}%，可申请豁免签报`;
              openType.value = '7';
              // 拼接签报标题和正文
              identifyRateForm.value.eoaTitle = `关于${contractModel?.value?.baseInfo?.departmentCode}${voucherSummaryInfo?.value?.departmentName}机构申请投保单${route?.query?.applyPolicyNo}的爱农宝核身率豁免的请示`;
              identifyRateForm.value.fontBody = `领导好：\n现申请${contractModel?.value?.baseInfo?.departmentCode || ''}${voucherSummaryInfo?.value?.departmentName || ''}机构，投保单${route?.query?.applyPolicyNo || ''}的爱农宝身份核验豁免，具体原因及核验情况如下，烦请领导审批，谢谢\n`;
              identifyRateForm.value.endBody = `【本保单承保农户数】：${farmerVerifyInfo?.allCustomerCount}户\n【已通过身份核验率农户数】：${farmerVerifyInfo?.customerVerifyCount}户\n【本保单身份核验率】：${farmerVerifyInfo?.customerVerifyRate}%\n【后补责任人】：${userInfo?.umName || ''}\n【后补时间】：${farmerVerifyInfo?.afterDays}天\n【出单机构】：${contractModel?.value?.baseInfo?.departmentCode || ''}${voucherSummaryInfo?.value?.departmentName || ''}`;
              identifyRateForm.value.eoaContent = identifyRateForm.value.fontBody + identifyRateForm.value.endBody;
            } else {
              const tipsMsg = res?.data?.exceptionPayload?.signDataList.map((item, index) => `${index + 1}、${item.signStatusDesc}`).join(';  ');
              confirmText.value = tipsMsg || '';
              openVisible.value = true;
              openType.value = '5';
            }
          }
        } else if (res.code === 'PEF1001') {
          message.error(res.msg);
          // PEF1001 代表后端触发反洗钱校验
          isAntiMoney.value = true;
          // 跳转到客户信息
          const customerDiv = document.querySelector('.customer-info');
          customerDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else if (res.code === 'PEC1002') {
          // 触发疑似个人场景 出单比录文件未录
          const tipsMsg = res?.data?.exceptionPayload?.formPayloadList.map((item, index) => `${index + 1}、${item.msg}`).join('<br>');
          confirmText.value = tipsMsg || '';
          openVisible.value = true;
          openType.value = '6';
          formPayloadList.value = res?.data?.exceptionPayload?.formPayloadList || [];
        } else if (res.code === SUCCESS_CODE) {
          message.success(res?.msg);
          // 关闭所有与投保相关的页面 第一屏、第二屏、附件管理
          deletPageTabListItem('/insuranceFormFill');
          deletPageTabListItem('/insuranceApply');
          deletPageTabListItem('/imageUpload');
          // 返回上一页或者首页
          if (pageTabList.value.length) {
            router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
          } else {
            router.push('/home');
          }
        } else if (res.code === REFUND_CODE) {
          goToRefund(res?.msg || '');
        } else {
          message.error(res?.msg || '提交核保失败');
        }
      }
      loading.value = false;
    } catch (error) {
      console.log(error);
    }
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
// 发起爱农宝核身率豁免签报
const submitIdentityApproval = async (eoaData: EoaData) => {
  await identifyRateRef?.value?.validate();
  try {
    // 用于提交签报的参数
    const params = {
      relationBusinessNo: route.query.applyPolicyNo,
      eoaType: '039', // eoa类型
      eoaSubject: identifyRateForm.value.eoaTitle, // 签报主题
      eoaBody: identifyRateForm.value.fontBody + `【申请原因】：${identifyRateForm.value.eoaReason}\n` + identifyRateForm.value.endBody, // 签报正文
      documentGroupId: eoaData.documentGroupId,
      approveChainList: eoaData.approveChainList,
      attachmentList: eoaData.fileInfos,
      departmentCode: contractModel?.value?.baseInfo?.departmentCode,
    };
    const fetchUrl = `${gateWay}${service.accept}/accept/eoa/createEoa`;
    const res = await $postOnClient(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      showIdentityApproval.value = false;
      // 刷新eoa数据
      getEoaData();
    } else {
      message.error(res?.msg as string);
    }
  } catch (error) {
    console.log(error);
  }
};
const imageUpload = () => {
  router.push({
    path: '/imageUpload',
    query: {
      applyPolicyNo: route.query.applyPolicyNo,
    },
  });
};
// 下载重复信息
const downloadInfo = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.accept}/apply/attachment/file/excelCommonExport`;
    await $postOnClient(
      fetchUrl,
      { exportType: 'DUP_INSURE_FARMER_LIST', applyPolicyNo: route.query.applyPolicyNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('下载成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
  } catch (error) {
    console.log(error);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const loadingReStart = ref(false);
const handleJumpEoaDetailOrApproval = (type?: string) => {
  // 豁免失败
  if (type === 'reStart') {
    // 拼接签报标题和正文
    loadingReStart.value = true;
    $get(`${gateWay}${service.farmer}/farmerList/getFarmerVerifyInfoByFarmerListNo`, { farmerListNo: contractModel?.value?.baseInfo?.farmerlistNo })
      .then(({ code, data, msg }) => {
        if (code === SUCCESS_CODE && data) {
          const { allCustomerCount = '', customerVerifyCount = '', customerVerifyRate = '', afterDays = '' } = data;
          identifyRateForm.value.eoaTitle = `关于${contractModel?.value?.baseInfo?.departmentCode}${voucherSummaryInfo?.value?.departmentName}机构申请投保单${route?.query?.applyPolicyNo}的爱农宝核身率豁免的请示`;
          identifyRateForm.value.fontBody = `领导好：\n现申请${contractModel?.value?.baseInfo?.departmentCode || ''}${voucherSummaryInfo?.value?.departmentName}机构，投保单${route?.query?.applyPolicyNo || ''}的爱农宝身份核验豁免，具体原因及核验情况如下，烦请领导审批，谢谢\n`;
          identifyRateForm.value.endBody = `【本保单承保农户数】：${allCustomerCount}户\n【已通过身份核验率农户数】：${customerVerifyCount}户\n【本保单身份核验率】：${customerVerifyRate}%\n【后补责任人】：${userInfo?.umName || ''}\n【后补时间】：${afterDays}天\n【出单机构】：${contractModel?.value?.baseInfo?.departmentCode || ''}${voucherSummaryInfo?.value?.departmentName || ''}`;
          identifyRateForm.value.eoaContent = identifyRateForm.value.fontBody + identifyRateForm.value.endBody;
          showIdentityApproval.value = true;
        } else {
          message.error(msg);
        }
      })
      .catch((err) => {
        message.error(err);
      })
      .finally(() => {
        loadingReStart.value = false;
      });
    // 都可到eoa详情页
  } else if (identifySignData.value?.eoaDetailUrl) {
    window.open(identifySignData.value.eoaDetailUrl);
  }
};

// 去退费
const showToRefund = ref(false);
const refundText = ref('');
const toRefundLoading = ref(false);
const goToRefund = (msg: string) => {
  refundText.value = msg;
  showToRefund.value = true;
};

const handleToRefund = () => {
  toRefundLoading.value = true;
  $get(`${gateWay}${service.accept}/applyPayInfo/queryRefundPayInfo`, { applyPolicyNo: route.query.applyPolicyNo })
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        router.push({
          name: 'paymentManage',
          query: {
            applyPolicyNo: route.query.applyPolicyNo,
          },
        });
      } else if (res && res.msg) {
        message.error(res.msg);
      }
    })
    .catch((error) => {
      console.log(error);
    })
    .finally(() => {
      toRefundLoading.value = false;
      showToRefund.value = false;
    });
};

onActivated(() => {
  getInit();
  getEoaData();
});

onDeactivated(() => {
  saveData();
});
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
    .bg-image-url {
      background-image: url(@/assets/images/content-head.png);
      background-size: cover;
    }
    :deep(.re-start-btn.ant-btn) {
      font-size: 12px;
      margin-left: -10px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
