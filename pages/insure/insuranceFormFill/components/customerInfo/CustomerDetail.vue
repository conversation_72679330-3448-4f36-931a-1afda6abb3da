<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="p-16px mb-16px bg-white rounded">
      <div class="form-title">
        <span v-if="customerType === 'insurantInfo'">被保险人信息</span>
        <span v-if="customerType === 'applicantInfo'">投保人信息</span>
        <span v-if="customerType === 'beneficaryInfo'">受益人信息</span>
      </div>
      <div v-if="customerType !== 'insurantInfo'" class="flex items-center space-x-[40px]">
        <a-form-item label="">
          <a-checkbox v-model:checked="sameInsuredPersons" @change="checkboxChange">与被保险人一致</a-checkbox>
        </a-form-item>
        <a-form-item v-if="customerType === 'applicantInfo'" required name="isConfirm" label="投保人是否已书面确定“投保人声明”">
          <a-radio-group v-model:value="data.isConfirm">
            <a-radio value="12">是</a-radio>
            <a-radio value="1">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="">
          <a-popover v-if="signData?.signStatus && signData?.signStatus !== '0'" placement="top">
            <template #content>
              <div>
                签报状态：{{ signData.signStatusDesc }}，签报号：<span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(signData.eoaDetailUrl)">{{ signData.eoaNo }}</span>
              </div>
            </template>
            <a-button class="mr-[8px]" type="primary">签报状态</a-button>
          </a-popover>
          <a-button v-if="signData?.signStatus === '3' || showRelatedApproval" type="primary" @click="handleApproval">发起签报</a-button>
          <span v-if="signData?.signStatus || showRelatedApproval" class="ml-[10px] text-red-500">投保人属于公司关联方</span>
        </a-form-item>
      </div>
      <div v-if="customerType === 'applicantInfo' && isForestry && sameInsuredPersons" class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="林业主体类型" name="subjectNumber" :label-col="labelColStyle">
          <a-select v-model:value="data.subjectNumber" class="w-full" :options="optionsData.AGRLYZTLX" />
        </a-form-item>
      </div>
      <div v-if="customerType === 'insurantInfo' || !sameInsuredPersons" class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="客户属性" name="personnelType" :label-col="labelColStyle" class="col-span-3">
          <a-radio-group v-model:value="data.personnelType" :disabled="insurantFiledDisabled" @change="radioChange">
            <a-radio value="1">个人（自然人）</a-radio>
            <a-radio value="0">团体（法人）</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item required label="名称" name="name" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]">
          <a-input v-if="data?.personnelType === '1'" v-model:value="data.name" placeholder="请输入" :disabled="insurantFiledDisabled" />
          <a-input v-if="!data?.personnelType" v-model:value="data.name" placeholder="请输入" :disabled="insurantFiledDisabled" />
          <a-auto-complete v-if="data?.personnelType === '0'" v-model:value="data.name" :dropdown-match-select-width="500" :filter-option="false" :options="nameOptions" placeholder="请输入" style="width: 75%" :disabled="insurantFiledDisabled" @search="nameChange" @blur="nameBlur">
            <template #option="option">
              <div @click="() => nameSelect(option)">{{ option.clientName }}</div>
            </template>
          </a-auto-complete>
          <span v-if="data?.personnelType === '0' && data.certifyResult === '1'" class="w-40px ml-8px text-[#0000008c] font-normal">已验证</span>
          <span v-if="data?.personnelType === '0' && data.certifyResult === '0'" class="w-40px ml-8px text-[#576B95] font-normal">未验证</span>
        </a-form-item>
        <!--        证件识别 -->
        <a-form-item v-if="noChecked" required label="证件类型" name="certificateNo" :label-col="labelColStyle" :rules="[{ validator: IDValidator }]" class="col-span-2">
          <div class="flex space-x-8px">
            <a-select v-model:value="data.certificateType" class="w-full" :options="data.personnelType === '1' ? optionsData.personalCertificate : optionsData.groupCertificate" :disabled="insurantFiledDisabled" @change="checkCertificateNo" />
            <a-input v-model:value="data.certificateNo" :disabled="insurantFiledDisabled" @change="certificateNoChange" />
            <a-button type="primary" class="ml-[10px]" @click="handleIdentify"> 证件识别 </a-button>
          </div>
        </a-form-item>
        <a-form-item v-if="!noChecked" required label="证件类型" name="certificateType" :label-col="labelColStyle">
          <a-select v-model:value="data.certificateType" class="w-full" :options="data.personnelType === '1' ? optionsData.personalCertificate : optionsData.groupCertificate" :disabled="insurantFiledDisabled" @change="checkCertificateNo" />
        </a-form-item>
        <a-form-item v-if="!noChecked" required label="证件号码" name="certificateNo" :label-col="labelColStyle" :rules="[{ validator: IDValidator }]">
          <a-input v-model:value="data.certificateNo" :disabled="insurantFiledDisabled" @change="certificateNoChange" />
        </a-form-item>
        <a-form-item name="certificateIssueDate" class="col-span-3">
          <RangeTimePicker v-model:start-time="data.certificateIssueDate" v-model:end-time="data.certificateValidDate" required />
        </a-form-item>
        <a-form-item required label="地址" name="address" :label-col="labelColStyle" class="col-span-2">
          <div class="flex space-x-8px">
            <a-form-item-rest>
              <RegionSelect v-model:province="data.province" v-model:city="data.city" v-model:county="data.county" :region-level="3" style="width: 60%" @change-selected="changeAddress" />
            </a-form-item-rest>
            <a-input v-model:value="data.address" style="width: 40%" placeholder="请输入" />
          </div>
        </a-form-item>
        <a-form-item required label="邮政编码" name="postcode" :label-col="labelColStyle" :rules="[{ validator: isPostCode }]">
          <a-input v-model:value="data.postcode" placeholder="请输入" />
        </a-form-item>
        <a-form-item required label="联系电话" name="mobileTelephone" :label-col="labelColStyle" :rules="[{ validator: isMobilePhone }]">
          <a-input v-model:value="data.mobileTelephone" placeholder="请输入" :disabled="insurantFiledDisabled" />
        </a-form-item>
        <a-form-item label="电子邮箱" name="email" :label-col="labelColStyle" :rules="[{ validator: isEmail }]">
          <a-input v-model:value="data.email" placeholder="请输入" />
        </a-form-item>
        <a-form-item v-if="customerType === 'insurantInfo'" required label="农业主体类型" name="subjectNumber" :label-col="labelColStyle">
          <a-select v-model:value="data.subjectNumber" class="w-full" :options="optionsData.farmerMain" />
        </a-form-item>
        <a-form-item v-if="customerType === 'applicantInfo' && isForestry" required label="林业主体类型" name="subjectNumber" :label-col="labelColStyle">
          <a-select v-model:value="data.subjectNumber" class="w-full" :options="optionsData.AGRLYZTLX" />
        </a-form-item>
        <!-- 客户属性-个人 -->
        <template v-if="!isInsuredGroup">
          <a-form-item v-if="customerType === 'insurantInfo'" label="贫困户标识" name="poorSymbol" :label-col="labelColStyle" required>
            <a-select v-model:value="data.poorSymbol" class="w-full" :options="optionsData.poorHouse" @change="handleChangePoorSymbol" />
          </a-form-item>
          <!-- 全部贫困户 && 二级机构是黑龙江(219)展示贫困户明细 -->
          <a-form-item v-if="customerType === 'insurantInfo' && data?.poorSymbol === '1' && secDeptCode === '219'" label="贫困户细分" name="poorDetailType" :label-col="labelColStyle" required>
            <a-select v-model:value="data.poorDetailType" class="w-full" :options="optionsData.poorDetailType" />
          </a-form-item>
          <a-form-item v-if="customerType === 'insurantInfo'" label="投保人与被保人关系" name="relationshipWithApplicant">
            <a-select v-model:value="data.relationshipWithApplicant" class="w-full" :options="optionsData.relation" />
          </a-form-item>
          <template v-if="insuredExpand">
            <a-form-item label="性别" name="sexCode" :label-col="labelColStyle" :required="isAntiMoney">
              <a-select v-model:value="data.sexCode" class="w-full" :options="optionsData.sex" />
            </a-form-item>
            <a-form-item label="国籍" name="nationality" :label-col="labelColStyle" :required="isAntiMoney">
              <a-select v-model:value="data.nationality" class="w-full" :options="optionsData.country" />
            </a-form-item>
            <a-form-item label="职业" name="professionCode" :label-col="labelColStyle" :required="isAntiMoney">
              <a-cascader v-model:value="profession" class="w-full" :options="optionsData.profession" change-on-select />
            </a-form-item>
            <a-form-item v-if="customerType === 'beneficaryInfo'" label="受益人与被保人关系" name="relationshipWithInsured">
              <a-select v-model:value="data.relationshipWithInsured" class="w-full" :options="relationOption" />
            </a-form-item>
            <a-form-item v-if="customerType !== 'beneficaryInfo'" label="年收入(元)" :name="['superviseInfoList', 0, 'yearlySalaries']" :rules="[{ validator: yearlySalariesValidator }]">
              <a-input v-model:value="data.superviseInfoList[0].yearlySalaries" />
            </a-form-item>
            <a-form-item v-if="customerType !== 'beneficaryInfo'" label="工作单位名称" :name="['superviseInfoList', 0, 'companyName']" :label-col="labelColStyle" :rules="[{ max: 100, message: '最多输入100个字符' }]">
              <a-input v-model:value="data.superviseInfoList[0].companyName" />
            </a-form-item>
          </template>
        </template>
        <!-- 客户属性-团体 -->
        <template v-if="isInsuredGroup">
          <a-form-item v-if="customerType === 'insurantInfo'" label="贫困户标识" name="poorSymbol" :label-col="labelColStyle" required>
            <a-select v-model:value="data.poorSymbol" class="w-full" :options="optionsData.poorHouse" @change="handleChangePoorSymbol" />
          </a-form-item>
          <!-- 全部贫困户 && 二级机构是黑龙江(219)展示贫困户明细 -->
          <a-form-item v-if="customerType === 'insurantInfo' && data?.poorSymbol === '1' && secDeptCode === '219'" label="贫困户细分" name="poorDetailType" :label-col="labelColStyle" required>
            <a-select v-model:value="data.poorDetailType" class="w-full" :options="optionsData.poorDetailType" />
          </a-form-item>
          <a-form-item label="企业归属" name="belongOrganizationNo" :label-col="labelColStyle">
            <a-select v-model:value="data.belongOrganizationNo" allow-clear class="w-full" :options="optionsData.applyOrg" />
          </a-form-item>
          <a-form-item label="行业" name="industryCode" :label-col="labelColStyle">
            <a-cascader v-model:value="industry" class="w-full" :options="optionsData.industry" change-on-select />
          </a-form-item>
          <a-form-item v-if="customerType === 'insurantInfo'" label="投保人与被保人关系" name="relationshipWithApplicant">
            <a-select v-model:value="data.relationshipWithApplicant" class="w-full" :options="optionsData.relationGroup" />
          </a-form-item>
          <template v-if="insuredExpand">
            <a-form-item label="经营范围" :name="['superviseInfoList', 0, 'businessScope']" :label-col="labelColStyle" :rules="[{ max: 100, message: '最多输入200个字符' }]" :required="isAntiMoney">
              <a-input v-model:value="data.superviseInfoList[0].businessScope" />
            </a-form-item>
            <a-form-item v-if="customerType !== 'beneficaryInfo'" label="国有属性" :name="['superviseInfoList', 0, 'enterpriseType']" :label-col="labelColStyle">
              <a-select v-model:value="data.superviseInfoList[0].enterpriseType" class="w-full" :options="optionsData.enterprise" />
            </a-form-item>
            <a-form-item v-if="customerType !== 'beneficaryInfo'" label="注册资本金(元)" :name="['superviseInfoList', 0, 'registeredFund']" :label-col="labelColStyle" :rules="[{ validator: yearlySalariesValidator }]">
              <a-input v-model:value="data.superviseInfoList[0].registeredFund" />
            </a-form-item>
            <a-form-item v-if="customerType === 'beneficaryInfo'" label="受益人与被保人关系" name="relationshipWithInsured">
              <a-select v-model:value="data.relationshipWithInsured" class="w-full" :options="relationGroupOption" />
            </a-form-item>
          </template>
        </template>
      </div>
      <div v-if="customerType === 'insurantInfo' || !sameInsuredPersons" :class="['flex', 'justify-end', 'items-center']">
        <div :class="['flex', 'items-center', 'space-x-[4px]', 'text-[#576B95]', 'cursor-pointer']" @click="handleExpand()">
          <VueIcon :icon="insuredExpand ? IconChevronLeftDoubleFont : IconChevronRightDoubleFont" class="rotate-90" />
          <span>{{ insuredExpand ? '收起' : '展示' }}更多</span>
        </div>
      </div>
    </div>
    <!-- 被保险人监管者信息 -->
    <div v-if="insuredExpand && (customerType === 'insurantInfo' || !sameInsuredPersons)" class="space-y-16px">
      <template v-for="(item, index) in data?.superviseInfoList?.[0].superviseExtendList">
        <!-- 授权办理业务人员 -->
        <div v-if="isInsuredGroup && item?.companyRelationType === '3'" :key="index" class="p-16px bg-white rounded">
          <div class="form-title">授权办理业务人员</div>
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item label="名称" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'name']" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]" :required="isAntiMoney">
              <a-input v-model:value="item.name" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="证件类型" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateType']" :label-col="labelColStyle" :required="isAntiMoney">
              <a-select v-model:value="item.certificateType" class="w-full" :options="optionsData.personalCertificate" />
            </a-form-item>
            <a-form-item label="证件号码" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateNo']" :label-col="labelColStyle" :rules="[{ validator: (rule, value) => personValidator(rule, value, item.certificateType as string) }]" :required="isAntiMoney">
              <a-input v-model:value="item.certificateNo" />
            </a-form-item>
            <a-form-item class="col-span-3" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateValidDate']">
              <RangeTimePicker v-model:start-time="item.certificateIssueDate" v-model:end-time="item.certificateValidDate" :required="isAntiMoney" />
            </a-form-item>
          </div>
        </div>
        <!-- 法定代办人 -->
        <div v-if="isInsuredGroup && item?.companyRelationType === '2'" :key="index" class="p-16px bg-white rounded">
          <div class="form-title">法定代办人/负责人</div>
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item label="名称" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'name']" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]" :required="isAntiMoney">
              <a-input v-model:value="item.name" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="证件类型" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateType']" :label-col="labelColStyle" :required="isAntiMoney">
              <a-select v-model:value="item.certificateType" class="w-full" :options="optionsData.personalCertificate" />
            </a-form-item>
            <a-form-item label="证件号码" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateNo']" :label-col="labelColStyle" :rules="[{ validator: (rule, value) => personValidator(rule, value, item.certificateType as string) }]" :required="isAntiMoney">
              <a-input v-model:value="item.certificateNo" />
            </a-form-item>
            <a-form-item class="col-span-3" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateValidDate']">
              <RangeTimePicker v-model:start-time="item.certificateIssueDate" v-model:end-time="item.certificateValidDate" :required="isAntiMoney" />
            </a-form-item>
          </div>
        </div>
        <!-- 控股股东/实际控制人 -->
        <div v-if="isInsuredGroup && item?.companyRelationType === '1'" :key="index">
          <div class="p-16px bg-white rounded">
            <div class="form-title">
              {{ customerType === 'beneficaryInfo' ? '控股股东/实际控制人/受益所有人' : '控股股东/实际控制人' }}
            </div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="名称" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'name']" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]" :required="isAntiMoney">
                <a-input v-model:value="item.name" placeholder="请输入" />
              </a-form-item>
              <a-form-item label="证件类型" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateType']" :label-col="labelColStyle" :required="isAntiMoney">
                <a-select v-model:value="item.certificateType" class="w-full" :options="optionsData.personalCertificate" />
              </a-form-item>
              <a-form-item label="证件号码" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateNo']" :label-col="labelColStyle" :rules="[{ validator: (rule, value) => personValidator(rule, value, item.certificateType as string) }]" :required="isAntiMoney">
                <a-input v-model:value="item.certificateNo" />
              </a-form-item>
              <a-form-item class="col-span-3" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateValidDate']">
                <RangeTimePicker v-model:start-time="item.certificateIssueDate" v-model:end-time="item.certificateValidDate" :required="isAntiMoney" />
              </a-form-item>
              <a-form-item v-if="customerType !== 'beneficaryInfo'" label="地址" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'address']" :label-col="labelColStyle" class="col-span-2">
                <div class="flex space-x-8px">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="item.province" v-model:city="item.city" v-model:county="item.county" :region-level="3" style="width: 60%" @change-selected="(option, type) => changeSuperviseAddress(option, type, index)" />
                  </a-form-item-rest>
                  <a-input v-model:value="item.address" style="width: 40%" placeholder="请输入" />
                </div>
              </a-form-item>
            </div>
          </div>
          <div v-if="isInsuredGroup && item?.companyRelationType === '1'" :key="index" class="text-14px text-[#576B95] font-normal flex justify-between my-[16px]">
            <div v-if="controlLastIndex === index" class="cursor-pointer" @click="addPeople('1', index)">
              <VueIcon :icon="IconTongyongXinzengFont" />
              {{ customerType === 'beneficaryInfo' ? '新增一位控股股东/实际控制人/受益所有人' : '新增一位控股股东/实际控制人' }}
            </div>
            <div class="cursor-pointer" @click="deletePeople('1', index)">
              <VueIcon :icon="IconDelFont" />
              删除
            </div>
          </div>
        </div>
        <!-- 受益所有人 -->
        <div v-if="isInsuredGroup && item?.companyRelationType === '4' && customerType !== 'beneficaryInfo'" :key="index">
          <div class="p-16px bg-white rounded">
            <div class="form-title">受益所有人</div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="名称" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'name']" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]" :required="isAntiMoney">
                <a-input v-model:value="item.name" placeholder="请输入" />
              </a-form-item>
              <a-form-item label="证件类型" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateType']" :label-col="labelColStyle" :required="isAntiMoney">
                <a-select v-model:value="item.certificateType" class="w-full" :options="optionsData.personalCertificate" />
              </a-form-item>
              <a-form-item label="证件号码" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateNo']" :label-col="labelColStyle" :rules="[{ validator: (rule, value) => personValidator(rule, value, item.certificateType as string) }]" :required="isAntiMoney">
                <a-input v-model:value="item.certificateNo" />
              </a-form-item>
              <a-form-item class="col-span-3" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'certificateValidDate']">
                <RangeTimePicker v-model:start-time="item.certificateIssueDate" v-model:end-time="item.certificateValidDate" :required="isAntiMoney" />
              </a-form-item>
              <a-form-item label="地址" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'address']" :label-col="labelColStyle" class="col-span-2" :required="isAntiMoney">
                <div class="flex space-x-8px">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="item.province" v-model:city="item.city" v-model:county="item.county" :region-level="3" style="width: 60%" @change-selected="(option, type) => changeSuperviseAddress(option, type, index)" />
                  </a-form-item-rest>
                  <a-input v-model:value="item.address" style="width: 40%" placeholder="请输入" />
                </div>
              </a-form-item>
              <a-form-item label="判定受益所有人方式" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'benefitMode']">
                <a-select v-model:value="item.benefitMode" class="w-full" :options="optionsData.benefitMode" />
              </a-form-item>
              <a-form-item label="受益所有人持股数量或表决权占比（%）" :name="['superviseInfoList', 0, 'superviseExtendList', index, 'equityRatio']" :rules="[{ validator: percentValidator }]">
                <a-input-number v-model:value="item.equityRatio" :min="0" />
              </a-form-item>
            </div>
          </div>
          <div :key="index" class="text-14px text-[#576B95] font-normal flex justify-between my-[16px]">
            <div v-if="beneficaryLastIndex === index" class="cursor-pointer" @click="addPeople('4', index)">
              <VueIcon :icon="IconTongyongXinzengFont" />
              新增一位受益所有人
            </div>
            <div class="cursor-pointer" @click="deletePeople('4', index)">
              <VueIcon :icon="IconDelFont" />
              删除
            </div>
          </div>
        </div>
      </template>
    </div>
  </a-form>
  <a-modal v-model:open="visible" title="提示" @cancel="handleCancel" @ok="handleOK">
    {{ `您确定要取消勾选吗？确定后需要重新录入${props.customerType === 'applicantInfo' ? '投保人' : '受益人'}信息！` }}
  </a-modal>

  <!--  证件识别-弹框 -->
  <identify-modal v-model:visible="identifyObj.visible" :document-type="documentType" :document-group-id="String(documentGroupId)" @ok-btn="identifyOkBtn" />
  <!-- 关联方交易识别签报 -->
  <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" v-model:preview-open="previewOpen" :show-uploadbtn="true" title="投保单关联方申请签报" :eoa-type="data.personnelType === '1' ? 'T17' : 'T18'" :total-actual-premium="totalActualPremium" :department-code="props.departmentCode" @ok="handleSubmit">
    <template #content>
      <a-form :colon="false" :label-col="{ style: { width: pxToRem(130) } }">
        <div class="bg-[#f8f8f8] rounded-[4px]">
          <a-form-item label="申请事项">关于 {{ applyPolicyNo }} 投保单关联方投保申请</a-form-item>
          <a-form-item label="险种">{{ combinedProductName }}</a-form-item>
          <a-form-item label="市场产品">{{ productName }}</a-form-item>
          <a-form-item label="保单保费">{{ totalActualPremium }}</a-form-item>
          <a-form-item label="关联方类型">{{ data.personnelType === '1' ? '个人（自然人）' : '团体（法人）' }}</a-form-item>
          <a-form-item label="关联方证件类型">{{ data.certificateTypeChName }}</a-form-item>
          <a-form-item label="关联方名称">{{ data.name }}</a-form-item>
          <a-form-item label="关联方证件号">{{ data.certificateNo }}</a-form-item>
        </div>
      </a-form>
    </template>
  </CommonApprovalModal>
</template>

<script lang="ts" setup>
import type { SelectValue } from 'ant-design-vue/es/select';
import { debounce } from 'lodash-es';
import { IconChevronLeftDoubleFont, IconChevronRightDoubleFont, IconTongyongXinzengFont, IconDelFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { RadioChangeEvent } from 'ant-design-vue/es/radio';
import type { ValueType } from 'ant-design-vue/es/vc-cascader/Cascader';
import type { SignData } from '../../insuranceFormFill.d';
import type { companyInfo, CustomerInfo, OptionsData } from './customerInfo';
import RangeTimePicker from './RangeTimePicker.vue';
import type { EoaData } from '@/pages/insure/insuranceFormFill/insuranceFormFill.d';
import { pxToRem } from '@/utils/tools';
import { usePost, $postOnClient } from '@/composables/request';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { checkClientName, isPostCode, isMobilePhone, isEmail, checkIDCardNo, isPassport, isSoldierCard, isDriveCard, isHM, checkHMT, checkHMResident, checkHMRP, checkTWP, isOrgCode, isTaxationNo, isBusinessNo, isLicenseNo, isSocialCreditNumber, checkForeignerCard } from '@/utils/validators';
import IdentifyModal from '@/pages/insure/insuranceFormFill/components/customerInfo/IdentifyModal.vue';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';

const props = defineProps<{
  optionsData: OptionsData; // 基础数据
  insurantFiledDisabled?: boolean; // 是否需要置灰被保人名称、证件类型、证件号码、联系电话
  customerType: string; // 客户类型 insurantInfo被保人 applicantInfo投保人 beneficaryInfo受益人
  isAntiMoney: boolean; // 是否触发反洗钱 个人和团体部分字段变为必录
  showRelatedApproval?: boolean; // 是否显示关联方交易识别签报
  totalActualPremium?: string; // 保单保费
  applyPolicyNo?: string; // 投保单号
  combinedProductName?: string; // 标的类型
  departmentCode?: string; // 机构码
  productName?: string; // 产品名称
  productCode?: string; // 产品编码
  signDataList?: SignData[]; // 签报状态
  isForestry?: boolean;
  secDeptCode?: string; // 二级机构
}>();

const formRef = ref();
// 签报信息
const signData = computed(() => {
  return props?.signDataList?.filter((item) => item?.signType === '033')?.[0];
});

const { gateWay, service } = useRuntimeConfig().public || {};
// 表单数据
const data = defineModel<CustomerInfo>('data', { default: {} });
const insuranceRelationship = defineModel<boolean>('insuranceRelationship', { default: {} });

const visible = ref(false); // 是否展示弹窗
const insuredExpand = ref(false); // 被保险人信息展开/收起
// 团体名称option数据
const nameOptions = ref<Array<companyInfo>>([]);

// 选择团体
const isInsuredGroup = computed(() => data.value.personnelType === '0');
// 受益所有人最后一次出现的位置
const beneficaryLastIndex = computed(() => {
  const superviseExtendList = data.value.superviseInfoList?.[0].superviseExtendList || [];
  // 找到所有公司关系类型为 '4' 的项
  const filteredList = superviseExtendList.filter((item) => item.companyRelationType === '4');
  // 返回最后一个匹配项的索引
  return filteredList.length > 0 ? superviseExtendList.lastIndexOf(filteredList[filteredList.length - 1]) : -1;
});
// 控制股东/实际控制人最后一次出现的位置
const controlLastIndex = computed(() => {
  const superviseExtendList = data.value.superviseInfoList?.[0].superviseExtendList || [];
  // 找到所有公司关系类型为 '1' 的项
  const filteredList = superviseExtendList.filter((item) => item.companyRelationType === '1');
  // 返回最后一个匹配项的索引
  return filteredList.length > 0 ? superviseExtendList.lastIndexOf(filteredList[filteredList.length - 1]) : -1;
});

// // 受益人与被保人关系下拉选择 选择与被保人一致需要去除本人选项(个人)
const relationOption = computed(() => {
  if (props?.optionsData?.relation) {
    const sameOption = props.optionsData.relation.filter((item) => item.value !== '01');
    return sameInsuredPersons.value ? props.optionsData.relation : sameOption;
  } else {
    return [];
  }
});

// // 受益人与被保人关系下拉选择 选择与被保人一致需要去除本人选项(团体)
const relationGroupOption = computed(() => {
  if (props?.optionsData?.relationGroup) {
    const sameOption = props.optionsData.relationGroup.filter((item) => item.value !== '01');
    return sameInsuredPersons.value ? props.optionsData.relationGroup : sameOption;
  } else {
    return [];
  }
});

// 行业 由于后端返回结构和antd组件数据源结构不一致，这里需要把行业的两个字段转为数组
const industry = computed({
  get() {
    if (!data?.value?.industryCode) {
      return [];
    } else if (!data?.value?.industryCodeLevel2) {
      return [data?.value?.industryCode];
    }
    return [data.value.industryCode, data.value.industryCodeLevel2] as ValueType;
  },
  set(value) {
    data.value.industryCode = value?.[0] as ValueType;
    data.value.industryCodeLevel2 = value?.[1] as ValueType;
  },
});

// 职业 由于后端返回结构和antd组件数据源结构不一致，这里需要把职业的两个字段转为数组
const profession = computed({
  get() {
    if (!data?.value?.professionCode) {
      return [];
    } else if (!data?.value?.professionCodeLevel2) {
      return [data?.value?.professionCode];
    }
    return [data.value.professionCode, data.value.professionCodeLevel2] as ValueType;
  },
  set(value) {
    data.value.professionCode = value?.[0] as ValueType;
    data.value.professionCodeLevel2 = value?.[1] as ValueType;
  },
});

// 是否与被保人一致 由于后端返回结构和antd组件单选框数据结构不一致 这里需要把字符串（0不一致 1一致）转为boolean
const sameInsuredPersons = computed({
  get() {
    return data?.value?.sameInsuredPersons !== '0';
  },
  set(value) {
    data.value.sameInsuredPersons = value ? '1' : '0';
  },
});

// 选择是否与被保险人一致
const checkboxChange = (e: { target: { checked: boolean } }) => {
  if (!e?.target?.checked) {
    visible.value = true;
  } else {
    // 选择一致将与被保人关系字段设置为本人
    if (props.customerType === 'beneficaryInfo') {
      // 受益人
      data.value.relationshipWithInsured = '01';
    }
  }
};

// 证件类型校验映射
const personalCertificateValidators: {
  [key: string]: (rule: Rule, value: string) => Promise<void>;
} = {
  '01': checkIDCardNo,
  '02': isPassport,
  '03': isSoldierCard,
  '04': isHM,
  '05': isDriveCard,
  '06': checkHMT,
  '07': checkIDCardNo,
  '08': checkForeignerCard,
  '09': checkHMResident,
  '41': checkHMRP,
  '42': checkTWP,
  '99': (rule: Rule, value: string) => {
    if (/[\u4E00-\u9FA5]/g.test(value)) {
      return Promise.reject('不能输入中文');
    }
    return Promise.resolve();
  },
};

const groupCertificateValidators: { [key: string]: (rule: Rule, value: string) => Promise<void> } = {
  '01': (rule: Rule, value: string) => {
    value = value?.trim().replace(/\s+/g, '');
    return isOrgCode(rule, value);
  },
  '02': (rule: Rule, value: string) => {
    value = value?.trim().replace(/\s+/g, '');
    return isTaxationNo(rule, value);
  },
  '04': isBusinessNo,
  '06': isLicenseNo,
  '08': (rule: Rule, value: string) => {
    value = value?.trim().replace(/\s+/g, '');
    return isSocialCreditNumber(rule, value);
  },
  '99': (rule: Rule, value: string) => {
    if (/[\u4E00-\u9FA5]/g.test(value)) {
      return Promise.reject('不能输入中文');
    }
    return Promise.resolve();
  },
};

// 证件号码校验
const IDValidator = (rule: Rule, value: string) => {
  const { certificateType } = data.value;
  if (!certificateType) {
    return Promise.reject('证件类别不能为空');
  }
  if (!value) {
    return Promise.reject('证件号码不能为空');
  }

  if (data.value.personnelType === '1') {
    const validator = personalCertificateValidators[certificateType];
    if (validator) {
      return validator(rule, value);
    }
  } else if (data.value.personnelType === '0') {
    const validator = groupCertificateValidators[certificateType];
    if (validator) {
      return validator(rule, value);
    }
  }

  return Promise.resolve();
};

// 监管者证件号码校验
const personValidator = (rule: Rule, value: string, certificateType: string) => {
  if (!value) {
    return Promise.resolve();
  }

  const validator = personalCertificateValidators[certificateType];
  if (validator) {
    return validator(rule, value);
  }

  return Promise.resolve();
};
// 年收入校验
const yearlySalariesValidator = (rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  // 正则表达式：最多10位整数，最多两位小数
  const regex = /^\d{1,10}(\.\d{1,2})?$/;

  if (regex.test(value)) {
    return Promise.resolve();
  } else {
    return Promise.reject('请输入最多10位整数及最多2位小数的数值');
  }
};

// 占比校验
const percentValidator = (rule: Rule, value: number) => {
  if (value > 100) {
    return Promise.reject('占比不能大于100');
  }
  return Promise.resolve();
};

// 团体名称改变
const nameSelect = (option: { clientName: string; creditCode: string; uuid: string }) => {
  // 点击确定自动填充证件类型和证件号码
  data.value.name = option.clientName;
  data.value.certificateType = '08';
  data.value.certificateNo = option.creditCode;
  data.value.cid = option.uuid;
  formRef?.value.validate(['certificateNo']);
  formRef?.value.validate(['certificateType']);
  formRef?.value.validate(['name']);
  data.value.certifyResult = '1';
};

// 证件类型改变
const checkCertificateNo = () => {
  formRef?.value.validate(['certificateNo']);
  // 身份证号码判断性别
  if (data.value.personnelType === '1' && data.value.certificateType === '01') {
    if (data.value.certificateNo && data.value.certificateNo.length === 18) {
      // 确保是18位身份证
      const genderDigit = parseInt(data.value.certificateNo.charAt(16));
      data.value.sexCode = genderDigit % 2 === 0 ? '1' : '0';
    } else {
      data.value.sexCode = ''; // 清空性别状态
    }
  }
};

// 新增监管者
const addPeople = (peopleType: string, index: number) => {
  const defaultPeople = {
    companyRelationType: peopleType,
    certificateNo: '', // 证件号码
    certificateType: '', // 证件类型
    certificateIssueDate: '', // 证件有效期开始日期
    certificateValidDate: '', // 证件有效期截止日期
    name: '', // 名称
    address: '', // 地址
    benefitMode: '', // 判定受益所有人方式
    equityRatio: '', // 受益所有人持股数量或表决权占比
  };
  data.value.superviseInfoList[0].superviseExtendList.splice(index + 1, 0, defaultPeople);
};

// 删除监管者
const deletePeople = (peopleType: string, index: number) => {
  const filterData = data.value.superviseInfoList[0].superviseExtendList.filter((item) => item.companyRelationType === peopleType);
  if (filterData.length === 1) {
    message.info('至少保留一个！');
    return;
  }
  data.value.superviseInfoList[0].superviseExtendList.splice(index, 1);
};

const labelColStyle = {
  style: {
    width: pxToRem(100),
  },
};
type AddressType = 'province' | 'city' | 'county';

// 被保险人地址
const address = ref({
  provinceName: '',
  cityName: '',
  countyName: '',
});

// 地址改变
const changeAddress = (option: { label: string; postcode: string }, type: AddressType) => {
  if (['province', 'city', 'county'].includes(type)) {
    address.value[`${type}Name` as 'provinceName' | 'cityName' | 'countyName'] = option?.label || '';
  }
  data.value.address = address.value.provinceName + address.value.cityName + address.value.countyName;
  data.value.postcode = option?.postcode;
  nextTick(() => {
    formRef?.value.validate(['address']);
    if (data.value.postcode) {
      formRef?.value.validate(['postcode']);
    }
  });
};

// 监管者地址改变
const changeSuperviseAddress = (option: { label: string }, type: string, index: number) => {
  if (['province', 'city', 'county'].includes(type)) {
    data.value.superviseInfoList[0].superviseExtendList[index][`${type}Name` as 'provinceName' | 'cityName' | 'countyName'] = option?.label || '';
  }

  const provinceName = data.value.superviseInfoList[0].superviseExtendList[index]?.provinceName || '';
  const cityName = data.value.superviseInfoList[0].superviseExtendList[index]?.cityName || '';
  const countyName = data.value.superviseInfoList[0].superviseExtendList[index]?.countyName || '';

  data.value.superviseInfoList[0].superviseExtendList[index].address = provinceName + cityName + countyName;
};

// 团体改变
const radioChange = (e: RadioChangeEvent) => {
  if (e?.target?.value === '0') {
    if (!data?.value?.superviseInfoList?.[0]?.superviseExtendList?.length || data?.value?.superviseInfoList?.[0]?.superviseExtendList?.length < 1) {
      // 切换的时候 赋予默认值
      data.value = {
        ...data.value,
        // relationshipWithApplicant: '',
        certificateType: '',
        relationshipWithInsured: '',
        superviseInfoList: [
          {
            superviseExtendList: [
              {
                companyRelationType: '3',
              },
              {
                companyRelationType: '2',
              },
              {
                companyRelationType: '1',
              },
              {
                companyRelationType: '4',
              },
            ],
          },
        ],
      };
    }
  } else {
    data.value = {
      ...data.value,
      // relationshipWithApplicant: '',
      relationshipWithInsured: '',
      certificateType: '',
      certifyResult: '',
      superviseInfoList: [
        {
          superviseExtendList: [],
        },
      ],
    };
  }
  // 被保人模块切换自然人和团体时，如果与投保人关系不是本人或本单位，则清空
  if (props.customerType === 'insurantInfo' && data.value.relationshipWithApplicant !== '01') {
    nextTick(() => {
      data.value.relationshipWithApplicant = '';
    });
  }
};

// 初始化监管者信息
const initSupervise = () => {
  const superviseList = data?.value?.superviseInfoList?.[0]?.superviseExtendList || [];

  if (superviseList.length === 0) {
    data.value.superviseInfoList = [
      {
        superviseExtendList: [{ companyRelationType: '3' }, { companyRelationType: '2' }, { companyRelationType: '1' }, { companyRelationType: '4' }],
      },
    ];
  } else {
    // 按照授权办理业务人员、法定代办人/负责人、控股股东/实际控制人、受益所有人的顺序排序
    const order = { 3: 1, 2: 2, 1: 3, 4: 4 };
    data.value.superviseInfoList = data.value.superviseInfoList.map((item) => ({
      ...item,
      superviseExtendList: item.superviseExtendList.sort((a, b) => order[a.companyRelationType as unknown as keyof typeof order] - order[b.companyRelationType as unknown as keyof typeof order]),
    }));
  }
};

// 展开收起切换
const handleExpand = () => {
  if (!insuredExpand.value) {
    initSupervise();
  }
  insuredExpand.value = !insuredExpand.value;
};

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

const clearValidate = async () => {
  if (formRef.value) {
    await formRef.value.clearValidate();
  }
};

defineExpose({
  validate,
  clearValidate,
});

const queryOrganizationInfo = await usePost<companyInfo[]>(`${gateWay}${service.accept}/web/applicationForm/queryOrganizationInfo`);

// 团体名称搜索
const nameChange = debounce(async (value: string) => {
  data.value.certifyResult = '0';
  const params = {
    clientName: value,
    orgNo: '',
    creditCode: '',
  };
  if (value) {
    const res = await queryOrganizationInfo.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      nameOptions.value = res.data.map((it) => {
        return {
          ...it,
          value: it.clientName,
        };
      });
    }
  }
}, 1000);

// 团体证件号码改变调用cis接口回显
const certificateNoChange = debounce(async (e) => {
  // 只有在证件为01（组织机构代码证）和08（统一社会信用代码）才会调用
  if (data.value.personnelType === '0' && ['01', '08'].includes(data.value.certificateType as string)) {
    const params = {
      clientName: '',
      orgNo: data.value.certificateType === '01' ? e.target.value : '',
      creditCode: data.value.certificateType === '08' ? e.target.value : '',
    };
    const res = await queryOrganizationInfo.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      if (res?.data && res?.data?.length > 0) {
        nameOptions.value = res.data;
        data.value.name = res.data?.[0]?.clientName;
        data.value.certifyResult = '1';
        data.value.cid = res.data?.[0]?.uuid;
      } else {
        nameOptions.value = [];
        data.value.name = '';
        data.value.certifyResult = '0';
      }
    }
  }
  // 身份证号码判断性别
  if (data.value.personnelType === '1' && data.value.certificateType === '01') {
    if (e.target.value.length === 18) {
      // 确保是18位身份证
      const genderDigit = parseInt(e.target.value.charAt(16));
      data.value.sexCode = genderDigit % 2 === 0 ? '1' : '0';
    } else {
      data.value.sexCode = ''; // 清空性别状态
    }
  }
}, 1000);
// 弹窗点击取消
const handleCancel = () => {
  data.value.sameInsuredPersons = '1';
};

// 弹窗点击确认
const handleOK = () => {
  visible.value = false;
  // 确认置空数据
  data.value = {
    id: data?.value?.id,
    personnelType: '1',
    sameInsuredPersons: '0',
    relationshipWithInsured: '',
    relationshipWithApplicant: '',
    nationality: '156',
    superviseInfoList: [
      {
        superviseExtendList: [],
      },
    ],
  };
};

onActivated(() => {
  insuredExpand.value = false;
});
// 团体名称失去焦点
const nameBlur = () => {
  // 未校验
  if (noChecked.value && data.value.name) {
    handleIdentify();
    // TODO: 证件识别
    // 1、显示 - 证件识别按钮
    // 2、点击证件识别按钮，弹框显示
    //     2、1 标题：您输入的客户名称未验证，请上传并识别客户证件影像
    //     2、2 内容：上传客户证件图片（必填）
    //     2、3 注：图片格式为JPG、PNG；如无证件图片，请点击“暂无证件照片”按钮，进行手动录入
    //     2、4 底部按钮：暂无证件照片、确认上传
    //     2、5 待上传文件：xxx.jpg
    //
    // 3、CIS接口返回
    //    3、1 信息不全，显示“无所需结果”
    //    3、2 提示：请上传并识别客户证件影像
  }
};
// 未校验
const noChecked = computed(() => data.value?.personnelType === '0' && data.value.certifyResult === '0');
/**
 * 证件识别-弹框
 */
const identifyObj = ref({
  visible: false,
  documentType: '', // 类型
  documentGroupId: '', // id
});
// 确定-cb
const identifyOkBtn = async (res = { companyNameValue: '', usccValue: '' }) => {
  const { usccValue, companyNameValue } = res || {};
  if (Object.keys(res).length > 0) {
    // 仅此种结果为已认证
    if (companyNameValue && usccValue) {
      data.value.name = companyNameValue;
      // 参考旧系统 默认ai返回的就是统一社会信用代码(08)
      data.value.certificateType = '08';
      data.value.certificateNo = usccValue;
      data.value.certifyResult = '1';
      const params = {
        clientName: companyNameValue,
        orgNo: '',
        creditCode: usccValue,
      };
      const res = await queryOrganizationInfo.fetchData(params);
      if (res && res.code === SUCCESS_CODE) {
        if (res?.data && res?.data?.length > 0) {
          data.value.cid = res.data?.[0]?.uuid;
        } else {
          data.value.cid = '';
        }
      }
    } else {
      // 仅识别出客户名称或证件编码仍然未认证
      data.value.certifyResult = '0';
      if (companyNameValue) {
        data.value.name = companyNameValue;
      }
      if (usccValue) {
        data.value.certificateNo = usccValue;
      }
    }
  }
  formRef?.value.validate(['certificateNo']);
  formRef?.value.validate(['certificateType']);
};
// 类型
const documentType = computed(() => {
  let documentType = '';
  const { customerType } = props;
  // 被保险人信息
  if (customerType === 'insurantInfo') {
    documentType = 'A13_01';
  } else if (customerType === 'applicantInfo') {
    // 投保人信息
    documentType = 'A13_02';
  } else if (customerType === 'beneficaryInfo') {
    // 受益人信息
    documentType = 'A13_03';
  }

  if (sameInsuredPersons.value) {
    // 被保人信息 投保人信息 被受益人信息 - sameInsuredPersons
    documentType = 'A13_01,A13_02,A13_03';
  }

  return documentType;
});
// 号码
const documentGroupId = computed(() => data.value?.certificateNo);
// 证件识别
const handleIdentify = () => {
  nextTick(() => {
    identifyObj.value.visible = true;
  });
};
// 发起签报
const showApproval = ref<boolean>(false);
const previewOpen = ref<boolean>(false);
const handleApproval = () => {
  showApproval.value = true;
};
const emits = defineEmits(['getEoaData', 'changeInsuranceRelationship']);
// 提交签报
const handleSubmit = async (eoaData: EoaData) => {
  try {
    // 用于提交签报的参数
    const params = {
      attachmentList: eoaData.fileInfos,
      relationBusinessNo: props.applyPolicyNo,
      eoaType: '033', // 关联方交易识别签报
      eoaSubject: `关于${props.applyPolicyNo}投保单关联方投保申请`,
      eoaBody: props.applyPolicyNo,
      documentGroupId: eoaData.documentGroupId,
      departmentCode: props.departmentCode,
      productCode: props.productCode,
      businessData: {
        planTypeName: props.combinedProductName,
        productName: props.productName,
        actualPremium: props.totalActualPremium,
        applicantName: data.value.name,
        certificateNo: data.value.certificateNo,
        certificateType: data.value.certificateType,
        personnelType: data.value.personnelType,
      },
      approveChainList: eoaData.approveChainList,
    };
    const res = await $postOnClient<{ msg: string; code: string }>(`${gateWay}${service.accept}/accept/eoa/createEoa`, params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      showApproval.value = false;
      emits('getEoaData');
    } else {
      message.error(res?.msg as string);
    }
  } catch (error) {
    console.log(error);
  }
};
// 根据签报号跳转eoa
const goEoa = (url: string) => {
  window.open(url);
};
watch(
  () => props.isAntiMoney,
  () => {
    // 触发反洗钱 展开更多
    if (props?.isAntiMoney && props?.customerType === 'insurantInfo') {
      insuredExpand.value = true;
    }
  },
);
watch(
  () => data.value.relationshipWithApplicant,
  () => {
    // 被保人模块中与投保人关系为本人时，需要将投保人模块中与被保人一致勾选上
    if (props.customerType === 'insurantInfo') {
      if (data.value.relationshipWithApplicant === '01') {
        insuranceRelationship.value = true;
      } else {
        insuranceRelationship.value = false;
      }
    }
  },
);
watch(insuranceRelationship, () => {
  // 投保人模块中，当insuranceRelationship为true时，勾选与被保人一致
  if (props.customerType === 'applicantInfo') {
    if (insuranceRelationship.value === true) {
      data.value.sameInsuredPersons = '1'; // 一致
    } else {
      data.value.sameInsuredPersons = '0'; // 不一致
    }
  }
  // 被保人模块中，当insuranceRelationship为true时，与被保人关系选择为本人，否则清空选择框
  if (props.customerType === 'insurantInfo') {
    if (insuranceRelationship.value === true) {
      data.value.relationshipWithApplicant = '01';
    } else {
      if (data.value.relationshipWithApplicant === '01') {
        data.value.relationshipWithApplicant = '';
      }
    }
  }
});
watch(
  () => data.value.sameInsuredPersons,
  () => {
    // 投保人模块中，勾选与被保人一致，需要在被保人模块中选择关系为本人，取消勾选与被保人一致，如果被保人模块中 与投保人关系为本人，则需清空与投保人关系选择框
    if (props.customerType === 'applicantInfo') {
      if (data.value.sameInsuredPersons === '1') {
        // 一致
        insuranceRelationship.value = true;
      } else {
        insuranceRelationship.value = false;
      }
    }
  },
);

const handleChangePoorSymbol = (val: SelectValue) => {
  if (val !== '1') {
    console.log('handleChangePoorSymbol: ', val);
    data.value.poorDetailType = '';
  }
};
</script>
