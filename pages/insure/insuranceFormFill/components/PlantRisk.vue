<template>
  <a-form ref="formRef" :model="riskList" :colon="false">
    <!-- 单标的 -->
    <template v-if="riskList.length > 0">
      <div v-for="(item, index) in riskList" :key="index">
        <div class="text-[#404442] text-[14px] font-semibold flex items-center mb-[16px]">
          <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
          {{ `标的${index + 1}` }}
        </div>
        <div class="grid grid-cols-3 gap-x-16px">
          <a-form-item required label="标的明细" :name="[index, 'combinedProductCode']" class="col-span-2" :label-col="{ style: { width: '90px' } }" :rules="[{ required: true, message: '请选择第5级标的' }]">
            <RiskCodeSelect ref="riskSelectRef" :start-level="3" :default-value="item.combinedProductCode" :department-code="departmentCode" :show-search="false" disabled allow-other-edit @change-value="handleRiskChange" />
          </a-form-item>
          <a-form-item required label="保险数量" :name="[index, 'riskAgrInfo', 'insuredNumber']" :rules="[{ validator: () => checkInsured(index) }]" :label-col="{ style: { width: '90px' } }">
            <a-input-group compact>
              <a-input v-model:value="riskList[index].riskAgrInfo.insuredNumber" disabled style="width: 70%" />
              <a-input v-model:value="riskList[index].riskAgrInfo.insuredUnitChName" disabled style="width: 30%" />
            </a-input-group>
          </a-form-item>
          <a-form-item label="保险金额" :name="[index, 'riskAgrInfo', 'insuredAmount']" :label-col="{ style: { width: '90px' } }">
            <a-input v-model:value="riskList[index].riskAgrInfo.insuredAmount" disabled />
          </a-form-item>
          <a-form-item label="保费金额" :name="[index, 'riskAgrInfo', 'premium']" :label-col="{ style: { width: '90px' } }" disabled>
            <a-input v-model:value="riskList[index].riskAgrInfo.premium" name="premium" :rules="[{ required: true }]" disabled />
          </a-form-item>
          <a-form-item required label="参保农户数" :name="[index, 'riskAgrInfo', 'farmersCount']" :rules="[{ required: true }]" :label-col="{ style: { width: '90px' } }">
            <a-input v-model:value="riskList[index].riskAgrInfo.farmersCount" disabled />
          </a-form-item>
          <a-form-item v-if="!isMultiTarget" required label="标的地址" class="col-span-3" :label-col="{ style: { width: '90px' } }">
            <div class="flex gap-x-8px">
              <RegionSelect v-model:province="addressList[0].province" v-model:city="addressList[0].city" v-model:county="addressList[0].county" v-model:town="addressList[0].town" v-model:village="addressList[0].village" :disabled="true" style="width: 65%" />
              <a-input v-model:value="addressList[0].address" placeholder="请输入" disabled style="width: 35%" />
            </div>
          </a-form-item>
        </div>
        <div v-if="riskList[index].riskAgrInfo.agrRiskAttribute?.length" class="grid grid-cols-3 gap-x-16px" :class="{ 'divide-line': !isMultiTarget }">
          <template v-for="(obj, idx) in riskList[index].riskAgrInfo.agrRiskAttribute">
            <!-- 单位保险产量及单位 -->
            <a-form-item
              v-if="obj.bussinessKey === 'unitInsureCount'"
              :key="obj.bussinessKey"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                { validator: () => checkUnitInsureCount(obj.notRequiredFlag, index) },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过20位，小数位不超过2位',
                },
              ]"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
            >
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="unitInsureCountOptions" style="width: 30%" />
              </a-input-group>
            </a-form-item>
            <!-- 约定价格（指数）及单位 -->
            <a-form-item
              v-if="obj.bussinessKey === 'priceAgreed'"
              :key="obj.bussinessKey"
              :label="obj.bussinessType"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :rules="[
                { required: !obj.notRequiredFlag },
                { validator: () => checkPriceAgreed(obj.notRequiredFlag, index) },
                {
                  pattern: /^\d{1,6}(\.\d{1,4})?$/,
                  message: '输入整数且整数位不超过6位，小数位不超过4位',
                },
              ]"
            >
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="priceAgreedOptions" style="width: 30%" />
              </a-input-group>
            </a-form-item>
            <!-- 周期 -->
            <a-form-item v-if="obj.bussinessKey === 'claimsCycle'" :key="obj.bussinessKey" required :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :rules="[{ required: true }]">
              <a-select v-model:value="obj.bussinessValue" :options="claimsCycleOptions" />
            </a-form-item>
            <!-- 每亩PH值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'agreedPh'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="`每${unitName || '亩'}PH值`"
              :rules="[
                { required: true },
                {
                  pattern: /^\d{1,12}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过12位，小数位不超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 每亩有机质值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'organicValue'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="`每${unitName || '亩'}有机质值`"
              :rules="[
                { required: true },
                {
                  pattern: /^\d{1,12}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过12位，小数位不超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 养殖险start -->
            <!-- 疾病观察期 -->
            <a-form-item
              v-if="obj.bussinessKey === 'diseaseObservation' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^[0-9]\d*$/,
                  message: '请输入非负整数',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" @change="(e) => changeDiseaseObservation(e, index)" />
            </a-form-item>
            <!-- 投保比例 -->
            <a-form-item
              v-if="obj.bussinessKey === 'insureProportion'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^(100|[1-9]?\d)(\.\d{1,4})?$/,
                  message: '请输入0~100范围内的数字且小数位不能超过4位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 养殖方式 -->
            <a-form-item v-if="obj.bussinessKey === 'farmingMethods'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="farmingMethodsOptions" />
            </a-form-item>
            <!-- 约定周期保险数量（头/只） -->
            <a-form-item
              v-if="obj.bussinessKey === 'cycleInsuredNumber'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^[1-9]\d*$/,
                  message: '请输入正整数',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 预计出栏日期 -->
            <a-form-item v-if="obj.bussinessKey === 'estimateSlaughterDate'" :key="obj.bussinessKey" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :required="!obj.notRequiredFlag" :label="obj.bussinessType">
              <a-date-picker v-model:value="obj.bussinessValue" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </a-form-item>
            <!-- 养殖数量(存栏量) -->
            <a-form-item
              v-if="obj.bussinessKey === 'breedAmount' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,9}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过9位，小数位不超过2位',
                },
                { validator: () => checkBreedAmountInput(obj.notRequiredFlag, index) },
              ]"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
            >
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="unitOptions" style="width: 30%" />
              </a-input-group>
              <span v-if="checkBreedAmount(index)" class="text-[#FAAD14]">承保保险数量超过/少于养殖数量，请关注</span>
            </a-form-item>
            <!-- 圈舍面积 -->
            <a-form-item
              v-if="obj.bussinessKey === 'pigstyArea' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d+(\.\d{1,2})?$/,
                  message: '小数部分不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="平方米" />
              <span v-if="checkPigstyArea(obj.bussinessValue, index)" class="text-[#FAAD14]">实际承保数量少于理论存栏数，请注意</span>
            </a-form-item>
            <!-- 养殖险end -->
            <!-- 林业险start -->
            <!-- 树龄（年） -->
            <a-form-item
              v-if="obj.bussinessKey === 'variousCountForest'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,14}(\.\d{1,2})?$/,
                  message: '最多14位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 平均密度（棵/亩） -->
            <a-form-item
              v-if="obj.bussinessKey === 'primeCostDensity2'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,14}(\.\d{1,2})?$/,
                  message: '最多14位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 林木属性 -->
            <a-form-item v-if="obj.bussinessKey === 'forestAttribute'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="forestAttributeOptions" />
            </a-form-item>
            <!-- 林木用途 -->
            <a-form-item v-if="obj.bussinessKey === 'forestBreed'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="forestBreedOptions" />
            </a-form-item>
            <!-- 每亩约定碳汇目标值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkTargetValue'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,14}(\.\d{1,4})?$/,
                  message: '最多14位正数且小数位不能超过4位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="吨" @change="(e) => handleCarbonChange(e, index)" />
            </a-form-item>
            <!-- 约定碳汇单价 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkPrice'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,14}(\.\d{1,2})?$/,
                  message: '最多14位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="元/吨" @change="(e) => handlePriceChange(e, index)" />
            </a-form-item>
            <!-- 约定碳汇目标增速 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkTargetRate'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,12}(\.\d{1,4})?$/,
                  message: '最多12位正数且小数位不能超过4位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="%" />
            </a-form-item>
            <!-- 林业险结束 -->
            <!-- 涉农 -->
            <!-- 农事作业区域 -->
            <a-form-item v-if="obj.bussinessKey === 'workArea'" :key="obj.bussinessKey" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :rules="[{ required: !obj.notRequiredFlag }, { max: 1000, message: '最多输入1000个字符' }]">
              <a-input v-model:value="obj.bussinessValue" />
            </a-form-item>
            <!-- 主要设备类型 -->
            <a-form-item v-if="obj.bussinessKey === 'mainDeviceType'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="mainDeviceTypeOptions" />
            </a-form-item>
          </template>
        </div>
      </div>
      <div class="divide-line"></div>
      <a-form-item v-if="isMultiTarget" required label="标的地址" class="col-span-3" :label-col="{ style: { width: '90px' } }">
        <div class="flex gap-x-8px">
          <RegionSelect v-model:province="addressList[0].province" v-model:city="addressList[0].city" v-model:county="addressList[0].county" v-model:town="addressList[0].town" v-model:village="addressList[0].village" :disabled="true" style="width: 65%" />
          <a-input v-model:value="addressList[0].address" placeholder="请输入" disabled style="width: 35%" />
        </div>
      </a-form-item>
    </template>
    <a-empty v-else :image="simpleImage" />
  </a-form>
  <a-modal v-model:open="infoVisible" :closable="false" :width="pxToRem(400)">
    <div class="flex items-center">当前录入疾病观察期超30天，请核实是否正确，如误录会造成后续理赔无法正常进行</div>
    <template #footer>
      <a-button @click="goEdit">返回修改</a-button>
      <a-button type="primary" @click="infoVisible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import type { RiskAddressInfo, RiskGroupInfo } from '../insuranceFormFill';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { $getOnClient } from '@/composables/request';
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, multiply } from '@/utils/tools';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const route = useRoute();

const props = defineProps<{
  departmentCode: string;
  productCode: string;
  productVersion: string;
  initRiskCode?: string;
}>();

const emits = defineEmits(['changeAmount']);

const unitOptions = [
  {
    value: '头',
    label: '头',
  },
  {
    value: '只',
    label: '只',
  },
  {
    value: '张',
    label: '张',
  },
];

const formRef = ref();
const addressList = defineModel<RiskAddressInfo[]>('addressList', {
  default: [],
});
const riskList = defineModel<RiskGroupInfo[]>('data', { default: [] });

const riskSelectRef = ref();
const unitName = computed(() => {
  return insuredNumberOptions.value.find((item) => item.value === riskList.value[0].riskAgrInfo.insuredUnit)?.label;
});
// 是否为多标的
const isMultiTarget = computed(() => {
  return riskList?.value?.length > 1;
});

// 标的变化
const handleRiskChange = (value, level, option) => {
  if (isMultiTarget.value) {
    return;
  }
  // 这里不太一样，需要选择第五级标的 临时修复标的第五级
  if (Array.isArray(riskSelectRef?.value[0]?.valueList) && riskSelectRef.value[0].valueList[4]) {
    riskList.value[0].combinedProductCode = riskSelectRef.value[0].valueList[4] || '';
  }
  // 其他可选，具体标的不能再选
  if (option && Array.isArray(riskSelectRef?.value[0]?.disabledList) && riskSelectRef.value[0].disabledList[level - 1]) {
    riskSelectRef.value[0].disabledList[level - 1] = option.encodeValue !== '其他';
  }
};

watch(
  () => route.query.applyPolicyNo,
  (oldValue, newValue) => {
    if (oldValue !== newValue) {
      if (riskSelectRef.value?.[0]?.disabledList) {
        riskSelectRef.value[0].disabledList = [true, true, true, true, true];
      }
    }
  },
  {
    immediate: true,
  },
);

const infoVisible = ref<boolean>(false);
const currentIndex = ref<number>(0);
// 修改疾病观察期
const changeDiseaseObservation = (e: Event, index: number) => {
  // 疾病观察期超30天，弹窗提示
  if (Number((e.target as HTMLInputElement).value) > 30) {
    infoVisible.value = true;
    currentIndex.value = index;
  }
};

// 返回修改疾病观察期
const goEdit = () => {
  // 疾病观察期重置为0
  const index = riskList.value[currentIndex.value].riskAgrInfo.agrRiskAttribute.findIndex((item) => item.bussinessKey === 'diseaseObservation');
  riskList.value[currentIndex.value].riskAgrInfo.agrRiskAttribute[index].bussinessValue = '';
  infoVisible.value = false;
};

// 校验填入的养殖数量是否等于保险数量
const checkBreedAmount = (index: number) => {
  const breedAmount = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'breedAmount')?.bussinessValue;
  const insuredAmount = riskList.value[index].riskAgrInfo.insuredNumber;

  if (!breedAmount || !insuredAmount) {
    return false;
  }

  return Number(breedAmount) !== Number(insuredAmount);
};

// 改变圈舍面积
const checkPigstyArea = (value: string, index: number) => {
  // 育肥猪  1.5  BB010000 能繁母猪 2 BB010100
  let culNum = 0;
  if (riskList.value[index].combinedProductCode === 'BB010000') {
    culNum = 1.5;
  } else if (riskList.value[index].combinedProductCode === 'BB010100') {
    culNum = 2;
  } else {
    return false;
  }

  // 实际承保数量（保险数量）
  const reality = Number(riskList.value[index].riskAgrInfo.insuredNumber);
  // 输入值为空时不校验
  if (!reality || !value) {
    return false;
  }
  // 理论存栏数
  const theory = Number(value) / culNum;
  // 实际承保数量小于理论存栏数时提示
  return reality / theory < 1;
};
// 保险数量，单位校验
const checkInsured = (index: number) => {
  if (!riskList.value[index].riskAgrInfo.insuredNumber) {
    return Promise.reject('请录入保险数量');
  }
  if (!riskList.value[index].riskAgrInfo.insuredUnit) {
    return Promise.reject('请录入保险数量单位');
  }
  return Promise.resolve();
};

// 单位保险数量校验
const checkUnitInsureCount = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'unitInsureCountUnit');
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入单位保险产量单位');
    }
  }

  return Promise.resolve();
};

// 价格指数校验
const checkPriceAgreed = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'priceAgreedUnit');
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入价格指数单位');
    }
  }

  return Promise.resolve();
};
// 养殖数量校验
const checkBreedAmountInput = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'breedUnit');
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入养殖数量单位');
    }
  }

  return Promise.resolve();
};
// 每亩约定碳汇目标值改变
const handleCarbonChange = (e: Event, index: number) => {
  // 约定碳汇单价
  const carbonSinkPrice = riskList?.value?.[index]?.riskAgrInfo?.agrRiskAttribute?.filter((item) => item.bussinessKey === 'carbonSinkPrice')?.[0]?.bussinessValue;
  // 林业险碳汇自动根据每亩约定碳汇目标值和约定碳汇单价 自动计算出主险单位保险金额
  if ((e.target as HTMLInputElement).value && carbonSinkPrice) {
    riskList?.value?.[index].planInfoList.forEach((subItem) => {
      if (subItem.isMain === '1') {
        subItem.unitInsuredAmount = Number(multiply(String(carbonSinkPrice), String((e.target as HTMLInputElement).value))).toFixed(2);
        emits('changeAmount', subItem.unitInsuredAmount);
      }
    });
  }
};
// 约定碳汇单价改变
const handlePriceChange = (e: Event, index: number) => {
  // 约定碳汇单价
  const carbonSinkTargetValue = riskList?.value?.[index]?.riskAgrInfo?.agrRiskAttribute?.filter((item) => item.bussinessKey === 'carbonSinkTargetValue')?.[0]?.bussinessValue;
  // 林业险碳汇自动根据每亩约定碳汇目标值和约定碳汇单价 自动计算出主险单位保险金额
  if ((e.target as HTMLInputElement).value && carbonSinkTargetValue) {
    riskList?.value?.[index].planInfoList.forEach((subItem) => {
      if (subItem.isMain === '1') {
        subItem.unitInsuredAmount = Number(multiply(String(carbonSinkTargetValue), String((e.target as HTMLInputElement).value))).toFixed(2);
        emits('changeAmount', subItem.unitInsuredAmount);
      }
    });
  }
};

defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
  clearValidate: async () => {
    if (formRef.value) {
      await formRef.value.clearValidate();
    }
  },
});

const { gateWay, service } = useRuntimeConfig().public || {};

const claimsCycleOptions = ref([]); // 周期下拉
const insuredNumberOptions = ref<SelectOptions[]>([]); // 保险数量单位
const unitInsureCountOptions = ref<SelectOptions[]>([]); // 单位保险数量下拉
const priceAgreedOptions = ref<SelectOptions[]>([]); // 价格指数下拉
const farmingMethodsOptions = ref<SelectOptions[]>([]); // 养殖方式下拉
const forestAttributeOptions = ref<SelectOptions[]>([]); // 林木属性下拉
const forestBreedOptions = ref<SelectOptions[]>([]); // 林木用途下拉
const mainDeviceTypeOptions = ref<SelectOptions[]>([]); // 主要设备类型下拉

onMounted(async () => {
  try {
    const res = await $post(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, ['AGRLPZQ', 'AGRYZCB', 'forestAttribute', 'forestBreed', 'mainDeviceType']);
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      // 周期下拉列表
      claimsCycleOptions.value = res.data.find((item) => item.value === 'AGRLPZQ')?.children || [];
      // 养殖方式下拉列表
      farmingMethodsOptions.value = res.data.find((item) => item.value === 'AGRYZCB')?.children || [];
      // 林木属性下拉
      forestAttributeOptions.value = res.data.find((item) => item.value === 'forestAttribute')?.children || [];
      // 林木用途下拉
      forestBreedOptions.value = res.data.find((item) => item.value === 'forestBreed')?.children || [];
      // 主要设备类型下拉
      mainDeviceTypeOptions.value = res.data.find((item) => item.value === 'mainDeviceType')?.children || [];
    }
  } catch (e) {
    console.log(e, 'init error');
  }
});

watch(
  () => [props.productCode, props.productVersion],
  async () => {
    if (props.productCode && props.productVersion) {
      const params = {
        productCode: props.productCode,
        productVersion: props.productVersion,
      };
      const numberRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryRiskNumberUnitList`, params);
      if (numberRes && numberRes?.code === SUCCESS_CODE) {
        insuredNumberOptions.value = numberRes.data || [];
      }
      const unitRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryProductionUnitList`, params);
      if (unitRes && unitRes?.code === SUCCESS_CODE) {
        unitInsureCountOptions.value = unitRes.data || [];
      }
      const indexRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryPriceIndexUnitList`, params);
      if (indexRes && indexRes?.code === SUCCESS_CODE) {
        priceAgreedOptions.value = indexRes.data || [];
      }
    }
  },
);
</script>

<style lang="less">
.divide-line {
  margin-top: 5px;
  padding-top: 16px;
  border-top: 1px solid #e6e8eb;
}
</style>
