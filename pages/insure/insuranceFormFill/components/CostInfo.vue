<template>
  <div>
    <a-form ref="formRef" :model="costInfo" :colon="false" :label-col="{ style: { width: pxToRem(110) } }">
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="财务标识" name="isPolicyBeforePayfee">
          <a-select v-model:value="costInfo.isPolicyBeforePayfee" class="w-full" :disabled="!isPolicyBeforePayFlag">
            <a-select-option value="1">是</a-select-option>
            <a-select-option value="0">否</a-select-option>
          </a-select>
        </a-form-item>
        <div v-if="signShow" class="flex col-span-2">
          <a-popover v-if="signData?.signStatus && signData?.signStatus !== '0'" placement="top">
            <template #content>
              <div>
                签报状态：{{ signData.signStatusDesc }}，签报号：<span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(signData.eoaDetailUrl)">{{ signData.eoaNo }}</span>
              </div>
            </template>
            <a-button class="mr-[8px]" type="primary">签报状态</a-button>
          </a-popover>
          <a-button type="primary" @click="openApproval">发起签报</a-button>
        </div>
      </div>
      <div v-show="signShow" class="col-span-12 mb-10px ml-12px text-[#F00] text-[12px]">除过我司从保业务外，非见费单需在页面触发【签报申请】，审批通过后才能申请核保。</div>
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="农险补贴" name="performanceValue1Default" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.performanceValue1Default" addon-after="%" />
        </a-form-item>
        <a-form-item :required="!commissionFlag" label="协办费" name="assisterCharge" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.assisterCharge" :disabled="commissionFlag" addon-after="%" @change="handleAssisterCharge" />
        </a-form-item>
        <a-form-item :required="!brokerageFlag" label="手续费/经纪费" name="commissionBrokerChargeProportion" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.commissionBrokerChargeProportion" :disabled="brokerageFlag" addon-after="%" @change="handleCommission" />
        </a-form-item>
      </div>
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item :required="!managementFeesFlag" label="工作经费" name="managementFees" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.managementFees" :disabled="managementFeesFlag" addon-after="%" />
        </a-form-item>
        <a-form-item :required="!disasterLossFlag" label="防灾防损费" name="calamitySecurityRate">
          <a-input v-model:value="costInfo.calamitySecurityRate" :disabled="disasterLossFlag" addon-after="%" />
        </a-form-item>
        <a-form-item label="共保出单费" name="coinsuranceInsureFeeRatio" :required="!commonGuaranteeFlag" :rules="[{ validator: coinsuranceInfoRule }]">
          <a-input v-model:value="costInfo.coinsuranceInsureFeeRatio" :disabled="commonGuaranteeFlag" addon-after="%" />
        </a-form-item>
      </div>
    </a-form>
    <!-- 总费率提示 -->
    <div v-show="totalRateFlag" class="col-span-12 mt-10px ml-16px text-[#F00] text-[12px]">费用比例之和超过{{ costInfo.totalSumFeeLimit }}%，请留意</div>
    <!-- 风险评估提示 -->
    <div v-show="riskTotalTips" class="col-span-12 mt-10px ml-16px text-[12px]" :class="isHighRiskMode ? 'text-[#f00]' : 'text-[#FAAD14]'">{{ riskTotalTips }}</div>
  </div>
  <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" title="非见费签报申请" eoa-type="T10" :total-actual-premium="totalActualPremium" :department-code="baseInfo.departmentCode" :eoa-title="eoaTitle" :file-required="true" @ok="handleSubmit" />
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { CostInfoType, BaseInfoType, CoinsuranceInfoType, EoaData, SignData } from '../insuranceFormFill';
import { pxToRem } from '@/utils/tools';
import { usePost } from '@/composables/request';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { SUCCESS_CODE } from '@/utils/constants';

const costInfo = defineModel<CostInfoType>('costInfo', { default: {} });
interface Props {
  baseInfo?: BaseInfoType; // 基础信息
  coinsuranceInfo?: CoinsuranceInfoType; // 共保信息
  businessSourceCode?: string; // 渠道来源
  signDataList: SignData[]; // 签报数据
  combinedProductCode: string; // 标的code
  totalActualPremium?: string; // 金额 用于发起签报
  isAcceptInsurance?: boolean; // 共从出单
}
const emits = defineEmits(['getEoaData']);
const props = withDefaults(defineProps<Props>(), {
  baseInfo: () => ({ assisterInfoList: [], coinsuranceMark: '', govSubsidyType: '', applyPolicyNo: '', departmentCode: '', totalActualPremium: '', acceptInsuranceFlag: '', productCode: '' }),
  coinsuranceInfo: () => ({ innerCoinsuranceMark: '', coinsuranceDetailList: [] }),
  businessSourceCode: '',
});
const { gateWay, service } = useRuntimeConfig().public || {};
// 是否展示审批弹窗
const showApproval = ref<boolean>(false);

// 打开审批弹窗
const openApproval = () => {
  if (props.baseInfo.totalActualPremium) {
    showApproval.value = true;
  } else {
    message.warning('请先保费计算');
  }
};
// 分值上限值为0时，禁用输入框
// const disabledLimitZero = (value: string) => {
//   return Number(value) === 0;
// };

// 非见费签报信息
const signData = computed(() => {
  return props?.signDataList?.filter((item) => item?.signType === '014')?.[0];
});

// eoa签报标题
const eoaTitle = computed(() => {
  return `关于投保单【${props.baseInfo.applyPolicyNo}】${props.baseInfo.productName}的出单申请`;
});

// 根据签报号跳转eoa
const goEoa = (url: string) => {
  window.open(url);
};

/** 协办费是否disabled
当非从共业务农保ID选择无时，协办费不可录入
1.系统外共保&是主承&农保id无 协办费禁用
2.系统内共保&农保id无 协办费禁用
3.系统外同时系统内共保&主承&农保id无 协办费禁用
渠道来源为代理禁用-2 */
const commissionFlag = computed(() => {
  const { innerCoinsuranceMark, coinsuranceDetailList } = props.coinsuranceInfo;
  const acceptInsuranceFlag = coinsuranceDetailList?.filter((item) => item.reinsureCompanyCode === '3005')?.[0]?.acceptInsuranceFlag || '1';
  const len = props.baseInfo.assisterInfoList?.length === 0 || props.baseInfo.assisterInfoList?.[0]?.assisterId === '无';
  const { coinsuranceMark } = props.baseInfo;
  return (len && (acceptInsuranceFlag === '1' || innerCoinsuranceMark === '1')) || (len && coinsuranceMark === '0');
});
// 如果协办费禁用，则设置默认值为0
watch(commissionFlag, (val) => {
  if (val) {
    costInfo.value.assisterCharge = '0';
  } else {
    costInfo.value.assisterCharge = '';
  }
});
// 防灾防损费是否disabled
const disasterLossFlag = ref<boolean>(true);
/** 共保出单是否disabled
系统外共保-0&主承-是则禁用；系统外同时系统内共保-2&主承则禁用；是否选择共保-否则禁用；上限值为0则禁用;选择共保类型-系统内共保禁用
innerCoinsuranceMark-1-系统内共保禁用 */
const commonGuaranteeFlag = computed(() => {
  const { coinsuranceMark } = props.baseInfo;
  const { innerCoinsuranceMark, coinsuranceDetailList } = props.coinsuranceInfo;
  const acceptInsuranceFlag = coinsuranceDetailList?.filter((item) => item.reinsureCompanyCode === '3005')?.[0]?.acceptInsuranceFlag || '1';
  return innerCoinsuranceMark === '1' || acceptInsuranceFlag === '1' || coinsuranceMark === '0';
});
// 出单保费禁用-清空值
watch(commonGuaranteeFlag, (val) => {
  if (val) {
    costInfo.value.coinsuranceInsureFeeRatio = '';
    formRef.value.clearValidate('coinsuranceInsureFeeRatio');
  }
});
/** 手续经费是否disabled-补贴类型为政策性1,2
补贴类型为政策性：baseInfo.govSubsidyType='1' 或者 '2'
补贴类型为商业性：baseInfo.govSubsidyType='3'
渠道来源细分包含"直接"：saleInfo.businessSourceCode='1'
渠道来源细分包含"代理"&"经纪"：saleInfo.businessSourceCode='2'或'3'
当补贴类型=政策性，此项置灰不允许填写；
当补贴类型=商业性&渠道来源细分包含"直接"， 此项置灰不允许填写
当补贴类型=商业性&渠道来源细分包含"代理/经纪"，此项必录 */
const brokerageFlag = computed(() => {
  // 补贴类型
  const { govSubsidyType } = props.baseInfo;
  // 业务来源
  return ['1', '2'].includes(govSubsidyType) || (govSubsidyType === '3' && props.businessSourceCode === '1');
});
// 禁用设置值为0
watch(brokerageFlag, (val) => {
  if (val) {
    costInfo.value.commissionBrokerChargeProportion = '0';
  } else {
    costInfo.value.commissionBrokerChargeProportion = '';
  }
});
// 由于保费计算后，后端值未落库，返回空，且brokerageFlag值是没有变化的，所以需要监听当前值，如果为禁用状态值添加0，防止表单校验住
watch(
  () => costInfo.value,
  () => {
    if (brokerageFlag.value) costInfo.value.commissionBrokerChargeProportion = '0';
    if (commissionFlag.value) costInfo.value.assisterCharge = '0';
    getPayFeeFlagSwitch();
  },
);
// 协办费有值，经纪代理费为0
const handleAssisterCharge = () => {
  costInfo.value.commissionBrokerChargeProportion = '0';
};
// 手续费/经纪费有值，经纪代理费为0
const handleCommission = () => {
  costInfo.value.assisterCharge = '0';
};
// 工作经费是否禁用
const managementFeesFlag = ref<boolean>(true);
// 选择否 && 不是从共出单-显示签报申请按钮&描述
const signShow = computed(() => costInfo.value.isPolicyBeforePayfee === '0' && !props.isAcceptInsurance);
// 校验
const validateNumberFee = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!value) {
    return Promise.reject();
  }
  if (!reg.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过2位');
  }
  if (Number(value) > 100) {
    return Promise.reject('请输入0~100范围内的数字');
  }
  const _name: string = field + 'Limit';
  if (!costInfo.value[_name]) {
    return Promise.reject(`费用上限未配置，无法录入，如有需要请联系核保人配置`);
  }
  if (Number(value) > Number(costInfo.value[_name])) {
    return Promise.reject(`费用上限配置为${costInfo.value[_name]}%，如需调整，请联系核保人`);
  }
  return Promise.resolve();
};
// 出单费校验
const coinsuranceInfoRule = (rule: Rule, value: string) => {
  if (!commonGuaranteeFlag.value) {
    return validateNumberFee(rule, value);
  } else {
    return Promise.resolve();
  }
};
const formRef = ref();
const validate = async () => {
  if (isHighRiskMode.value) {
    return { valid: false };
  }
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
// 当前输入的总费率计算,如过超过上限则提示
const totalRateFlag = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = costInfo.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);
  return Number(costInfo.value.totalSumFeeLimit) < count;
});
// 当前输入的总费率如果是风险评估，根据规则展示提示
const riskTotalTips = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = costInfo.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);
  const { busiModeNo } = props.baseInfo;

  // baseinfo.busiModeNo为01 商险业务模式为保险+期货，存在跟单费用，请注意
  if (busiModeNo === '01') {
    return '商险业务模式为保险+期货，存在跟单费用，请注意';
  }

  // baseinfo.busiModeNo为02 商险业务模式为二次开发类，存在跟单费用，请注意
  if (busiModeNo === '02') {
    return '商险业务模式为二次开发类，存在跟单费用，请注意';
  }

  // baseinfo.busiModeNo为03 15-20% 提示商险业务模式为银抵类，跟单费用超15%，请注意 超过20%提示商险业务模式为银抵类，跟单费用不可超20%，请调整
  if (busiModeNo === '03') {
    if (count > 20) {
      return '商险业务模式为银抵类，跟单费用不可超20%，请调整';
    } else if (count > 15) {
      return '商险业务模式为银抵类，跟单费用超15%，请注意';
    }
  }

  // baseinfo.busiModeNo为04 10-20% 商险业务模式为创新储备政策类，跟单费用超10%，请注意 超过20%提示商险业务模式为创新储备政策类，跟单费用不可超20%，请调整
  if (busiModeNo === '04') {
    if (count > 20) {
      return '商险业务模式为创新储备政策类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为创新储备政策类，跟单费用超10%，请注意';
    }
  }

  // baseinfo.busiModeNo为05 10-20% 商险业务模式为创新储备非政策类，跟单费用超10%，请注意 超过20%提示商险业务模式为创新储备非政策类，跟单费用不可超20%，请调整
  if (busiModeNo === '05') {
    if (count > 20) {
      return '商险业务模式为创新储备非政策类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为创新储备非政策类，跟单费用超10%，请注意';
    }
  }

  // baseinfo.busiModeNo为06 10-20% 商险业务模式为其他类，跟单费用超10%，请注意 超过20%提示商险业务模式为其他类，跟单费用不可超20%，请调整
  if (busiModeNo === '06') {
    if (count > 20) {
      return '商险业务模式为其他类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为其他类，跟单费用超10%，请注意';
    }
  }

  return '';
});

// 判断是否为03、04、05、06业务模式且费用大于20%
const isHighRiskMode = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = costInfo.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);
  const { busiModeNo } = props.baseInfo;

  const highRiskModes = ['03', '04', '05', '06'];
  return highRiskModes.includes(busiModeNo) && count > 20;
});

defineExpose({
  validate,
});
// 获取见费开关-财务标识是否disabled
const isPolicyBeforePayFlag = ref<boolean>(false);
const queryPayFeeFlagSwitch = await usePost<boolean>(`${gateWay}${service.administrate}/switchMulti/checkSwitchResult`);
const getPayFeeFlagSwitch = async () => {
  const params = {
    switchCode: 'ALLOW_BEFORE_PAY_FEE',
    departmentNo: props.baseInfo.departmentCode,
    agriculturalRiskObjectDetailCode: props.combinedProductCode,
    allowanceTypeCode: props.baseInfo?.govSubsidyType,
    coinsuranceFlag: props.baseInfo?.coinsuranceMark,
    mainInsureFlag: props.baseInfo?.acceptInsuranceFlag,
  };
  try {
    const res = await queryPayFeeFlagSwitch.fetchData(params);
    if (res && res.code === '000000') {
      isPolicyBeforePayFlag.value = res?.data;
    }
  } catch (error) {
    console.log(error);
  }
};
const createEoa = await usePost(`${gateWay}${service.accept}/accept/eoa/createEoa`);
// 发起签报
const handleSubmit = async (eoaData: EoaData) => {
  try {
    const params = {
      relationBusinessNo: props.baseInfo.applyPolicyNo,
      eoaType: '014',
      eoaSubject: eoaData.eoaTitle,
      documentGroupId: eoaData.documentGroupId,
      eoaBody: eoaData.eoaContent,
      productCode: props.baseInfo.productCode,
      departmentCode: props.baseInfo.departmentCode,
      approveChainList: eoaData.approveChainList,
      attachmentList: eoaData.fileInfos,
    };
    const res = await createEoa.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      emits('getEoaData');
      showApproval.value = false;
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
</script>
