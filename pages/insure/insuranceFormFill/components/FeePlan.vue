<template>
  <a-form ref="ownerFormRef" :model="baseInfo">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item label="责任人" name="premiumOwner" has-feedback :rules="[{ required: true, trigger: 'change', validator: ownerValid }]">
        <a-input v-model:value="baseInfo.premiumOwner" placeholder="请填写UM号" />
      </a-form-item>
    </div>
  </a-form>
  <a-form ref="formRef" :model="payInfoList" class="fee-plan">
    <a-table :columns="columns" :data-source="payInfoList" :pagination="false" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ column, index, record }">
        <!-- 补贴类型 -->
        <template v-if="column.dataIndex === 'payerType'">
          <a-form-item :name="[index, 'payerType']" :rules="[{ validator: typeValid }]">
            <a-select v-model:value="payInfoList[index].payerType" style="width: 100%" :options="payerTypeOptions" :disabled="payInfoList[index].payerType ? true : false" @change="(val) => changePayerType(val, index)" />
          </a-form-item>
        </template>
        <!-- 付款人名称 -->
        <template v-if="column.dataIndex === 'paymentPersonName'">
          <a-form-item :name="[index, 'paymentPersonName']" :rules="[{ required: true, message: '请填写付款人名称' }]">
            <a-input v-model:value="payInfoList[index].paymentPersonName" style="width: 100%" placeholder="请输入付款人名称" />
          </a-form-item>
        </template>
        <!-- 收付途径 -->
        <template v-if="column.dataIndex === 'paymentPath'">
          <a-form-item :name="[index, 'paymentPath']" :rules="[{ required: true, message: '收付途径不能为空' }]">
            <a-select v-model:value="payInfoList[index].paymentPath" style="width: 100%" :options="paymentPathOptions" @change="(val) => changePaymentPath(val, index)" />
          </a-form-item>
        </template>
        <!-- 应收保费 -->
        <template v-if="column.dataIndex === 'actualPremium'">
          <a-form-item :name="[index, 'actualPremium']" :rules="[{ validator: numValid }]">
            <a-input-number v-model:value="payInfoList[index].actualPremium" :min="0" :precision="2" @change="(val) => handleChange(val, index)" />
          </a-form-item>
        </template>
        <!-- 缴费起止期 -->
        <template v-if="column.dataIndex === 'paymentPeriod'">
          <div class="flex items-center">
            <a-form-item :name="[index, 'paymentBeginDate']">
              <a-date-picker v-model:value="payInfoList[index].paymentBeginDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" disabled />
            </a-form-item>
            <VueIcon class="mb-[12px] mx-[4px]" :icon="IconChevronRightFont" />
            <a-form-item :name="[index, 'paymentEndDate']" :rules="[{ validator: endDateValid }]">
              <a-date-picker v-model:value="payInfoList[index].paymentEndDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled-date="disabledDate(index)" />
            </a-form-item>
          </div>
        </template>
        <!-- 账户类型 -->
        <template v-if="column.dataIndex === 'bankAttribute'">
          <!-- 0709农险缴费付款人一致性校验需求 改为不能编辑（目的：防止在缴费时录入的付款人信息与出单时录入的付款人信息不一致） -->
          <a-form-item :name="[index, 'bankAttribute']">
            <a-select v-model:value="payInfoList[index].bankAttribute" style="width: 100%" :options="bankAttributeOptions" disabled @change="(val) => changeBankAttribute(val, index)" />
          </a-form-item>
        </template>
        <!-- 身份证 -->
        <template v-if="column.dataIndex === 'certificateNo'">
          <!-- 0709农险缴费付款人一致性校验需求 改为不能编辑（目的：防止在缴费时录入的付款人信息与出单时录入的付款人信息不一致） -->
          <a-form-item :name="[index, 'certificateNo']">
            <a-input v-model:value="payInfoList[index].certificateNo" style="width: 100%" placeholder="请输入身份证" disabled />
          </a-form-item>
        </template>
        <!-- 银行总行 -->
        <template v-if="column.dataIndex === 'bankHeadquartersCode'">
          <a-form-item :name="[index, 'bankHeadquartersCode']">
            <a-select v-model:value="payInfoList[index].bankHeadquartersCode" style="width: 100%" :options="bankOptions" :disabled="payInfoList[index].paymentPath !== '03'" show-search placeholder="请选择" :field-names="{ label: 'bankTypeName', value: 'bankTypeCode' }" :filter-option="filterOption" @change="(val) => changeBank(val as string, index)" />
          </a-form-item>
        </template>
        <!-- 银行分行 -->
        <template v-if="column.dataIndex === 'bankCode'">
          <a-form-item :name="[index, 'bankCode']">
            <a-select v-model:value="payInfoList[index].bankCode" style="width: 100%" :options="payInfoList[index].branchBankOptions" :disabled="payInfoList[index].paymentPath !== '03'" placeholder="请选择" :field-names="{ label: 'bankCodeName', value: 'bankCode' }" :filter-option="filterBankBranchOption" show-search @change="(val, options) => selectBank(val, options as Record<string, string>, index)" />
          </a-form-item>
        </template>
        <!-- 开户行明细 -->
        <template v-if="column.dataIndex === 'bankDetail'">
          <a-form-item :name="[index, 'bankDetail']">
            <a-input v-model:value="payInfoList[index].bankDetail" style="width: 100%" placeholder="" :disabled="payInfoList[index].paymentPath !== '03'" />
          </a-form-item>
        </template>
        <!-- 银行账号 -->
        <template v-if="column.dataIndex === 'bankAccountNo'">
          <a-form-item :name="[index, 'bankAccountNo']" :rules="[{ message: '请输入8到30位数字', min: 8, max: 30 }]">
            <a-input v-model:value="payInfoList[index].bankAccountNo" style="width: 100%" placeholder="" :disabled="payInfoList[index].paymentPath !== '03'" @input="(value) => handleInput(value, index)" />
          </a-form-item>
        </template>
        <!-- 预计收回日期 -->
        <template v-if="column.dataIndex === 'receivableDate'">
          <a-form-item :name="[index, 'receivableDate']" :rules="[{ validator: receivableDateValid }]">
            <a-date-picker v-model:value="payInfoList[index].receivableDate" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="(val) => receivableDateChange(val as string, index)" />
          </a-form-item>
        </template>
        <!-- 备注 -->
        <template v-if="column.dataIndex === 'otherPayerTypeDesc'">
          <a-form-item v-if="record.payerType === '6'" :name="[index, 'otherPayerTypeDesc']" :rules="[{ required: true, message: '请填写备注' }]">
            <a-input v-model:value="payInfoList[index].otherPayerTypeDesc" style="width: 100%" placeholder="请输入备注" :maxlength="200" show-count />
          </a-form-item>
          <div v-else>-</div>
        </template>
        <template v-if="column.dataIndex === 'action' && (record.flag || record.disableDelFlag !== 'Y')">
          <a-button type="link" @click="removePricingPlan(index)">删除</a-button>
        </template>
      </template>
    </a-table>
    <div class="mt-[12px] text-[#576B95] flex items-center space-x-[4px] cursor-pointer">
      <VueIcon :icon="IconTongyongXinzengFont" @click="addPricingPlan" />
      <span @click="addPricingPlan">增加一条收费计划</span>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import dayjs, { type Dayjs } from 'dayjs';
import type { Rule } from 'ant-design-vue/es/form';
import { IconTongyongXinzengFont, IconChevronRightFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import type { payInfo, BaseInfo } from '../insuranceFormFill';
import { usePost, useGet, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { SelectOptions } from '@/apiTypes/apiCommon';
// import { checkIDCardNo } from '@/utils/validators';
import NP from 'number-precision';

// 禁止农户分期付款管控 开关
const farmerSwitch = ref<boolean>(false);
const payInfoList = defineModel<payInfo[]>('payInfoList', { default: [] });
const baseInfo = defineModel<BaseInfo>('baseInfo', { default: {} });
const payerTypeSumOfPayInfo = defineModel<Record<string, unknown>>('payerTypeSumOfPayInfo', { default: {} }); // 收费按类别汇总用于校验
const props = defineProps<{
  innerCoinsuranceMark: string; // 是否系统外共保
  combinedProductCode: string; // 标的类型
}>();
const columns: TableColumnsType = [
  {
    title: '期次',
    dataIndex: 'termNo',
    key: 'termNo',
    width: '4%',
  },
  {
    title: '补贴类型',
    dataIndex: 'payerType',
    key: 'payerType',
  },
  {
    title: '付款人名称',
    dataIndex: 'paymentPersonName',
    key: 'paymentPersonName',
  },
  {
    title: '收付途径',
    dataIndex: 'paymentPath',
    key: 'paymentPath',
  },
  {
    title: '应收保费',
    dataIndex: 'actualPremium',
    key: 'actualPremium',
  },
  {
    title: '缴费起止期',
    dataIndex: 'paymentPeriod',
    key: 'paymentPeriod',
  },
  {
    title: '账户类型',
    dataIndex: 'bankAttribute',
    key: 'bankAttribute',
  },
  {
    title: '身份证',
    dataIndex: 'certificateNo',
    key: 'certificateNo',
  },
  {
    title: '银行总行',
    dataIndex: 'bankHeadquartersCode',
    key: 'bankHeadquartersCode',
    width: '8%',
  },
  {
    title: '银行分行',
    dataIndex: 'bankCode',
    key: 'bankCode',
    width: '11%',
  },
  {
    title: '开户行明细',
    dataIndex: 'bankDetail',
    key: 'bankDetail',
  },
  {
    title: '银行账号',
    dataIndex: 'bankAccountNo',
    key: 'bankAccountNo',
  },
  {
    title: '预计收回日期',
    dataIndex: 'receivableDate',
    key: 'receivableDate',
  },
  {
    title: '备注',
    dataIndex: 'otherPayerTypeDesc',
    key: 'otherPayerTypeDesc',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: '5%',
  },
];
const payerTypeOptions = ref<SelectOptions[]>([]); // 补贴类型选项
const bankOptions = ref<Record<string, string>[]>([]); // 银行总行选项
const formRef = ref();
const ownerFormRef = ref();
const validateInsuranceEndDate = ref<boolean>(false);
const paymentPathOptions = ref<SelectOptions[]>([]);
const bankAttributeOptions = ref<SelectOptions[]>([]);
const validateFunc = async () => {
  if (ownerFormRef.value) {
    try {
      await ownerFormRef.value.validateFields();
      if (formRef.value) {
        await formRef.value.validateFields();
      }
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
// 提交核保时校验
const validate = () => {
  validateInsuranceEndDate.value = true;
  return validateFunc();
};
// 保费计算时校验
const validateCal = () => {
  validateInsuranceEndDate.value = false;
  return validateFunc();
};
const clearObject = (obj: Record<string, string | string[]>) => {
  Object.keys(obj).forEach((key) => {
    obj[key] = '';
  });
};
// 禁止农户分期付款管控开关关闭，农户部分分期不可超过4期, 超过4期选项置灰
const setOptionDisabled = () => {
  if (!farmerSwitch.value) {
    const farmerItemList = payInfoList.value.filter((item) => item.payerType === '5');
    if (payerTypeOptions.value.length > 0) {
      payerTypeOptions.value.forEach((item: SelectOptions) => {
        if (item.value === '5' && farmerItemList.length >= 4) {
          // 农户
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
    }
  }
};
// 新增一条收费计划
const addPricingPlan = () => {
  setOptionDisabled();
  const copyData = JSON.parse(JSON.stringify(payInfoList.value[0]));
  clearObject(copyData);
  copyData.termNo = (payInfoList.value.length + 1).toString();
  // 判断是否为新增行
  copyData.flag = true;
  copyData.typeDisabled = false;
  // 缴费起期默认为当前日期,缴费止期和预计收回日期默认为保险止期
  copyData.paymentBeginDate = dayjs(new Date()).format('YYYY-MM-DD');
  copyData.paymentEndDate = baseInfo.value.insuranceEndDate;
  copyData.receivableDate = baseInfo.value.insuranceEndDate;
  // 系统外共保，缴费止期上限赋值为“录单日期和保险止期的孰晚者”
  if (baseInfo.value.coinsuranceMark === '1' && props.innerCoinsuranceMark !== '1') {
    const expiryDateOfInsurance = baseInfo.value.insuranceEndDate; // 保险止期
    const lastTimeStamp = Math.max(new Date(expiryDateOfInsurance).getTime(), new Date().getTime());
    copyData.paymentEndDate = dayjs(lastTimeStamp);
  }
  copyData.payPlanInfoList = [];
  payInfoList.value.push(copyData);
};

// 删除一条收费计划
const removePricingPlan = (index: number) => {
  changePremium(index, 'delete');
  // 删除再去校验一次
  payInfoList.value.forEach((it, index) => {
    formRef.value.validateFields([[index, 'actualPremium']]);
  });
};
// 修改补贴类型
const changePayerType = (val: unknown, index: number) => {
  payInfoList.value[index].typeDisabled = true;
  // 新增分期的预计收回时间与同类型的一致
  const sameItem = payInfoList.value.find((item) => item.payerType === val);
  if (sameItem) {
    payInfoList.value[index].receivableDate = sameItem.receivableDate;
  }
  setOptionDisabled();
};
// 应收保费转换为string
const handleChange = (val: number | string, index: number) => {
  payInfoList.value[index].actualPremium = val?.toString() || '0';
  // 各级首期保费支持手调
  changePremium(index);
};
// 判断新增期次应收保费是否超过首期来源应收保费
// const validTotal = (value: string, index: number) => {
//   const type = payInfoList.value[index].payerType;
//   // 首期来源应收保费支持手调
//   const originIndex = payInfoList.value.findIndex((item: { payerType: string }) => item.payerType === type);
//   const filterPayInfoList = payInfoList.value.filter((item: { payerType: string }, inx) => item.payerType === type && originIndex !== inx);
//   if (originIndex !== -1) {
//     if (originIndex === index) {
//       filterPayInfoList.forEach((it, inx) => {
//         // 首期来源应收保费改变时，遍历一次，超了就校验住
//         if (Number(it.actualPremium) > Number(value)) {
//           formRef.value.validateFields([[inx, 'actualPremium']]);
//         } else {
//           formRef.value.clearValidate([[inx, 'actualPremium']]);
//         }
//       });
//     } else {
//       // 首期来源应收保费与当前
//       return Number(value) > Number(payInfoList.value[originIndex].actualPremium);
//     }
//   }
//   return false;
// };
// 修改应收保费联动
const changePremium = (index: number, delFlag?: string) => {
  if (payInfoList.value.length > 0) {
    const type = payInfoList.value[index].payerType;
    const sumType = `payerType${type}`;
    const total = payerTypeSumOfPayInfo.value?.[sumType];
    let num = Number(total);
    const originIndex = payInfoList.value.findIndex((item: { payerType: string }) => item.payerType === type);
    const originTypeItem = payInfoList.value.find((item: { payerType: string }) => item.payerType === type);
    const currentTypeItem = payInfoList.value[index];
    if (type && originIndex === index && payerTypeSumOfPayInfo.value) {
      const filterPayerTypes = payInfoList.value.filter((it) => it.payerType === type);
      let numTotal = 0;
      filterPayerTypes.forEach((item) => {
        numTotal = NP.plus(numTotal, Number(item.actualPremium || '0'));
      });
      payerTypeSumOfPayInfo.value[sumType] = numTotal.toFixed(2).toString();
      return;
    }
    if (delFlag === 'delete') {
      payInfoList.value.splice(index, 1);
      if (type && originTypeItem)
        originTypeItem.actualPremium = NP.plus(Number(originTypeItem.actualPremium || '0'), Number(currentTypeItem.actualPremium || '0'))
          .toFixed(2)
          .toString();
      setOptionDisabled();
    } else if (type) {
      // 从首期应收保费中减掉对应增加的应收保费值
      payInfoList.value.forEach((item: { actualPremium: string; payerType: string }, i: number) => {
        if (i !== originIndex && item.payerType === type) {
          num = NP.minus(num, Number(item.actualPremium || '0'));
        }
      });
      // 小于等于0提示，大于0取消提示
      if (index !== originIndex) {
        payInfoList.value[originIndex].actualPremium = num.toFixed(2).toString();
        if (num <= 0) {
          formRef.value.validateFields([[originIndex, 'actualPremium']]);
        } else {
          formRef.value.clearValidate([[originIndex, 'actualPremium']]);
        }
      }
    }
  }
};
// 修改收付途径
const changePaymentPath = (val: unknown, index: number) => {
  // 不为银行转账时清空账户类型和身份证
  if (val !== '03') {
    payInfoList.value[index].bankAttribute = '';
    payInfoList.value[index].certificateNo = '';
    payInfoList.value[index].bankAccountNo = '';
    payInfoList.value[index].bankDetail = '';
    payInfoList.value[index].bankCode = '';
    payInfoList.value[index].bankHeadquartersCode = '';
    validateFunc();
  }
};
// 修改账户类型
const changeBankAttribute = (val: unknown, index: number) => {
  // 不为个人时清空身份证
  if (val !== '1') {
    payInfoList.value[index].certificateNo = '';
    validateFunc();
  }
};
// 选择银行总行
const changeBank = (value: string, index: number) => {
  payInfoList.value[index].branchBankOptions = [];
  getBankBranchList(value, index);
  payInfoList.value[index].bankCode = '';
  payInfoList.value[index].bankDetail = '';
};
// 选择分行
const selectBank = (value: unknown, options: Record<string, string>, index: number) => {
  payInfoList.value[index].bankDetail = options.bankCodeName;
};
// 应收保费校验规则
const numValid = (_rule: Rule & { field?: string }) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const itemAgreePremium = payInfoList?.value?.[index]?.actualPremium;
  if (!itemAgreePremium || Number(itemAgreePremium) === 0) {
    return Promise.reject('不能为空且不能为0');
  } else if (Number(itemAgreePremium) < 0) {
    return Promise.reject('不能小于0');
  } else if (isNaN(Number(itemAgreePremium))) {
    return Promise.reject('请输入数字');
  }
  // if (validTotal(value, index)) {
  //   return Promise.reject('新增期次的应收保费不能超过首期来源应收保费');
  // }
  return Promise.resolve();
};
// 补贴类型校验规则
const typeValid = (_rule: Rule & { field?: string }) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const itemPayerType = payInfoList.value[index].payerType;
  if (itemPayerType === '') {
    return Promise.reject('补贴类型不能为空');
  }
  return Promise.resolve();
};
// 账户类型
// const bankAttributeValid = (_rule: Rule & { field?: string }) => {
//   const { field } = _rule;
//   const index = Number(field?.split('.')[0]);
//   const paymentPath = payInfoList.value[index].paymentPath;
//   const bankAttribute = payInfoList.value[index].bankAttribute;
//   if (paymentPath === '03' && !bankAttribute) {
//     return Promise.reject('账户类型不能为空');
//   }
//   return Promise.resolve();
// };
// 身份证
// const certificateNoValid = (_rule: Rule & { field?: string }) => {
//   const { field } = _rule;
//   const index = Number(field?.split('.')[0]);
//   const bankAttribute = payInfoList.value[index].bankAttribute;
//   const certificateNo = payInfoList.value[index].certificateNo;
//   if (bankAttribute === '1') {
//     return !certificateNo ? Promise.reject('身份证不能为空') : checkIDCardNo(_rule, certificateNo);
//   }
//   return Promise.resolve();
// };
// 缴费止期校验条件
const endDateValid = (_rule: Rule & { field?: string }, value: number) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const item = payInfoList.value[index];
  if (item.paymentEndDate === '') {
    return Promise.reject('请输入缴费止期');
  }
  // 农户部分
  if (item.payerType === '5') {
    if (value < Date.now()) {
      return Promise.reject('缴费止期不可早于当前时间');
    }
  }
  const startTime = item.paymentBeginDate; // 缴费起期
  const endTime = item.paymentEndDate; // 缴费止期
  const expiryDateOfInsurance = baseInfo.value.insuranceEndDate; // 保险止期
  if (new Date(startTime).getTime() > new Date(endTime).getTime()) {
    return Promise.reject('缴费止期不能小于缴费起期');
  }
  const lastTimeStamp = Math.max(new Date(expiryDateOfInsurance).getTime(), new Date().getTime());
  // 系统外共保，缴费止期需小于录单日期和保险止期中的最晚者
  const isOutsideCo = baseInfo.value.coinsuranceMark === '1' && props.innerCoinsuranceMark !== '1';
  if (isOutsideCo) {
    if (new Date(endTime).getTime() > lastTimeStamp) {
      return Promise.reject('缴费止期不能晚于录单日期和保险止期的最晚者');
    }
  } else {
    if (new Date(endTime).getTime() > new Date(expiryDateOfInsurance).getTime() && validateInsuranceEndDate.value) {
      return Promise.reject('缴费止期不能晚于保险止期');
    }
  }
  return Promise.resolve();
};
// 预计收回时间校验
const receivableDateValid = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const item = payInfoList.value[index];
  if (item.receivableDate === '') {
    return Promise.reject('请输入预计收回日期');
  }
  if (oneYearValid(value)) {
    return Promise.resolve();
  }
  return Promise.reject('最晚不得超保险止期12个月');
};
// 校验是否超止期一年
const oneYearValid = (endDate: string) => {
  const bgDate = baseInfo.value.insuranceEndDate;
  const currentYear = new Date(bgDate).getTime();
  const nextYear = new Date(endDate).getTime();
  const limitTime = (nextYear - currentYear) / (checkYearDay(bgDate, endDate) * 24 * 3600 * 1000);
  return limitTime < 1;
};
// 校验一年的天数是365天(平年)，还是366天(闰年)
const checkYearDay = (startDay: string, endDay: string) => {
  const startYear = new Date(startDay).getFullYear();
  const endYear = new Date(endDay).getFullYear();
  // 起始时间是闰年
  if ((startYear % 4 === 0 && startYear % 100 !== 0) || startYear % 400 === 0) {
    if (new Date(startDay).getTime() < new Date(`${startYear}-02-29`).getTime()) {
      return 366;
    }
  }
  // 终止时间是闰年
  if ((endYear % 4 === 0 && endYear % 100 !== 0) || endYear % 400 === 0) {
    if (new Date(startDay).getTime() >= new Date(`${startYear}-02-28`).getTime()) {
      return 366;
    }
  }
  return 365;
};
// 缴费止期上限
const disabledDate = (index: number) => {
  return (current: Dayjs) => {
    const expiryDateOfInsurance = baseInfo.value.insuranceEndDate; // 保险止期
    const startDate = payInfoList.value[index].paymentBeginDate; // 缴费起期
    // 获取当前日期
    const currentDay = dayjs();
    // 将保险结束日期转换为 dayjs 对象
    const expiryDay = dayjs(expiryDateOfInsurance);
    // 比较两个日期并获取较晚的日期
    const lastDate = expiryDay.isBefore(currentDay) ? currentDay : expiryDay;
    // 缴费止期不能大于lastDate，不能小于缴费起期
    const result = current > lastDate || current.isBefore(dayjs(startDate));
    return current && result;
  };
};
// 获取银行分行
const getBankBranchList = async (value: string, index: number) => {
  try {
    const result = await getSubBankListByBankBranchKey.fetchData({ bankKey: value });
    if (result?.code === SUCCESS_CODE) {
      payInfoList.value[index].branchBankOptions = result.data;
    }
  } catch (error) {
    console.log(error);
  }
};
defineExpose({ validate, validateCal, getBankBranchList });
const { gateWay, service } = useRuntimeConfig().public || {};
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`);
// 获取下拉框选项
const getOptions = async () => {
  try {
    const params = ['agrPayerType', 'bankAttribute', 'paymentPath'];
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      payerTypeOptions.value = res.data?.filter((item) => item.value === 'agrPayerType')[0]?.children || []; // 补贴类型options
      paymentPathOptions.value = res.data?.filter((item) => item.value === 'paymentPath')[0]?.children || []; // 收付途径options
      bankAttributeOptions.value = res.data?.filter((item) => item.value === 'bankAttribute')[0]?.children || []; // 帐号类型options
      setOptionDisabled();
      getSwitchResult();
      getBankList();
      // 获取分行
      for (const [index, item] of payInfoList.value.entries()) {
        if (item.bankHeadquartersCode) {
          await getBankBranchList(item.bankHeadquartersCode, index);
        }
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 获取开关
const getSwitchResult = async () => {
  const fetchUrl = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const res = await $postOnClient<boolean>(fetchUrl, {
    switchCode: 'FORBID_FARMER_MUTIL_TERM',
    agriculturalRiskObjectDetailCode: props.combinedProductCode || '',
    allowanceTypeCode: baseInfo.value?.govSubsidyType || '',
    departmentNo: baseInfo.value?.departmentCode || '',
    coinsuranceFlag: baseInfo.value?.coinsuranceMark || '',
    mainInsureFlag: baseInfo.value?.acceptInsuranceFlag || '',
  });
  if (res && res.code === SUCCESS_CODE) {
    farmerSwitch.value = res.data;
    // 禁止农户分期付款管控 打开，不可以进行农户自缴保费分期付款的操作
    if (farmerSwitch.value && payerTypeOptions.value.length > 0) {
      payerTypeOptions.value.forEach((item: SelectOptions) => {
        if (item.value === '5') {
          // 农户
          item.disabled = true;
        }
      });
    }
  }
};
// 预计收回日期修改，同一来源同步修改
const receivableDateChange = (date: string, index: number) => {
  // 当前修改的预计收回日期的补贴类型
  const currentType = payInfoList.value[index].payerType;
  payInfoList.value.forEach((item) => {
    if (currentType === item.payerType) {
      item.receivableDate = date as string;
    }
  });
};
const getHqBankList = await useGet<Record<string, string>[]>(`${gateWay}${service.accept}/saleInfo/getHqBankList`); // 获取总行
// 获取银行总行
const getBankList = async () => {
  try {
    const result = await getHqBankList.fetchData();
    if (result?.code === SUCCESS_CODE) {
      bankOptions.value = result.data;
    }
  } catch (error) {
    console.log(error);
  }
};
const getSubBankListByBankBranchKey = await useGet<Record<string, string>[]>(`${gateWay}${service.accept}/saleInfo/getSubBankListByBankKey`); // 获取分行
// 搜索总行
const filterOption = (input: string, option: Record<string, string>) => {
  return option.bankTypeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 搜索分行
const filterBankBranchOption = (input: string, option: Record<string, string>) => {
  return option.bankCodeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

onMounted(() => {
  getOptions();
});
const umVerificationReq = await useGet(`${gateWay}${service.administrate}/user/umVerification`);
// 责任人校验规则
const ownerValid = async (rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('责任人um号不能为空');
  }
  try {
    const params = { umCode: value };
    const res = await umVerificationReq.fetchData(params);
    if (res && res.data === true) {
      return Promise.resolve();
    } else {
      return Promise.reject('请填写正确的um号');
    }
  } catch (error) {
    console.log(error);
  }
  return Promise.resolve();
};
const handleInput = (data, index: number) => {
  payInfoList.value[index].bankAccountNo = data.target.value.replace(/\D/g, '');
};
// 保险止期变化，预计收回日期同步变化
watch(
  () => baseInfo.value.insuranceEndDate,
  () => {
    payInfoList.value.forEach((item) => {
      item.receivableDate = baseInfo.value.insuranceEndDate;
    });
  },
);
</script>
<style lang="less" scoped>
.fee-plan {
  width: calc(100vw - 420px);
}
</style>
