<template>
  <div class="insurance-plan space-y-16px bg-[#ffffff]">
    <a-form ref="formRef" :model="data" :colon="false">
      <div class="flex items-center flex-wrap space-x-[20px] text-[#576B95] cursor-pointer p-[16px]">
        <span v-for="(subItem, index) in insuranceList" :key="subItem?.insuranceProposalName" @click="showInsurance(subItem.insuranceProposalName, 0, index)">
          {{ subItem.insuranceProposalName }}
          <VueIcon :icon="IconChevronRightDoubleFont" />
        </span>
      </div>
      <div v-for="(item, mainIndex) in data" :key="mainIndex" class="bg-[#ffffff] px-[16px] rounded">
        <div class="flex justify-between mb-[16px]">
          <div class="text-[#404442] text-[14px] font-semibold flex items-center">
            <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
            {{ `方案${data?.length > 1 ? mainIndex + 1 : ''}` }}
          </div>
          <div class="flex items-center space-x-8px">
            <a-button type="primary" @click="addInsurance(mainIndex)">添加附加险</a-button>
          </div>
        </div>
        <div class="table-wrap">
          <a-table :columns="columns" :data-source="item.planInfoList" :pagination="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : '')">
            <template #bodyCell="{ text, column, record, index }">
              <template v-if="column.dataIndex === 'number'">
                {{ index + 1 }}
              </template>
              <template v-if="column.dataIndex === 'combinedProductName'">
                {{ data[mainIndex]?.combinedProductName }}
              </template>
              <template v-if="column.dataIndex === 'isMain'">
                {{ text === '1' ? '主险' : '附加险' }}
              </template>
              <template v-if="column.dataIndex === 'planName'">
                <span v-if="record?.isMain === '1'">{{ text }}</span>
                <a-form-item v-else :name="[mainIndex, 'planInfoList', index, 'planName']">
                  <a-select v-model:value="item.planInfoList[index].planCode" class="w-[120px]" @select="(_value: any, option: any) => handleSelect(mainIndex, index, option)">
                    <template v-for="option in ridersOptions" :key="option.planCode">
                      <a-select-option :value="option.planCode" :title="option.planName" :disabled="item.planInfoList.filter((plan) => plan.planCode === option.planCode)?.length > 0">
                        {{ option.planName }}
                      </a-select-option>
                    </template>
                  </a-select>
                </a-form-item>
              </template>
              <!-- 每次赔偿限额 -->
              <template v-if="column.dataIndex === 'eachCompensationMaxAmount'">
                <a-form-item :name="[mainIndex, 'planInfoList', index, 'eachCompensationMaxAmount']" :rules="[{ validator: (_rule, value) => limitValidator(value, record.totalInsuredAmount) }]">
                  <a-input-number v-model:value="item.planInfoList[index].eachCompensationMaxAmount" :min="0" />
                </a-form-item>
              </template>
              <!-- 单位保险金额 -->
              <template v-if="column.dataIndex === 'unitInsuredAmount'">
                <a-form-item :name="[mainIndex, 'planInfoList', index, 'unitInsuredAmount']" :rules="[{ validator: unitAmountValidator }]">
                  <a-input-number v-model:value="item.planInfoList[index].unitInsuredAmount" :min="0" @change="(value) => amountChange(mainIndex, index, value as number)" />
                </a-form-item>
              </template>
              <!-- 费率 -->
              <template v-if="column.dataIndex === 'expectPremiumRate'">
                <a-form-item :name="[mainIndex, 'planInfoList', index, 'expectPremiumRate']" :rules="[{ validator: centralFinanceValidator }]">
                  <a-input-number v-model:value="item.planInfoList[index].expectPremiumRate" :min="0" @change="(value) => rateChange(mainIndex, index, value as number)" />
                </a-form-item>
              </template>
              <!-- 单位保费 -->
              <template v-if="column.dataIndex === 'unitPrimium'">
                <a-form-item :name="[mainIndex, 'planInfoList', index, 'unitPrimium']" :rules="[{ validator: amountValidator }]">
                  <a-input-number v-model:value="item.planInfoList[index].unitPrimium" :min="0" />
                </a-form-item>
              </template>
              <!-- 复核保费 -->
              <template v-if="column.dataIndex === 'totalActualPremium'">
                <a-form-item
                  :name="[mainIndex, 'planInfoList', index, 'totalActualPremium']"
                  :rules="[
                    { required: true, message: '不能为空' },
                    { pattern: /^(?!0{13,})(0|[1-9]\d{0,11})(\.\d{1,2})?$/, message: '整数部分最多12位，小数部分最多2位' },
                  ]"
                >
                  <a-input-number v-model:value="item.planInfoList[index].totalActualPremium" :min="0" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <span v-if="record?.isMain !== '1'" class="text-[14px] text-[#576B95] font-normal cursor-pointer" @click="deleteInsurance(mainIndex, index)"> 删除 </span>
              </template>
            </template>
          </a-table>
        </div>
        <div class="bg-[#E6E8EB] h-[1px] w-full my-[17px]" />
        <!-- 保费来源 只有最后一项的时候展示，兼容多标的 -->
        <div v-if="mainIndex === data.length - 1">
          <div class="flex items-center justify-between text-[rgba(0,0,0,0.55)] font-normal mb-[16px]">
            <div>保费来源</div>
            <div class="flex items-center">
              <span>附加险保费来源：</span>
              <a-form-item :name="[mainIndex, 'riskAgrInfo', 'planAttributeSameMain']">
                <span class="text-[rgba(0,0,0,0.55)] font-normal">与主险一致：</span>
                <a-radio-group v-model:value="item.riskAgrInfo.planAttributeSameMain" :disabled="data?.length > 1">
                  <a-radio value="Y">
                    <span class="text-[rgba(0,0,0,0.55)] font-normal">是</span>
                  </a-radio>
                  <a-radio value="N">
                    <span class="text-[rgba(0,0,0,0.55)] font-normal">否</span>
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
          </div>
          <a-table :columns="sourceColumns" :data-source="sourceList" :pagination="false">
            <template #bodyCell="{ column, index }">
              <template v-if="column.dataIndex === 'centralFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'centralFinance']" :rules="[{ validator: () => centralFinanceValidator(index, sourceList[index].centralFinance) }]">
                  <a-input-number v-model:value="sourceList[index].centralFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'cityFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'cityFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="sourceList[index].cityFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'countyFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'countyFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="sourceList[index].countyFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'provincialFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'provincialFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="sourceList[index].provincialFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'farmersFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'farmersFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="sourceList[index].farmersFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index, true)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'otherFinance'">
                <a-form-item class="w-[100px]" :name="[index, 'otherFinance']" :rules="[{ validator: (_rule, value) => otherFinanceValidator(value, mainIndex, index) }]">
                  <a-input-number v-model:value="sourceList[index].otherFinance" :min="0" @change="() => handleFinanceChange(mainIndex, index)" />
                </a-form-item>
              </template>
            </template>
          </a-table>
          <div class="grid grid-cols-3 gap-x-10px my-[16px]">
            <a-form-item label="农户自缴减免系数" required :name="[mainIndex, 'riskAgrInfo', 'reductionCoefficient']" :rules="[{ validator: (rule: Rule, value: number) => coefficientValidator(value, mainIndex) }]">
              <a-input-number v-model:value="item.riskAgrInfo.reductionCoefficient" :style="{ width: '100%' }" placeholder="输入介于0-农户自缴上限" :min="0" />
            </a-form-item>
            <a-form-item label="农户保费是否代缴" required :name="[mainIndex, 'riskAgrInfo', 'substitute']">
              <a-select v-model:value="item.riskAgrInfo.substitute" class="w-full" @change="(value) => substituteChange(value as string, mainIndex)">
                <a-select-option value="Y">是</a-select-option>
                <a-select-option value="N">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item v-if="item.riskAgrInfo.substitute === 'Y'" label="代缴保费资金来源" required :name="[mainIndex, 'riskAgrInfo', 'fundingSource']">
              <a-select v-model:value="item.riskAgrInfo.fundingSource" :options="sourceOptions" />
            </a-form-item>
          </div>
        </div>
      </div>
      <div class="p-16px leading-[22px] text-[rgba(0, 0, 0, 0.9)]">
        <div>
          总计：保单保险金额={{ sumRiskGroupAmount?.totalInsuredAmount || 0 }} ｜ 减免前保单保费金额={{ sumRiskGroupAmount?.totalStandardPremium || 0 }}｜ 减免后保单保费金额={{ sumRiskGroupAmount?.totalAgreePremium || 0 }} ｜ 保单复核保费={{ sumRiskGroupAmount?.totalActualPremium || 0 }}
          <span v-if="sumRiskGroupAmount?.totalAgreePremium !== sumRiskGroupAmount?.totalActualPremium" class="text-[#FAAD14]">保费计算保费与手调保费不同，请注意核实</span>
        </div>
        <div v-if="data.length === 1">保险数量：{{ props.insuredNumber || 0 }}{{ unitName }} ｜ 参保户次：{{ props.farmersCount || 0 }}户</div>
        <div v-if="data.length > 1">
          <template v-for="(item, index) in data" :key="index">
            <div>标的名称：{{ item.combinedProductName }} ｜ 保险数量：{{ item.riskAgrInfo.insuredNumber || 0 }}{{ item.riskAgrInfo.insuredUnitChName }} ｜ 参保户次：{{ item.riskAgrInfo.farmersCount || 0 }}户</div>
          </template>
        </div>
      </div>
    </a-form>
    <a-modal v-model:open="open" :title="title" :width="pxToRem(800)" ok-text="应用" @ok="handleOk">
      <div>
        <div class="text-[rgba(0,0,0,0.90)] font-normal">
          <VueIcon :icon="IconJichuxinxiFont" />
          方案基础信息
        </div>
        <div class="bg-[#F8F8F8] text-[rgba(0,0,0,0.55)] h-[100px] space-y-[16px] px-[15px] mt-[8px]">
          <div class="grid grid-cols-2 pt-[20px]">
            <div>方案名称：{{ insuranceDetail?.insuranceProposalName || '-' }}</div>
            <div>产品：{{ insuranceDetail?.marketProductName || '-' }}</div>
          </div>
          <div class="flex items-center">机构：{{ insuranceDetail?.departmentName || '-' }}</div>
        </div>
        <div class="text-[rgba(0,0,0,0.90)] font-normal mt-[16px] mb-[8px]">
          <VueIcon :icon="IconJianguanNormalFont" />
          产品信息
        </div>
        <template v-for="(item, detailIndex) in insuranceDetail?.planList">
          <div v-if="item?.isMain === '1'" :key="detailIndex" class="bg-[#F8F8F8] text-[rgba(0,0,0,0.55)] space-y-[16px] px-[15px] py-[16px]">
            <div class="grid grid-cols-2">
              <div class="text-[rgba(0,0,0,0.90)]">主险：{{ item?.planName || '' }}</div>
              <div>单位保额：{{ item?.unitInsuredAmount || 0 }}元</div>
            </div>
            <div class="grid grid-cols-2">
              <div>单位保费：{{ item?.unitPrimium || 0 }}元</div>
              <div>费率：{{ item?.expectPremiumRate || 0 }}%</div>
            </div>
          </div>
          <div v-if="item?.isMain === '0'" :key="detailIndex" class="bg-[#F8F8F8] text-[rgba(0,0,0,0.55)] space-y-[16px] px-[15px] py-[16px]">
            <div class="grid grid-cols-2">
              <div class="pr-[16px]">附加险{{ detailIndex }}：{{ item?.planName || '' }}</div>
              <div v-if="item.feesInfoSameFlag !== 'Y'">单位保额：{{ item?.unitInsuredAmount || 0 }}元</div>
              <div v-if="item.feesInfoSameFlag === 'Y'">同主险</div>
            </div>
            <div v-if="item.feesInfoSameFlag !== 'Y'" class="grid grid-cols-2">
              <div>单位保费：{{ item?.unitPrimium || 0 }}元</div>
              <div>费率：{{ item?.expectPremiumRate || 0 }}%</div>
            </div>
          </div>
          <div v-if="detailIndex + 1 !== insuranceDetail?.planList?.length" :key="detailIndex" class="h-[1px] bg-[rgba(230,232,235,1)] mx-[14px]" />
        </template>
        <div class="text-[rgba(0,0,0,0.90)] font-normal mt-[16px]">
          <VueIcon :icon="IconPingtaijiekouFont" />
          方案附件
        </div>
        <div v-if="insuranceDetail?.fileInfos && insuranceDetail?.fileInfos?.length > 0" class="bg-[#F8F8F8] space-y-[16px] px-[15px] mt-[8px] py-[16px]">
          <div v-for="fileInfo in insuranceDetail.fileInfos" :key="fileInfo.fileKey">
            <span class="text-[#4E6085] font-normal cursor-pointer" @click="downloadFile(fileInfo)">
              {{ fileInfo?.fileName }}
            </span>
          </div>
        </div>
        <div class="text-[rgba(0,0,0,0.55)] my-[16px]">
          <div>方案规则说明：</div>
          <div>配置内容将默认带出到出单页面，对于数值不确定的字段，请不要填写，以免影响出单。</div>
          <div>举例：若单位保险=单位保额·费率，则单位保费留空即可，系统会自动计算，若单位保险的固定值，请如实填写。</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { IconChevronRightDoubleFont, IconJichuxinxiFont, IconJianguanNormalFont, IconPingtaijiekouFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import type { ColumnType } from 'ant-design-vue/es/table/interface';
import type { RiskGroupInfo, SumRiskGroupAmount, InsurancePlanDetail, FileInfo, PlanInfoListItem, PlanInfo } from '../insuranceFormFill';
import { $postOnClient, usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob, pxToRem, multiply, add } from '@/utils/tools';
// import { useIndexDB } from '@/composables/useIndexDB';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import { cloneDeep } from 'lodash-es';

// const { getCacheData } = useIndexDB();
const data = defineModel<Array<RiskGroupInfo>>('data', { default: [] });
const props = defineProps<{
  productVersion: string; // 产品版本
  productCode: string; // 产品编码
  departmentCode: string; // 机构编码
  shortTimeCoefficient: number; // 年限系数
  govSubsidyType: string; // 商业补贴类型
  sumRiskGroupAmount: SumRiskGroupAmount; // 保险方案合计
  insuredNumber: number; // 保险数量
  farmersCount: number; // 参保户次
  insuredUnit: string; // 单位
}>();
const { gateWay, service } = useRuntimeConfig().public || {};
// 预设保险方案list
const insuranceList = ref<Array<InsurancePlanDetail>>([]);

const formRef = ref();

// 是否展示弹窗
const open = ref<boolean>(false);
// 弹窗标题
const title = ref<string>('');
// 附加险下拉选项
const ridersOptions = ref<Array<PlanInfo>>([]);
// 单位下拉选项
const unitOptions = ref<SelectOptions[]>([]);
// 代缴保费资金来源下拉选项
const sourceOptions = ref<SelectOptions[]>([]);
// 产品主险数据
const mainInsuranceData = ref<Array<PlanInfo>>([]);
// 详情数据
const insuranceDetail = ref<InsurancePlanDetail>();
// 当前打开的预设方案角标
const currentIndex = ref<number>(0);

// 保险方案表格columns
const columns: Array<ColumnType> = [
  {
    title: '序号',
    dataIndex: 'number',
    key: 'number',
    width: pxToRem(56),
  },
  {
    title: '标的名称',
    dataIndex: 'combinedProductName',
    key: 'combinedProductName',
    width: pxToRem(80),
  },
  {
    title: '主险/附加险标识',
    dataIndex: 'isMain',
    key: 'isMain',
    width: pxToRem(140),
  },
  {
    title: '险种码',
    dataIndex: 'planCode',
    key: 'planCode',
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: pxToRem(140),
  },
  {
    title: '每次赔偿限额',
    dataIndex: 'eachCompensationMaxAmount',
    key: 'eachCompensationMaxAmount',
    width: pxToRem(140),
  },
  {
    title: '单位保险金额',
    dataIndex: 'unitInsuredAmount',
    key: 'unitInsuredAmount',
    width: pxToRem(140),
  },
  {
    title: '保险金额',
    dataIndex: 'totalInsuredAmount',
    key: 'totalInsuredAmount',
    width: pxToRem(140),
  },
  {
    title: '费率(%)',
    dataIndex: 'expectPremiumRate',
    key: 'expectPremiumRate',
    width: pxToRem(140),
  },
  {
    title: '单位保费',
    dataIndex: 'unitPrimium',
    key: 'unitPrimium',
    width: pxToRem(140),
  },
  {
    title: '基准保费金额',
    dataIndex: 'totalStandardPremium',
    key: 'totalStandardPremium',
    width: pxToRem(140),
  },
  {
    title: '减免后保费金额',
    dataIndex: 'totalAgreePremium',
    key: 'totalAgreePremium',
    width: pxToRem(140),
  },
  {
    title: '复核保费',
    dataIndex: 'totalActualPremium',
    key: 'totalActualPremium',
    width: pxToRem(120),
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
    width: pxToRem(80),
  },
];
// 保费来源政策性columns
const policySourceColumns = [
  {
    title: '险种代码',
    dataIndex: 'planCode',
    key: 'planCode',
    width: pxToRem(120),
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: pxToRem(120),
  },
  {
    title: '中央补贴(%)',
    dataIndex: 'centralFinance',
    key: 'centralFinance',
  },
  {
    title: '省级补贴(%)',
    dataIndex: 'provincialFinance',
    key: 'provincialFinance',
  },
  {
    title: '地方补贴(%)',
    dataIndex: 'cityFinance',
    key: 'cityFinance',
  },
  {
    title: '县级补贴(%)',
    dataIndex: 'countyFinance',
    key: 'countyFinance',
  },
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
  },
];
// 保费来源商业性columns
const businessSourceColumns = [
  {
    title: '险种代码',
    dataIndex: 'planCode',
    key: 'planCode',
    width: '20%',
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: '30%',
  },
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
    width: '25%',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
    width: '25%',
  },
];

// 保费来源表格datasource
const sourceList = computed(() => {
  if (data.value?.length === 1) {
    // 单标的
    return data.value[data.value.length - 1].riskAgrInfo.planAttributeSameMain === 'Y' ? [data.value[data.value.length - 1].planInfoList?.[0]] : data.value[data.value.length - 1].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    data.value.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return data.value[data.value.length - 1].riskAgrInfo.planAttributeSameMain === 'Y' ? [result?.[0]] : result;
  }
});

// 总计保险数量单位
const unitName = computed(() => {
  return unitOptions.value.find((item) => item.value === props.insuredUnit)?.label;
});

const sourceColumns = computed(() => {
  // 03代表商业险
  if (props?.govSubsidyType === '3') {
    return businessSourceColumns;
  } else {
    return policySourceColumns;
  }
});

// 每次限额校验
const limitValidator = (value: number, insuranceValue: number) => {
  if (!value) {
    return Promise.resolve();
  }
  // 不能大于保险金额
  if (Number(value) > Number(insuranceValue)) {
    return Promise.reject('不能大于保险金额');
  }

  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');

  if (integerPart && integerPart.length > 12) {
    return Promise.reject('整数部分不能超过12位');
  }

  if (decimalPart && decimalPart.length > 2) {
    return Promise.reject('小数部分不能超过2位');
  }

  return Promise.resolve();
};

// 单位保险金额校验
const unitAmountValidator = (_rule: Rule, value: number) => {
  if (!value && value != 0) {
    return Promise.reject('不能为空');
  }
  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');

  if (integerPart && integerPart.length > 12) {
    return Promise.reject('整数部分不能超过12位');
  }

  if (decimalPart && decimalPart.length > 2) {
    return Promise.reject('小数部分不能超过2位');
  }

  return Promise.resolve();
};

// 金额校验
const amountValidator = (_rule: Rule, value: number) => {
  if (!value && value != 0) {
    return Promise.reject('不能为空');
  }
  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');

  if (integerPart && integerPart.length > 9) {
    return Promise.reject('整数部分不能超过9位');
  }

  if (decimalPart && decimalPart.length > 6) {
    return Promise.reject('小数部分不能超过6位');
  }

  return Promise.resolve();
};

// 农户自缴减免系数校验
const coefficientValidator = (value: number, mainIndex: number) => {
  if (Number(value) < 0) {
    return Promise.reject('不能小于0');
  }
  // 最大值为险种中农户自缴比例的最小值 farmersFinance
  const farmersFinanceMin = Math.min(...data.value[mainIndex].planInfoList.map((item) => Number(item.farmersFinance)));

  if (Number(value * 100) > farmersFinanceMin) {
    return Promise.reject('不能大于最小农户自缴系数');
  }
  return Promise.resolve();
};

// 比例校验，比例录入范围0-100范围内的数字且小数位不能超过4位
const financeValidator = (_rule: Rule, value: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 中央政策来源 如果是中央政策补贴则必填
const centralFinanceValidator = (_rule: Rule, value: number) => {
  if (!value && value !== 0) {
    return Promise.reject('不能为空');
  }
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 校验是否等于100
const otherFinanceValidator = (value: number, mainIndex: number, index: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }
  const num1 = String(sourceList.value[index].centralFinance || '0');
  const num2 = String(sourceList.value[index].provincialFinance || '0');
  const num3 = String(sourceList.value[index].cityFinance || '0');
  const num4 = String(sourceList.value[index].countyFinance || '0');
  const num5 = String(sourceList.value[index].farmersFinance || '0');
  const num6 = String(sourceList.value[index].otherFinance || '0');

  // 地方政策补贴则 省市区不能都为空
  if (props?.govSubsidyType === '2') {
    if (!num2 && !num3 && !num4) {
      return Promise.reject('省、市、区不能都为空');
    }
  }

  // 将数值乘以10000转换为整数进行计算
  let sum = '';
  if (props.govSubsidyType === '3') {
    sum = add(num5, num6);
  } else {
    sum = [num1, num2, num3, num4, num5, num6].reduce((acc, num) => add(acc, num), '0');
  }

  // 校验总和是否等于100
  if (parseFloat(sum) !== 100) {
    if (props.govSubsidyType === '3') {
      return Promise.reject('农户自缴和其他补贴总和必须等于100');
    } else {
      return Promise.reject('中央补贴、省级补贴、地方补贴、县级补贴、农户自缴、其他补贴总和必须等于100');
    }
  }
  return Promise.resolve();
};

// 补贴类型为商业险时 置空省市区数据
const handleFinanceChange = (mainIndex: number, index: number, isFarmer?: boolean) => {
  // 触发是否等于100比例的校验
  formRef.value.validateFields([[index, 'otherFinance']]);
  // 如果是农户改变，触发农户自缴系数不能大于最小农户自缴比例的校验
  if (isFarmer) {
    formRef.value.validateFields([[mainIndex, 'riskAgrInfo', 'reductionCoefficient']]);
  }
};

// 添加附加险
const addInsurance = (mainIndex: number) => {
  data.value[mainIndex].planInfoList.push({
    isMain: '0',
  });
};

// 删除附加险
const deleteInsurance = (mainIndex: number, index: number) => {
  data.value[mainIndex].planInfoList.splice(index, 1);
};

// 展示保险方案弹窗
const showInsurance = async (insuranceProposalName: string, mainIndex: number, index: number) => {
  title.value = insuranceProposalName;
  currentIndex.value = mainIndex;
  insuranceDetail.value = insuranceList.value?.[index];
  open.value = true;
};

// 选择险种名称
const handleSelect = (mainIndex: number, index: number, option: { title: string | undefined; value: string }) => {
  data.value[mainIndex].planInfoList[index].planName = option.title;
  // 回填dutyInfoList到险种，后端需要
  const dutyInfoList = ridersOptions.value.filter((item) => item.planCode === option.value)?.[0]?.dutyInfoList;
  data.value[mainIndex].planInfoList[index].dutyInfoList = dutyInfoList;
};

// 农户是否代缴选择否清空资金来源
const substituteChange = (value: string, index: number) => {
  if (value === 'N') {
    data.value[index].riskAgrInfo.fundingSource = '';
  }
};
// 单位保险金额改变
const amountChange = (mainIndex: number, index: number, value: number, isPlan?: boolean) => {
  // 确保输入值为有效数字，如果为NaN则转换为0
  const insuredNumber = Number(data.value[mainIndex].riskAgrInfo.insuredNumber) || 0;
  const expectPremiumRate = isNaN(parseFloat(data.value[mainIndex].planInfoList[index].expectPremiumRate as string)) ? 0 : parseFloat(data.value[mainIndex].planInfoList[index].expectPremiumRate as string) / 100;

  // 校验整数12位，小数2位
  const regex = /^(?!0{13,})(0|[1-9]\d{0,11})(\.\d{1,2})?$/;
  if (!regex.test(String(value))) {
    return;
  }

  // 保险金额=单位保险金额*保险数量
  data.value[mainIndex].planInfoList[index].totalInsuredAmount = Number(multiply(String(insuredNumber), String(value || 0))).toFixed(2);
  if (!isPlan) {
    // 单位保费=单位保额*费率
    data.value[mainIndex].planInfoList[index].unitPrimium = Number(multiply(String(expectPremiumRate), String(value || 0))).toFixed(6);
  }
  // 基准保费金额 减免后保费金额 复核保费 如果没值 初始化为0
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalStandardPremium) {
    data.value[mainIndex].planInfoList[index].totalStandardPremium = '0';
  }
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalAgreePremium) {
    data.value[mainIndex].planInfoList[index].totalAgreePremium = '0';
  }
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalActualPremium) {
    data.value[mainIndex].planInfoList[index].totalActualPremium = '0';
  }

  // 触发对填写金额字段的单独校验
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'eachCompensationMaxAmount']]);
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'unitPrimium']]);
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'totalActualPremium']]);
};

// 费率改变
const rateChange = (mainIndex: number, index: number, value: number) => {
  // 确保输入值为有效数字，如果为NaN则转换为0
  const rate = isNaN(value) ? 0 : value / 100;
  const unitInsuredAmount = Number(data.value[mainIndex].planInfoList[index].unitInsuredAmount) || 0;

  // 单位保费=单位保额*费率
  data.value[mainIndex].planInfoList[index].unitPrimium = Number(multiply(String(unitInsuredAmount), String(rate))).toFixed(6);
};

// 文件下载
const downloadFile = async (fileInfo: FileInfo) => {
  const url = gateWay + service.accept + '/sys/insurance/downloadFile';
  const blob = await $postOnClient(url, { ...fileInfo });
  downloadBlob(blob as unknown as Blob, fileInfo.fileName, fileInfo.fileType);
};

// 处理数据
const transformData = (data: Array<RiskGroupInfo>) => {
  // 多标数据合并
  data.forEach((item) => {
    item.planInfoList = item.planInfoList.map((plan) => {
      const sourceItem = sourceList.value.find((source) => source.planCode === plan.planCode);
      if (sourceItem) {
        return {
          ...plan,
          centralFinance: props.govSubsidyType === '3' ? '' : sourceItem.centralFinance,
          provincialFinance: props.govSubsidyType === '3' ? '' : sourceItem.provincialFinance,
          cityFinance: props.govSubsidyType === '3' ? '' : sourceItem.cityFinance,
          countyFinance: props.govSubsidyType === '3' ? '' : sourceItem.countyFinance,
          farmersFinance: sourceItem.farmersFinance,
          otherFinance: sourceItem.otherFinance,
        };
      }
      return plan;
    });
    // 把所有项的 riskAgrInfo对象中的reductionCoefficient substitute planAttributeSameMain字段重置为最后一项的值
    const lastItem = data[data.length - 1].riskAgrInfo;
    item.riskAgrInfo = {
      ...item.riskAgrInfo,
      reductionCoefficient: lastItem.reductionCoefficient,
      substitute: lastItem.substitute,
      planAttributeSameMain: lastItem.planAttributeSameMain,
    };
  });
  // 附加险与主险一致，把附加险的保费来源数据处理成与主险的一致
  data.forEach((item) => {
    if (item?.riskAgrInfo?.planAttributeSameMain === 'Y') {
      const mainInsurance = item.planInfoList.filter((item) => item.isMain === '1');
      if (item.planInfoList.length > 1) {
        item.planInfoList = item.planInfoList.map((plan) => {
          if (plan.isMain !== '1') {
            return {
              ...mainInsurance?.[0],
              ...plan,
              centralFinance: mainInsurance?.[0].centralFinance,
              provincialFinance: mainInsurance?.[0].provincialFinance,
              cityFinance: mainInsurance?.[0].cityFinance,
              countyFinance: mainInsurance?.[0].countyFinance,
              farmersFinance: mainInsurance?.[0].farmersFinance,
              otherFinance: mainInsurance?.[0].otherFinance,
            };
          } else {
            return plan;
          }
        });
      }
    }
  });
  return data;
};

// 林业险碳汇填完碳汇单价要自动带出主险单位保险金额及后续金额
const changeAmount = (value: number) => {
  formRef.value.validateFields([[0, 'planInfoList', 0, 'unitInsuredAmount']]);
  amountChange(0, 0, value);
};

defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
  clearValidate: async () => {
    if (formRef.value) {
      await formRef.value.clearValidate();
    }
  },
  transformData,
  changeAmount: (value: number) => changeAmount(value),
});

const getProductList = async () => {
  const params = {
    productCode: props.productCode,
    version: props.productVersion,
  };
  const res = await getProductInfo.fetchData(params);
  if (res && res?.code === SUCCESS_CODE) {
    // 附加险
    const extraInsurance = res.data.planList.filter((item: PlanInfoListItem) => item.isMain === '0');
    // 主险
    const mainInsurance = res.data.planList.filter((item: PlanInfoListItem) => item.isMain === '1');
    ridersOptions.value = extraInsurance as PlanInfo[];
    mainInsuranceData.value = mainInsurance as PlanInfo[];
  }
};

// 获取方案预设列表
const getPlanList = async () => {
  const params = {
    departmentNo: props.departmentCode,
    marketProductNo: props.productCode,
    productVersionNo: props.productVersion,
  };
  const res = await getPlanData.fetchData(params);
  if (res && res?.code === SUCCESS_CODE) {
    if (res?.data.records?.length > 0) {
      insuranceList.value = res.data.records || '';
    } else {
      insuranceList.value = [];
    }
  }
};

// 获取产品列表
const getProductInfo = await usePost<InsurancePlanDetail>(`${gateWay}${service.accept}/web/applicationForm/productInfos`);
// 获取方案预设列表
const getPlanData = await usePost<{ records: Array<InsurancePlanDetail> }>(`${gateWay}${service.accept}/sys/insurance/qryPlanList`);
const initOptions = async () => {
  const params = ['fundingSource', 'AGRZZJGZSBXDW'];
  const res = await $postOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, params);
  if (res && res?.code === SUCCESS_CODE) {
    sourceOptions.value = res.data.find((item: { value: string }) => item.value === 'fundingSource')?.children || [];
    unitOptions.value = res.data.find((item: { value: string }) => item.value === 'AGRZZJGZSBXDW')?.children || [];
  }
};
onMounted(() => {
  // 获取基础信息
  initOptions();
});

// 农户自缴系数改变，刷新方案对应的金额
// const handleBlur = (mainIndex: number) => {
// data.value[mainIndex].planInfoList.forEach((item, index: number) => {
//   if (item?.unitInsuredAmount) {
//     amountChange(mainIndex, index, parseFloat(item.unitInsuredAmount || ''));
//   }
// });
// };

const handleOk = () => {
  if (insuranceDetail?.value) {
    // 选择方案时 主险的费率不带出
    // 产品主险费率
    data.value.forEach((item, mainIndex) => {
      item.planInfoList = cloneDeep(insuranceDetail.value.planList) as PlanInfoListItem[];
      item.riskAgrInfo.planAttributeSameMain = insuranceDetail.value.planAttributeSameMain;
      item.riskAgrInfo.planConfigId = insuranceDetail.value.id;
      // 初始化金额默认值
      insuranceDetail.value.planList.forEach((item, index: number) => {
        amountChange(mainIndex, index, parseFloat(item.unitInsuredAmount || ''), true);
      });
    });
    open.value = false;
  }
};

// 获取产品信息
watch(
  () => [props.productCode, props.productVersion],
  () => {
    if (props.productCode && props.productVersion) {
      getProductList();
    }
  },
);

// 获取预设保险方案名称列表
watch(
  () => [props.productCode, props.productVersion, props.departmentCode],
  () => {
    if (props.productCode && props.productVersion && props.departmentCode) {
      getPlanList();
    }
  },
);
</script>

<style lang="less" scoped>
.insurance-plan {
  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }
  .table-wrap {
    width: calc(100vw - 420px);
  }
}
</style>
