<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item label="农保ID" class="col-span-3" name="assisterInfoList" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ required: true, validator: checkId }]">
        <div class="flex items-center">
          <a-select v-model:value="assisterInfoList" class="w-full" mode="multiple" :filter-option="filterOption" :max-tag-count="8" @select="handleSelect" @change="(value, option) => handleChange(value as Array<string>)">
            <!-- 最多选择8个农保id 选择无就不能再选择其他id -->
            <a-select-option v-for="item in assistIds" :key="item.assisterNameAndUm" :value="item.assisterId" :disabled="(assisterInfoList.length >= 8 && assisterInfoList.findIndex((assister: string) => assister === item.assisterId) === -1) || (assisterInfoList.includes('无') && item.assisterId !== '无')">
              {{ item.assisterNameAndUm }}
            </a-select-option>
          </a-select>
        </div>
      </a-form-item>
      <a-form-item required label="保险起期" name="insuranceBeginDate" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ required: true, message: '请选择保险起期' }]">
        <a-date-picker v-model:value="data.insuranceBeginDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled-date="disabledDate" @change="changeBeginDate" />
        <span v-if="showBeginTips" class="text-[#FAAD14]">提示：保险起期距离当前日期超过1个月，请核实确认</span>
      </a-form-item>
      <div v-if="showApprovalBtn && props.allowInsuranceDatePastFlag" class="w-[84px] flex">
        <a-popover v-if="signData?.signStatus && signData?.signStatus !== '0'" placement="top">
          <template #content>
            <div>
              签报状态：{{ signData.signStatusDesc }}，签报号：<span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(signData.eoaDetailUrl)">{{ signData.eoaNo }}</span>
            </div>
          </template>
          <a-button type="primary" class="mr-[8px]">签报状态</a-button>
        </a-popover>
        <a-button type="primary" @click="launchApproval">发起签报</a-button>
      </div>
      <a-form-item label="保险期限" name="insuranceTerm" :label-col="{ style: { width: pxToRem(110) } }">
        <a-select v-model:value="insuranceTerm" :options="termOption" class="w-full" @change="handleTermChange" />
      </a-form-item>
      <a-form-item required label="保险止期" name="insuranceEndDate" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ required: true, message: '请选择保险止期' }]">
        <a-date-picker v-model:value="data.insuranceEndDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        <span v-if="showEndTips" class="text-[#FAAD14]">该单为保险期限超一年，请核实数据录入的准确性和真实性</span>
      </a-form-item>
      <a-form-item required label="起止时间" name="timeRange" :label-col="{ style: { width: pxToRem(110) } }">
        <a-radio-group v-model:value="data.timeRange">
          <a-radio value="0">0-24时</a-radio>
          <a-radio value="1">12-12时</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item required label="年限系数" name="shortTimeCoefficient" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ validator: coefficientValidator }]">
        <a-input-number v-model:value="data.shortTimeCoefficient" :min="0" style="width: 100%" placeholder="请输入" />
      </a-form-item>
      <a-form-item required name="disputedSettleMode" label="争议处理方式" :label-col="{ style: { width: pxToRem(110) } }">
        <a-select v-model:value="data.disputedSettleMode" class="w-full" :options="selectOption" @change="resolutionChange" />
      </a-form-item>
      <a-form-item v-if="data.disputedSettleMode === '2'" required label="仲裁机构" name="arbitralDepartment" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ max: 100, message: '不能超过100个字符' }]">
        <a-input v-model:value="data.arbitralDepartment" placeholder="请输入" />
      </a-form-item>
    </div>
  </a-form>
  <CommonApprovalModal v-if="showApproval" v-model:copy-btn-disabled="copyBtnDisabled" v-model:open="showApproval" v-model:preview-open="previewOpen" :copy-approval-btn="true" :show-reset-btn="true" title="保险起期前置业务申请签报" :show-preview-btn="true" eoa-type="T16" :total-actual-premium="data.totalActualPremium" :department-code="data.departmentCode" @ok="handleSubmit" @reset="handleReset" @preview="handlePreview" @copy-approval="copyApproval">
    <template #content>
      <a-form ref="eoaRef" :model="formData" :colon="false">
        <div class="bg-[#f8f8f8] rounded-[4px]">
          <a-form-item label="该项目已申请签报号" name="relationBusinessNo" extra="注：同一项目只需要申请一次签报" :label-col="labelColStyle">
            <div class="flex">
              <a-input v-model:value="formData.relationBusinessNo" placeholder="请输入" :style="{ width: '216px' }" />
              <a-button class="ml-[8px]" type="primary" :disabled="!formData.relationBusinessNo" @click="searchEoa">查询</a-button>
            </div>
          </a-form-item>
          <a-form-item label="申请事项" :rules="[{ required: true, message: '事项名称不能为空' }]" name="eoaSubject" :label-col="labelColStyle">
            <a-input v-model:value="formData.eoaSubject" placeholder="请输入" :maxlength="80" show-count>
              <template #addonBefore>
                <span>关于</span>
              </template>
              <template #addonAfter>
                <span>保险起期前置的申请</span>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="涉及险种" :label-col="labelColStyle">
            <a-input v-model:value="agriculturalRiskObjectClassName" disabled :style="{ width: '216px' }" />
          </a-form-item>
          <a-form-item label="预估总保费" required name="projectFee" extra="注：需填写该险种范围下我司累计年预估保费" :label-col="labelColStyle" :rules="[{ required: true, message: '预估总保费不能为空' }, { validator: ValidProjectFee }]">
            <div class="flex items-center">
              <a-input v-model:value="formData.projectFee" :style="{ width: '216px' }">
                <template #addonAfter>
                  <span>人民币</span>
                </template>
              </a-input>
              <a-tooltip placement="top">
                <template #title>注：需要填写该险种范围下我司累计年预估保费</template>
                <VueIcon :icon="IconErrorCircleFilledFont" class="text-[14px] ml-[17px] text-[rgba(0,0,0,0.55)]" />
              </a-tooltip>
            </div>
          </a-form-item>
          <a-form-item label="保险起期范围" name="date" required :label-col="labelColStyle">
            <a-range-picker v-model:value="formData.date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </a-form-item>
          <a-form-item label="保险起期前置原因" name="eoaBody" :rules="[{ required: true, message: '保险起期前置原因不能为空' }]" :label-col="labelColStyle">
            <a-textarea v-model:value="formData.eoaBody" />
          </a-form-item>
        </div>
      </a-form>
    </template>
    <template #previewContent>
      <div v-for="(item, index) in previewData" :key="index">{{ item }}</div>
    </template>
  </CommonApprovalModal>
  <a-modal v-model:open="warningOpen" :mask-closable="false" :width="pxToRem(450)" :centered="true">
    <div>
      <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
      <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
    </div>
    <div class="mt-[10px]">保险起期小于当前日期，存在倒签风险，请完成签报审批。</div>
    <template #footer>
      <a-button key="back" type="primary" @click="warningOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import dayjs, { type Dayjs } from 'dayjs';
import type { SelectProps } from 'ant-design-vue';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { RawValueType } from 'ant-design-vue/es/vc-tree-select/TreeSelect';
import type { LabelInValueType } from 'ant-design-vue/es/vc-select/Select';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { BaseInfo, AssisterInfo, BackSignContent, RiskGroupInfoType, EoaData, SignData, RiskAddressInfo } from '../insuranceFormFill';
import { pxToRem } from '@/utils/tools';
import { usePost, $postOnClient } from '@/composables/request';
import { useUserStore } from '@/stores/useUserStore';
import { message } from '@/components/ui/Message';
import { useIndexDB } from '@/composables/useIndexDB';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import { SUCCESS_CODE } from '@/utils/constants';

const copyBtnDisabled = ref<boolean>(true);

const { gateWay, service } = useRuntimeConfig().public || {};
const { getCacheData } = useIndexDB();
const props = defineProps<{
  riskGroupInfoList: RiskGroupInfoType[]; // 保险方案
  signDataList: SignData[];
  allowInsuranceDatePastFlag: boolean; // 允许保险起期前置开关
  isAcceptInsurance?: boolean; // 从共出单
  addressList: RiskAddressInfo[]; // 标的地址
}>();
const termOption = [
  {
    label: '3个月',
    value: '3',
  },
  {
    label: '4个月',
    value: '4',
  },
  {
    label: '半年',
    value: '6',
  },
  {
    label: '一年',
    value: '1',
  },
];

const data = defineModel<BaseInfo>('data', { default: {} });
const emits = defineEmits(['getEoaData']);

const { userInfo } = useUserStore();
const showApproval = ref<boolean>(false);
const previewOpen = ref<boolean>(false);
const agriculturalRiskObjectClassName = ref<string>('');
watch(
  () => props.riskGroupInfoList,
  () => {
    if (props.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassName) {
      agriculturalRiskObjectClassName.value = props.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassName;
    }
  },
);
// 保险起期前置签报内容
const formData = ref<BackSignContent>({});
// 保险起期前置签报ref
const eoaRef = ref();
// 判断 保险起期前置 提示弹窗 是否显示
const warningOpen = ref(false);
// 农保id options
const assistIds = ref<Array<AssisterInfo>>([
  {
    assisterId: '无',
    idCardNo: '',
    umCode: '',
    assisterNameAndUm: '无',
  },
]);
// 农保id value
const assisterInfoList = ref<Array<string>>([]);

// 争议处理方式options
const selectOption = ref<SelectProps['options']>();
// 保险期限
const insuranceTerm = ref();

const formRef = ref();

// 倒签签报信息
const signData = computed(() => {
  return props?.signDataList?.filter((item) => item?.signType === '032')?.[0];
});

// 是否显示倒签签报按钮
const showApprovalBtn = computed(() => {
  if (props.isAcceptInsurance) return false; // 从共出单直接不显示倒签签报相关
  if (data?.value?.insuranceBeginDate) {
    // 起止时间选择0-24时，保险起期为选择的日期00:00:00
    if (data?.value?.timeRange === '0') {
      const insuranceBeginDate = dayjs(data?.value?.insuranceBeginDate);
      const today = dayjs().startOf('day'); // 获取今天的开始时间
      if (insuranceBeginDate.isValid()) {
        // 确保保险起期日期有效
        return insuranceBeginDate.valueOf() <= today.valueOf(); // 通过时间戳比较
      }
      return false; // 如果保险起期日期无效，返回false
    } else {
      // 起止时间选择12-12时的逻辑
      const insuranceBeginDateAt12 = dayjs(data?.value?.insuranceBeginDate).set('hour', 12).set('minute', 0).set('second', 0);
      const now = dayjs();
      return now.diff(insuranceBeginDateAt12, 'second') > 0; // 简化原有的逻辑
    }
  }
  return false; // 默认情况下返回false
}); // 当保险起期小于当前日期，弹出提示弹窗
const changeBeginDate = () => {
  if (showApprovalBtn.value) {
    warningOpen.value = true;
  }
  if (dayjs(data?.value?.insuranceEndDate).diff(dayjs(data?.value?.insuranceBeginDate), 'year', true) > Number(data?.value.shortTimeCoefficient)) {
    message.info('提示：保险起止期已大于年限系数');
  }
};
// 保险起止期已大于年限系数 弹窗提示
watch(
  () => data?.value?.insuranceEndDate,
  () => {
    if (dayjs(data?.value?.insuranceEndDate).diff(dayjs(data?.value?.insuranceBeginDate), 'year', true) > Number(data?.value.shortTimeCoefficient)) {
      message.info('提示：保险起止期已大于年限系数');
    }
  },
);
// 保险起始日期是否大于当前日期一个月
const showBeginTips = computed(() => {
  return dayjs(data?.value?.insuranceBeginDate).diff(dayjs(), 'month', true) >= 1;
});

// 保险期限超过一年
const showEndTips = computed(() => {
  return dayjs(data?.value?.insuranceEndDate).diff(dayjs(data?.value?.insuranceBeginDate), 'year', true) >= 1;
});

// 起保终保日期大于年限系数
// const coefficientTips = computed(() => {
//   if (!data?.value.shortTimeCoefficient || Number(data?.value.shortTimeCoefficient) < 0) {
//     return false;
//   }
//   return (
//     dayjs(insuranceDate?.value?.[1]).diff(dayjs(insuranceDate?.value?.[0]), 'year', true) >
//     Number(data?.value.shortTimeCoefficient)
//   );
// });

const filterOption = (input: string, option: { key: string }) => {
  return option.key.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 共保id选择无需要提示
const handleSelect = (value: RawValueType | LabelInValueType) => {
  if (value === '无') {
    assisterInfoList.value = ['无'];
    data.value.assisterInfoList = [
      {
        assisterId: '无',
        idCardNo: '',
        umCode: '',
        assisterNameAndUm: '无',
      },
    ];
    message.info('选择“无”将无法录入并支付协办费(共保非主承除外)!');
  }
};

// 共保id改变
const handleChange = (value: Array<string>) => {
  const results: Array<AssisterInfo> = [];
  if (Array.isArray(value)) {
    assistIds.value.forEach((item: AssisterInfo) => {
      if (value?.includes(item.assisterId) && item.assisterId !== '无') {
        results.push(item);
      }
    });
  }
  data.value.assisterInfoList = results;
};

// 保险期限改变 刷新保险止期
const handleTermChange = (value: SelectValue) => {
  if (data?.value?.insuranceBeginDate) {
    const endDate = value !== '1' ? dayjs(data?.value?.insuranceBeginDate).add(Number(value), 'month') : dayjs(data?.value?.insuranceBeginDate).add(Number(value), 'year');
    data.value.insuranceEndDate = endDate.subtract(1, 'day').format('YYYY-MM-DD');
    formRef?.value?.validate(['insuranceEndDate']);
  }
};

// 农保id校验
const checkId = (_rule: Rule) => {
  if (assisterInfoList?.value?.length < 1) {
    return Promise.reject('农保ID不能为空');
  }
  return Promise.resolve();
};

// 年限系数校验
const coefficientValidator = (_rule: Rule, value: number) => {
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!reg.test(String(value))) {
    return Promise.reject('请输入数字且小数位不能超过2位！');
  }
  if (value <= 0) {
    return Promise.reject('年限系数要大于0');
  }
  if (value >= 100) {
    return Promise.reject('年限系数要小于100');
  }
  return Promise.resolve();
};

// 争议方式改变清空仲裁机构
const resolutionChange = () => {
  data.value.arbitralDepartment = '';
};

// 根据签报号跳转eoa
const goEoa = (url: string) => {
  window.open(url);
};

// 根据签报号查询签报信息
const searchEoa = async () => {
  const fetchUrl = gateWay + service.accept + '/accept/eoa/queryProjectByEoaNo';
  const params = {
    eoaNo: formData.value.relationBusinessNo,
    projectType: '1',
    eoaType: '032', // 倒签签报类型
    agriculturalRiskObjectClassCode: props.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode,
    departmentCode: data.value.departmentCode,
  };
  const res = await $postOnClient<{ projectFee: string; projectPeriodBeginDate: string; projectPeriodEndDate: string; eoaNo: string; eoaBody: string; eoaSubject: string }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    formData.value = {};
    copyBtnDisabled.value = false;
    formData.value.projectFee = res.data.projectFee;
    formData.value.date = [res.data.projectPeriodBeginDate || '', res.data.projectPeriodEndDate || ''];
    formData.value.eoaNo = res.data.eoaNo;
    formData.value.relationBusinessNo = res.data.eoaNo;
    formData.value.eoaBody = res.data.eoaBody;
    formData.value.eoaSubject = res.data.eoaSubject;
  } else {
    message.error(res?.msg || '');
    copyBtnDisabled.value = true;
  }
};
// 复用签报
const copyApproval = async () => {
  const fetchUrl = gateWay + service.accept + '/accept/eoa/reuseEoaNoForApplyPolicyNo';
  const params = {
    applyPolicyNo: data.value.applyPolicyNo,
    agriculturalRiskObjectClassCode: props.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode,
    departmentCode: data.value.departmentCode,
    insuranceBeginDate: data.value.insuranceBeginDate,
    eoaNo: formData.value.eoaNo,
    totalActualPremium: data.value.totalActualPremium,
    projectType: '1',
    eoaType: '032',
  };
  const res = await $postOnClient<{ projectFee: string; projectPeriodBeginDate: string; projectPeriodEndDate: string; eoaNo: string }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    message.success(res?.msg || '');
    showApproval.value = false;
    emits('getEoaData');
    handleReset();
  } else {
    message.error(res?.msg || '');
    copyBtnDisabled.value = true;
  }
};

// 校验预估总保费
const ValidProjectFee = (rule: Rule, value: string) => {
  const reg = /^\d{0,12}(\.\d{1,2})?$/;
  if (!reg.test(value)) {
    return Promise.reject('只能录入数字,支持12位整数位,2位小数位!');
  }
  return Promise.resolve();
};
// 关闭弹窗重置表单数据
const handleReset = async () => {
  eoaRef?.value.resetFields();
  formData.value = {};
  copyBtnDisabled.value = true;
};
defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
  clearValidate: async () => {
    if (formRef.value) {
      await formRef.value.clearValidate();
    }
  },
});
const launchApproval = () => {
  if (data.value.totalActualPremium) {
    showApproval.value = true;
  } else {
    message.warning('请先保费计算');
  }
};
const createEoa = await usePost<{ msg: string; code: string }>(`${gateWay}${service.accept}/accept/eoa/createEoa`);
const previewEoa = await usePost<{ msg: string; data: string[]; code: string } | null>(`${gateWay}${service.accept}/accept/eoa/previewApplyReason`);
// 预览签报
const handlePreview = (eoaData: EoaData) => {
  previewOrCreate(eoaData, 'preview');
};
// 提交签报
const handleSubmit = async (eoaData: EoaData) => {
  previewOrCreate(eoaData, 'submit');
};
const previewData = ref<string[]>();
// 预览和创建签报接口
const previewOrCreate = async (eoaData: EoaData, type: string) => {
  await eoaRef?.value?.validate();
  try {
    // 用于提交签报的参数
    const params = {
      relationBusinessNo: data.value.applyPolicyNo,
      eoaType: '032', // 倒签签报类型
      eoaSubject: formData.value.eoaSubject,
      eoaBody: formData.value.eoaBody,
      productCode: data.value.productCode,
      departmentCode: data.value.departmentCode,
      documentGroupId: eoaData.documentGroupId,
      businessData: {
        projectType: '1', // 倒签签报
        departmentCode: data.value.departmentCode,
        agriculturalRiskObjectClassCode: props.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode,
        insuranceBeginDate: data.value.insuranceBeginDate,
        projectPeriodBeginDate: formData.value.date?.[0] || '',
        projectPeriodEndDate: formData.value.date?.[1] || '',
        totalActualPremium: data.value.totalActualPremium,
        projectFee: formData.value.projectFee,
      },
      approveChainList: eoaData.approveChainList,
      attachmentList: eoaData.fileInfos,
    };
    if (type === 'preview') {
      const res = await previewEoa.fetchData(params);
      if (res && res.code === SUCCESS_CODE) {
        message.success(res?.msg);
        previewData.value = (res.data || []) as string[];
        previewOpen.value = true;
      } else {
        message.error(res?.msg as string);
      }
    } else {
      const res = await createEoa.fetchData(params);
      if (res && res.code === SUCCESS_CODE) {
        message.success(res?.msg);
        showApproval.value = false;
        emits('getEoaData');
        handleReset();
      } else {
        message.error(res?.msg as string);
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 获取农保id
const queryAssistListReq = await usePost<AssisterInfo[]>(`${gateWay}${service.accept}/saleInfo/queryAssistList`);

// 获取基础信息数据
const queryBaseData = async () => {
  const requestParams = {
    url: `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`,
    params: ['disputeType'],
  };
  const res = (await getCacheData('optionData', requestParams)) as SelectOptions[];
  selectOption.value = res?.[0]?.children;
};
// 获取农保id数据
const getAssistIdData = async () => {
  const address = props.addressList[0] || {};
  const params = {
    um: userInfo.umCode,
    departmentNo: data.value.departmentCode,
    orderVillage: address.village, // 村编码
  };
  const res = await queryAssistListReq.fetchData(params);
  if (res?.code === '000000') {
    assistIds.value.push(...res.data);
  }
};
// 初始化默认值 起保终保字段和农保id字段与组件类型不一致，需转换
watch(
  () => data?.value?.assisterInfoList,
  () => {
    if (data?.value?.assisterInfoList?.length > 0) {
      assisterInfoList.value = data.value.assisterInfoList?.map((item) => item.assisterId) || [''];
    } else {
      assisterInfoList.value = [];
    }
  },
);

const labelColStyle = {
  style: {
    width: pxToRem(130),
  },
};
const disabledDate = (current: Dayjs) => {
  // 获取当前日期
  const currentDay = dayjs();
  // 允许保险起期前置的开关关闭，禁用当天及之前的日期
  if (!props.allowInsuranceDatePastFlag) {
    return current && current < currentDay;
  } else {
    return false;
  }
};
onMounted(() => {
  queryBaseData();
});

watch(
  () => data.value.departmentCode,
  () => {
    if (data.value.departmentCode) {
      getAssistIdData();
    }
  },
);
</script>

<style>
.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  width: fit-content;

  &::before {
    position: absolute;
    left: 0;
    top: 6px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
</style>
