<template>
  <a-form v-if="Object.keys(data).length" ref="formRef" :model="data" :colon="false">
    <div class="flex justify-between">
      <a-form-item label="共保类型" :label-col="{ style: { width: pxToRem(data.innerCoinsuranceMark === '1' ? 80 : 106) } }" required>
        <a-radio-group v-model:value="data.innerCoinsuranceMark" @change="innerCoinsuranceMarkChange(data.innerCoinsuranceMark)">
          <a-radio :value="'0'">系统外共保</a-radio>
          <!-- 非主承时，禁用系统内共保 -->
          <a-radio v-if="showInSystemBtn" :value="'1'" :disabled="acceptInsuranceFlag === '0'">系统内共保</a-radio>
          <a-radio v-if="showInSystemBtn" :value="'2'">系统外同时系统内共保</a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- 发起内部共保审批签报button -->
      <div v-if="data.innerCoinsuranceMark === '1'">
        <a-popover v-if="signData?.signStatus && signData?.signStatus !== '0'" placement="top">
          <template #content>
            <div>
              签报状态：{{ signData.signStatusDesc }}，签报号：<span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(signData.eoaDetailUrl)">{{ signData.eoaNo }}</span>
            </div>
          </template>
          <a-button type="primary" class="mr-[8px]">签报状态</a-button>
        </a-popover>
        <a-button type="primary" @click="showApprovalModal">
          {{ `发起内部共保审批签报` }}
        </a-button>
      </div>
      <!-- 发起内部外部共保审批签报button -->
      <div v-if="data.innerCoinsuranceMark === '2'">
        <a-popover v-if="bothSignData?.signStatus && bothSignData?.signStatus !== '0'" placement="top">
          <template #content>
            <div>
              签报状态：{{ bothSignData.signStatusDesc }}，签报号：<span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(bothSignData.eoaDetailUrl)">{{ bothSignData.eoaNo }}</span>
            </div>
          </template>
          <a-button type="primary" class="mr-[8px]">签报状态</a-button>
        </a-popover>
        <a-button type="primary" @click="showApprovalModal">
          {{ `发起内部外部共保审批签报` }}
        </a-button>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px">
      <template v-if="data.innerCoinsuranceMark !== '1'">
        <a-form-item label="共保协议代码" name="innerCoinsuranceAgreement" :rules="[{ required: true, message: '共保协议代码不能为空', trigger: 'blur' }]">
          <a-input v-model:value="data.innerCoinsuranceAgreement" :style="{ width: pxToRem(207) }" />
        </a-form-item>
      </template>
      <a-form-item label="总保额" :colon="true">
        {{ data.totalInsuredAmount || '0' }}
      </a-form-item>
      <a-form-item label="总保费" :colon="true">
        {{ data.totalPremium || '0' }}
      </a-form-item>
    </div>
    <!-- 系统内共保表格 -->
    <InSystem v-if="data.innerCoinsuranceMark === '1' && saleInfo" ref="inSystemRef" v-model="data.coinsuranceDetailList" :department-code="departmentCode" :develop-flg="saleInfo.developFlg" />
    <!-- 系统外共保表格 -->
    <OutsideSystem v-if="!data?.innerCoinsuranceMark || data.innerCoinsuranceMark === '0'" ref="outsideSystemRef" v-model="data.coinsuranceDetailList" :company-list="companyList" />
    <!-- 系统内外共保表格 -->
    <OutsideSystem v-if="data.innerCoinsuranceMark === '2'" ref="bothInSystemRef" v-model="data.coinsuranceDetailList" :company-list="companyList" />
    <InSystem v-if="data.innerCoinsuranceMark === '2' && saleInfo" ref="bothOutsideSystemRef" v-model="data.coinsuranceDetailList" :department-code="departmentCode" :develop-flg="saleInfo.developFlg" />
  </a-form>
  <a-empty v-else />
  <CommonApprovalModal v-model:open="showApproval" v-model:preview-open="previewOpen" v-model:copy-btn-disabled="copyBtnDisabled" :title="approvalTitle" :show-preview-btn="true" approval-chain-type="1" :total-actual-premium="totalActualPremium" :show-reset-btn="true" :file-required="true" :copy-approval-btn="true" @ok="handleSubmit" @reset="handleReset" @preview="handlePreview" @get-url="getUrl" @copy-approval="copyApproval">
    <template #content>
      <a-form ref="eoaRef" :model="formData" :colon="false">
        <div class="bg-[#f8f8f8] rounded-[4px]">
          <a-row :gutter="16">
            <a-col :span="14">
              <a-form-item label="该项目已申请签报号" name="relationBusinessNo" :label-col="labelColStyle">
                <div class="flex">
                  <a-input v-model:value="formData.relationBusinessNo" placeholder="请输入" :style="{ width: '216px' }" />
                  <a-button class="ml-[8px]" type="primary" @click="searchEoa">查询</a-button>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="共保申请事项" :rules="[{ required: true, message: '事项名称不能为空' }]" name="eoaSubject" :label-col="labelColStyle">
            <a-input v-model:value="formData.eoaSubject" placeholder="请输入" :maxlength="80" show-count>
              <template #addonBefore>
                <span>关于</span>
              </template>
              <template #addonAfter>
                <span>项目内共保保费划分的请示</span>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="共保预估保费" required name="projectFee" extra="注：需填写该项目所涉及的全险种我司累计年预估保费" :label-col="labelColStyle" :rules="[{ required: true, message: '共保预估保费不能为空' }, { validator: ValidProjectFee }]">
            <div class="flex items-center">
              <a-input v-model:value="formData.projectFee" :style="{ width: '216px' }">
                <template #addonAfter>
                  <span>人民币</span>
                </template>
              </a-input>
              <a-tooltip placement="top">
                <template #title>注：需要填写该险种范围下我司累计年预估保费</template>
                <VueIcon :icon="IconErrorCircleFilledFont" class="text-[14px] ml-[17px] text-[rgba(0,0,0,0.55)]" />
              </a-tooltip>
            </div>
          </a-form-item>
          <a-form-item label="项目共保时间" name="date" required :label-col="labelColStyle">
            <a-range-picker v-model:value="formData.date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </a-form-item>
          <a-form-item label="项目共保说明" name="eoaBody" :rules="[{ required: true, message: '保险起期前置原因不能为空' }]" :label-col="labelColStyle">
            <a-input v-model:value="formData.eoaBody" />
          </a-form-item>
        </div>
      </a-form>
    </template>
    <template #previewContent>
      <div class="bg-[#f8f8f8] rounded-[4px] px-[16px] py-[16px]">
        <div v-for="(item, index) in previewData" :key="index">{{ item }}</div>
      </div>
    </template>
  </CommonApprovalModal>
</template>

<script setup lang="ts">
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { SaleInfo, CoinsuranceContent, EoaData, SignData } from '../../insuranceFormFill';
import InSystem from './InSystem.vue';
import OutsideSystem from './OutsideSystem.vue';
import type { Company, CoinsuranceInfo, CoinsuranceDetail } from './coinsuranceInfo';
import { $getOnClient, useGet, usePost, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';

// 系统内ref
const inSystemRef = ref();
// 系统外ref
const outsideSystemRef = ref();
// 系统内外 -> 系统内ref
const bothInSystemRef = ref();
// 系统内外 -> 系统外ref
const bothOutsideSystemRef = ref();
const previewOpen = ref<boolean>(false);
const showApproval = ref<boolean>(false);
// 是否展示系统内共保及系统内外共保
const showInSystemBtn = ref<boolean>(true);
// 从共开关
const nonMainVerifySwitch = ref<boolean>(false);
// 系统内共保签报内容
const formData = ref<CoinsuranceContent>({});
// 系统内共保签报ref
const eoaRef = ref();

const copyBtnDisabled = ref<boolean>(true);

const formRef = ref();

const { departmentCode, acceptInsuranceFlag, saleInfo, totalActualPremium, applyPolicyNo, insuranceBeginDate, signDataList, productCode } = defineProps<{
  departmentCode?: string; // 机构code
  acceptInsuranceFlag?: string; // 是否主承保 1是 0否
  saleInfo?: SaleInfo; // 业务员信息
  totalActualPremium?: string; // 整单总保费
  applyPolicyNo?: string; // 投保单号
  insuranceBeginDate?: string; // 保险起期
  signDataList: SignData[]; // 签报数据
  productCode: string; // 产品编码
}>();
const emits = defineEmits(['getEoaData']);
const data = defineModel<CoinsuranceInfo>({ default: {} });

// 共保公司列表
const companyList = ref<Company[]>([]);

// 切换共保类型
const innerCoinsuranceMarkChange = (value: string | undefined) => {
  // 系统外共保
  const outsideSystem: CoinsuranceDetail = {
    reinsureCompanyCode: '3005',
    reinsureCompanyName: '中国平安财产保险股份有限公司',
    insuredAmount: '0',
    premium: '0',
    reinsureScale: '',
    acceptInsuranceFlag,
    coinsuranceType: '0',
  };
  // 系统内共保
  const inSystem: CoinsuranceDetail = {
    acceptInsuranceFlag,
    reinsureCompanyCode: saleInfo ? saleInfo.departmentCode : '', // 机构编码
    reinsureCompanyName: '',
    coinsureCompanyFinanceDeptCode: '',
    coinsureEmployeeList: saleInfo ? saleInfo.preRiskEmployeeInfoList : [], // 业务员列表
    reinsureScale: '',
    premium: '0',
    coinsuranceType: '1',
  };
  // 初始化共保对象结构
  const coinsuranceInfo: CoinsuranceInfo = {
    innerCoinsuranceMark: value,
    totalInsuredAmount: '0',
    totalPremium: '0',
    innerCoinsuranceAgreement: '',
  };
  // 按照共保类型初始化表格模型
  if (value === '0') {
    coinsuranceInfo.coinsuranceDetailList = [outsideSystem];
  } else if (value === '1') {
    coinsuranceInfo.coinsuranceDetailList = [inSystem];
  } else if (value === '2') {
    coinsuranceInfo.coinsuranceDetailList = [{ ...inSystem, acceptInsuranceFlag: '1' }, outsideSystem];
  }
  // 共保模型初始化
  data.value = coinsuranceInfo;
};
// 内共保签报详情
const signData = computed(() => {
  return signDataList?.filter((item) => item?.signType === '030')?.[0];
});

// 内外共保签报详情
const bothSignData = computed(() => {
  return signDataList?.filter((item) => item?.signType === '043')?.[0];
});

// 根据签报号跳转eoa
const goEoa = (url: string) => {
  window.open(url);
};

// 提交前数据处理，主要用于去除disabled和options
const transformData = (data: CoinsuranceInfo) => {
  if (data?.coinsuranceDetailList?.length && data?.coinsuranceDetailList?.length > 0) {
    return {
      ...data,
      coinsuranceDetailList: data.coinsuranceDetailList.map((item) => {
        const { fromFirst, developFlg, ...rest } = item; // 解构并排除fromFirst和developFlg
        return {
          ...rest,
          coinsureEmployeeList: item.coinsureEmployeeList?.map((childItem) => {
            return {
              coDevelopChannelClassCode: childItem.coDevelopChannelClassCode,
              mainEmployeeFlag: childItem.mainEmployeeFlag,
              employeeCode: childItem.employeeCode,
              employeeName: childItem.employeeName,
              commisionScale: childItem.commisionScale,
            };
          }),
        };
      }),
    };
  } else {
    return data;
  }
};

const initData = () => {
  // 系统内
  if (data.value.innerCoinsuranceMark === '1') {
    inSystemRef.value?.initData();
  } else if (data.value.innerCoinsuranceMark === '2') {
    // 系统内外
    bothInSystemRef.value?.initData();
  }
};

const validate = async () => {
  await formRef.value?.validateFields();
  let validateArr = [];
  // 系统内
  if (data.value.innerCoinsuranceMark === '1') {
    validateArr = [inSystemRef.value?.validate()];
  } else if (data.value.innerCoinsuranceMark === '2') {
    // 系统内外
    validateArr = [bothInSystemRef.value?.validate(), bothOutsideSystemRef.value?.validate()];
  } else {
    // 系统外
    validateArr = [outsideSystemRef.value?.validate()];
  }
  const results = await Promise.all(validateArr);
  const isValid = results.every((result) => result?.valid);
  if (isValid) {
    // 所有表单校验通过
    return { valid: true, errors: [] };
  } else {
    return { valid: false, errors: ['校验不通过'] };
  }
};

// 将转换过后的data传给父组件
defineExpose({
  transformData,
  validate,
  initData,
  nonMainVerifySwitch,
});

// nuxt.config runtime配置
const { gateWay, service } = useRuntimeConfig().public;

// 获取共保公司接口
const getCoinsuranceCompanyListReq = await useGet<Company[]>(`${gateWay}${service.accept}/coinsurance/getCoinsuranceCompanyList`);

// 获取共保公司列表
const getCoinsuranceCompanyList = async () => {
  const res = await getCoinsuranceCompanyListReq.fetchData();
  if (res?.code === SUCCESS_CODE) {
    companyList.value = res.data || [];
  }
};
// 获取开关 控制是否展示内共保和内外共保
const getSwitches = async () => {
  const res = await $getOnClient<{ innerCoinsuranceSwitch: boolean; eoa2GenerateChainUrl: string; nonMainVerifySwitch: boolean }>(`${gateWay}${service.accept}/sys/switchConfig/queryAcceptSwitches`);
  if (res?.code === SUCCESS_CODE) {
    showInSystemBtn.value = res.data.innerCoinsuranceSwitch;
    nonMainVerifySwitch.value = res.data.nonMainVerifySwitch;
  }
};
// 跳转eoa链接
const getUrl = async () => {
  const params = {
    projectType: data.value.innerCoinsuranceMark == '1' ? '0' : '2', // 内共保 0, 内外共保 2
    eoaType: data.value.innerCoinsuranceMark == '1' ? '030' : '043', // 系统内共保030， 内外共保043
    departmentCode: departmentCode,
    projectFee: formData.value.projectFee,
    coinsuranceDetailList: data.value.coinsuranceDetailList,
  };
  const res = await $postOnClient<string>(`${gateWay}${service.accept}/accept/eoa/queryEoaTemplate`, params);
  if (res?.code === SUCCESS_CODE) {
    window.open(res?.data || '');
  } else {
    message.error(res?.msg || '');
  }
};

// 签报弹窗表头
const approvalTitle = ref<string>('');
// 打开发起签报弹窗
const showApprovalModal = async () => {
  // 系统内共保审批签报
  if (data.value.innerCoinsuranceMark == '1') {
    approvalTitle.value = '内部共保业务申请签报';
    if (!totalActualPremium) {
      message.warning('请先保费计算');
    } else if ((data.value.coinsuranceDetailList as CoinsuranceDetail[])?.length < 2) {
      message.warning('业务机构不能少于2个');
    } else {
      const result = await inSystemRef.value?.validate();
      if (result.valid) {
        showApproval.value = true;
      }
    }
  } else if (data.value.innerCoinsuranceMark == '2') {
    approvalTitle.value = '内部外部共保业务申请签报';
    const innerList = (data.value.coinsuranceDetailList as CoinsuranceDetail[])?.filter((item) => item.coinsuranceType === '1');
    const outList = (data.value.coinsuranceDetailList as CoinsuranceDetail[])?.filter((item) => item.coinsuranceType === '0');
    if (!totalActualPremium) {
      message.warning('请先保费计算');
    } else if (innerList?.length < 2) {
      message.warning('业务机构不能少于2个');
    } else if (outList?.length < 2) {
      message.warning('共保公司不能少于2个');
    } else {
      const result = await bothInSystemRef.value?.validate();
      if (result.valid) {
        showApproval.value = true;
      }
    }
  }
};
const labelColStyle = {
  style: {
    width: pxToRem(130),
  },
};
const createEoa = await usePost<{ resultObject: string }>(`${gateWay}${service.accept}/accept/eoa/createEoa`);
const previewEoa = await usePost<{ msg: string; data: string[]; code: string } | null>(`${gateWay}${service.accept}/accept/eoa/previewApplyReason`);
// 预览签报
const handlePreview = (eoaData: EoaData) => {
  previewOrCreate(eoaData, 'preview');
};
// 提交签报
const handleSubmit = async (eoaData: EoaData) => {
  previewOrCreate(eoaData, 'submit');
};
const previewData = ref<string[]>();
// 预览和创建签报接口
const previewOrCreate = async (eoaData: EoaData, type: string) => {
  await eoaRef?.value?.validate();
  try {
    // 用于提交签报的参数
    const params = {
      attachmentList: eoaData.fileInfos,
      relationBusinessNo: applyPolicyNo,
      eoaType: data.value.innerCoinsuranceMark == '1' ? '030' : '043', // 系统内共保030， 内外共保034
      eoaSubject: formData.value.eoaSubject, // 申请事项
      eoaBody: formData.value.eoaBody, // 共保说明
      departmentCode: departmentCode, // 机构
      productCode: productCode, // 产品编码
      documentGroupId: eoaData.documentGroupId,
      approvalChain: eoaData.approvalChain, // 审批链中文名字
      templateKey: eoaData.templateChainKey, // 审批链key
      businessData: {
        projectType: data.value.innerCoinsuranceMark == '1' ? '0' : '2', // 内共保 0, 内外共保 2
        insuranceBeginDate: insuranceBeginDate, // 保险起期
        projectPeriodBeginDate: formData.value.date?.[0] || '', // 项目起期
        projectPeriodEndDate: formData.value.date?.[1] || '', // 项目止期
        totalActualPremium: totalActualPremium, // 总保费
        projectFee: formData.value.projectFee, // 预估保费
        coinsuranceDetailList: data.value.coinsuranceDetailList,
      },
    };
    if (type === 'preview') {
      const res = await previewEoa.fetchData(params);
      if (res && res.code === SUCCESS_CODE) {
        message.success(res?.msg);
        previewData.value = (res.data || []) as string[];
        previewOpen.value = true;
      } else {
        message.error(res?.msg as string);
      }
    } else {
      const res = await createEoa.fetchData(params);
      if (res && res.code === SUCCESS_CODE) {
        message.success(res?.msg);
        showApproval.value = false;
        handleReset();
        emits('getEoaData');
      } else {
        message.error(res?.msg as string);
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 关闭弹窗重置表单数据
const handleReset = async () => {
  eoaRef?.value.resetFields();
};
// 根据签报号查询签报信息
const searchEoa = async () => {
  const fetchUrl = gateWay + service.accept + '/accept/eoa/queryProjectByEoaNo';
  const params = {
    eoaNo: formData.value.relationBusinessNo,
    projectType: data.value.innerCoinsuranceMark == '1' ? '0' : '2',
    eoaType: data.value.innerCoinsuranceMark == '1' ? '030' : '043', // 系统内共保030， 内外共保043
    coinsuranceDetailList: data.value.coinsuranceDetailList,
    applyPolicyNo,
    insuranceBeginDate,
    totalActualPremium,
  };
  const res = await $postOnClient<{ projectFee: string; projectPeriodBeginDate: string; projectPeriodEndDate: string; eoaNo: string; eoaBody: string; eoaSubject: string }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    copyBtnDisabled.value = false;
    formData.value.projectFee = res.data.projectFee;
    formData.value.date = [res.data.projectPeriodBeginDate || '', res.data.projectPeriodEndDate || ''];
    formData.value.eoaNo = res.data.eoaNo;
    formData.value.relationBusinessNo = res.data.eoaNo;
    formData.value.eoaBody = res.data.eoaBody;
    formData.value.eoaSubject = res.data.eoaSubject;
  } else {
    message.error(res?.msg || '');
    copyBtnDisabled.value = true;
  }
};

// 复用签报
const copyApproval = async () => {
  const fetchUrl = gateWay + service.accept + '/accept/eoa/reuseEoaNoForApplyPolicyNo';
  const params = {
    applyPolicyNo,
    insuranceBeginDate,
    eoaNo: formData.value.eoaNo,
    totalActualPremium,
    projectType: data.value.innerCoinsuranceMark == '1' ? '0' : '2',
    eoaType: data.value.innerCoinsuranceMark == '1' ? '030' : '043', // 系统内共保030， 内外共保043
    coinsuranceDetailList: data.value.coinsuranceDetailList,
  };
  const res = await $postOnClient<{ projectFee: string; projectPeriodBeginDate: string; projectPeriodEndDate: string; eoaNo: string }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    message.success(res?.msg || '');
    showApproval.value = false;
    emits('getEoaData');
    handleReset();
  } else {
    message.error(res?.msg || '');
    copyBtnDisabled.value = true;
  }
};
// 校验预估总保费
const ValidProjectFee = (rule: Rule, value: string) => {
  const reg = /^\d{0,12}(\.\d{1,2})?$/;
  if (!reg.test(value)) {
    return Promise.reject('只能录入数字,支持12位整数位,2位小数位!');
  }
  return Promise.resolve();
};
onMounted(async () => {
  // 获取开关是否展示内外共保
  getSwitches();
  // 共保公司列表
  getCoinsuranceCompanyList();
});
</script>
