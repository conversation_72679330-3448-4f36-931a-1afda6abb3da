<template>
  <a-form ref="formRef" :model="list">
    <a-table :columns="columns" :data-source="list" :pagination="false" :expand-icon="() => null" :row-class-name="setRowClassName" :default-expand-all-rows="true" :children-column-name="'coinsureEmployeeList'">
      <template #bodyCell="{ column, record, index }">
        <!-- 是否主承 -->
        <template v-if="column.key === 'acceptInsuranceFlag'">
          <a-radio v-if="record.hasOwnProperty('acceptInsuranceFlag')" :checked="index === 0" disabled />
        </template>
        <!-- 业务机构 -->
        <template v-if="column.key === 'reinsureCompanyCode'">
          <a-form-item v-if="record.hasOwnProperty('reinsureCompanyCode')" :name="[index, 'reinsureCompanyCode']" :rules="[{ validator: () => companyCodeValidator(record) }]">
            <DepartmentSearch
              :unauthorized="true"
              :disabled="index === 0"
              :dept-code="record.reinsureCompanyCode"
              @change-dept-code="
                (value: string) => {
                  setDeptCode(record, value, index);
                }
              "
            />
          </a-form-item>
        </template>
        <!-- 财务机构编码 -->
        <template v-if="column.key === 'coinsureCompanyFinanceDeptCode'">
          <template v-if="index === 0">
            {{ record.coinsureCompanyFinanceDeptCode }}
          </template>
          <a-form-item v-if="record.hasOwnProperty('coinsureCompanyFinanceDeptCode') && index !== 0" :name="[index, 'coinsureCompanyFinanceDeptCode']" :rules="[{ validator: () => finDeptCodeValidator(record.coinsureCompanyFinanceDeptCode) }]">
            <a-input v-model:value="record.coinsureCompanyFinanceDeptCode" :style="{ width: pxToRem(100) }" :disabled="record?.fromFirst" placeholder="请输入" />
          </a-form-item>
        </template>
        <!-- 业务员 -->
        <template v-if="column.key === 'employeeCode'">
          <a-select
            v-if="record.hasOwnProperty('employeeCode')"
            v-model:value="record.employeeCode"
            :options="record.employeeOptions"
            option-label-prop="employeeName"
            :disabled="record?.fromFirst"
            :field-names="{ label: 'employeeName', value: 'employeeCode' }"
            :style="{ width: pxToRem(160) }"
            @change="
              () => {
                employeeChange(record);
              }
            "
          />
        </template>
        <!-- 出单渠道 -->
        <template v-if="column.key === 'coDevelopChannelClassCode'">
          <a-select v-if="record.hasOwnProperty('coDevelopChannelClassCode')" v-model:value="record.coDevelopChannelClassCode" :options="record.channelOptions" :field-names="{ label: 'channelSourceName', value: 'channelSourceCode' }" :style="{ width: pxToRem(96) }" />
        </template>
        <!-- 份额 -->
        <template v-if="column.key === 'reinsureScale'">
          <!-- 业务机构份额 -->
          <a-form-item v-if="record?.coinsureEmployeeList" :name="[index, 'reinsureScale']" :rules="[{ validator: () => scaleValidator(record) }]">
            <a-input v-model:value="record.reinsureScale" :disabled="record?.fromFirst && index !== 0" :style="{ width: pxToRem(76) }" placeholder="请输入" @change="handleReinsureScale" />
          </a-form-item>
          <!-- 业务员份额 -->
          <a-form-item v-else :name="[index, 'commisionScale']" :rules="[{ validator: () => commisionScaleValidator(record) }]">
            <a-input v-model:value="record.commisionScale" :style="{ width: pxToRem(76) }" :disabled="record?.fromFirst" placeholder="请输入" @change="handleCommisionScale" />
          </a-form-item>
        </template>
        <!-- 保费 -->
        <template v-if="column.key === 'premium'">
          {{ record.premium }}
        </template>
        <!-- 操作 -->
        <template v-if="column.key === 'action'">
          <div class="flex flex-col">
            <a-button v-if="!(index === 0 || record?.fromFirst) && !record.hasOwnProperty('employeeCode')" type="link" size="small" :style="{ textAlign: 'left' }" @click="deleteRow(record, index)"> 删除 </a-button>
            <!-- <div v-if="record.hasOwnProperty('reinsureCompanyCode') && !record?.fromFirst" class="w-[72px] cursor-pointer text-[#576B95] font-normal" @click="addEmployee(record.coinsureEmployeeList)">增加业务员</div> -->
          </div>
        </template>
      </template>
    </a-table>
  </a-form>
  <div class="mt-[12px] mb-[20px] text-[#576B95]">
    <div class="flex items-center space-x-[4px] cursor-pointer w-fit" @click="addRow()">
      <VueIcon :icon="IconTongyongXinzengFont" />
      <span>新增业务机构</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTongyongXinzengFont } from '@pafe/icons-icore-agr-an';
import { cloneDeep } from 'lodash-es';
// import type { Rule } from 'ant-design-vue/es/form/interface';
import type { CoinsureEmployee, CoinsuranceDetail, Channel, FinanceDept } from './coinsuranceInfo';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, generateUUID, add } from '@/utils/tools';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';

// props
const props = defineProps<{
  departmentCode?: string;
  developFlg?: string; // 是否为共展
}>();
const formRef = ref();
// v-model
const data = defineModel<CoinsuranceDetail[]>({ default: [] });

const list = computed(() => {
  return data.value?.filter((item) => item.coinsuranceType === '1');
});
// 机构所在的index，用于form表单校验
const deptIndexArr = computed(() => {
  return list.value.map((item, index: number) => {
    // 如果是首项，返回当前 index
    if (index === 0) {
      return index;
    }
    // 获取之前所有项的 coinsureEmployeeList 的长度之和
    const prevCoinsureListLength = list.value.slice(0, index).reduce((sum, currItem) => {
      return sum + (currItem.coinsureEmployeeList?.length || 0);
    }, 0);
    // 返回之前所有项的长度和当前 index
    return prevCoinsureListLength + index;
  });
});

// 业务员所在的index，用于form表单校验份额
const employeeIndexArr = computed(() => {
  const minIndex = 0;
  const maxIndex = list.value.length + list.value.reduce((sum, item) => sum + (item.coinsureEmployeeList?.length || 0), 0) - 1;

  // 找到其中缺失的整数，即是业务员对应的index
  return Array.from({ length: maxIndex - minIndex + 1 }, (_, i) => minIndex + i).filter((index) => !deptIndexArr.value.includes(index));
});

// 系统内共保表格表头 config
const columns = [
  { title: '是否主承保', key: 'acceptInsuranceFlag' },
  { title: '业务机构', key: 'reinsureCompanyCode', width: pxToRem(240) },
  { title: '财务机构编码', key: 'coinsureCompanyFinanceDeptCode' },
  { title: '业务员', key: 'employeeCode', width: pxToRem(140) },
  { title: '出单渠道', key: 'coDevelopChannelClassCode' },
  { title: '份额(%)', key: 'reinsureScale', width: pxToRem(80) },
  { title: '保费(元)', key: 'premium' },
  { title: '操作', key: 'action' },
];

// 业务员行表格背景色
const setRowClassName = (record: { employeeCode: string }) => {
  return Object.prototype.hasOwnProperty.call(record, 'employeeCode') ? 'table-striped' : '';
};

// 初始化系统内共保数据
const initData = () => {
  setTimeout(() => {
    list.value.forEach((item, index) => {
      setDeptCode(item, item.reinsureCompanyCode as string, index);
    });
  }, 1000);
};

// 表单校验
const validate = async () => {
  if (formRef?.value) {
    try {
      await formRef?.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
defineExpose({
  validate,
  initData,
});
// nuxt.config runtime配置
const { gateWay, service } = useRuntimeConfig().public;
const acceptUrlPrefix = `${gateWay}${service.accept}`;

// 获取业务员列表 index为当前父级机构
const getSasEmployeeList = async (record: CoinsuranceDetail, index: number) => {
  const params = { departmentCode: record.reinsureCompanyCode || props.departmentCode };
  try {
    const res = await $getOnClient<CoinsureEmployee[]>(`${acceptUrlPrefix}/saleInfo/getSasEmployeeList`, params);
    if (res?.code === SUCCESS_CODE && res.data) {
      // 第一行默认是来源于第一屏业务员
      if (record && index !== 0) {
        initEmployeeData(record, res.data);
      } else {
        initFromFirstData(record, res.data);
      }
    } else {
      // 返回数据不存在 则重置record数据
      const { coinsureEmployeeList = [] } = record;
      record.coinsureEmployeeList = coinsureEmployeeList.map((item) => {
        return {
          ...item,
          employeeOptions: [],
          channelOptions: [],
          employeeCode: '',
          employeeName: '',
          coDevelopChannelClassCode: '',
          uuid: generateUUID(),
        };
      });
    }
  } catch (error) {
    console.log(error, 'error');
  }
};

// 初始化业务员数据
const initEmployeeData = (record: CoinsuranceDetail, employeeOptions: CoinsureEmployee[]) => {
  if (record?.coinsureEmployeeList?.length) {
    record.coinsureEmployeeList.forEach((item, index) => {
      // 带出第一个业务员的数据
      if (index === 0) {
        item.employeeCode = item.employeeCode || employeeOptions?.[0]?.employeeCode;
        item.employeeName = item.employeeName || employeeOptions?.[0]?.employeeName;
        item.uuid = generateUUID();
        item.coDevelopChannelClassCode = item.coDevelopChannelClassCode || employeeOptions?.[0]?.coDevelopChannelClassCode;
      } else {
        item.employeeCode = item.employeeCode || '';
        item.employeeName = item.employeeName || '';
        item.uuid = generateUUID();
        item.coDevelopChannelClassCode = item.coDevelopChannelClassCode || '';
      }
      item.employeeOptions = cloneDeep(employeeOptions);
      getChannelSourceList(item);
    });
  }
};

// 初始化第一屏数据
const initFromFirstData = (record: CoinsuranceDetail, employeeOptions: CoinsureEmployee[]) => {
  // 为共展时初始化第一屏的数据
  if (record?.coinsureEmployeeList?.length) {
    record.coinsureEmployeeList = record.coinsureEmployeeList.map((item) => {
      return {
        ...item,
        employeeOptions: cloneDeep(employeeOptions),
        coDevelopChannelClassCode: '',
        fromFirst: true, // 为共展时 fromFirst来源第一屏为true
        uuid: generateUUID(),
      };
    });
    record.coinsureEmployeeList.forEach((item) => {
      getChannelSourceList(item);
    });
  }
  record.fromFirst = true;
};

// 获取业务渠道
const getChannelSourceList = async (employee: CoinsureEmployee) => {
  const params = { employeeCode: employee.employeeCode };
  const res = await $getOnClient<Channel[]>(`${acceptUrlPrefix}/coinsurance/getChannelSourceListForCoinsurance`, params);
  if (res?.code === SUCCESS_CODE && res.data && res.data.length) {
    employee.channelOptions = res.data;
    // 默认取第一个渠道
    employee.coDevelopChannelClassCode = res.data[0].channelSourceCode;
  }
};

// 获取财务机构编码
const getFinanceDepartmentCode = async (record: CoinsuranceDetail) => {
  const params = {
    departmentCode: record.reinsureCompanyCode || props.departmentCode,
  };
  const res = await $getOnClient<FinanceDept>(`${acceptUrlPrefix}/coinsurance/getFinanceDepartmentCode`, params);
  if (res?.code === SUCCESS_CODE) {
    if (record && res.data && res.data.financeDeptCode) {
      record.coinsureCompanyFinanceDeptCode = res.data.financeDeptCode;
    }
  }
};

// 机构份额校验
const scaleValidator = (record: CoinsuranceDetail) => {
  const value = record.reinsureScale;
  if (!value && value !== '0') {
    return Promise.reject('不能为空');
  }
  const regex = /^\d+(\.\d{1,4})?$/;

  if (!regex.test(value)) {
    return Promise.reject('请输入最多4位小数的数值');
  }
  let sum = '0';

  list.value.forEach((item: CoinsuranceDetail) => {
    if (item.reinsureScale) {
      sum = add(item.reinsureScale, sum);
    }
  });
  if (sum === '0') {
    return Promise.reject('不能为空');
  }
  if (Number(sum) !== 100) {
    return Promise.reject('份额比例的和不等100');
  }

  return Promise.resolve();
};

// 业务员份额校验
const commisionScaleValidator = (record: CoinsuranceDetail) => {
  if (!record.commisionScale && record.commisionScale !== '0') {
    return Promise.reject('不能为空');
  }
  const regex = /^\d+(\.\d{1,4})?$/;

  if (!regex.test(record.commisionScale)) {
    return Promise.reject('请输入最多4位小数的数值');
  }
  let sum = '0';
  // 通过uuid找到校验的业务员所属机构
  const foundItem = list.value.find((item) => Array.isArray(item.coinsureEmployeeList) && item.coinsureEmployeeList.some((employee) => employee.uuid === record.uuid));

  if (foundItem?.coinsureEmployeeList && foundItem?.coinsureEmployeeList?.length > 0) {
    foundItem.coinsureEmployeeList.forEach((item: CoinsureEmployee) => {
      if (item?.commisionScale) {
        sum = add(item.commisionScale, sum);
      }
    });
  }
  if (Number(sum) !== 100) {
    return Promise.reject('同一机构份额比例的和不等100');
  }
  return Promise.resolve();
};

// 业务机构校验
const companyCodeValidator = (record: CoinsuranceDetail) => {
  // 机构不能为空
  if (!record?.reinsureCompanyCode) {
    return Promise.reject('机构不能为空');
  }
  // 不能录入相同机构 reinsureCompanyCode
  let validator = false;
  const codeMap = new Map();

  // 遍历list数组，查看是否有相同的机构编码
  list.value.forEach((item: CoinsuranceDetail) => {
    if (item?.reinsureCompanyCode) {
      if (codeMap.has(item.reinsureCompanyCode)) {
        codeMap.set(item.reinsureCompanyCode, codeMap.get(item.reinsureCompanyCode) + 1);
      } else {
        codeMap.set(item.reinsureCompanyCode, 1);
      }
    }
  });

  // 检查是否有重复的机构编码
  codeMap.forEach((count) => {
    if (count > 1) {
      validator = true;
    }
  });

  if (validator) {
    return Promise.reject('不能重复选择机构');
  }

  return Promise.resolve();
};

// 财务机构校验
const finDeptCodeValidator = async (value: string) => {
  const res = await $getOnClient(`${acceptUrlPrefix}/coinsurance/checkFinanceDepartmentCode`, {
    financeDepartmentCode: value,
  });
  if (res?.code === SUCCESS_CODE && res.data) {
    if (res.data === 'N') {
      return Promise.reject('财务机构不存在');
    }
  }
  return Promise.resolve();
};

// 系统内共保，出单机构机构，财务机构编码，业务员，出单渠道
const setDeptCode = (record: CoinsuranceDetail, value: string, index: number) => {
  record.reinsureCompanyCode = value;
  getSasEmployeeList(record, index);
  getFinanceDepartmentCode(record);
  deptIndexArr.value.forEach((item) => {
    formRef?.value?.validate([[item, 'reinsureCompanyCode']]);
  });
};

// 新增业务机构
const addRow = () => {
  // 系统外共保信息对象
  const detail = {
    uuid: generateUUID(),
    reinsureCompanyCode: '',
    acceptInsuranceFlag: '0',
    coinsureCompanyFinanceDeptCode: '',
    coinsureEmployeeList: [
      {
        channelOptions: [],
        coDevelopChannelClassCode: '',
        employeeCode: '',
        employeeName: '',
        employeeOptions: [],
        mainEmployeeFlag: '0',
        commisionScale: '100',
        uuid: generateUUID(),
      },
    ],
    channelSourceCode: '',
    reinsureScale: '',
    premium: '0',
    coinsuranceType: '1',
  };
  data.value.push(detail);
};

// 删除业务机构或业务员
const deleteRow = (record: { uuid?: string | undefined }) => {
  // 删除行
  if (data.value && data.value.length) {
    data.value.map((detail: CoinsuranceDetail, detailIndex: number) => {
      if (detail.uuid === record.uuid) {
        data.value.splice(detailIndex, 1);
      }
      detail.coinsureEmployeeList?.map((employee: CoinsureEmployee, employeeIndex: number) => {
        if (employee.uuid === record.uuid) {
          detail.coinsureEmployeeList?.splice(employeeIndex, 1);
        }
      });
    });
  }
};

// 添加业务员
// const addEmployee = (coinsureEmployeeList: CoinsureEmployee[]) => {
//   coinsureEmployeeList.push({
//     uuid: generateUUID(),
//     employeeCode: '',
//     employeeName: '',
//     reinsureScale: '',
//     mainEmployeeFlag: '0',
//     coDevelopChannelClassCode: '',
//     employeeOptions: coinsureEmployeeList[0]?.employeeOptions,
//     channelOptions: [],
//   });
// };

// 切换业务员
const employeeChange = (record: CoinsureEmployee) => {
  // 更新业务员名称
  record.employeeName = record.employeeOptions?.find((item) => item.employeeCode === record.employeeCode)?.employeeName;
  getChannelSourceList(record);
};

// 机构份额修改触发校验
const handleReinsureScale = () => {
  deptIndexArr.value.forEach((item) => {
    formRef?.value?.validate([[item, 'reinsureScale']]);
  });
};
// 业务员份额修改触发校验
const handleCommisionScale = () => {
  employeeIndexArr.value.forEach((item) => {
    formRef?.value?.validate([[item, 'commisionScale']]);
  });
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 0px;
}
</style>
