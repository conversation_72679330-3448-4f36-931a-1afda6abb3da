<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[76px] w-ful bg-image-url">
          <span class="text-[24px] text-[#00190c] leading-[76px] ml-[16px]">投保单信息</span>
        </div>
        <a-spin :spinning="loading">
          <div class="space-y-12px">
            <InfoGroupBox id="policy-info" title="投保单信息">
              <a-descriptions :column="4" :colon="false">
                <a-descriptions-item class="font-number" label="投保单号">{{ contractModel?.baseInfo?.applyPolicyNo || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="验标任务号">{{ contractModel?.extendInfo?.insrCheckNo || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="产品名称">{{ contractModel?.baseInfo?.productName || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.baseInfo?.farmerlistNo">
                  <a-button type="primary" :loading="btnLoading" @click="singleFarmerListExport">导出清单</a-button>
                </a-descriptions-item>
                <a-descriptions-item class="font-number" label="清单编号">{{ contractModel?.baseInfo?.farmerlistNo || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="导入户数">{{ contractModel?.baseInfo?.farmerlistFarmerNum || '-' }} 户</a-descriptions-item>
                <a-descriptions-item class="font-number" label="承保数量">{{ contractModel?.baseInfo?.farmerlistRiskNum || '-' }} {{ contractModel.baseInfo?.unitName }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="验真率">{{ contractModel?.baseInfo?.verifyRate || '-' }} %</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.insurantInfoList?.[0]?.personnelType === '1'" class="font-number" label="爱农宝核身率">{{ voucherSummaryInfo?.customerVerifyRate === 0 ? '0' : voucherSummaryInfo?.customerVerifyRate || '-' }} %</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.baseInfo?.riskAsmtNo" class="font-number" label="风险评估编号">
                  <span class="text-[#576B95] cursor-pointer font-number" @click="goRiskAssessDetail">{{ contractModel?.baseInfo?.riskAsmtNo }}</span>
                </a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox id="basic-info" title="基础信息">
              <a-descriptions :colon="false">
                <a-descriptions-item label="农保ID">
                  <span v-for="(item, index) in contractModel?.baseInfo?.assisterInfoList" :key="item.assisterId" class="font-number">
                    {{ item.assisterId }}
                    <span v-if="index !== contractModel.baseInfo.assisterInfoList.length - 1">、</span>
                  </span>
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions :colon="false">
                <a-descriptions-item class="font-number" label="保险起期">{{ contractModel?.baseInfo?.insuranceBeginDate || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="保险期限">{{ contractModel?.baseInfo?.insuranceBeginEndDateType || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="保险止期">{{ contractModel?.baseInfo?.insuranceEndDate }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="起止时间">{{ contractModel?.baseInfo?.timeRange === '0' ? '0-24时' : '12-12时' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="年限系数">{{ contractModel?.baseInfo?.shortTimeCoefficient || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="争议处理方式">{{ contractModel?.baseInfo?.disputedSettleModeChName || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.baseInfo?.disputedSettleMode === '2'" class="font-number" label="仲裁机构">{{ contractModel?.baseInfo?.arbitralDepartment || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="投保方式">{{ contractModel?.baseInfo?.applyPolicyTypeChName || '-' }}</a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox id="customer-info" title="客户信息">
              <customerInfo :contract-model="contractModel" />
            </InfoGroupBox>
            <InfoGroupBox id="project-info" title="项目信息">
              <productInfo :contract-model="contractModel" />
            </InfoGroupBox>
            <InfoGroupBox id="target-info" title="标的信息">
              <plantRisk :contract-model="contractModel" />
            </InfoGroupBox>
            <InfoGroupBox id="insurance-plan" title="保险方案">
              <template v-for="(riskGroupInfo, riskIndex) in contractModel.riskGroupInfoList" :key="riskIndex">
                <div class="form-title">
                  <span>{{ `方案${contractModel.riskGroupInfoList?.length > 1 ? riskIndex + 1 : ''}` }}</span>
                </div>
                <div class="mb-[16px]">
                  <a-table :columns="insurancePlanColumns" :data-source="riskGroupInfo?.planInfoList" :pagination="false">
                    <template #bodyCell="{ column, index, text, record }">
                      <template v-if="column.dataIndex === 'number'">
                        {{ index + 1 }}
                      </template>
                      <template v-if="column.dataIndex === 'combinedProductName'">
                        {{ riskGroupInfo?.combinedProductName }}
                      </template>
                      <template v-if="column.dataIndex === 'isMain'">
                        {{ text === '1' ? '主险' : '附加险' }}
                      </template>
                      <template v-if="column.dataIndex === 'operation'">
                        <a-space>
                          <a-button type="primary" size="small" @click="handleDuty(record as PlanInfoType)">责任</a-button>
                          <a-button type="primary" size="small" :loading="loadings[riskIndex][index]" @click="downTermOrRate('term', record.planCode, index, riskIndex)">条款</a-button>
                          <a-button type="primary" size="small" :loading="loadingRate[riskIndex][index]" @click="downTermOrRate('rate', record.planCode, index, riskIndex)">费率</a-button>
                        </a-space>
                      </template>
                    </template>
                  </a-table>
                </div>
              </template>
              <div class="py-[12px]">保费来源</div>
              <a-table class="mb-[12px]" :columns="sourceColumns" :data-source="sourceList" :pagination="false" />
              <a-descriptions :colon="false">
                <a-descriptions-item label="农户自缴减免系数">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.reductionCoefficient || '-' }}</a-descriptions-item>
                <a-descriptions-item label="农户保费是否代缴">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y' ? '是' : '否' }}</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y'" label="代缴保费资金来源">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo.fundingSourceChName || '-' }}</a-descriptions-item>
              </a-descriptions>
              <div class="mt-16px leading-[22px] text-[rgba(0, 0, 0, 0.9)]">
                <div>总计：保单保险金额={{ contractModel?.sumRiskGroupAmount?.totalInsuredAmount || 0 }} ｜ 减免前保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalStandardPremium || 0 }}｜ 减免后保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalAgreePremium || 0 }} ｜ 保单复核保费={{ contractModel?.sumRiskGroupAmount?.totalActualPremium || 0 }}</div>
                <div v-if="contractModel.riskGroupInfoList.length === 1">保险数量：{{ contractModel.baseInfo?.insuredNumber || 0 }}{{ contractModel.riskGroupInfoList?.[0]?.riskAgrInfo?.insuredUnitChName }} ｜ 参保户次：{{ contractModel.baseInfo?.farmersCount || 0 }}户</div>
                <div v-if="contractModel.riskGroupInfoList.length > 1">
                  <template v-for="(item, index) in contractModel.riskGroupInfoList" :key="index">
                    <div>标的名称：{{ item.combinedProductName }} ｜ 保险数量：{{ item.riskAgrInfo.insuredNumber || 0 }}{{ item.riskAgrInfo.insuredUnitChName }} ｜ 参保户次：{{ item.riskAgrInfo.farmersCount || 0 }}户</div>
                  </template>
                </div>
              </div>
            </InfoGroupBox>
            <InfoGroupBox id="fee-plan" title="收费计划">
              <a-table :columns="feePlanColumns" :data-source="contractModel.payInfoList" :pagination="false">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'paymentPeriod'">
                    {{ record.paymentBeginDate + ' 至 ' + record.paymentEndDate }}
                  </template>
                  <template v-if="column.dataIndex === 'certificateNo'">
                    <span class="break-all">{{ record.certificateNo }}</span>
                  </template>
                  <template v-if="column.dataIndex === 'bankAccountNo'">
                    <span class="break-all">{{ record.bankAccountNo }}</span>
                  </template>
                </template>
              </a-table>
            </InfoGroupBox>
            <InfoGroupBox id="disclaimers-info" title="免赔信息">
              <a-table class="mb-[12px]" :columns="noClaimColumns" :data-source="contractModel.noclaimInfoList" :pagination="false" />
              <a-descriptions :colon="false">
                <a-descriptions-item label="免赔描述">{{ contractModel?.baseInfo?.deductionDesc || '-' }}</a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox id="special-appoint" title="特别约定">
              <a-table v-if="contractModel.specialPromiseList.length > 0" :columns="specialColumns" :data-source="contractModel.specialPromiseList" :pagination="false" />
              <a-table v-else :columns="specialColumns" :data-source="specialDataDefault" :pagination="false">
                <template #bodyCell="{ column }">
                  <template v-if="column.dataIndex === 'promiseTypeChName'">通用特约</template>
                </template>
              </a-table>
            </InfoGroupBox>
            <InfoGroupBox id="fee-info" title="费用信息">
              <a-descriptions :colon="false">
                <a-descriptions-item label="财务标识">{{ contractModel?.costInfo?.isPolicyBeforePayfeeChName || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="costSignData?.signStatus && costSignData?.signStatus !== '0'" label="签报状态">{{ costSignData?.signStatusDesc }}</a-descriptions-item>
                <a-descriptions-item v-if="costSignData?.signStatus && costSignData?.signStatus !== '0'" label="签报号">
                  <span class="text-[#2d8cf0] cursor-pointer" @click="goEoa(costSignData?.eoaDetailUrl)"> {{ costSignData?.eoaNo }}</span>
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions :colon="false">
                <a-descriptions-item class="font-number" label="农险补贴">{{ contractModel?.costInfo?.performanceValue1Default || '-' }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="协办费">{{ contractModel?.costInfo?.assisterCharge || '-' }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="手续费/经纪费">{{ contractModel?.costInfo?.commissionBrokerChargeProportion || '-' }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="工作经费">{{ contractModel?.costInfo?.managementFees || '-' }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="防灾防损费">{{ contractModel?.costInfo?.calamitySecurityRate || '-' }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="共保出单费">{{ contractModel?.costInfo?.coinsuranceInsureFeeRatio || '-' }}%</a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox id="channel-info" title="渠道信息">
              <a-descriptions :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4" :colon="false">
                <a-descriptions-item class="font-number" label="出单机构">{{ contractModel?.saleInfo?.departmentCodeAndName || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="是否共展">{{ contractModel?.saleInfo?.developFlgChName || '-' }}</a-descriptions-item>
              </a-descriptions>
              <a-descriptions v-for="(item, index) in contractModel?.saleInfo?.employeeInfoList" :key="index" :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4" :colon="false">
                <a-descriptions-item class="font-number" label="业务员信息">{{ item.employeeName || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="业务员执业证号">{{ item.employeeProfCertifNo || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="业务员联系方式">{{ item.employeeMobileTelephone || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="contractModel?.saleInfo?.developFlg === 'Y'" class="font-number" label="占比">
                  {{ item.commisionScale || '-' }} %
                  <span v-if="item.mainEmployeeFlag === '1'">（主业务员）</span>
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4" :colon="false">
                <a-descriptions-item class="font-number" label="渠道来源">{{ contractModel?.saleInfo?.channelSourceName || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="渠道来源细分">{{ contractModel?.saleInfo?.channelSourceDetailName || '-' }}</a-descriptions-item>
                <!-- 代理人 -->
                <template v-if="contractModel?.saleInfo?.businessSourceCode === '2'">
                  <a-descriptions-item class="font-number" label="代理人">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agentName || '-' }}</a-descriptions-item>
                  <a-descriptions-item class="font-number" label="代理协议">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agentAgreementNoChName || '-' }}</a-descriptions-item>
                  <a-descriptions-item class="font-number" label="中介执业证号">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
                  <a-descriptions-item class="font-number" label="中介人员名称">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
                </template>
                <!-- 经纪人 -->
                <template v-if="contractModel?.saleInfo?.businessSourceCode === '3'">
                  <a-descriptions-item class="font-number" label="经纪人">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.brokerName || '-' }}</a-descriptions-item>
                  <a-descriptions-item class="font-number" label="中介执业证号">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
                  <a-descriptions-item class="font-number" label="中介人员名称">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
                </template>
                <!-- 主介绍人 -->
                <a-descriptions-item v-if="showIntroducer" class="font-number" label="主介绍人代码">{{ contractModel?.saleInfo?.primaryIntroducerInfo?.primaryIntroducerCode || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="showIntroducer" class="font-number" label="主介绍人名称">{{ contractModel?.saleInfo?.primaryIntroducerInfo?.primaryIntroducerName || '-' }}</a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox v-if="contractModel.baseInfo.coinsuranceMark === '1'" id="co-insurance" title="共保信息">
              <a-descriptions :colon="false">
                <a-descriptions-item class="font-number" label="共保类型">{{ contractModel?.coinsuranceInfo?.innerCoinsuranceMarkChName || '-' }}</a-descriptions-item>
              </a-descriptions>
              <a-descriptions :colon="false">
                <a-descriptions-item class="font-number" label="共保协议代码">{{ contractModel?.coinsuranceInfo?.innerCoinsuranceAgreement || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="总保额">{{ contractModel?.coinsuranceInfo?.totalInsuredAmount || '-' }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="总保费">{{ contractModel?.coinsuranceInfo?.totalPremium || '-' }}</a-descriptions-item>
              </a-descriptions>
              <a-table :columns="coinsuranceColumns" :data-source="contractModel.coinsuranceInfo?.coinsuranceDetailList" :pagination="false">
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex === 'acceptInsuranceFlag'">{{ text === '1' ? '是' : '否' }}</template>
                </template>
              </a-table>
            </InfoGroupBox>
          </div>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" ghost @click="handleGoLink">附件管理</a-button>
      <a-button type="primary" ghost @click="goInspectionInfo">验标信息</a-button>
      <a-button
        type="primary"
        ghost
        @click="
          processOpen = true;
          queryUnderwriteAssistant();
        "
        >核保信息</a-button
      >
    </footer>
    <!-- 点击核保信息按钮 -->
    <InsureTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
    <!-- 责任弹窗 -->
    <a-modal v-model:open="visible" title="责任" centered :width="pxToRem(600)">
      <div v-if="dutyInfoList?.length > 0">
        <div v-for="(item, index) in dutyInfoList" :key="index">{{ item.dutyCode }}{{ item.dutyName }}</div>
      </div>
      <a-empty v-else :image="simpleImage" />
      <template #footer>
        <a-button type="primary" @click="visible = false">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import type { contractModelType, dutyInfoListType, PlanInfoType } from './applyPolicyDetails.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import InsureTrack from '@/pages/review/insureProcess/components/InsureTrack.vue';
import { Empty } from 'ant-design-vue';
import customerInfo from '~/pages/detail/customerInfo/customerInfo.vue';
import plantRisk from '@/pages/detail/plantRisk/plantRisk.vue';
import productInfo from '@/pages/detail/productInfo/productInfo.vue';
// 点击核保信息按钮，打开弹窗
const processOpen = ref<boolean>(false);
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const getContainer = () => scrollWrapper.value || window;
const scrollWrapper = ref();
const route = useRoute();
// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#policy-info', title: '保单信息' },
  { key: '2', href: '#basic-info', title: '基础信息' },
  { key: '3', href: '#customer-info', title: '客户信息' },
  { key: '4', href: '#project-info', title: '项目信息' },
  { key: '5', href: '#target-info', title: '标的信息' },
  { key: '6', href: '#insurance-plan', title: '保险方案' },
  { key: '7', href: '#fee-plan', title: '收费计划' },
  { key: '8', href: '#disclaimers-info', title: '免赔信息' },
  { key: '9', href: '#special-appoint', title: '特别约定' },
  { key: '10', href: '#fee-info', title: '费用信息' },
  { key: '11', href: '#channel-info', title: '渠道信息' },
]);

// 免赔信息表头
const noClaimColumns: TableColumnsType = [
  { title: '免赔项目', dataIndex: 'noclaimItemZh', key: 'noclaimItemZh', width: pxToRem(400) },
  { title: '免赔类型', dataIndex: 'noclaimTypeZh', key: 'noclaimTypeZh', width: pxToRem(136) },
  { title: '免赔率(%)', dataIndex: 'noclaimRate', key: 'noclaimRate' },
  { title: '免赔额', dataIndex: 'noclaimAmount', key: 'noclaimAmount' },
];
// 收费计划表头
const feePlanColumns: TableColumnsType = [
  { title: '期次', dataIndex: 'termNo', key: 'termNo', width: '5%' },
  { title: '补贴类型', dataIndex: 'payerTypeName', key: 'payerTypeName', width: pxToRem(80) },
  { title: '付款人名称', dataIndex: 'paymentPersonName', key: 'paymentPersonName', width: pxToRem(100) },
  { title: '收付途径', dataIndex: 'paymentPathName', key: 'paymentPathName', width: pxToRem(80) },
  { title: '应收保费', dataIndex: 'actualPremium', key: 'actualPremium', width: pxToRem(80) },
  { title: '缴费起止期', dataIndex: 'paymentPeriod', key: 'paymentPeriod', width: pxToRem(200) },
  { title: '账户类型', dataIndex: 'bankAttributeName', key: 'bankAttributeName', width: pxToRem(80) },
  { title: '身份证', dataIndex: 'certificateNo', key: 'certificateNo', width: pxToRem(60) },
  { title: '银行总行', dataIndex: 'bankHeadquartersName', key: 'bankHeadquartersName', width: pxToRem(60) },
  { title: '银行分行', dataIndex: 'bankName', key: 'bankName', width: pxToRem(100) },
  { title: '开户行明细', dataIndex: 'bankDetail', key: 'bankDetail', width: pxToRem(125) },
  { title: '银行账号', dataIndex: 'bankAccountNo', key: 'bankAccountNo', width: pxToRem(80) },
  { title: '预计收回日期', dataIndex: 'receivableDate', key: 'receivableDate', width: pxToRem(100) },
  { title: '备注', dataIndex: 'otherPayerTypeDesc', key: 'otherPayerTypeDesc', width: pxToRem(80) },
];
// 保险方案表头
const insurancePlanColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'number', key: 'number', width: pxToRem(56) },
  { title: '标的名称', dataIndex: 'combinedProductName', key: 'combinedProductName', width: pxToRem(86) },
  { title: '主险/附加险标识', dataIndex: 'isMain', key: 'isMain', width: pxToRem(145) },
  { title: '险种码', dataIndex: 'planCode', key: 'planCode' },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(140) },
  { title: '每次赔偿限额', dataIndex: 'eachCompensationMaxAmount', key: 'eachCompensationMaxAmount', width: pxToRem(140) },
  { title: '单位保险金额', dataIndex: 'unitInsuredAmount', key: 'unitInsuredAmount', width: pxToRem(140) },
  { title: '保险金额', dataIndex: 'totalInsuredAmount', key: 'totalInsuredAmount', width: pxToRem(140) },
  { title: '费率(%)', dataIndex: 'expectPremiumRate', key: 'expectPremiumRate', width: pxToRem(140) },
  { title: '单位保费', dataIndex: 'unitPrimium', key: 'unitPrimium', width: pxToRem(140) },
  { title: '基准保费金额', dataIndex: 'totalStandardPremium', key: 'totalStandardPremium', width: pxToRem(140) },
  { title: '减免后保费金额', dataIndex: 'totalAgreePremium', key: 'totalAgreePremium', width: pxToRem(140) },
  { title: '复核保费', dataIndex: 'totalActualPremium', key: 'totalActualPremium', width: pxToRem(120) },
  { title: '文件', dataIndex: 'operation', fixed: 'right' },
];
// 保费来源columns
const sourceColumns: TableColumnsType = [
  { title: '险种代码', dataIndex: 'planCode', key: 'planCode', width: pxToRem(120) },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(120) },
  { title: '中央补贴(%)', dataIndex: 'centralFinance', key: 'centralFinance' },
  { title: '省级补贴(%)', dataIndex: 'provincialFinance', key: 'provincialFinance' },
  { title: '地方补贴(%)', dataIndex: 'cityFinance', key: 'cityFinance' },
  { title: '县级补贴(%)', dataIndex: 'countyFinance', key: 'countyFinance' },
  { title: '农户自缴(%)', dataIndex: 'farmersFinance', key: 'farmersFinance' },
  { title: '其他补贴(%)', dataIndex: 'otherFinance', key: 'otherFinance' },
];
// 特约表头
const specialColumns: TableColumnsType = [
  { title: '特约类型', dataIndex: 'promiseTypeChName', width: pxToRem(100) },
  { title: '特约内容', dataIndex: 'promiseDesc' },
];
// 特约无数据时默认数据
const specialDataDefault = [
  {
    promiseCode: '', // 编码
    promiseDesc: '无其他特别约定', // 特约内容
    promiseType: '',
  },
];
// 共保表头配置
const coinsuranceColumns: TableColumnsType = [
  { title: '共保公司代码', dataIndex: 'reinsureCompanyCode' },
  { title: '共保公司名称', dataIndex: 'reinsureCompanyName' },
  { title: '保险金额', dataIndex: 'insuredAmount' },
  { title: '保费金额', dataIndex: 'premium' },
  { title: '共保比例(%)', dataIndex: 'reinsureScale' },
  { title: '是否主承保', dataIndex: 'acceptInsuranceFlag' },
];

// 保费来源表格datasource
const sourceList = computed(() => {
  if (contractModel.value.riskGroupInfoList?.length === 1) {
    // 单标的
    return contractModel.value.riskGroupInfoList[0].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    contractModel.value.riskGroupInfoList.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return result;
  }
});

const initContractModel = (data: contractModelType) => {
  return {
    baseInfo: data.baseInfo || {}, // 基础信息
    extendInfo: data.extendInfo || {},
    organizerInfo: data.organizerInfo || {}, // 组织者
    applicantInfoList: data.applicantInfoList || [], // 投保人
    insurantInfoList: data.insurantInfoList || [], // 被保人
    beneficaryInfoList: data.beneficaryInfoList || [], // 收益人
    riskAddressInfoList: data.riskAddressInfoList || [], // 标的
    riskGroupInfoList: data.riskGroupInfoList || [], // 标的
    sumRiskGroupAmount: data.sumRiskGroupAmount || {}, // 保费计划合计
    coinsuranceInfo: data.coinsuranceInfo || {}, // 共保
    noclaimInfoList: data.noclaimInfoList || [], // 免赔
    payInfoList: data.payInfoList || [], // 收费计划
    costInfo: data.costInfo || {}, // 费用信息
    specialPromiseList: data.specialPromiseList || [], // 特约
    saleInfo: data.saleInfo || {},
    payerTypeSumOfPayInfo: data.payerTypeSumOfPayInfo || {}, // 收费按类别汇总用于校验
    extendGroupInfo: data.extendGroupInfo || {},
  };
};
const btnLoading = ref<boolean>(false);
// 导出清单数据
const singleFarmerListExport = async () => {
  try {
    btnLoading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/file/singleFarmerListExport`;
    await $getOnClient(
      fetchUrl,
      { position: '3', farmerListNo: contractModel.value?.baseInfo?.farmerlistNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('导出成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
const data: contractModelType = {
  baseInfo: {
    assisterInfoList: [],
    coinsuranceMark: '',
    applyPolicyType: '',
    farmerlistNo: '',
    farmersCount: '',
    disputedSettleModeChName: '',
    renewalTypeName: '',
    lastPolicyNo: '',
    insuranceBeginDate: '',
    shortTimeCoefficient: '',
    insuranceEndDate: '',
    timeRange: '',
    applyPolicyNo: '',
    govSubsidyTypeChName: '',
    inputBy: '',
    policyNo: '',
    productName: '',
    insuranceBeginEndDateType: '',
    deductionDesc: '',
    farmerlistFarmerNum: '',
    farmerlistRiskNum: '',
    verifyRate: '',
    unitName: '',
    disputedSettleMode: '',
    applyPolicyTypeChName: '',
    insuredNumber: '',
    arbitralDepartment: '',
  }, // 基础信息
  extendInfo: {
    tenderBusiness: '', // 项目来源
    tenderBusinessName: '', // 项目名称
    lossRate: '', // 风险程度
    customerType: '', // 客户类型
    customerAbbrName: '', // 客户简称
  },
  applicantInfoList: [], // 投保人
  insurantInfoList: [], // 被保人
  beneficaryInfoList: [], // 收益人
  riskAddressInfoList: [], // 标的
  riskGroupInfoList: [], // 标的
  sumRiskGroupAmount: {}, // 保费计划合计
  coinsuranceInfo: {
    innerCoinsuranceMark: '',
    innerCoinsuranceAgreement: '',
    totalPremium: '',
    totalInsuredAmount: '',
    coinsuranceDetailList: [],
    innerCoinsuranceMarkChName: '',
  }, // 共保
  noclaimInfoList: [], // 免赔
  payInfoList: [], // 收费计划
  costInfo: {
    calamitySecurityRate: '', // 防灾防损费
    isPolicyBeforePayfee: '', // 财务标识
    assisterCharge: '', // 协办费
    commissionBrokerChargeProportion: '', // 手续费/经纪费
    managementFees: '', // 工作经费
    coinsuranceInsureFeeRatio: '', // 共保出单
    performanceValue1Default: '', // 农险补贴
    totalSumFeeLimit: '', //  string; // 总费用之和上限值
    assisterChargeLimit: '', // 协办费上限值
    commissionBrokerChargeProportionLimit: '', // 手续费/经纪费上限
    managementFeesLimit: '', // 工作经费上限-  // 管理费(业务员的工作经费比例
    calamitySecurityRateLimit: '', // 防灾防损费上限值
    performanceValue1DefaultLimit: '', // 农险补贴上限值
    coinsuranceInsureFeeRatioLimit: '', // 共保出单费上限值
  }, // 费用信息
  specialPromiseList: [], // 特约
  saleInfo: {
    departmentName: '',
    channelSourceDetailName: '',
    channelSourceName: '',
    saleagentIntroducerCode: '',
    employeeInfoList: [],
    developFlg: '',
    businessSourceCode: '',
    channelSourceCode: '',
    channelSourceDetailCode: '',
    primaryIntroducerInfo: {},
    brokerInfoList: [],
    agentInfoList: [],
    departmentCodeAndName: '',
    developFlgChName: '',
  },
  payerTypeSumOfPayInfo: {}, // 收费按类别汇总用于校验
  reinsuranceInfo: {},
  organizerInfo: {},
  extendGroupInfo: {},
};
const contractModel = ref(initContractModel(data));
const voucherSummaryInfo = ref({});
const loading = ref(false);
const getInit = async () => {
  loading.value = true;
  try {
    const res = await $getOnClient<{ contract: contractModelType }>(`${gateWay}${service.accept}/web/applicationForm/viewContractDetail`, { applyPolicyNo: route.query.applyPolicyNo });
    if (res && res.code === SUCCESS_CODE) {
      contractModel.value = initContractModel(res.data?.contract || {});
      voucherSummaryInfo.value = res.data.voucherSummaryInfoVO || {};
      if (contractModel.value.payInfoList?.length > 0) {
        // payInfoList有数据，右侧锚点显示收费计划
        const index = anchorItems.value.findIndex((item) => item.href === '#fee-plan');
        if (index === -1) {
          anchorItems.value.splice(6, 0, {
            key: '7',
            href: '#fee-plan',
            title: '收费计划',
          });
        }
      } else {
        // payInfoList没数据，右侧锚点不显示收费计划
        anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
      }
      // 如果共保信息存在，右侧大纲显示共保信息
      const hasCoinsurance = anchorItems.value.findIndex((item) => item.href === '#co-insurance') === -1;
      if (contractModel.value.baseInfo.coinsuranceMark === '1' && hasCoinsurance) {
        anchorItems.value.push({ key: '12', href: '#co-insurance', title: '共保信息' });
      }
    }
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};
const router = useRouter();
// 跳转附件管理页面
const handleGoLink = () => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: route.query?.applyPolicyNo || '',
      bizType: 'docViewTreeApply',
    },
  });
};
// 跳转验标页面
const goInspectionInfo = () => {
  router.push({
    path: '/inspectionInfo',
    query: {
      applyPolicyNo: route.query.applyPolicyNo,
      isHiddenProcess: 'Y',
    },
  });
};
// 跳转风险评估页面
const goRiskAssessDetail = () => {
  router.push({
    path: '/riskAssessDetail',
    query: { riskAsmtNo: contractModel.value?.baseInfo?.riskAsmtNo },
  });
};
// 核保信息
interface QueryApprovalTaskList {
  approveStatus: string;
  approveResultDesc: string;
  approveUm: string;
  approveTime: string;
  createdBy: string;
  createdDate: string;
}
const insureInfo = ref<QueryApprovalTaskList[]>([]);
const chainStr = ref<string>('');
const queryUnderwriteAssistant = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/evntApprovalTaskRecord/queryApprovalTaskList`;
    const res = await $getOnClient<{ taskRecordVOList: QueryApprovalTaskList[]; chainList: Record<string, string>[] }>(fetchUrl, { voucherNo: route.query.applyPolicyNo });
    const { taskRecordVOList = [], chainList = [] } = res?.data || {};
    if (res?.code === SUCCESS_CODE) {
      insureInfo.value = taskRecordVOList || [];
      chainStr.value = chainList.join('->');
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
// 主介绍人
const showIntroducer = ref(false);
watchEffect(() => {
  const channelSourceCode = contractModel.value?.saleInfo?.channelSourceCode;
  const channelSourceDetailCode = contractModel.value?.saleInfo?.channelSourceDetailCode?.split(',')?.[0] ?? '';
  if ((channelSourceCode === '7' && ['D', 'L'].includes(channelSourceDetailCode)) || (channelSourceCode === 'C' && channelSourceDetailCode === 'G')) {
    showIntroducer.value = true;
  } else {
    showIntroducer.value = false;
  }
});
interface SignData {
  signType: string; // 签报类型
  signDesc: string; // 签报类型说明
  signStatus: string; // 签报状态
  signStatusDesc: string; // 签报状态说明
  eoaNo: string; // 签报号
  eoaDetailUrl: string; // 签报详情url
}
// 签报数据
const signDataList = ref<SignData[]>([]);
// 获取eoa签报数据
const getEoaData = async () => {
  const res = await $getOnClient<SignData[]>(`${gateWay}${service.accept}/accept/eoa/queryApplyPolicySignDataList`, { applyPolicyNo: route.query.applyPolicyNo });
  if (res && res.code === SUCCESS_CODE) {
    signDataList.value = res.data || [];
  }
};
// 非见费签报信息
const costSignData = computed(() => {
  return signDataList.value?.filter((item) => item?.signType === '014')?.[0];
});
// 根据签报号跳转eoa
const goEoa = (url: string) => {
  window.open(url);
};
// 点击责任按钮-显示弹窗
const dutyInfoList = ref<dutyInfoListType[]>([]);
const visible = ref<boolean>(false);
const handleDuty = (record: PlanInfoType) => {
  dutyInfoList.value = record.dutyInfoList || [];
  visible.value = true;
};
const loadings = ref<boolean[][]>([]);
const loadingRate = ref<boolean[][]>([]);
// 下载文件-条款&费率
const downTermOrRate = async (downType: string, planCode: string, index: number, planIndex: number) => {
  try {
    if (downType === 'term') {
      loadings.value[planIndex][index] = true;
    } else {
      loadingRate.value[planIndex][index] = true;
    }
    const fetchUrl = `${gateWay}${service.policy}/web/policy/policyDownloadTermOrRate`;
    const res = await $postOnClient<{ url: string }>(fetchUrl, { planCode, downType, acceptInsuranceTime: new Date().getTime().toString() });
    if (res?.code === SUCCESS_CODE) {
      window.open(res?.data?.url);
    } else {
      message.error(res?.msg || '');
    }
    loadings.value[planIndex][index] = false;
    loadingRate.value[planIndex][index] = false;
  } catch (error) {
    console.log(error);
    loadings.value[planIndex][index] = false;
    loadingRate.value[planIndex][index] = false;
  }
};
// 先设置值，防止页面初始化报错
watch(
  contractModel,
  (newVal) => {
    if (newVal?.riskGroupInfoList) {
      loadings.value = newVal.riskGroupInfoList.map((planItem) => new Array(planItem.planInfoList.length * 2).fill(false));
      loadingRate.value = newVal.riskGroupInfoList.map((planItem) => new Array(planItem.planInfoList.length * 2).fill(false));
    }
  },
  { immediate: true },
);
onActivated(() => {
  getInit();
  getEoaData();
});

const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
    :deep(.ant-descriptions) {
      .ant-descriptions-item {
        padding-bottom: 12px;
        color: rgba(0, 0, 0, 0.6);
        .ant-descriptions-item-content {
          color: #333333;
        }
      }
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
  .divide-line {
    margin-top: 5px;
    padding-top: 16px;
    border-top: 1px solid #e6e8eb;
  }
  .ant-table-cell .ant-btn.ant-btn-sm:first-of-type,
  .ant-table-cell .ant-btn:first-of-type {
    padding-left: 7px;
  }
}
</style>
