export interface UploadFile {
  documentName: string;
  documentId: string;
  thumbnail: string; // 缩略图，上传图片会返回
  documentFormat: string;
  uploadPath: string;
  selected: boolean;
  bucketName: string;
  storageType: string;
  documentLimitFlag?: boolean; // 是否禁用
}

// 文件详情类型
export interface FileDetail {
  documentId: string; // 文件ID
  documentName: string; // 文件名称
  documentFormat: string; // 文件格式
  documentLimitFlag?: boolean; // 是否禁用
  uploadPath: string; // 上传路径
  bucketName: string; // 存储桶名称
  storageType: string; // 存储类型
  thumbnail: string; // 缩略图URL
  selected: boolean; // 是否选中
}

// 文件分类模块类型
export interface FileModule {
  typeName: string; // 分类名称
  typeCode: string; // 分类编码
  docLimitFlag: string; // 文件限制标志 00:非必填 01:出单必录 02：出单可缓 03：纸质归档
  fileDetailList: FileDetail[]; // 文件列表
  checkedAll: boolean; // 是否全选
  indeterminate: boolean; // 是否半选状态
  pageNum?: number;
  pageSize?: number;
  totalNum?: number;
  localTotalNum: number; // 本地展示的当前文件总数量
  loading?: boolean;
  fileTotalNeedNum?: number; // 需要上传图片张数
}

// export interface GroupType {
//   docGroupType: string;
//   id: string;
//   typeCode: string;
//   typeName: string;
// }

// API响应类型
export interface ApiResult<T> {
  code: string | number;
  data: T;
  msg?: string;
}

// 文件组查询响应类型
export interface DocumentGroupQueryResponse {
  fileTotalSize: number;
  fileTypeList: FileModule[];
  noTypeFileList?: {
    fileDetailList: FileDetail[];
  }[];
}

export interface fileNameItem {
  docType: string;
  nameList: string[];
}
export interface DocumentNameResponse {
  fileNameList: fileNameItem[];
}

// 文件上传配置类型
export interface BatchUploadConfig {
  batchSize: number; // 每批上传数量
  currentBatch: number; // 当前批次
  totalFiles: number; // 总文件数
  successCount: number; // 成功数量
  failCount: number; // 失败数量
}

// 上传队列类型
export interface UploadQueue {
  files: Array<File & { uid: string }>;
  targetType: string;
  targetList: FileDetail[];
}

export interface SearchFormState {
  startTime: ''; // 公示起期
  endTime: ''; // 公示止期
}
