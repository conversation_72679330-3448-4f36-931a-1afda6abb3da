<template>
  <div class="m-16px flex gap-x-[12px] pd-[56px] relative">
    <div v-show="showFileUploadLoading" class="absolute top-0 left-0 w-full h-full bg-white/50 flex justify-center items-center z-[1000]">
      <a-spin :tip="showFileUploadLoadingTips" />
    </div>
    <div class="rounded bg-white w-[291px] p-12px container-height" @dragenter.prevent="handleDragEnter" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop">
      <a-upload :multiple="true" :custom-request="() => {}" :before-upload="(file, fileList) => handleNoclassifyFileChange(file, fileList, noclassify.typeCode, noclassify.fileDetailList)" :show-upload-list="false">
        <div class="upload-box" :class="{ 'upload-box-dragging': isDragging }">
          <div class="flex flex-col items-center">
            <img class="w-[46px] h-[54px]" :src="uploadImg" />
            <div class="text-xs text-[#333333] mt-[8px] mb-[4px]">可直接拖拽文件到这里，或点击添加</div>
            <div class="text-xs text-[rgba(0,0,0,0.6)]">单次不超过<span class="font-number-medium text-[#07c160]">200&nbsp;</span>份</div>
          </div>
        </div>
      </a-upload>
      <div class="flex items-center justify-between mt-[16px] mb-[8px]">
        <div class="text-rgba(0,0,0,0.9) text-[16px] font-bold">未分类文件</div>
        <a-checkbox v-model:checked="noclassify.checkedAll" :indeterminate="noclassify.indeterminate" @change="(e) => handleAllCheckedChange(e, noclassify)">
          <span class="text-xs text-[#606060]">全选</span>
        </a-checkbox>
      </div>
      <div v-if="noclassify.fileDetailList?.length > 0" class="grid grid-cols-2 gap-[8px] noclassify-height">
        <!-- 可拖拽文件 -->
        <div v-for="file in noclassify.fileDetailList" :key="file.documentId" class="file-box" draggable="true" @dragstart="dragStart(file, noclassify)" @dragend="dragEnd">
          <a-checkbox v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(noclassify)" />
          <div class="h-full flex flex-col justify-center items-center">
            <img v-if="file.thumbnail && isImage(file.documentFormat)" class="h-[80px] w-[70px]" :src="file.thumbnail" />
            <div v-else class="text-[50px] h-[80px] flex justify-center items-center">
              <VueIcon :icon="getFileIcon(file.documentName)" />
            </div>
            <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" :title="file.documentName">{{ file.documentName }}</div>
          </div>
        </div>
      </div>
      <div v-else class="mt-[100px]">
        <a-empty :image="simpleImage" />
      </div>
    </div>
    <div class="rounded bg-white flex-grow p-12px container-height flex-1">
      <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold ml-12px">承保资料({{ totalCount }})</div>
      <div class="flex justify-between mb-8px">
        <div class="flex items-center gap-x-16px text-[rgba(0,0,0,0.6)] ml-12px">
          <span>注：1.单个附件大小不超过10M</span>
          <span class="gap-[5px]">
            2.图片格式
            <a-tooltip placement="top">
              <VueIcon :icon="IconErrorCircleFilledFont" />
              <template #title>
                <span>图片附件格式为：BMP, DIB, JPG, JPEG, JPE, JFIF, GIF, TIF, TIFF, PNG</span>
              </template>
            </a-tooltip>
          </span>
          <span class="gap-[5px]"
            >3.文档格式
            <a-tooltip placement="top">
              <VueIcon :icon="IconErrorCircleFilledFont" />
              <template #title>
                <span>文档附件格式为：DOC, XLS, PDF, MSG, HTM, PPT, PPTX, RAR, ZIP, TXT, XLSX, DOCX</span>
              </template>
            </a-tooltip></span
          >
        </div>
        <div class="flex items-center gap-x-8px">
          <a-select v-model:value="docTypeValue" class="w-[277px]" placeholder="请选择资料类型" allow-clear show-search option-filter-prop="label" :options="docTypeList" />
          <a-button @click="addDocType">
            <span class="text-[#5a5a5a]">
              <VueIcon :icon="IconAddFont" />
              添加资料类型
            </span>
          </a-button>
        </div>
      </div>
      <a-form ref="searchForm" :model="searchFormState">
        <div class="grid grid-cols-2 gap-12px">
          <!-- 可拖入区域 -->
          <div v-for="classify in classifyList" :key="classify.typeCode" class="classify-container" @dragenter.prevent="handleDragEnterItem" @dragover.prevent="handleDragOverItem" @dragleave.prevent="handleDragLeaveItem" @drop.prevent="(e) => handleDropItem(e, classify)">
            <div class="flex items-center justify-between mb-[8px]">
              <div class="flex items-center">
                <a-checkbox v-model:checked="classify.checkedAll" :indeterminate="classify.indeterminate" @change="(e) => handleAllCheckedChange(e, classify)">
                  <span class="sub-title">{{ classify.typeName }}</span>
                </a-checkbox>
                <!-- 必传数量 -->
                <span v-if="['01', '02', '03'].includes(classify.docLimitFlag) && classify.fileTotalNeedNum" class="text-xs text-[rgba(0,0,0,0.6)] ml-[5px]">{{ `必传数量 (${classify.localTotalNum}/${classify.fileTotalNeedNum})` }}</span>
              </div>
              <div class="text-xs text-[rgba(0,0,0,0.6)] flex items-center">
                <a-tooltip title="暂无数据">
                  <span class="mr-16px">资料说明</span>
                </a-tooltip>
                <a-tooltip title="暂无数据">
                  <span>查看示例</span>
                </a-tooltip>
                <span class="inline-block w-[1px] h-[16px] bg-[#d4d6d9] mx-[12px]" />
                <span v-if="classify.docLimitFlag === '00'" class="text-[#576B95] cursor-pointer" @click="handleDeleteDoc(classify)"><VueIcon :icon="IconTongyongShanchuFont" /><span class="ml-[4px]">删除分类</span></span>
                <span v-if="classify.docLimitFlag === '01'" class="green-tag">出单必录</span>
                <span v-if="classify.docLimitFlag === '02'" class="blue-tag">出单可缓</span>
                <span v-if="classify.docLimitFlag === '03'" class="blue-tag">纸质归档</span>
              </div>
            </div>
            <!-- 公示起止期 -->
            <a-row v-if="classify.offlinePublicityFlag === '1'" :gutter="16">
              <a-col span="12">
                <a-form-item label="公示起期" name="startTime" :rules="[{ required: true, trigger: 'change', validator: validateDate }]">
                  <a-date-picker v-model:value="searchFormState.startTime" placeholder="选择起期" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" allow-clear @change="changeDate" />
                </a-form-item>
              </a-col>
              <a-col span="12">
                <a-form-item label="公示止期" name="endTime" :rules="[{ required: true, trigger: 'change', validator: validateDate }]">
                  <a-date-picker v-model:value="searchFormState.endTime" placeholder="选择止期" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" allow-clear @change="changeDate" />
                </a-form-item>
              </a-col>
            </a-row>
            <ScrollLoadAttachments v-model:current-page-num="classify.pageNum" v-model:page-size="classify.pageSize" v-model:total-num="classify.totalNum" v-model:loading="classify.loading" class="grid-container" @load-more="loadMore(classify)">
              <div v-if="VALIDATE_TYPE.includes(classify?.typeCode) && (isDuplicateIDAndBank || hasDuplicates(classify.fileDetailList))" class="bg-[#fff7e6] text-[#d46b08] px-[4px] py-[2px] mb-[3px]"><ExclamationCircleFilled class="text-[#F7BA1E] mr-[6px]" />身份信息和银行信息中存在相同照片，请关注</div>
              <div class="grid">
                <a-upload :multiple="true" :custom-request="() => {}" :before-upload="(file, fileList) => handleFileChange(file, fileList, classify.typeCode, classify.fileDetailList)" :show-upload-list="false">
                  <div class="file-box flex justify-center items-center gap-x-[2px] text-[#606060]" :class="{ 'file-box-dragging': isDraggingItem }">
                    <VueIcon :icon="IconUploadFont" />
                    <span class="text">上传文件</span>
                  </div>
                </a-upload>
                <template v-for="file in classify.fileDetailList" :key="file.documentId">
                  <div v-if="!file.documentLimitFlag" class="file-box" :draggable="true" @dragstart="dragStart(file, classify)" @dragend="dragEnd">
                    <a-checkbox v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(classify)" />
                    <div class="h-full flex flex-col justify-center items-center">
                      <img v-if="file.thumbnail && isImage(file.documentFormat)" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file, classify.fileDetailList)" />
                      <div v-else class="text-[50px] h-[80px] flex justify-center items-center" @click="handleClickFile(file, classify.fileDetailList)">
                        <VueIcon :icon="getFileIcon(file.documentName)" />
                      </div>
                      <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" :title="file.documentName">{{ file.documentName }}</div>
                    </div>
                  </div>
                  <div v-else class="file-box">
                    <div class="h-full flex flex-col justify-center items-center">
                      <img v-if="file.thumbnail && isImage(file.documentFormat)" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file, classify.fileDetailList)" />
                      <div v-else class="text-[50px] h-[80px] flex justify-center items-center" @click="handleClickFile(file, classify.fileDetailList)">
                        <VueIcon :icon="getFileIcon(file.documentName)" />
                      </div>
                      <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" :title="file.documentName">{{ file.documentName }}</div>
                    </div>
                  </div>
                </template>
              </div>
            </ScrollLoadAttachments>
          </div>
        </div>
      </a-form>
    </div>
    <div class="fixed h-[56px] w-full bg-white bottom-0 footer-boxshadow">
      <div class="h-[56px] flex items-center justify-center gap-8px">
        <a-button type="primary" @click="() => (allDeleteModal = true)">删除</a-button>
        <!-- <a-button type="primary">移动到</a-button> -->
        <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
      </div>
    </div>
    <a-modal v-model:open="deleteModal" title="提醒" @ok="deleteDocType">
      <div>确认要将资料类型及其类型下所有资料删除？</div>
    </a-modal>
    <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleDeleteFile">
      <div>确认删除？</div>
    </a-modal>
    <ImageViewer ref="viewRef" :list="viewList" :current="viewCurrent" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import dayjs from 'dayjs';
import { IconUploadFont, IconAddFont, IcPdfColor, IcWordColor, IcDianziqingdanColor, IcWenjianColor, IconErrorCircleFilledFont, IconTongyongShanchuFont, IcShipingColor, IcYingxiangColor } from '@pafe/icons-icore-agr-an';
import type { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import { Empty } from 'ant-design-vue';
import type { FileModule, UploadFile, DocumentGroupQueryResponse, SearchFormState } from './imageUpload.d';
import uploadImg from '@/assets/images/ic-upload.png';
import { SUCCESS_CODE, VALIDATE_TYPE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { $postOnClient } from '@/composables/request';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import ScrollLoadAttachments from '@/components/ui/ScrollLoadAttachments.vue';
import { type ApiResult, $post } from '@/utils/request';
import { useUploadFiles } from '@/composables/useUploadFiles';
import { isImage, isUnPreviewImage } from '@/utils/tools';
import { intersectionWith, uniqBy } from 'lodash-es';

const { gateWay, service } = useRuntimeConfig().public || {};

// 还需补充
const fileTypeMap = new Map([
  ['XLSX', IcDianziqingdanColor],
  ['XLS', IcDianziqingdanColor],
  ['DOC', IcWordColor],
  ['DOCX', IcWordColor],
  ['PDF', IcPdfColor],
  ['MP4', IcShipingColor],
]);

const totalCount = ref(0);
const docTypeValue = ref('');
const docTypeList = ref<{ label: string; value: string }[]>([]);

const route = useRoute();
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const getDocTypeList = async () => {
  try {
    const fetchUrl = gateWay + service.administrate + '/attachment/documentType/list';
    const { data, code } = await $postOnClient(fetchUrl, {
      bizType: 'docTypeApply',
      bizNo: route.query.applyPolicyNo,
    });
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      docTypeList.value = data.map((item) => ({ label: item.parameterName, value: item.parameterCode }));
    }
  } catch (e) {
    console.log(e);
  }
};

// 未分类文件
const noclassify = reactive<FileModule>({
  typeName: '未分类文件',
  typeCode: '',
  fileDetailList: [],
  checkedAll: false,
  indeterminate: false,
  docLimitFlag: '',
});
const offlinePublicityFlag = ref<string>('');
// 已分类列表
const classifyList = ref<Array<FileModule>>([]);

const getDocList = async () => {
  const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/query';
  try {
    const { code, data } = await $post<ApiResult<DocumentGroupQueryResponse>>(fetchUrl, { bizNo: route.query.applyPolicyNo, bizType: 'docTypeApply' });
    if (code === SUCCESS_CODE && data) {
      totalCount.value = data.fileTotalSize;
      classifyList.value = data.fileTypeList.map((item) => ({
        ...item,
        checkedAll: false,
        indeterminate: false,
        pageNum: item.pageNum || 1, // 赋值
        pageSize: item.pageSize || 20,
        totalNum: item.total || 0,
        localTotalNum: item.total || 0, // 本地展示的当前文件总数量
        loading: false,
      }));
      noclassify.fileDetailList = data.noTypeFileList?.[0]?.fileDetailList || [];
      const publicItem = classifyList.value.find((item) => item.typeCode === 'A04'); // 公示照片
      offlinePublicityFlag.value = publicItem?.offlinePublicityFlag;
      searchFormState.startTime = publicItem?.offlinePublicityStartDate || '';
      searchFormState.endTime = publicItem?.offlinePublicityEndDate || '';
    }
  } catch (e) {
    console.log(e);
  }
};

// applyPolicyNo变化时，需要刷新页面数据
watch(
  () => route.query.applyPolicyNo,
  () => {
    getDocTypeList();
    getDocList();
  },
  {
    immediate: true,
  },
);

const handleAllCheckedChange = (e: CheckboxChangeEvent, target: FileModule) => {
  target.indeterminate = false;
  target.fileDetailList.forEach((el) => (el.selected = e.target.checked || false));
};

const handleCheckedChange = (target: FileModule) => {
  target.checkedAll = target.fileDetailList.every((i) => i.selected);
  if (target.fileDetailList.some((i) => i.selected)) {
    target.indeterminate = !target.fileDetailList.every((i) => i.selected);
  } else {
    target.indeterminate = false;
  }
};
// 记录拖拽文件
const dragFile = ref<UploadFile>();
const moveFile = ref(false); // 文件拖拽移动中
// 记录待拖拽文件所属对象
const moduleSnapshot = ref<FileModule>();
const dragStart = (targetFile: UploadFile, fileModule: FileModule) => {
  moveFile.value = true;
  if (targetFile?.documentLimitFlag) {
    return;
  }
  dragFile.value = targetFile;
  moduleSnapshot.value = fileModule;
};

const dragEnd = () => {
  moveFile.value = false;
};

const removeFile = async (file: UploadFile, fileList: UploadFile[]) => {
  const removeIndex = fileList.findIndex((item) => item.documentId === file.documentId);
  fileList.splice(removeIndex, 1);
};

const drop = (targetModule: FileModule) => {
  if (moduleSnapshot.value && dragFile.value) {
    const selectedList = moduleSnapshot.value?.fileDetailList.filter((i) => i.selected);
    if (selectedList.length > 1 && dragFile.value.selected) {
      selectedList.forEach((current) => {
        current.selected = false;
        targetModule.fileDetailList.unshift(current); // 存在分页，统一在数组前塞数据
        removeFile(current, moduleSnapshot.value?.fileDetailList || []);
        targetModule.localTotalNum = targetModule.localTotalNum + 1; // 更新拖入文件总数
        moduleSnapshot.value.localTotalNum = moduleSnapshot.value.localTotalNum - 1; // 更新拖出文件总数
      });
    } else {
      targetModule.fileDetailList.unshift(dragFile.value); // 存在分页，统一在数组前塞数据
      removeFile(dragFile.value, moduleSnapshot.value.fileDetailList);
      targetModule.localTotalNum = targetModule.localTotalNum + 1; // 更新拖入文件总数
      moduleSnapshot.value.localTotalNum = moduleSnapshot.value.localTotalNum - 1; // 更新拖出文件总数
    }
    handleCheckedChange(targetModule);
    moduleSnapshot.value.checkedAll = false;
    moduleSnapshot.value.indeterminate = false;
  }
};

const acceptType = ['BMP', 'DIB', 'JPG', 'JPEG', 'JPE', 'JFIF', 'GIF', 'TIF', 'TIFF', 'PNG', 'PSD', 'PCX', 'EXIF', 'DOC', 'XLS', 'PDF', 'MSG', 'HTM', 'PPT', 'PPTX', 'RAR', 'ZIP', 'TXT', 'XLSX', 'DOCX'];

// 添加上传状态
const MAX_SIZE = 10 * 1024 * 1024; // 10MB
// 文件验证
const validateFile = (file: File): boolean => {
  const fileExtension = file.name.split('.').pop()?.toUpperCase();

  if (!fileExtension || !acceptType.includes(fileExtension)) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }

  if (file.size > MAX_SIZE) {
    message.warning(`${file.name} 大小超过10MB`);
    return false;
  }

  const isDuplicate = (list: UploadFile[]) => list.some((_file) => _file.documentName === file.name);
  // 优化内容：在照片上传时，身份证、银行卡(A13、A14)照片如属于同一张照片，可以上传成功，做风险提示（在），不做系统拦截。
  if (isDuplicate(targetUploadList.value)) {
    message.warning(`${file.name}附件已存在，不能再上传`);
    return false;
  }

  // if (isDuplicate(noclassify.fileDetailList || []) || classifyList.value.some((classify) => isDuplicate(classify.fileDetailList || []))) {
  //   message.warning(`${file.name}附件已存在，不能再上传`);
  //   return false;
  // }

  return true;
};
const hasDuplicates = (arr: UploadFile[]) => arr?.length && arr?.length !== uniqBy(arr, 'documentName')?.length;
const isDuplicateIDAndBank = computed(() => {
  const A13Array: UploadFile[] = [];
  const A14Array: UploadFile[] = [];
  classifyList.value.forEach((classify) => {
    if (classify.typeCode === 'A13') {
      A13Array.push(...classify.fileDetailList);
    } else if (classify.typeCode === 'A14') {
      A14Array.push(...classify.fileDetailList);
    }
  });
  const intersectionArray = intersectionWith(A13Array, A14Array, (a, b) => a.documentName === b.documentName);
  return intersectionArray.length;
});
const { upload, showFileUploadLoading, showFileUploadLoadingTips, uploadedFile } = useUploadFiles({ validate: validateFile });
const targetUploadList = ref<UploadFile>([]);

// 上传成功回调
watch(uploadedFile, (val) => {
  targetUploadList.value.unshift({ ...val, selected: false });
  // 更新对应类型的文件总数量
  classifyList.value.forEach((classify) => {
    if (classify.typeCode === val?.documentType) {
      classify.localTotalNum = classify?.localTotalNum + 1;
    }
  });
});

// 修改 handleFileChange 函数,只负责收集文件
const handleFileChange = (file: File & { uid: string }, fileList: Array<File & { uid: string }>, targetType: string, targetList: UploadFile[]) => {
  // 验证文件总数
  // if (!canUpload.value) return false;
  // if (fileList.length > MAX_FILES) {
  //   message.error(`一次上传最多不能超过${MAX_FILES}张，请重新选择要上传的附件！`);
  //   canUpload.value = false;
  //   return false;
  // }

  // // 将新文件添加到上传队列
  // uploadQueue.value = {
  //   files: [...uploadQueue.value.files, file],
  //   targetType,
  //   targetList,
  // };

  // if (uploadQueue.value.files.length === fileList.length) {
  //   // 开始上传
  //   startUpload();
  // }
  // return false;

  targetUploadList.value = targetList;
  return upload(file, fileList, {
    bizNo: route.query.applyPolicyNo || '',
    bizType: 'docTypeApply',
    fileType: targetType,
  });
};

const isDragging = ref(false); // 文件上传拖拽中

const handleDragEnter = () => {
  if (moveFile.value) {
    return;
  }
  isDragging.value = true;
};

const handleDragOver = () => {
  if (moveFile.value) {
    return;
  }
  isDragging.value = true;
};

const handleDragLeave = (e) => {
  // 确保只在鼠标真正离开容器时触发
  if (e.currentTarget.contains(e.relatedTarget)) return;
  isDragging.value = false;
};

const handleDrop = (e) => {
  isDragging.value = false;

  // 如果是从外部拖拽的文件（而不是内部元素拖拽）
  if (e.dataTransfer.files.length > 0 && !moveFile.value) {
    const files = Array.from(e.dataTransfer.files);

    // 验证和上传文件
    files.forEach((file) => {
      handleNoclassifyFileChange(file, files, noclassify.typeCode, noclassify.fileDetailList);
    });
  }

  // 重置状态
  moveFile.value = false;
};

const isDraggingItem = ref(false); // 文件上传拖拽中
const handleDragEnterItem = () => {
  if (moveFile.value) {
    return;
  }
  isDraggingItem.value = true;
};

const handleDragOverItem = () => {
  if (moveFile.value) {
    return;
  }
  isDraggingItem.value = true;
};

const handleDragLeaveItem = (e) => {
  // 确保只在鼠标真正离开容器时触发
  if (e.currentTarget.contains(e.relatedTarget)) return;
  isDraggingItem.value = false;
};

const handleDropItem = (e, classify) => {
  isDraggingItem.value = false;
  // 如果是从外部拖拽的文件（而不是内部元素拖拽）
  if (e.dataTransfer.files.length > 0 && !moveFile.value) {
    const files = Array.from(e.dataTransfer.files);

    // 验证和上传文件
    files.forEach((file) => {
      handleNoclassifyFileChange(file, files, classify.typeCode, classify.fileDetailList);
    });
  } else {
    drop(classify);
  }
  // 重置状态
  moveFile.value = false;
};

// 未分类文件上传
const handleNoclassifyFileChangeTimes = ref(0); // handleNoclassifyFileChange方法执行次数
const handleNoclassifyFileChange = async (file: File & { uid: string }, fileList: Array<File & { uid: string }>, targetType: string, targetList: UploadFile[]) => {
  // 未分类文件不存在分页，最多可存200条数据，已和产品沟通
  isDragging.value = false;
  handleNoclassifyFileChangeTimes.value += 1;
  if (targetList.length + fileList.length >= 201) {
    if (handleNoclassifyFileChangeTimes.value === fileList.length) {
      // 仅提示一次
      message.error('未分类文件已达上限，请把未分类的文件移到其他分类中，再进行上传');
      handleNoclassifyFileChangeTimes.value = 0;
    }
    return false;
  }
  // return false;
  handleFileChange(file, fileList, targetType, targetList);
};

const getFileIcon = (fileName: string) => {
  const fileExtension = fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  return isUnPreviewImage(fileExtension) ? IcYingxiangColor : fileTypeMap.get(fileExtension) || IcWenjianColor;
};

const handleDeleteFile = async () => {
  const noclassifySelected = noclassify.fileDetailList.filter((item) => item.selected);
  let classifySelected: UploadFile[] = [];
  classifyList.value.forEach((classify) => {
    const selectedList = classify.fileDetailList.filter((item) => item.selected);
    classifySelected = [...classifySelected, ...selectedList];
  });
  if (classifySelected.length > 0 && classifySelected.every((item) => item.documentLimitFlag)) {
    message.error('所选文件不可删除');
    return;
  }
  if (noclassifySelected?.length === 0 && classifySelected?.length === 0) {
    message.error('请至少勾选1个要删除的文件');
  } else {
    const deleteList = [...noclassifySelected.map((k) => k.documentId), ...classifySelected.map((v) => v.documentId)];
    const url = gateWay + service.administrate + '/attachment/document/delete';
    try {
      const { code } = await $postOnClient(url, { bizType: 'docTypeApply', bizNo: route.query.applyPolicyNo, documentId: deleteList.join(',') });
      if (code === SUCCESS_CODE) {
        noclassify.fileDetailList = noclassify.fileDetailList.filter((item) => !item.selected);
        noclassify.checkedAll = false;
        noclassify.indeterminate = false;
        classifyList.value.forEach((classify) => {
          const delNum = classify.fileDetailList.filter((item) => item.selected).length; // 删除的数量
          classify.localTotalNum = classify.localTotalNum - delNum; // 更新当前文件总数
          classify.fileDetailList = classify.fileDetailList.filter((item) => !item.selected);
          classify.checkedAll = false;
          classify.indeterminate = false;
        });
        allDeleteModal.value = false;
        // getDocList();
      } else {
        message.error('删除失败，请重试!');
      }
    } catch (e) {
      message.error('删除失败，请重试!');
      console.log(e);
    }
  }
};

const viewRef = ref(null);
const viewList = ref<
  {
    bucketName: string;
    uploadPath: string;
    thumbnail: string; // 缩略图url
    documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
    documentName?: string;
    id?: string;
  }[]
>([]);
const viewCurrent = ref(0);
const handleClickFile = async (file: UploadFile, fileList: UploadFile[]) => {
  // 全部都到预览页展示
  viewCurrent.value = fileList.findIndex((k) => k.documentId === file.documentId);
  viewList.value = fileList.map((v) => ({
    bucketName: v.bucketName,
    uploadPath: v.uploadPath,
    thumbnail: v.thumbnail,
    documentFormat: v.documentFormat,
    documentName: v.documentName,
    id: v.documentId,
  }));
  if (viewRef.value) {
    viewRef.value?.openPreview();
  }
  // 如果点击图片，则预览，否则下载
  // if (file.thumbnail) {
  //   viewCurrent.value = fileList.findIndex((k) => k.documentId === file.documentId);
  //   viewList.value = fileList.map((v) => ({
  //     bucketName: v.bucketName,
  //     uploadPath: v.uploadPath,
  //     thumbnail: v.thumbnail,
  //     documentFormat: v.documentFormat,
  //     documentName: v.documentName,
  //     id: v.documentId,
  //   }));
  //   if (viewRef.value) {
  //     viewRef.value?.openPreview();
  //   }
  // } else {
  //   const { data } =
  //     (await $postOnClient('/api/iobs/getInIobsUrl', {
  //       iobsBucketName: file.bucketName,
  //       fileKey: file.uploadPath,
  //       storageTypeCode: file.storageType,
  //     })) || {};
  //   if (data?.fileUrl) {
  //     window.open(data?.fileUrl);
  //   }
  // }
};

const addDocType = () => {
  if (!docTypeValue.value) {
    message.warning('请选择要添加的资料类型');
    return;
  }
  const selected = docTypeList.value.find((doc) => doc.value === docTypeValue.value);
  const hasAdd = classifyList.value.some((item) => item.typeCode === selected?.value);
  if (selected && !hasAdd) {
    classifyList.value.push({ typeName: selected.label, fileDetailList: [], checkedAll: false, indeterminate: false, typeCode: selected.value, docLimitFlag: '00' });
  } else {
    message.warning('该类型文件已存在，请勿重新添加');
  }
};

const deleteModal = ref(false);
const waitDeleteDocType = ref<string>();
const handleDeleteDoc = async (target: FileModule) => {
  waitDeleteDocType.value = target.typeCode;
  deleteModal.value = true;
};
const allDeleteModal = ref(false);
const deleteDocType = async () => {
  const fetchurl = gateWay + service.administrate + '/attachment/documentGroup/deleteByDocType';
  try {
    const { code } = await $postOnClient(fetchurl, {
      bizNo: route.query.applyPolicyNo,
      docType: waitDeleteDocType.value,
      bizType: 'docTypeApply',
    });
    if (code === SUCCESS_CODE) {
      classifyList.value = classifyList.value.filter((classify) => classify.typeCode !== waitDeleteDocType.value);
    }
  } catch (e) {
    console.log(e);
  } finally {
    deleteModal.value = false;
  }
};

const submitLoading = ref(false);
const saveList = async () => {
  const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/save';
  const fileList: Array<{ typeCode: string; documentId: string }> = [];
  classifyList.value.forEach((classify) => {
    classify.fileDetailList.forEach((file) => {
      fileList.push({
        typeCode: classify.typeCode,
        documentId: file.documentId,
      });
    });
  });
  try {
    submitLoading.value = true;
    const { code } = await $postOnClient(fetchUrl, { bizNo: route.query.applyPolicyNo, bizType: 'docTypeApply', saveFileDetailVOS: fileList });
    if (code === SUCCESS_CODE) {
      message.success('保存成功');
      // 刷新页面
      getDocList();
    }
    submitLoading.value = false;
  } catch (e) {
    submitLoading.value = false;
    console.log(e);
  }
};
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  startTime: '', // 公示起期
  endTime: '', // 公示起期
});
const validateDate = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('请选择日期');
  }
  const startTime = dayjs(searchFormState.startTime);
  const endTime = dayjs(searchFormState.endTime);
  if (endTime.isBefore(startTime)) {
    return Promise.reject('公示止期不得早于公示起期');
  }
  return Promise.resolve();
};
// 改变公示时间触发校验
const changeDate = () => {
  searchForm.value?.validate();
};
const submit = () => {
  if (noclassify.fileDetailList.length > 0) {
    message.error('存在未分类的附件信息，请调整。');
  } else if (searchForm.value && offlinePublicityFlag.value === '1') {
    // 确认公示起止时间
    confirmDate();
  } else {
    saveList();
  }
};
// 确认公示起止时间
const confirmDate = async () => {
  // 校验公示起止期
  await searchForm.value.validate();
  try {
    submitLoading.value = true;
    const fetchUrl = gateWay + service.accept + '/publicity/updateOfflinePublicityTime';
    const params = {
      beginDate: searchFormState.startTime && dayjs(searchFormState.startTime).format('YYYY-MM-DD HH:mm:ss'),
      endDate: searchFormState.endTime && dayjs(searchFormState.endTime).format('YYYY-MM-DD HH:mm:ss'),
      applyPolicyNo: route.query.applyPolicyNo,
    };
    const res = await $postOnClient<{ code: string; msg: string }>(fetchUrl, params);
    if (res?.code === SUCCESS_CODE) {
      saveList();
    } else {
      message.error(res?.msg || '');
    }
  } catch (e) {
    console.log(e);
  } finally {
    submitLoading.value = false;
  }
};
const loadMore = async (item: FileModule) => {
  // console.log('loadmore', item);
  // 加载下一页数据
  item.loading = true;
  const { typeCode, pageNum } = item;
  const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/queryPageSubType';
  // const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/query';
  try {
    const { code, data } = await $post<ApiResult<DocumentGroupQueryResponse>>(fetchUrl, { bizNo: route.query.applyPolicyNo, documentType: typeCode, pageNum, bizType: 'docTypeApply' });
    // const { code, data } = await $post<ApiResult<DocumentGroupQueryResponse>>(fetchUrl, { bizNo: route.query.applyPolicyNo, bizType: 'docTypeApply' });
    item.loading = false;
    if (code === SUCCESS_CODE && data) {
      item.pageNum = data.fileTypeList[0].pageNum;
      item.pageSize = data.fileTypeList[0].pageSize;
      item.totalNum = data.fileTypeList[0].total;
      item.fileDetailList = [...item.fileDetailList, ...data.fileTypeList[0].fileDetailList];
    }
  } catch (e) {
    item.loading = false;
    console.log(e);
  }
};
</script>

<style lang="less" scoped>
.container-height {
  height: calc(100vh - 120px);
  overflow-y: auto;
}
.noclassify-height {
  max-height: calc(100% - 200px);
  overflow-y: auto;
  overflow-x: hidden;
}
.sub-title {
  position: relative;
  font-size: 14px;
  color: #404442;
  font-weight: 600;
  padding-left: 8px;

  &::before {
    position: absolute;
    left: 0px;
    top: 4px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
.upload-box {
  width: 279px;
  height: 140px;
  background: rgba(7, 193, 96, 0.05);
  border: 1px dashed rgba(7, 193, 96, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-box-dragging {
  transform: scale(1.05);
  border: 2px dashed rgba(7, 193, 96, 1);
  background: rgba(7, 193, 96, 0.1);
  box-shadow: 0 0 10px rgba(7, 193, 96, 0.3);
}

.file-box {
  cursor: pointer;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  width: 130px;
  height: 130px;
  position: relative;
  background: #ffffff;
}

.file-box-dragging {
  .text {
    transform: scale(1.05);
  }
  .txdicon {
    transform: scale(1.05);
  }
}

.classify-container {
  background: #fafafc;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  padding: 12px;
}
.grid-container {
  max-height: 580px;
  overflow-y: auto;
  overflow-x: hidden;
  .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 默认是3列布局 */
    gap: 8px; /* 网格间距 */
  }
}
@media (min-width: 1920px) {
  .grid-container {
    .grid {
      grid-template-columns: repeat(4, 1fr); /* 1920及以上为4列布局 */
    }
  }
}
.green-tag {
  background: rgba(7, 193, 96, 0.05);
  border: 1px solid rgba(7, 193, 96, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #07c160;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blue-tag {
  background: rgba(61, 126, 255, 0.05);
  border: 1px solid rgba(61, 126, 255, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #3d7eff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.orange-tag {
  background: rgba(255, 91, 0, 0.05);
  border: 1px solid rgba(255, 91, 0, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #ff5b00;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-boxshadow {
  box-shadow: 0px 2px 12px 0px rgba(212, 214, 217, 1);
}
</style>
