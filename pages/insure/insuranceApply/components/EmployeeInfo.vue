<template>
  <template v-for="(item, i) in list" :key="i">
    <a-form-item label="业务员信息" :name="['saleInfo', 'employeeInfoList', i, 'employeeCode']" :rules="[{ required: true, message: '请选择业务员' }]">
      <a-select v-model:value="item.employeeCode" placeholder="请选择" :options="employeeList" allow-clear show-search :loading="employeeLoading" option-filter-prop="label" :disabled="hasSubmit" @change="(value, option) => changeEmployeeInfo(option, i)" />
    </a-form-item>
    <a-form-item label="业务员执业证号" :name="['saleInfo', 'employeeInfoList', i, 'employeeProfCertifNo']">
      <a-input v-model:value.trim="item.employeeProfCertifNo" placeholder="请输入" :disabled="hasSubmit" />
    </a-form-item>
    <a-form-item label="业务员联系方式" :name="['saleInfo', 'employeeInfoList', i, 'employeeMobileTelephone']" :rules="[{ validator: isMobilePhone }]">
      <div class="flex">
        <a-input v-model:value.trim="item.employeeMobileTelephone" placeholder="请输入11位手机号码" :disabled="hasSubmit" />
        <a-tooltip placement="topRight">
          <template #title>填写后将自动展示在公示清单-承保机构电话处</template>
          <ExclamationCircleFilled />
        </a-tooltip>
      </div>
    </a-form-item>
    <div v-if="developFlg === 'Y'" class="grid grid-cols-[140px_87px_1fr] gap-x-[20px]">
      <a-form-item
        v-if="developFlg === 'Y'"
        label="占比"
        validate-first
        :name="['saleInfo', 'employeeInfoList', i, 'commisionScale']"
        :label-col="{ style: { width: 'auto' } }"
        :rules="[
          { required: true, message: '请输入占比' },
          { pattern: /^(?:[1-9]\d?(?:\.\d{1,4})?|0\.\d{1,4})$/, message: '请输入0～100范围内的数字，且小数位不超过4位', trigger: 'change' },
        ]"
      >
        <a-input v-model:value.trim="item.commisionScale" :disabled="hasSubmit" class="percent-input">
          <template #addonAfter>
            <div class="w-[26px]">%</div>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item v-if="developFlg === 'Y'" :name="['saleInfo', 'employeeInfoList', i, 'mainEmployeeFlag']" class="ismain-radio">
        <a-radio-group v-model:value="item.mainEmployeeFlag">
          <a-radio value="1" :disabled="hasSubmit" @change="(e) => isMainChange(e, i)">主业务员</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="developFlg === 'Y' && !hasSubmit" :name="['saleInfo', 'employeeInfoList', i, 'mainEmployeeFlag']">
        <div v-show="i === 0" class="flex cursor-pointer items-center" @click="addEmployee">
          <a-tooltip placement="topRight">
            <template #title>新增业务员</template>
            <VueIcon :icon="IconTongyongXinzengFont" class="text-[14px] text-[#576B95] mr-[3px]" />
          </a-tooltip>
        </div>
        <div v-show="i !== 0" class="flex cursor-pointer items-center" @click="deleteEmployee(i)">
          <a-tooltip placement="topRight">
            <template #title>删除</template>
            <VueIcon :icon="IconTongyongShanchuFont" class="text-[14px] text-[#576B95] mr-[3px]" />
          </a-tooltip>
        </div>
      </a-form-item>
    </div>
  </template>
</template>

<script setup lang="ts">
import { IconTongyongXinzengFont, IconTongyongShanchuFont } from '@pafe/icons-icore-agr-an';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { isMobilePhone } from '@/utils/validators';

const props = defineProps<{
  developFlg: string;
  departmentCode: string;
  hasSubmit: boolean;
  riskAsmtNo: string; // 风险评估编号
  validateFields: (params: unkown) => void;
}>();

const isUpdateEmployee = ref<boolean>(true);
const emits = defineEmits(['change']);
const list = defineModel<
  Array<{
    employeeCode: string;
    employeeName: string;
    employeeProfCertifNo: string;
    commisionScale: string;
    mainEmployeeFlag: string;
    employeeMobileTelephone: string;
  }>
>({ default: [] });

const changeEmployeeInfo = (option: DefaultOptionType, index: number) => {
  list.value[index].employeeCode = option?.value as string;
  list.value[index].employeeName = option?.label || '';
  emits('change');
};

const addEmployee = () => {
  list.value.push({
    employeeCode: '',
    employeeName: '',
    employeeProfCertifNo: '', // 业务员执业证号
    commisionScale: '', // 占比
    mainEmployeeFlag: '',
    employeeMobileTelephone: '', //业务员联系方式
  });
};

const deleteEmployee = (index: number) => {
  list.value.splice(index, 1);
  // 如果删除了主业务员，则设置第一个为主业务员
  const mainEmployeeIndex = list.value.findIndex((val) => val.mainEmployeeFlag === '1');
  if (mainEmployeeIndex < 0 && list.value.length > 0) {
    list.value[0].mainEmployeeFlag = '1';
  }
};

// 是否共展
watch(
  () => props.developFlg,
  () => {
    if (props.developFlg === 'NS') {
      if (list.value.length > 1) {
        const firstEmployee = {
          employeeCode: list.value[0].employeeCode,
          employeeName: list.value[0].employeeName,
          employeeProfCertifNo: list.value[0].employeeProfCertifNo,
          employeeMobileTelephone: list.value[0].employeeMobileTelephone,
          commisionScale: '',
          mainEmployeeFlag: '1',
        };
        list.value = [firstEmployee];
      }
    }
  },
);

const employeeLoading = ref(false);
const employeeList = ref<Array<SelectOptions>>([]);

watch([() => props.departmentCode, () => props.riskAsmtNo], async () => {
  try {
    employeeLoading.value = true;
    const res = await $getOnClient('/api/common/getSasEmployeeList', { departmentCode: props.departmentCode, riskAsmtNo: props.riskAsmtNo });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      employeeList.value = res.data;
      // todo 待重构
      if (res.data.length > 0 && list.value.length < 2 && (!isUpdateEmployee.value || !list?.value?.[0].employeeCode)) {
        const defaultEmployee = employeeList.value?.[0];
        for (const [index, item] of list.value.entries()) {
          item.employeeCode = defaultEmployee.value || '';
          item.employeeName = defaultEmployee.label || '';
          props.validateFields([['saleInfo', 'employeeInfoList', index, 'employeeCode']]);
        }
      }
      if (res?.data?.length < 1 && list.value?.[0]?.employeeCode) {
        employeeList.value = [];
        for (const [index, item] of list.value.entries()) {
          item.employeeCode = '';
          item.employeeName = '';
          props.validateFields([['saleInfo', 'employeeInfoList', index, 'employeeCode']]);
        }
      }
      isUpdateEmployee.value = false;
    } else {
      // 异常情况初始化业务员数据
      employeeList.value = [];
      for (const [index, item] of list.value.entries()) {
        item.employeeCode = '';
        item.employeeName = '';
        props.validateFields([['saleInfo', 'employeeInfoList', index, 'employeeCode']]);
      }
    }
  } catch (e) {
    console.log(e, '获取业务员失败');
  } finally {
    employeeLoading.value = false;
  }
});

const isMainChange = (e: RadioChangeEvent, i: number) => {
  try {
    list.value.forEach((employee, index) => {
      // 如果当前索引不是主业务员的索引，则清空其主业务员标志
      employee.mainEmployeeFlag = index === i ? '1' : '';
    });
  } catch (err) {
    console.log('主业务员状态变更时发生错误:', err); // 错误处理
  }
};
</script>

<style lang="less" scoped>
.percent-input {
  :deep(.ant-input-group-addon) {
    padding: 0;
  }
}
.ismain-radio {
  :deep(.ant-radio-wrapper-in-form-item) {
    margin-inline-end: 0px;
    & span + span {
      padding-inline-end: 0px;
    }
  }
}
span.anticon.anticon-exclamation-circle {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.55);
}
</style>
