<template>
  <div class="page-container">
    <main class="main-wrapper">
      <div v-show="bannerShow" class="mx-14px mt-[14px] relative">
        <img class="w-full" src="@/assets/images/insure/banner.png" />
        <div class="absolute cursor-pointer w-[16px] h-[16px] flex justify-center items-center rounded-[2px] top-[2px] right-[2px]" @click="bannerShow = false">
          <VueIcon :icon="IconTankuangGuanbiXuantingFont" class="text-[14px] text-[rgba(0,0,0,0.55)]" />
        </div>
      </div>
      <div :class="['flex', 'items-center', 'justify-between', 'mx-14px', bannerShow ? 'mt-[8px]' : 'mt-[14px]']">
        <!-- <a-tabs v-model:active-key="formState.baseInfo.renewalType" :tab-bar-gutter="16">
          <a-tab-pane key="0" tab="新保" />
          <a-tab-pane key="1" tab="续保" />
        </a-tabs> -->
      </div>
      <div class="bg-white mx-14px mb-[14px] px-[24px] pt-[16px] pb-[6px] rounded form-area">
        <a-spin :spinning="pageLoading">
          <a-form ref="formRef" :colon="false" :label-col="{ style: { width: pxToRem(110) } }" :model="formState">
            <!-- 基本信息 -->
            <div class="grid grid-cols-3 gap-x-16px bottom-line">
              <a-form-item label="出单机构" :name="['baseInfo', 'departmentCode']" :rules="[{ required: true, message: '请选择出单机构' }]">
                <department-search :disabled="hasSubmit || fromLastStep || isRisk" :dept-code="formState.baseInfo.departmentCode" @change-dept-code="changeDeptCode" />
              </a-form-item>
              <a-form-item class="col-span-2" label="是否共展" :name="['saleInfo', 'developFlg']">
                <!-- <a-radio-group v-model:value="formState.saleInfo.developFlg" :disabled="hasSubmit" @change="developFLgChange"> -->
                <a-radio-group v-model:value="formState.saleInfo.developFlg" disabled @change="developFLgChange">
                  <a-radio value="Y">是</a-radio>
                  <a-radio value="N">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <!-- 业务员 -->
            <div class="grid gap-x-16px bottom-line" :class="formState.saleInfo.developFlg === 'N' ? 'grid-cols-3' : 'grid-cols-4'">
              <EmployeeInfo v-model="formState.saleInfo.employeeInfoList" :department-code="formState.baseInfo.departmentCode" :develop-flg="formState.saleInfo.developFlg" :has-submit="hasSubmit" :validate-fields="formRef?.validateFields" :risk-asmt-no="formState.baseInfo.riskAsmtNo" @change="handleEmployeeChange" />
            </div>
            <div class="grid grid-cols-3 gap-x-16px bottom-line">
              <a-form-item label="渠道来源" :name="['saleInfo', 'channelSourceCode']" :rules="[{ required: true, message: '请选择渠道来源' }]">
                <a-select v-model:value="formState.saleInfo.channelSourceCode" :options="channelSourceList" allow-clear show-search option-filter-prop="label" placeholder="请选择" :disabled="hasSubmit" @change="changeChannelSource" />
              </a-form-item>
              <a-form-item label="渠道来源细分" :name="['saleInfo', 'channelSourceDetailCode']" :rules="[{ required: true, message: '请选择渠道来源细分' }]">
                <a-select v-model:value="formState.saleInfo.channelSourceDetailCode" :options="channelSourceDetailList" allow-clear show-search option-filter-prop="label" placeholder="请选择" :disabled="hasSubmit" @change="changeChannelDetailCode" />
              </a-form-item>
              <a-form-item v-if="showReportOrNot" label="上报团金业绩" :name="['saleInfo', 'isReportTuanjin']" :rules="[{ required: true, message: '请选择是否上报团金业绩' }]">
                <a-radio-group v-model:value="formState.saleInfo.isReportTuanjin" :disabled="hasSubmit">
                  <a-radio value="Y">是</a-radio>
                  <a-radio value="N">否</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item v-if="formState.saleInfo.isReportTuanjin === 'Y'" label="商机来源" :name="['saleInfo', 'businessOpportunitySource']" :rules="[{ required: true, message: '请选择商机来源' }]">
                <a-select v-model:value="formState.saleInfo.businessOpportunitySource" placeholder="请选择" :options="businessSourceList" allow-clear show-search :filter-option="false" :default-active-first-option="false" :loading="searchBusinessLoading" :disabled="hasSubmit" :style="{ width: '100%' }" @focus="resetBusinessSourceList" />
              </a-form-item>
              <a-form-item v-if="formState.saleInfo.isReportTuanjin === 'Y'" label="团金E立项号" :name="['saleInfo', 'tuanjinProjectNo']">
                <a-input v-model:value.trim="formState.saleInfo.tuanjinProjectNo" placeholder="请输入" allow-clear :disabled="hasSubmit" :maxlength="64" show-count />
              </a-form-item>
              <!-- 代理人 -->
              <AgentInfo v-if="sourceType === '2'" v-model="formState.saleInfo.agentInfoList" :department-code="formState.baseInfo.departmentCode" :channel-source-detail-code="formState.saleInfo.channelSourceDetailCode" :has-submit="hasSubmit" :validate-fields="formRef?.validateFields" />
              <!-- 经纪人 -->
              <BrokerInfo v-if="sourceType === '3'" v-model="formState.saleInfo.brokerInfoList" :department-code="formState.baseInfo.departmentCode" :has-submit="hasSubmit" />
              <a-form-item v-if="showIntroducer" label="主介绍人代码" :name="['saleInfo', 'presenterCode']">
                <div class="flex items-center">
                  <a-input v-model:value.trim="formState.saleInfo.presenterCode" placeholder="请输入" allow-clear :disabled="hasSubmit" />
                  <a-button v-if="!hasSubmit" class="ml-8px" type="primary" :loading="searchIntroducerLoading" @click="queryIntroducer">查询</a-button>
                </div>
              </a-form-item>
              <a-form-item v-if="showIntroducer" label="主介绍人名称" :name="['saleInfo', 'presenterName']">
                <a-input v-model:value.trim="formState.saleInfo.presenterName" placeholder="请输入" allow-clear :disabled="hasSubmit" />
              </a-form-item>
            </div>
            <div class="form-title">项目信息</div>
            <div class="grid grid-cols-3 gap-x-16px bottom-line">
              <a-form-item label="项目来源" :name="['saleInfo', 'isTenderBusiness']" :rules="[{ required: true, message: '请选择项目来源' }]">
                <a-select v-model:value="formState.saleInfo.isTenderBusiness" placeholder="请选择" :options="projectSourceList" allow-clear :disabled="hasSubmit" @change="changeIsTenderBusiness" />
              </a-form-item>
              <a-form-item label="项目名称" :name="['saleInfo', 'tenderBusinessNo']" :rules="[{ required: true, message: '请选择项目名称' }]">
                <a-select v-model:value="formState.saleInfo.tenderBusinessNo" placeholder="请选择" :options="projectNameList" allow-clear show-search :filter-option="false" :default-active-first-option="false" :loading="searchProjectLoading" :disabled="hasSubmit" @focus="resetTenderProjectList" @search="debounceSearchProject" @change="changeTenderBusiness" />
              </a-form-item>
              <a-form-item v-if="showCustomerAndRisk" label="客户类型" :name="['baseInfo', 'customerType']" :rules="[{ required: true, message: '请选择客户类型' }]">
                <a-select v-model:value="formState.baseInfo.customerType" placeholder="请选择" :options="customerTypeList" allow-clear show-search option-filter-prop="label" :disabled="hasSubmit" @change="changeCustomerType" />
              </a-form-item>
              <a-form-item v-if="showCustomerAbbrName && showCustomerAndRisk" label="客户简称" :name="['baseInfo', 'customerAbbrName']" :rules="[{ required: true, message: '请选择客户简称' }]">
                <a-select v-model:value="formState.baseInfo.customerAbbrName" placeholder="请选择" :options="customerNameList" allow-clear show-search option-filter-prop="label" :disabled="hasSubmit" @change="changeCustomerName" />
              </a-form-item>
              <a-form-item
                v-if="showCustomerAndRisk"
                label="风险程度"
                validate-first
                :name="['baseInfo', 'lossRate']"
                :rules="[
                  { required: true, message: '请输入风险程度' },
                  { pattern: /^\d{1,4}(\.\d{1,4})?$/, message: '输入数字且整数位不超过4位，小数位不超过4位', trigger: 'change' },
                ]"
              >
                <a-input v-model:value.trim="formState.baseInfo.lossRate" placeholder="请输入" :disabled="showCustomerAbbrName || hasSubmit" allow-clear>
                  <template #addonAfter>
                    <div class="w-[32px]">%</div>
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item v-if="showFutureCompany" label="期货公司名称" :name="['extendInfo', 'futureCompanyNo']" :rules="[{ required: true, message: '请选择客户简称' }]">
                <a-select v-model:value="formState.extendInfo.futureCompanyNo" placeholder="请选择" :options="FutureCompanyOptions" allow-clear show-search option-filter-prop="label" :disabled="hasSubmit" @change="changeFutureCompany" />
              </a-form-item>
            </div>
            <div class="form-title">产品信息</div>
            <div class="grid grid-cols-3 gap-x-16px bottom-line">
              <a-form-item class="col-span-2" label="产品名称" :name="['baseInfo', 'productCode']" :rules="[{ required: true, message: '请选择产品名称' }]">
                <div v-if="formState.baseInfo.showProductTextFlag === '1'">{{ formState.baseInfo.productCode }}{{ formState.baseInfo.productName }}</div>
                <product-select v-else ref="productRef" v-model:value="formState.baseInfo.productCode" :department-code="formState.baseInfo.departmentCode" :encode-key="formState.baseInfo.riskCode" module="accept" :disabled="hasSubmit || fromLastStep || isRisk" :agent-agreement-no="formState.saleInfo.agentInfoList[0].agentAgreementNo" :channel-source-code="formState.saleInfo.channelSourceCode" :show-tips="fromLastStep" @product-change="changeProduct" />
              </a-form-item>
              <a-form-item label="补贴类型" :name="['baseInfo', 'govSubsidyType']">
                <a-select v-model:value="formState.baseInfo.govSubsidyType" placeholder="请选择" :options="subsidyTypeList" disabled />
              </a-form-item>
              <a-form-item label="标的" :name="['baseInfo', 'riskCode']" class="col-span-3" :rules="[{ required: true, message: '请选择标的' }]">
                <a-form-item-rest>
                  <RiskCodeSelect ref="riskCodeRef" :default-value="defaultRisk" :default-mul-risk="defaultMulRisk" :department-code="formState.baseInfo.departmentCode" :disabled="hasSubmit || fromLastStep || isRisk" :is-multi-target="isMultiTarget" :multi-option="multiOption" :allow-edit="allowEdit" @change-value="changeRiskCode" />
                </a-form-item-rest>
              </a-form-item>
              <a-form-item v-if="showRiskAsmtNo" label="风险评估编号" :name="['baseInfo', 'riskAsmtNo']">
                <div v-if="isRisk">{{ formState?.baseInfo?.riskAsmtName }}-{{ formState?.baseInfo?.riskAsmtNo }}</div>
                <a-select v-else v-model:value="formState.baseInfo.riskAsmtNo" placeholder="请选择" :disabled="hasSubmit" :options="riskCodeOptions" allow-clear show-search :default-active-first-option="false" option-filter-prop="label" @change="handleRiskCodeChange" />
              </a-form-item>
              <a-form-item label="标的地址" :name="['riskAddressInfoList', 0, 'address']" class="col-span-3" :rules="[{ validator: validateAddress }]">
                <div class="flex">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="formState.riskAddressInfoList[0].province" v-model:city="formState.riskAddressInfoList[0].city" v-model:county="formState.riskAddressInfoList[0].county" v-model:town="formState.riskAddressInfoList[0].town" v-model:village="formState.riskAddressInfoList[0].village" class="grow mr-8px w-[70%]" :disabled="hasSubmit" @change-selected="changeRiskAddress" />
                  </a-form-item-rest>
                  <div class="w-[30%]">
                    <a-input v-model:value.trim="formState.riskAddressInfoList[0].address" placeholder="请输入" allow-clear :disabled="hasSubmit" />
                  </div>
                </div>
              </a-form-item>
            </div>
            <div class="form-title">其他信息</div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="是否共保" :name="['baseInfo', 'coinsuranceMark']" :rules="[{ required: true, message: '请选择是否共保' }]">
                <a-radio-group v-model:value="formState.baseInfo.coinsuranceMark" :disabled="hasSubmit" @change="changeCoinsuranceMark">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item v-if="formState.baseInfo.coinsuranceMark === '1'" label="是否主承" :name="['baseInfo', 'acceptInsuranceFlag']" :rules="[{ required: true, message: '请选择是否主承' }]">
                <a-radio-group v-model:value="formState.baseInfo.acceptInsuranceFlag" :disabled="hasSubmit">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="投保方式" :name="['baseInfo', 'applyPolicyType']" :rules="[{ required: true, message: '请选择投保方式' }]">
                <a-radio-group v-model:value="formState.baseInfo.applyPolicyType" :disabled="hasSubmit">
                  <a-radio value="2">组织投保</a-radio>
                  <a-radio value="1">非组织投保</a-radio>
                </a-radio-group>
              </a-form-item>
              <!-- <a-form-item v-if="showObservation" label="疾病观察期" validate-first :name="['baseInfo', 'diseaseObservation']" :rules="[{ required: true, message: '请输入疾病观察期' }, { pattern: /^(100|[1-9]?[0-9])$/, message: '请输入0～100范围内的数字', trigger: 'change' }]">
                <a-input v-model:value.trim="formState.baseInfo.diseaseObservation" placeholder="请输入" allow-clear :disabled="hasSubmit" @change="debounceChangeObservation" />
              </a-form-item> -->
              <a-form-item v-if="!isMultiTarget" label="承保数量" validate-first :name="['baseInfo', 'insuredNumber']" :rules="[{ validator: checkInsuredNumber, trigger: 'change' }]">
                <a-input-group compact>
                  <a-input v-model:value.trim="formState.baseInfo.insuredNumber" placeholder="请输入" allow-clear :disabled="hasSubmit" style="width: 60%" />
                  <a-select v-model:value.trim="formState.baseInfo.insuranceUnit" placeholder="请选择" :disabled="hasSubmit" :options="insuranceNumsUnitList" style="width: 40%" @change="handleUnit" />
                </a-input-group>
              </a-form-item>
              <a-form-item
                label="承保户数"
                validate-first
                :name="['baseInfo', 'farmersCount']"
                :rules="[
                  { required: true, message: '请输入承保户数' },
                  { pattern: /^[1-9]\d*$/, message: '请输入整数，必须大于0，不能超过10亿', trigger: 'change' },
                ]"
              >
                <a-input v-model:value.trim="formState.baseInfo.farmersCount" placeholder="请输入" allow-clear :disabled="hasSubmit" :maxlength="10" />
              </a-form-item>
            </div>
            <div v-if="isMultiTarget">
              <template v-for="(item, index) in formState.riskAgrList" :key="index">
                <div class="grid grid-cols-3 gap-x-16px">
                  <a-form-item :label="`标的${index + 1}`" validate-first :name="['riskAgrList', index, 'agriculturalRiskObjectDetailName']">
                    <a-input v-model:value="item.agriculturalRiskObjectDetailName" allow-clear disabled />
                  </a-form-item>
                  <a-form-item label="承保数量" validate-first :name="['riskAgrList', index, 'insuredNumber']" :rules="[{ validator: () => checkRiskInsuredNumber(index), trigger: 'change' }]">
                    <a-input-group compact>
                      <a-input v-model:value.trim="item.insuredNumber" placeholder="请输入" allow-clear :disabled="hasSubmit" style="width: 60%" />
                      <a-select v-model:value.trim="item.insureUnit" placeholder="请选择" :disabled="hasSubmit" :options="insuranceNumsUnitList" style="width: 40%" @change="(value, option) => handleRiskUnit(index, option)" />
                    </a-input-group>
                  </a-form-item>
                </div>
              </template>
            </div>
          </a-form>
        </a-spin>
      </div>
    </main>
    <footer v-if="route.query.origin !== 'insuranceTracking'" class="footer-wrapper">
      <a-button v-show="fromLastStep && showEditBtn && formState.baseInfo.saveStatus !== '0'" class="mr-8px" type="primary" @click="edit">修改</a-button>
      <a-button class="mr-8px" :loading="saveLoading" :disabled="hasSubmit" @click="save">暂存</a-button>
      <a-button class="mr-8px" type="primary" :loading="submitLoading" :disabled="hasSubmit" @click="submit">提交</a-button>
      <a-button v-show="hasSubmit" type="primary" :loading="formFillLoading" @click="redirectToFormFill">转投保</a-button>
    </footer>
  </div>
  <!-- 修改确认弹窗 -->
  <a-modal v-model:open="openVisible" :width="pxToRem(450)" @ok="handleConfirmOk">
    <div class="mb-[10px]">
      <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
      <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
    </div>
    <div>修改投保单信息需要重新提交审核，验标要求也会随之变化，请谨慎操作!</div>
  </a-modal>
  <a-modal v-model:open="resultVisible" :closable="false" :width="pxToRem(400)">
    <div class="flex flex-col items-center">
      <VueIcon :icon="IconCheckCircleFilledFont" class="text-[40px] text-[#07C160]" />
      <div class="mt-[18px]">
        {{ resultMsg }}
        <template v-if="formState.baseInfo.preRiskNo">
          {{ '，投保申请单号：' + formState.baseInfo.preRiskNo }}
          <a-tooltip placement="top">
            <template #title>
              <span>复制</span>
            </template>
            <VueIcon :icon="IconFuzhiFont" @click="copyText(formState.baseInfo.preRiskNo)" />
          </a-tooltip>
        </template>
      </div>
    </div>
    <template #footer>
      <a-button
        :loading="submitLoading"
        @click="
          resultVisible = false;
          resetForm();
        "
        >录下一单</a-button
      >
      <a-button
        v-show="hasSubmit"
        type="primary"
        :loading="formFillLoading"
        @click="
          resultVisible = false;
          redirectToFormFill();
        "
        >转投保</a-button
      >
      <a-button type="primary" :loading="submitLoading" @click="resultVisible = false">知道了</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconCheckCircleFilledFont, IconFuzhiFont, IconTankuangGuanbiXuantingFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { RadioChangeEvent } from 'ant-design-vue';
import { cloneDeep, debounce } from 'lodash-es';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import type { Rule } from 'ant-design-vue/es/form';
import EmployeeInfo from './components/EmployeeInfo.vue';
import AgentInfo from './components/AgentInfo.vue';
import BrokerInfo from './components/BrokerInfo.vue';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import type { FormState, CustomerOptions, PreRiskRes } from '@/apiTypes/insure';
import type { PageTabInject } from '@/apiTypes/interface.d';
import { pxToRem, copyText } from '@/utils/tools';
import { useUserStore } from '@/stores/useUserStore';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const bannerShow = ref(true);
const { userInfo } = useUserStore();
const route = useRoute();
const router = useRouter();
const { gateWay, service } = useRuntimeConfig().public || {};
const acceptApi = gateWay + service.accept;

const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const defaultRisk = ref('');
const defaultMulRisk = ref([]);

const { pageTabList } = inject<PageTabInject>('pageTab', {});

const formRef = ref();
const riskCodeRef = ref();
const productRef = ref();
// 是否需要机构联动标的地址
const updateRiskAddress = ref<boolean>(true);
// 是否为多标的
const isMultiTarget = ref<boolean>(false);
// 多标的option
const multiOption = ref<DefaultOptionType>([]);
// 是否展示修改按钮
const showEditBtn = ref<boolean>(true);
const formState = reactive<FormState>({
  baseInfo: {
    renewalType: '0', // 新保/续保
    departmentCode: '',
    productCode: '',
    productName: '',
    productVersion: '',
    govSubsidyType: undefined, // 补贴类型，产品带出
    applyPolicyType: '1', // 组织投保/非组织投保
    coinsuranceMark: '0', // 是否共保
    acceptInsuranceFlag: '', // 是否主承
    customerType: undefined, // 客户类型
    lossRate: '', // 风险程度
    riskCode: '',
    riskLevel: '',
    insuredNumber: '',
    farmersCount: '',
    insuredRate: '', // 费率
    totalInsuredAmount: '', // 总保额
    yearOrderRateAll: '', // 总跟单费用率
    saveStatus: '',
    insuranceUnit: undefined,
    insuredUnitChName: '',
    showProductTextFlag: '',
    riskAsmtNo: '',
  },
  saleInfo: {
    developFlg: 'N', // 是否共展
    channelSourceCode: undefined, // 渠道来源
    channelSourceDetailCode: undefined, // 渠道来源细分
    isTenderBusiness: '', // 项目来源
    tenderBusinessName: '', // 项目名称
    tenderBusinessNo: undefined, // 项目名称-code
    isReportTuanjin: '',
    businessOpportunitySource: '', // 商机来源
    tuanjinProjectNo: '',
    presenterCode: '', // 主介绍人code
    presenterName: '', // 主介绍人name
    employeeInfoList: [{ employeeCode: undefined, employeeName: undefined, employeeProfCertifNo: undefined, commisionScale: undefined, mainEmployeeFlag: '1' }],
    agentInfoList: [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }],
    brokerInfoList: [{ brokerCode: '', brokerName: '', agencySaleName: '', agencySaleProfCertifNo: '' }],
  },
  riskAddressInfoList: [{ province: '', provinceName: '', city: '', cityName: '', town: '', townName: '', county: '', countyName: '', village: '', villageName: '', address: '' }],
  extendInfo: { preRiskNo: '', fixNo: '', farmerListNo: '', futureCompanyNo: '', futureCompanyName: '' },
  riskAgrList: [
    {
      agriculturalRiskObjectDetailCode: '', // 农险标的细分代码
      agriculturalRiskObjectDetailName: '', // 农险标的细分名称
      insuredNumber: '', // 投保数量
      insureUnit: '', //投保数量单位
    },
  ],
});
// 是否确认修改
const confirmEdit = ref<boolean>(false);
// 是否显示修改确认弹窗
const openVisible = ref<boolean>(false);
const hasSubmit = computed(() => (formState.baseInfo.saveStatus === '1' || route.query.origin === 'insuranceTracking') && !confirmEdit.value);
const fromLastStep = computed(() => !!route.query.applyPolicyNo);
const mainEmployee = computed(() => formState.saleInfo.employeeInfoList.find((employee) => employee.mainEmployeeFlag === '1')?.employeeCode);
// 获取渠道类型：1、直接渠道  2、代理渠道  3、经纪渠道
const sourceType = computed(() => (formState.saleInfo.channelSourceDetailCode ? formState.saleInfo.channelSourceDetailCode.split(',')?.[1] : ''));
// 是否展示风险评估编号 route.riskAsmtNo存在（代表从风险评估查询页面跳转）或者商业性非从共
const showRiskAsmtNo = computed(() => {
  return route?.query?.riskAsmtNo || (!(formState?.baseInfo?.acceptInsuranceFlag === '0') && formState?.baseInfo?.govSubsidyType === '3');
});
// 是否为风险评估页面跳转
const isRisk = computed(() => !!route?.query?.riskAsmtNo);
// 请求基础数据：项目来源、客户类型、补贴类型
const projectSourceList = ref<Array<SelectOptions>>([]);
const subsidyTypeList = ref<Array<SelectOptions>>([]); // 补贴类型关联产品
const customerTypeList = ref<Array<SelectOptions>>([]);
const getBaseInfo = async () => {
  try {
    const res = await $post<{ projectSourceList: Array<SelectOptions>; customerTypeList: Array<SelectOptions>; subsidyTypeList: Array<SelectOptions> }>('/api/common/getParmBaseConstantConf', ['customerType', 'isTenderBusiness', 'subsidyType']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      projectSourceList.value = res.data.projectSourceList || [];
      formState.saleInfo.isTenderBusiness = '1';
      customerTypeList.value = res.data.customerTypeList || [];
      formState.baseInfo.customerType = undefined;
      subsidyTypeList.value = res.data.subsidyTypeList || [];
    } else {
      projectSourceList.value = [];
      customerTypeList.value = [];
      subsidyTypeList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
// 页面一进入就查询基础数据
getBaseInfo();
const pageLoading = ref(false);
// 有投保申请单号则查询详情
const queryRiskDetail = async () => {
  const id = route.query.id;
  if (id) {
    pageLoading.value = true;
    try {
      const res = await $getOnClient<FormState>(`${acceptApi}/web/preRisk/getPreRiskInfo/${id}`);
      if (res?.code !== SUCCESS_CODE || !res.data) {
        message.error(res?.msg || '查询详情失败，请稍后再试');
        formState.baseInfo.departmentCode = defaultDeptCode.value;
        return;
      }
      const { baseInfo, saleInfo, riskAddressInfoList, extendInfo, riskAgrList = [] } = res.data;
      formState.baseInfo = baseInfo;
      formState.saleInfo = {
        ...saleInfo,
        agentInfoList: saleInfo.agentInfoList.length < 1 ? [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }] : saleInfo.agentInfoList,
      };
      formState.riskAddressInfoList = riskAddressInfoList || [{}];
      formState.extendInfo = extendInfo;
      defaultRisk.value = baseInfo.riskCode;
      updateRiskAddress.value = false;
      if (riskAgrList.length > 0) {
        isMultiTarget.value = true;
        formState.riskAgrList = riskAgrList;
        defaultMulRisk.value = riskAgrList?.map((item) => item.agriculturalRiskObjectDetailCode);
      }
    } catch (err) {
      console.log(err);
    } finally {
      pageLoading.value = false;
    }
  }
};

// 有风险评估单号就查看详情并填充
const queryRiskAssessDetail = async () => {
  const id = route.query.riskAsmtNo;
  if (id) {
    formState.baseInfo.riskAsmtNo = id;
    pageLoading.value = true;
    try {
      const res = await $getOnClient<FormState>(`${acceptApi}/web/risk/rpt/queryByRiskAsmtNoForApply?riskAsmtNo=${id}`);
      if (res?.code !== SUCCESS_CODE || !res.data) {
        message.error(res?.msg || '查询风险评估详情失败，请稍后再试');
        formState.baseInfo.departmentCode = defaultDeptCode.value;
        return;
      }
      const { baseInfo, riskAddressInfoList, riskAgrList = [] } = res.data;
      formState.baseInfo = {
        ...formState.baseInfo,
        ...baseInfo,
      };
      formState.riskAddressInfoList = riskAddressInfoList || [{}];
      defaultRisk.value = baseInfo.riskCode;
      // updateRiskAddress.value = false;
      if (riskAgrList.length > 0) {
        isMultiTarget.value = true;
        formState.riskAgrList = riskAgrList;
        defaultMulRisk.value = riskAgrList?.map((item) => item.agriculturalRiskObjectDetailCode);
      }
    } catch (err) {
      console.log(err);
    } finally {
      pageLoading.value = false;
    }
  }
};
const riskCodeOptions = ref([]);

// 根据产品和机构获取风险评估编号数据
watchEffect(async () => {
  if (showRiskAsmtNo.value && !route?.query?.riskAsmtNo) {
    const url = gateWay + service.accept + '/web/risk/rpt/queryRiskAsmtNos';
    const params = {
      aplyDeptNo: formState?.baseInfo?.departmentCode,
      prodNo: formState?.baseInfo?.productCode,
      riskAsmtNo: '',
      salemanNo: formState.saleInfo.employeeInfoList?.[0]?.employeeCode,
    };
    try {
      const res = await $post(url, params);
      if (res && res.code === SUCCESS_CODE) {
        riskCodeOptions.value = res.data;
      } else {
        riskCodeOptions.value = [];
        message.warning(res.msg);
      }
    } catch (error) {
      console.log(error);
    }
  }
});

// 风险编码改变需要重新更新投保第一屏数据
const handleRiskCodeChange = async (value) => {
  if (value) {
    pageLoading.value = true;
    try {
      const res = await $getOnClient<FormState>(`${acceptApi}/web/risk/rpt/queryByRiskAsmtNoForApply?riskAsmtNo=${value}`);
      if (res?.code !== SUCCESS_CODE || !res.data) {
        message.error(res?.msg || '查询风险评估详情失败，请稍后再试');
        formState.baseInfo.departmentCode = defaultDeptCode.value;
        return;
      }
      const { baseInfo, riskAddressInfoList } = res.data;
      // 不更新机构、业务员、产品、标的
      formState.baseInfo = {
        ...formState.baseInfo,
        applyPolicyType: baseInfo.applyPolicyType,
        coinsuranceMark: baseInfo.coinsuranceMark,
        farmersCount: baseInfo.farmersCount,
        insuranceUnit: baseInfo.insuranceUnit,
        insuranceUnitCnName: baseInfo.insuranceUnitCnName,
        insuredNumber: baseInfo.insuredNumber,
        riskAsmtName: baseInfo.riskAsmtName,
        riskAsmtNo: baseInfo.riskAsmtNo,
      };
      formState.riskAddressInfoList = riskAddressInfoList || [{}];
      // defaultRisk.value = baseInfo.riskCode;
      // updateRiskAddress.value = false;
      // if (riskAgrList.length > 0) {
      //   isMultiTarget.value = true;
      //   formState.riskAgrList = riskAgrList;
      //   defaultMulRisk.value = riskAgrList?.map((item) => item.agriculturalRiskObjectDetailCode);
      // }
    } catch (err) {
      console.log(err);
    } finally {
      pageLoading.value = false;
    }
  }
};

// 修改业务员信息需要重置风险评估编号
const handleEmployeeChange = () => {
  if (!route?.query?.riskAsmtNo && !fromLastStep.value) {
    formState.baseInfo.riskAsmtNo = '';
  }
};
watch(
  () => sourceType.value,
  () => {
    // 非代理协议清空代理人信息
    if (sourceType.value !== '2') {
      formState.saleInfo.agentInfoList = [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
    }
  },
);
const timeTemp = ref('');
const checkPageCanEdit = () => {
  // 重置修改按钮显示状态
  showEditBtn.value = true;
  // 从菜单点击进入
  // 从菜单点击进入并录入到第二屏，关闭第二屏页签回到第一屏
  const keys = Object.keys(route.query || {});
  const item = (pageTabList && pageTabList.value ? pageTabList.value : []).filter((val) => val.path === '/insuranceFormFill');
  if (keys.length === 1 && keys[0] === 't') {
    // 链接没有参数
    // 第二屏存在，则一直不能恢复可填状态
    if (!item.length && hasSubmit.value) {
      formState.baseInfo.saveStatus = '';
      formState.baseInfo.preRiskNo = '';
    }
  }
  timeTemp.value = String(route.query.t);
};
onMounted(() => {
  timeTemp.value = String(route.query.t);
  // queryRiskDetail();
  if (!route?.query?.id && !route?.query?.riskAsmtNo) {
    formState.baseInfo.departmentCode = defaultDeptCode.value;
  }
});
// 切换tab刷新
onActivated(() => {
  checkPageCanEdit();
  queryRiskDetail();
  queryRiskAssessDetail();
});
// 选择机构
const changeDeptCode = (value: string) => {
  formState.baseInfo.departmentCode = value;
  defaultRisk.value = '';
  if (!route?.query?.riskAsmtNo && !fromLastStep.value) {
    formState.baseInfo.riskAsmtNo = '';
  }
};
watch(
  () => formState.baseInfo.departmentCode,
  async () => {
    if (formState.baseInfo.departmentCode) {
      try {
        // 机构变动，回填标的地址 风险评估不用回填
        if (updateRiskAddress.value && !route?.query?.riskAsmtNo) {
          formState.saleInfo.tenderBusinessNo = ''; // 修改机构项目名称清空
          const addressRes = await $getOnClient<{ province: string; provinceName: string; city: string; cityName: string; town: string; townName: string; county: string; countyName: string; village: string; villageName: string; address: string }>(`${gateWay}${service.administrate}/public/getAddressByDepartmentCode`, { departmentCode: formState.baseInfo.departmentCode });
          if (addressRes && addressRes.code === SUCCESS_CODE) {
            if (addressRes?.data) {
              formState.riskAddressInfoList = [addressRes.data];
              formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);
            } else {
              formState.riskAddressInfoList = [{ province: '', provinceName: '', city: '', cityName: '', town: '', townName: '', county: '', countyName: '', village: '', villageName: '', address: '' }];
            }
          }
        }
        updateRiskAddress.value = true;
      } catch (err) {
        console.log('获取地址失败', err);
      }
    }
  },
);
// 选择渠道来源
const channelSourceList = ref<Array<SelectOptions>>([]);
const channelSourceDetailList = ref<Array<SelectOptions>>([]);
// 重置渠道来源默认值
const resetChannelSourceInit = () => {
  let code = '';
  let children: SelectOptions[] = [];

  if (channelSourceList.value.length === 0) {
    console.log('渠道来源列表为空');
    return;
  }
  const selectedSource = formState.saleInfo.channelSourceCode ? channelSourceList.value.find((val) => val.value === formState.saleInfo.channelSourceCode) : channelSourceList.value[0];
  if (selectedSource) {
    code = selectedSource.value || '';
    children = selectedSource.children || [];
  } else {
    code = channelSourceList.value[0].value || '';
    children = channelSourceList.value[0].children || [];
  }

  formState.saleInfo.channelSourceCode = code;
  formRef.value.validateFields([['saleInfo', 'channelSourceCode']]);
  // 更新渠道来源细分
  channelSourceDetailList.value = children;
};
// 主业务员变动影响渠道
watchEffect(async () => {
  if (mainEmployee.value) {
    try {
      const res = await $getOnClient('/api/common/getChannelSourceList', { employeeCode: mainEmployee.value });
      if (res && res.code === '000000' && Array.isArray(res.data)) {
        channelSourceList.value = res.data;
      } else {
        channelSourceList.value = [];
      }
      resetChannelSourceInit();
    } catch (err) {
      console.log(err);
    }
  }
});
// 是否共展改变
const developFLgChange = (e: RadioChangeEvent) => {
  // 选择否 初始化共展业务员信息
  if (e?.target?.value === 'N') {
    formState.saleInfo.employeeInfoList = [{ employeeCode: '', employeeName: '', employeeProfCertifNo: '', commisionScale: '', mainEmployeeFlag: '1' }];
  }
};
// 选择渠道来源
const changeChannelSource = (_: SelectValue, option: DefaultOptionType) => {
  formState.saleInfo.channelSourceCode = option?.value as string;
  channelSourceDetailList.value = option?.children as SelectOptions[];
};
// 选择渠道来源细分
const changeChannelDetailCode = () => {
  formState.saleInfo.brokerInfoList = [{ brokerCode: '', brokerName: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
  formState.saleInfo.agentInfoList = [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
  // 初始化经纪人和代理人
  // if (sourceType.value === '3') {
  //   formState.saleInfo.brokerInfoList = [{ brokerCode: '', brokerName: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
  //   formState.saleInfo.agentInfoList = [];
  // } else if (sourceType.value === '2') {
  //   formState.saleInfo.agentInfoList = [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
  //   formState.saleInfo.brokerInfoList = [];
  // } else if (sourceType.value === '1') {
  //   formState.saleInfo.brokerInfoList = [];
  //   formState.saleInfo.agentInfoList = [];
  // }
};
// 回填渠道来源细分
watch(channelSourceDetailList, (val) => {
  // 渠道来源细分数组为空则清空选中项
  if (val?.length === 0) {
    formState.saleInfo.channelSourceDetailCode = '';
  } else {
    // 当前选中值在列表中存在则值不变，否则默认选第一项
    formState.saleInfo.channelSourceDetailCode = formState.saleInfo.channelSourceDetailCode && val.some((item) => item.value === formState.saleInfo.channelSourceDetailCode) ? formState.saleInfo.channelSourceDetailCode : val[0].value;
    formRef.value.validateFields([['saleInfo', 'channelSourceDetailCode']]);
  }
});
// 是否显示期货公司选择框
const showFutureCompany = ref<boolean>(false);
const FutureCompanyOptions = ref<SelectOptions[]>([]);
// 获取期货公司下拉选项
const getOptions = async () => {
  const url = `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`;
  try {
    const res = await $postOnClient<SelectOptions[]>(url, ['AGRQHGS']);
    if (res && res.code === SUCCESS_CODE) {
      FutureCompanyOptions.value = res.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
getOptions();
watch(
  () => formState.baseInfo.productCode,
  () => {
    if (!formState.baseInfo.productCode) {
      isMultiTarget.value = false;
    }
  },
);
// 默认不展示
const showCustomerAndRisk = ref<boolean>(false);
// 获取隐藏客户字段开关
const getCustomerSwitch = async () => {
  const url = gateWay + service.administrate + '/switchMulti/checkSwitchResult';
  const params = {
    switchCode: 'CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH',
    allowanceTypeCode: formState?.baseInfo?.govSubsidyType || '',
    agriculturalRiskObjectClassCode: formState?.baseInfo?.riskCode?.[0] || '',
    departmentNo: formState?.baseInfo?.departmentCode,
  };
  try {
    const res = await $post(url, params);
    if (res && res.code === SUCCESS_CODE) {
      showCustomerAndRisk.value = res.data;
    } else {
      showCustomerAndRisk.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};
// 根据机构及标的地址控制开关显隐客户类型及风险程度字段
const debounceGetCustomerSwitch = debounce(getCustomerSwitch, 1000);
watch([() => formState?.baseInfo?.govSubsidyType, () => formState?.baseInfo?.riskCode, () => formState?.baseInfo?.departmentCode], () => {
  if (formState?.baseInfo.departmentCode) {
    debounceGetCustomerSwitch();
  }
});

// 选择产品名称
const changeProduct = async (value: string, option: DefaultOptionType) => {
  if (!route?.query?.riskAsmtNo && !fromLastStep.value) {
    formState.baseInfo.riskAsmtNo = '';
  }
  formState.baseInfo.productCode = option?.value as string;
  formState.baseInfo.productName = option?.realLabel || '';
  formState.baseInfo.productVersion = option?.version || '';
  formState.baseInfo.govSubsidyType = option?.subsidyType || '';
  //  5级标的有值，则不回显标的
  if (Number(formState.baseInfo.riskLevel) !== 5) {
    formState.baseInfo.riskCode = option?.targetType || '';
    defaultRisk.value = formState.baseInfo.riskCode;
  }
  formRef.value.validateFields([['baseInfo', 'riskCode']]);
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const data = {
    productCode: option?.value,
    version: option?.version,
  };
  const res = await $postOnClient<{ subsidyType: string; planList: Record<string, string>[] }>(url, data);
  if (res && res.code === SUCCESS_CODE) {
    formState.baseInfo.govSubsidyType = res.data.subsidyType || '';
    // 是否为多标产品
    if (res?.data?.targetTypeList?.length > 1) {
      isMultiTarget.value = true;
      multiOption.value = res?.data?.targetTypeList.map((item) => ({
        encodeValue: `${item.fourLevTargetName}/${item.fiveLevTargetName}`,
        encodeKey: item.fiveLevTargetType,
      }));
      // 多标产品改变时需要重置4级标的的值
      defaultRisk.value = '';
    } else {
      isMultiTarget.value = false;
      multiOption.value = [];
    }
    const len = res.data.planList.findIndex((item) => item.isMain === '1' && item.planType === '05');
    showFutureCompany.value = len !== -1;
  }
};

// 投保数量单位，如果机构改变&&route没有id则清空
watchEffect(async () => {
  if (formState.baseInfo.departmentCode) {
    getParmBaseConstantConfData();
    if (!route.query?.id) {
      formState.baseInfo.insuredUnitChName = '';
      formState.baseInfo.insuranceUnit = undefined;
    }
  }
  // 有多标的获取多标的
  if (formState.baseInfo.showProductTextFlag === '1') {
    const url = gateWay + service.accept + '/web/applicationForm/productInfos';
    const data = {
      productCode: formState.baseInfo.productCode,
      version: formState.baseInfo.productVersion,
    };
    const res = await $postOnClient<{ subsidyType: string; planList: Record<string, string>[] }>(url, data);
    if (res && res.code === SUCCESS_CODE) {
      formState.baseInfo.govSubsidyType = res.data.subsidyType || '';
      // 是否为多标产品
      if (res?.data?.targetTypeList?.length > 1) {
        isMultiTarget.value = true;
        multiOption.value = res?.data?.targetTypeList.map((item) => ({
          encodeValue: `${item.fourLevTargetName}/${item.fiveLevTargetName}`,
          encodeKey: item.fiveLevTargetType,
        }));
        // 多标产品改变时需要重置4级标的的值
        defaultRisk.value = '';
      } else {
        isMultiTarget.value = false;
        multiOption.value = [];
      }
      const len = res.data.planList.findIndex((item) => item.isMain === '1' && item.planType === '05');
      showFutureCompany.value = len !== -1;
    }
  }
});

// 获取期货公司名称
const changeFutureCompany = (value: string, options: SelectOptions) => {
  formState.extendInfo.futureCompanyName = options.label;
};
// 期货公司隐藏时，清空已选值
watch(showFutureCompany, (val) => {
  if (!val) {
    formState.extendInfo.futureCompanyName = '';
    formState.extendInfo.futureCompanyNo = '';
  }
});
// 选择标的
const changeRiskCode = (value: string, level: string, option) => {
  // 多标不需要更新
  if (String(level) === '4' && isMultiTarget.value) {
    if (option?.length > 0) {
      const riskAgrList = cloneDeep(formState.riskAgrList);
      formState.riskAgrList = option.map((item) => {
        const riskItem = riskAgrList?.find((riskAgr) => riskAgr.agriculturalRiskObjectDetailCode === item.encodeKey);
        if (riskItem?.agriculturalRiskObjectDetailCode) {
          return {
            agriculturalRiskObjectDetailCode: item.encodeKey, // 农险标的细分代码
            agriculturalRiskObjectDetailName: item.encodeValue, // 农险标的细分名称
            insuredNumber: riskItem?.insuredNumber, // 投保数量
            insureUnit: riskItem?.insureUnit, //投保数量单位
          };
        } else {
          return {
            agriculturalRiskObjectDetailCode: item.encodeKey, // 农险标的细分代码
            agriculturalRiskObjectDetailName: item.encodeValue, // 农险标的细分名称
            insuredNumber: '', // 投保数量
            insureUnit: insuranceNumsUnitList.value?.find((item) => item.defaultSelect)?.value || '', //投保数量单位
          };
        }
      });
    }
  } else {
    formState.baseInfo.riskCode = value;
    formState.baseInfo.riskLevel = level;
    formRef.value.validateFields([['baseInfo', 'riskCode']]);
  }
};
type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';
// 选择标的地址
const changeRiskAddress = async (option: { label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    formState.riskAddressInfoList[0][`${type}Name`] = option?.label || '';
  }

  // 拼接地址时过滤掉undefined/null值，避免出现NaN
  const addressParts = [formState.riskAddressInfoList[0]?.provinceName, formState.riskAddressInfoList[0]?.cityName, formState.riskAddressInfoList[0]?.countyName, formState.riskAddressInfoList[0]?.townName, formState.riskAddressInfoList[0]?.villageName].filter(Boolean);

  formState.riskAddressInfoList[0].address = addressParts.join('') || '';

  formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);

  if (['county'].includes(type)) {
    const url = gateWay + service.accept + '/policy/insuranceRegionValid';
    const res = await $postOnClient<{ subsidyType: string }>(url, formState);
    if (res && res.code !== SUCCESS_CODE) {
      message.info(res.msg || '');
    }
  }
};
// 监听标的变动，如果是养殖险则显示疾病观察期，否则清空疾病观察期
const showObservation = ref<boolean>(false);
watchEffect(() => {
  if (!isMultiTarget.value && formState.baseInfo?.riskCode?.startsWith('B')) {
    showObservation.value = true;
  } else {
    showObservation.value = false;
    formState.baseInfo.diseaseObservation = '';
  }
});
// 修复出单机构校验问题
watch(
  () => formState.baseInfo.departmentCode,
  () => {
    formRef?.value?.validate([['baseInfo', 'departmentCode']]);
  },
);
// 是否上报团金业绩
const showReportOrNot = ref<boolean>(false);
// 是否显示 上报团金业绩
// 渠道来源对应关系：
// 1——业务员、2——公司业务、3——公司业务部、4——专员自身、5——管理人员、6——新渠道、7——综合开拓、8——银保渠道、9——车商
// C——重点客户、E——营销员、F——专业代理、H——个人代理人、J——传统、A——营销员、G——代理渠道、B——非重客经纪、Z——创展渠道、X——移动渠道、U——联合渠道
// 渠道来源明细对应关系：
// K——自身业务、A——兼业代理人业务、G——平安养老险、S——证券、X——信托、F——专业代理人业务、B——传统经纪业务、C——合作临分业务、T——众安代理、Z——众安直通、J——众安经纪
// 0——综拓其他、1——网上销售、2——电话销售、3——门店、4——创展其他、5——战略联盟、N——代理其他、Q——零售、H——个人代理人业务、P——创保、D——个险收展、L——个险营销、R——移动
// M——车商其他、V——车商、U——网点业务、W——平台合作业务
watch(
  () => formState.saleInfo.channelSourceCode,
  (val) => {
    if (val && !['1', '2', '3', '4', '5', '6', '7', '9', 'E', 'F', 'H', 'A', 'G', 'Z', 'X', 'U'].includes(val)) {
      showReportOrNot.value = true;
    } else {
      showReportOrNot.value = false;
      formState.saleInfo.isReportTuanjin = '';
      formState.saleInfo.businessOpportunitySource = '';
      formState.saleInfo.tuanjinProjectNo = '';
    }
  },
);
watch(
  () => formState.saleInfo.isReportTuanjin,
  (val) => {
    if (val !== 'Y') {
      formState.saleInfo.businessOpportunitySource = '';
      formState.saleInfo.tuanjinProjectNo = '';
    }
  },
);
// 查询商机来源
const businessSourceList = ref<SelectOptions[]>([]);
const allBusinessSourceList = ref<SelectOptions[]>([]); // 存储全量商机来源（第一页），用来搜索后给下拉列表重新赋值
const searchBusinessLoading = ref(false);
watchEffect(async () => {
  if (formState.saleInfo.isReportTuanjin === 'Y' && formState.baseInfo.departmentCode) {
    searchBusinessLoading.value = true;
    try {
      const fetchUrl = gateWay + service.accept + '/saleInfo/queryBizSourceList';
      const res = await $getOnClient(fetchUrl);
      searchBusinessLoading.value = false;
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        businessSourceList.value = res.data;
        allBusinessSourceList.value = res.data;
      } else {
        businessSourceList.value = [];
        allBusinessSourceList.value = [];
      }
      initBusinessSource();
    } catch (err) {
      console.log(err);
    } finally {
      searchBusinessLoading.value = false;
    }
  }
});

// 根据关键字远程搜索商机来源
// const handleSearchBusiness = async (keyword: string) => {
//   if (keyword && formState.saleInfo.isReportTuanjin === 'Y' && formState.baseInfo.departmentCode) {
//     try {
//       searchBusinessLoading.value = true;
//       const fetchUrl = gateWay + service.accept + '/saleInfo/queryBizSourceList';
//       const res = await $getOnClient(fetchUrl);
//       if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
//         businessSourceList.value = res.data;
//       } else {
//         businessSourceList.value = [];
//       }
//     } catch (err) {
//       console.log(err);
//     } finally {
//       searchBusinessLoading.value = false;
//     }
//   }
// };
// const debounceSearchBusiness = debounce(handleSearchBusiness, 500);

// 聚焦时商机来源的下拉选项重新赋值为全量商机来源（第一页）
const resetBusinessSourceList = () => {
  businessSourceList.value = JSON.parse(JSON.stringify(allBusinessSourceList.value));
};

// 商机来源：默认选中第一项
const initBusinessSource = () => {
  if (formState.saleInfo.businessOpportunitySource) {
    const index = businessSourceList.value?.findIndex((item) => item.value === formState.saleInfo.businessOpportunitySource);
    if (index > -1) {
      formState.saleInfo.businessOpportunitySource = businessSourceList.value?.[index]?.value ?? '';
    } else {
      formState.saleInfo.businessOpportunitySource = businessSourceList.value?.[0]?.value ?? '';
    }
  } else {
    formState.saleInfo.businessOpportunitySource = businessSourceList.value?.[0]?.value ?? '';
  }
};

// 主介绍人
const showIntroducer = ref(false);
watchEffect(() => {
  const channelSourceCode = formState.saleInfo.channelSourceCode;
  const channelSourceDetailCode = formState.saleInfo.channelSourceDetailCode?.split(',')?.[0] ?? '';
  if ((channelSourceCode === '7' && ['D', 'L'].includes(channelSourceDetailCode)) || (channelSourceCode === 'C' && channelSourceDetailCode === 'G')) {
    showIntroducer.value = true;
  } else {
    showIntroducer.value = false;
  }
});
// 根据主介绍人代码查询主介绍人名称
const searchIntroducerLoading = ref(false);
const queryIntroducer = async () => {
  const presenterCode = formState.saleInfo.presenterCode;
  if (!presenterCode) {
    message.error('请输入主介绍人代码');
    return; // 直接返回，避免后续不必要的代码执行
  }

  try {
    searchIntroducerLoading.value = true;
    const res = await $getOnClient<{ agentName: string; business: string }>('/api/accept/getIntroducerInfoByAgent', {
      agentCode: presenterCode,
    });
    if (res?.code !== SUCCESS_CODE) {
      message.error(res?.msg || '查询失败'); // 默认错误消息
      return;
    }
    const { data } = res;
    if (!data) {
      message.error('主介绍人不存在');
      return;
    }
    const { agentName, business } = data;
    const channelDetailCode = formState.saleInfo.channelSourceDetailCode?.split(',')[0];
    if ((channelDetailCode === 'L' || channelDetailCode === 'D') && business === 'LBS') {
      formState.saleInfo.presenterName = agentName;
    } else if (channelDetailCode === 'G' && business === 'GBS') {
      formState.saleInfo.presenterName = agentName;
    } else {
      formState.saleInfo.presenterName = '';
      message.error(`您所录入的主介绍人不属于${channelDetailCode === 'L' || channelDetailCode === 'D' ? '寿险系列' : '养老险系列'}`);
    }
  } catch (err) {
    console.log(err);
  } finally {
    searchIntroducerLoading.value = false;
  }
};
// 根据项目来源和机构编码，查询项目名称
const projectNameList = ref<Array<SelectOptions>>([]);
const allProjectNameList = ref<Array<SelectOptions>>([]); // 存储全量项目名称（第一页），用来搜索后给下拉列表重新赋值
const searchProjectLoading = ref(false);

watchEffect(async () => {
  if (formState.baseInfo.departmentCode && formState.saleInfo.isTenderBusiness !== '') {
    try {
      searchProjectLoading.value = true;
      const res = await $postOnClient('/api/accept/queryTenderProjectList', {
        departmentCode: formState.baseInfo.departmentCode,
        keyword: '',
        pageNum: 1,
        pageSize: 50,
        tenderSource: formState.saleInfo.isTenderBusiness,
      });
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        projectNameList.value = res.data;
        allProjectNameList.value = res.data;
      } else {
        projectNameList.value = [];
        allProjectNameList.value = [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      searchProjectLoading.value = false;
    }
  }
});
// 改变项目来源
const changeIsTenderBusiness = () => {
  formState.saleInfo.tenderBusinessNo = '';
};
// 根据关键字远程搜索项目名称
const handleSearchProject = async (keyword: string) => {
  if (keyword && formState.baseInfo.departmentCode && formState.saleInfo.isTenderBusiness !== '') {
    try {
      searchProjectLoading.value = true;
      const res = await $postOnClient('/api/accept/queryTenderProjectList', {
        departmentCode: formState.baseInfo.departmentCode,
        keyword,
        pageNum: 1,
        pageSize: 50,
        tenderSource: formState.saleInfo.isTenderBusiness,
      });
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        projectNameList.value = res.data;
      } else {
        projectNameList.value = [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      searchProjectLoading.value = false;
    }
  }
};
const debounceSearchProject = debounce(handleSearchProject, 500);

// 聚焦时项目名称的下拉选项重新赋值为全量项目名称（第一页）
const resetTenderProjectList = () => {
  projectNameList.value = JSON.parse(JSON.stringify(allProjectNameList.value));
};

// 选择项目名称
const changeTenderBusiness = (value: SelectValue, option: DefaultOptionType) => {
  formState.saleInfo.tenderBusinessNo = option?.value as string;
  formState.saleInfo.tenderBusinessName = option?.label || '';
};

const showCustomerAbbrName = ref<boolean>(false); // 潜力客户、集团客户，显示客户简称，并带出风险程度且不可编辑
const changeCustomerType = (value: SelectValue) => {
  if (['02', '03'].includes(value as string)) {
    showCustomerAbbrName.value = true;
  } else {
    showCustomerAbbrName.value = false;
    formState.baseInfo.customerAbbrName = '';
    formState.baseInfo.lossRate = '';
  }
};

// 根据客户类型查询客户信息
const customerNameList = ref<Array<CustomerOptions>>([]);
watchEffect(async () => {
  if (showCustomerAbbrName.value && formState.baseInfo.departmentCode) {
    const params = {
      deptCode: formState.baseInfo.departmentCode,
      customerType: formState.baseInfo.customerType,
    };
    try {
      const url = gateWay + service.accept + '/saleInfo/queryCustomerList';
      const res = await $postOnClient(url, params);
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        if (res.data.length > 0) {
          customerNameList.value = res.data.map((item) => {
            return {
              label: item.customerAbbrName,
              value: item.customerAbbrName,
              lossRate: item.lossRate,
            };
          });
        } else {
          // 没数据，清空客户简称和风险程度
          customerNameList.value = [];
          formState.baseInfo.lossRate = '';
          formState.baseInfo.customerAbbrName = '';
        }
      } else {
        // 没数据，清空客户简称和风险程度
        customerNameList.value = [];
        formState.baseInfo.lossRate = '';
        formState.baseInfo.customerAbbrName = '';
      }
      if (customerNameList.value.findIndex((item) => item.value === formState.baseInfo.customerAbbrName) === -1) {
        formState.baseInfo.customerAbbrName = customerNameList.value?.[0].value ?? '';
        formState.baseInfo.lossRate = customerNameList.value?.[0].lossRate ?? '';
        formRef.value.validateFields([['baseInfo', 'lossRate']]);
      }
    } catch (err) {
      console.log(err);
    }
  }
});
const changeCustomerName = (value: SelectValue, option: DefaultOptionType) => {
  formState.baseInfo.lossRate = option.lossRate || '';
  formRef.value.validateFields([['baseInfo', 'lossRate']]);
};
// 选择共保后，将是否主承默认值改为‘1’
const changeCoinsuranceMark = (e: RadioChangeEvent) => {
  formState.baseInfo.acceptInsuranceFlag = e?.target?.value === '1' ? '1' : '';
};
// 操作结果模态框
const resultVisible = ref(false);
const resultMsg = ref('');

// 重置表单
const resetForm = () => {
  formRef.value.resetFields();
  defaultRisk.value = '';
  resultVisible.value = false;
  formState.baseInfo.saveStatus = '';
  formState.baseInfo.riskLevel = '1';
  formState.baseInfo.riskCode = '';
  formState.baseInfo.preRiskNo = '';
  riskCodeRef?.value?.resetData();
  formState.saleInfo.isTenderBusiness = '1';
  productRef?.value?.clearOption();
  formState.riskAddressInfoList = [{ province: '', provinceName: '', city: '', cityName: '', town: '', townName: '', county: '', countyName: '', village: '', villageName: '', address: '' }];
};

// 修改
const edit = () => {
  if (!openVisible.value) {
    openVisible.value = true;
  }
};

// 是否可以修改多标的
const allowEdit = ref<boolean>(false);

// 修改确认
const handleConfirmOk = () => {
  confirmEdit.value = true;
  openVisible.value = false;
  formState.baseInfo.saveStatus = '';
  allowEdit.value = isMultiTarget.value ? true : false;
  showEditBtn.value = false;
};

// 暂存
const saveLoading = ref(false);
const save = async () => {
  if (!formState.baseInfo.departmentCode) {
    message.error('请选择出单机构');
  } else {
    saveLoading.value = true;
    try {
      // 多标的需要去除riskAgrList节点
      const params = !isMultiTarget.value ? (({ riskAgrList, ...rest }) => rest)(formState) : formState;
      const res = await $postOnClient<{ preRiskNo: string }>(acceptApi + '/web/preRisk/tempSavePreRisk', params);
      if (res?.code === SUCCESS_CODE) {
        formState.baseInfo.preRiskNo = (res?.data?.preRiskNo as string) || '';
        resultVisible.value = true;
        resultMsg.value = res?.msg;
      } else {
        message.error(res?.msg || '');
      }
    } catch (err) {
      console.log(err);
    } finally {
      saveLoading.value = false;
    }
  }
};
// 提交
const submitLoading = ref(false);
const submit = async () => {
  // submitLoading.value = true; // 开始加载
  pageLoading.value = true;
  try {
    formRef.value.validate().then(async () => {
      // 多标的需要去除riskAgrList节点
      const params = !isMultiTarget.value ? (({ riskAgrList, ...rest }) => rest)(formState) : formState;
      const res = await $postOnClient<PreRiskRes>(`${acceptApi}/web/preRisk/submitPreRisk`, params);
      if (res?.code === SUCCESS_CODE) {
        formState.baseInfo.preRiskNo = res.data?.preRiskNo || '';
        formState.baseInfo.saveStatus = res.data?.saveStatus || '';
        resultMsg.value = res.msg;
        resultVisible.value = true;
        confirmEdit.value = false;
        if (res?.data?.resultData?.message) {
          message.info(res?.data?.resultData?.message);
        }
      } else {
        message.error(res?.msg || '提交失败'); // 提供默认错误消息
      }
    });
  } catch (err) {
    console.log(err);
  } finally {
    // submitLoading.value = false; // 结束加载
    pageLoading.value = false;
  }
};
const formFillLoading = ref(false);
// 转投保
const redirectToFormFill = async () => {
  formFillLoading.value = true;
  try {
    const res = await $getOnClient(acceptApi + '/web/applicationForm/applicationContract', {
      preRiskNo: formState.baseInfo.preRiskNo,
    });
    if (res && res.code === SUCCESS_CODE && res.data) {
      confirmEdit.value = false;
      // 转投保成功不需要提示，其他情况需要提示
      if (res?.msg !== '转投保成功') {
        message.info(res?.msg || '');
      }
      router.push({
        path: '/insuranceFormFill',
        query: { applyPolicyNo: res.data as string },
      });
    } else {
      message.error(res?.msg || '转投保失败'); // 提供默认错误消息
    }
  } catch (err) {
    console.log(err);
  } finally {
    formFillLoading.value = false;
  }
};
// 标的地址校验
const validateAddress = (_rule: Rule, value: string) => {
  if (!formState.riskAddressInfoList[0].province) {
    return Promise.reject('请选择地址');
  } else if (!value) {
    return Promise.reject('请输入地址');
  }
  return Promise.resolve();
};
const insuranceNumsUnitList = ref<Record<string, string>[]>([]);
// 查询投保数量单位
const getParmBaseConstantConfData = async () => {
  const res = await $getOnClient(`${gateWay}${service.administrate}/public/queryRiskNumberUnitList`, { productCode: formState.baseInfo.productCode, productVersion: formState.baseInfo.productVersion });
  if (res?.code === '000000' && Array.isArray(res?.data)) {
    insuranceNumsUnitList.value = res?.data;
    // 默认选择承保数量单位
    if (route.query.id) return;
    if (isMultiTarget.value) {
      // 多标
      formState.riskAgrList?.forEach((item) => {
        item.insureUnit = insuranceNumsUnitList.value.find((item) => item.defaultSelect)?.value || '';
      });
    } else {
      formState.baseInfo.insuranceUnit = insuranceNumsUnitList.value.find((item) => item.defaultSelect)?.value || undefined;
    }
  }
};
// 投保数量单位
const handleUnit = (value: string, option: DefaultOptionType) => {
  formState.baseInfo.insuredUnitChName = option.label;
  formState.baseInfo.insuranceUnit = option.value as string;
};
// 多标的投保数量单位
const handleRiskUnit = (index: number, option: DefaultOptionType) => {
  formState.riskAgrList[index].insureUnit = option.value as string;
};
// 保险数量，单位校验
const checkInsuredNumber = () => {
  if (!formState.baseInfo.insuredNumber) {
    return Promise.reject('请录入承保数量');
  }
  const reg = /^(?:0\.(?!0+$)\d{1,2}|[1-9]\d{0,8}(?:\.\d{1,2})?)$/;
  if (!reg.test(formState.baseInfo.insuredNumber)) {
    return Promise.reject('请输入大于0的数字，整数位不超过9位，小数位不超过2位');
  }
  if (!formState.baseInfo.insuranceUnit) {
    return Promise.reject('请选择承保数量单位');
  }
  return Promise.resolve();
};
const checkRiskInsuredNumber = (index: number) => {
  if (!formState?.riskAgrList?.[index]?.insuredNumber) {
    return Promise.reject('请录入承保数量');
  }
  const reg = /^(?:0\.(?!0+$)\d{1,2}|[1-9]\d{0,8}(?:\.\d{1,2})?)$/;
  if (!reg.test(formState?.riskAgrList?.[index]?.insuredNumber)) {
    return Promise.reject('请输入大于0的数字，整数位不超过9位，小数位不超过2位');
  }
  if (!formState?.riskAgrList?.[index]?.insureUnit) {
    return Promise.reject('请选择承保数量单位');
  }
  return Promise.resolve();
};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  overflow: auto;
  padding-bottom: 48px;
  .main-wrapper {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          .ant-tabs-tab {
            padding-top: 0;
            padding-bottom: 8px;
            margin: 0 12px;
            font-size: 14px;
          }

          .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: rgba(0, 0, 0, 0.9);
              font-weight: bold;
            }
          }
        }
      }
    }
    :deep(.ant-tabs-nav::before) {
      border-bottom: 0;
    }
  }
  .footer-wrapper {
    height: 48px;
    background: #fff;
    position: fixed;
    bottom: 0;
    width: calc(100% - 160px);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.ant-input-group-addon) {
    background-color: #f2f3f5;
  }
}
</style>
