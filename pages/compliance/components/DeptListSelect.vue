<template>
  <a-input-group>
    <a-row :gutter="6">
      <a-col :span="8">
        <a-input v-model:value="inputCode" :disabled="disabled" placeholder="请输入" type="number" />
      </a-col>
      <a-col :span="16">
        <a-select v-model:value="selectCode" placeholder="请选择" :disabled="disabled" :options="departmentOptions" :style="{ width: '100%' }" :field-names="{ label: 'departmentNoAndName', value: 'belongBusinessDepartmentNo' }" show-search allow-clear :filter-option="filterOption" />
      </a-col>
    </a-row>
  </a-input-group>
</template>

<script setup lang="ts">
import { ref, type Ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { useUserStore } from '@/stores/useUserStore';
import { $post } from '@/utils/request';
import type { DeptCodeName, DepartmentOptions } from '@/apiTypes/compliance';

/*
  结构：输入框+下拉框+复选框（监管平台使用）
  实现逻辑：
  1、父子组件传参通过props/emit实现；
  2、本组件维护两个响应对象：输入框的值、下拉框的值；
  3、每次进入本组件时，将入参deptCode填充至输入框，无默认值时填充当前用户的有权限机构列表的第一项；
  4、监听输入框的值改变时，联动调用机构列表接口并默认选中第一项，下拉框的值改变；
  5、监听下拉框的值改变时，将选中的机构编码回传给页面。
*/
const props = defineProps<{
  deptCode: string | undefined;
  disabled?: boolean;
  resetDept?: boolean;
}>();
const emit = defineEmits(['changeDeptCode']);
const needToDebaounce = ref(false);
const notShowErrorTips = ref(false);

const inputCode = ref(''); // 输入框的值
const selectCode = ref(''); // 下拉框的值
const departmentOptions: Ref<DeptCodeName[]> = ref([]);

const filterOption = (input: string, option: DepartmentOptions) => option?.departmentNoAndName?.includes(input);

// 查询机构列表
const getDepartmentList = async (params: { belongBusinessDepartmentNo: string }) => {
  // departmentOptions.value = [];
  let deptCode = '';
  const { data, code, msg } = (await $post('/api/common/listDeptCodeName', params)) || {};
  if (code === SUCCESS_CODE) {
    if (Array.isArray(data) && data.length > 0) {
      departmentOptions.value = data.map((item) => ({
        belongBusinessDepartmentNo: item.belongBusinessDepartmentNo,
        departmentNoAndName: item.departmentNoAndName,
      }));
      deptCode = departmentOptions.value[0].belongBusinessDepartmentNo;
    }
  } else {
    message.error(msg || '');
  }
  selectCode.value = deptCode;
};

// 节流查询机构下拉列表
const departmentChangeDebounce = debounce((params: { belongBusinessDepartmentNo: string }) => {
  getDepartmentList(params);
}, 1000);

const departmentChange = (params: { belongBusinessDepartmentNo: string }) => {
  getDepartmentList(params);
};

const changeSelect = () => {
  if (!notShowErrorTips.value) {
    departmentOptions.value = [];
    selectCode.value = '';
  }
};

const changeSelectDebounce = debounce(() => {
  changeSelect();
}, 500);

// 首次进入本组件时，默认值将当前用户的有权限机构列表的第一项填充至输入框
const { userInfo } = useUserStore();
const userDept = userInfo.deptList?.[0] ?? '';
inputCode.value = props.deptCode || userDept;

// 监听入参deptCode变动时，deptCode有值时将其填充至输入框；值为undefined时代表重置操作
watch(
  () => props.deptCode,
  (val) => {
    if (val && inputCode.value !== val) {
      needToDebaounce.value = false;
      inputCode.value = props.deptCode || '';
    }
    // 重置，清空数据
    if (val === undefined) {
      inputCode.value = '';
      selectCode.value = '';
      departmentOptions.value = [];
    }
  },
);

// 监听输入框变动时，则联动下拉框，重新发起机构列表请求
watch(
  inputCode,
  () => {
    /*
    机构编码格式：值不为2（无总部权限）时长度>=3、值为2（有总部权限）
    以下情况不进行机构列表查询：
    1、值不等于2且长度<3
    2、值等于2且没有总部权限
  */
    notShowErrorTips.value = false;
    if ((inputCode.value !== '2' && inputCode.value.length < 3) || (inputCode.value === '2' && !userInfo.deptList.includes('2'))) {
      // 频繁输入时，及时更新selectcode值，会有错误提示
      if (needToDebaounce.value) {
        changeSelectDebounce();
      } else {
        changeSelect();
      }
      // departmentOptions.value = [];
      // selectCode.value = '';
    } else {
      notShowErrorTips.value = true;
      if (needToDebaounce.value) {
        departmentChangeDebounce({ belongBusinessDepartmentNo: inputCode.value });
      } else {
        departmentChange({ belongBusinessDepartmentNo: inputCode.value });
      }
      needToDebaounce.value = true;
    }
  },
  {
    immediate: true,
  },
);

// 监听下拉框变动时，则将选中的机构编码回传到页面（值为空时也需要回传给页面，页面在进行表单校验时才会进行拦截）
watch(
  () => selectCode.value,
  (val) => {
    emit('changeDeptCode', val);
  },
);
</script>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type='number'] {
  -moz-appearance: textfield;
}
</style>
