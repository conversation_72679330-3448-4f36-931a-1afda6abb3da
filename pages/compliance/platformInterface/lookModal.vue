<template>
  <a-modal :width="pxToRem(800)" wrap-class-name="platform-edit-modal" :open="visible" title="查看接口配置" @cancel="handleClose" @ok="handleClose">
    <a-spin :spinning="loading">
      <a-form ref="formRef" :model="form" label-align="left">
        <div class="title text-[14px] mb-[9px] text-[#333]"><VueIcon class="mr-[3px]" :icon="IconInformationFont" />基础信息</div>
        <div class="content rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row ::gutter="28">
            <a-col span="12">
              <a-form-item label="归属平台">{{ form.thirdPartyPlatformName }}</a-form-item>
            </a-col>
            <a-col span="12" />
            <a-col span="12">
              <a-form-item label="接口名称">{{ form.thirdPartyPlatformInterfaceName }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口代码">{{ form.thirdPartyPlatformInterfaceNo }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="是否支持修正下发">{{ render(form.revisionIssueFlag, xiuzhengOptions) }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="客户端标识">{{ form.dockItfcClntIdtf }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="客户端身份标识">{{ form.dockItfcClntIdttIdtf }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="秘钥">{{ form.dockItfcSectKey }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="平台异步处理标志">{{ platAsnyHndlText.get(form.platAsnyHndlFlag || '') }}</a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="title text-[14px] mb-[9px] text-[#333]"><VueIcon class="mr-[3px]" :icon="IconShezhiFont" /> 设置信息</div>
        <div class="content rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28">
            <a-col span="12">
              <a-form-item label="实时标志">{{ render(form.realtimeFlagCode, realtimeFlagCodeOptions) }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="定时/非定时">{{ render(form.scheduleFlagCode, scheduleFlagCodeOptions) }}</a-form-item>
            </a-col>
            <a-col v-if="form.scheduleFlagCode === 1" span="12">
              <a-form-item label="定时执行时间点">{{ form.scheduleExecutionBeginTime }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口执行顺序">{{ form.thirdPartyPlatformInterfaceExecutionSequence }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="生效日期">{{ form.effectiveTime }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="失效日期">{{ form.invalidTime }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口版本">{{ form.interfaceVersion }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="acss网关地址">
                <a-tooltip placement="topLeft" :title="form.pageUrl" arrow-point-at-center>
                  <div class="max-w-[220px] truncate">{{ form.pageUrl }}</div>
                </a-tooltip>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口服务地址">
                <a-tooltip placement="topLeft" :title="form.interfaceServiceAddress" arrow-point-at-center>
                  <div class="max-w-[220px] truncate">{{ form.interfaceServiceAddress }}</div>
                </a-tooltip>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="连接超时时间">{{ form.connectionTimeoutTime }} 毫秒</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="调用场景">{{ form.callScenesCodeLabel }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权账号">{{ form.interfaceAuthorizationAccountNo }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权密码">{{ form.interfaceAuthorizationPassword }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="请求报文格式代码">{{ render(form.requestMessageFormatCode, transData(requestMessageFormatCodeOption)) }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="请求类型代码">{{ render(form.requestTypeCode, transData(requestTypeCodeOption)) }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="是否加密">{{ render(form.encryptionFlag, encryptionFlagOption) }}</a-form-item>
            </a-col>
            <a-col v-if="form.encryptionFlag === '1'" span="12">
              <a-form-item label="加密标志">{{ form.encryptionSymbol }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="图片上传限制大小">{{ form.imageUploadLimitSize || '-' }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="备注信息">{{ form.remark }}</a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="报数设置">{{ form.dataReportSetting }}</a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>

    <template #footer>
      <a-space>
        <a-button @click="handleDownLoad">报文模版下载</a-button>
        <a-button type="primary" @click="handleClose">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { IconInformationFont, IconShezhiFont } from '@pafe/icons-icore-agr-an';
import { debounce } from 'lodash-es';
import { ref } from 'vue';
import type { CallSceneLis, InterfaceBaseType, PlatformBaseType } from '@/apiTypes/compliance';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, useGet, $getOnClient } from '@/composables/request';

const { fetchData: queryCallSceneListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryCallSceneList');
const { fetchData: queryReqMsgFormatCodeListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryReqMsgFormatCodeList');
const { fetchData: queryRequestTypeListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryRequestTypeList');
const { fetchData: getInterfaceDetailRes } = await useGet<InterfaceBaseType>('/api/compliance/getInterfaceDetail');

const props = defineProps<{
  visible: boolean;
  id: string;
}>();
const emit = defineEmits(['update:visible']);
const handleClose = () => {
  emit('update:visible', false);
};

const requestMessageFormatCodeOption = ref<CallSceneLis[]>([]);
const getListReqMsgFormatCodeList = async () => {
  const { data, code } = (await queryReqMsgFormatCodeListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    requestMessageFormatCodeOption.value = data;
  }
};

const requestTypeCodeOption = ref<CallSceneLis[]>([]);
const getListRequestTypeCodeList = async () => {
  const { data, code } = (await queryRequestTypeListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    requestTypeCodeOption.value = data;
  }
};

const sceneList = ref<CallSceneLis[]>([]);
const getListCallSceneList = async () => {
  const { data, code } = (await queryCallSceneListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    sceneList.value = data;
  }
};

// 查询详情
const loading = ref(false);
const getInterfaceDetail = async () => {
  loading.value = true;
  getInterfaceDetailRes({ id: props.id })
    .then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && data) {
        const { thirdPartyPlatformName, thirdPartyPlatformInterfaceName, thirdPartyPlatformInterfaceNo, revisionIssueFlag, dockItfcClntIdtf, dockItfcClntIdttIdtf, dockItfcSectKey, platAsnyHndlFlag, realtimeFlagCode, scheduleFlagCode, scheduleExecutionBeginTime, thirdPartyPlatformInterfaceExecutionSequence, effectiveTime, invalidTime, interfaceVersion, pageUrl, interfaceServiceAddress, connectionTimeoutTime, callScenesCodeLabel, interfaceAuthorizationAccountNo, interfaceAuthorizationPassword, requestMessageFormatCode, requestTypeCode, encryptionFlag, remark, encryptionSymbol, parmThrdPtyPlatItfcReqtMsgTmplDto, imageUploadLimitSize, dataReportSetting } = data;
        form.value = {
          thirdPartyPlatformName,
          thirdPartyPlatformInterfaceName,
          thirdPartyPlatformInterfaceNo,
          revisionIssueFlag,
          dockItfcClntIdtf,
          dockItfcClntIdttIdtf,
          dockItfcSectKey,
          platAsnyHndlFlag,
          realtimeFlagCode,
          scheduleFlagCode,
          scheduleExecutionBeginTime,
          thirdPartyPlatformInterfaceExecutionSequence,
          effectiveTime,
          invalidTime,
          interfaceVersion,
          pageUrl,
          interfaceServiceAddress,
          connectionTimeoutTime,
          callScenesCodeLabel,
          interfaceAuthorizationAccountNo,
          interfaceAuthorizationPassword,
          requestMessageFormatCode,
          requestTypeCode,
          encryptionFlag,
          encryptionSymbol,
          remark,
          parmThrdPtyPlatItfcReqtMsgTmplDto,
          imageUploadLimitSize,
          dataReportSetting,
        } as Partial<
          PlatformBaseType &
            InterfaceBaseType & {
              [props: string]: string;
            }
        >;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const platAsnyHndlText = new Map([
  ['1', '是'],
  ['0', '否'],
]);

const encryptionFlagOption = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];
const realtimeFlagCodeOptions = [
  { label: '同步', value: 1 },
  { label: '异步', value: 0 },
];
const scheduleFlagCodeOptions = [
  { label: '定时', value: 1 },
  { label: '非定时', value: 0 },
];
const xiuzhengOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

const render = (val: number | string | undefined | null, options: Array<{ label: string; value: string | number }>) => {
  const tmp = options.find((item) => String(item.value) === String(val));
  return tmp?.label;
};

const transData = (options: CallSceneLis[]) => {
  const arr: Array<{ label: string; value: string | number }> = [];
  options.forEach((item) => {
    arr.push({ label: item.callScenesCodeLabel, value: item.callScenesCode });
  });
  return arr;
};

const handleDownLoad = () => {
  const { parmThrdPtyPlatItfcReqtMsgTmplDto } = form.value;
  if (!parmThrdPtyPlatItfcReqtMsgTmplDto) {
    message.error('请先上传报文模版');
    return;
  }
  handleLookDownLoad(parmThrdPtyPlatItfcReqtMsgTmplDto);
};
const handleLookDownLoad = async (templateInfo: { requestMessageTemplateFileAddress: string; requestMessageTemplateFileName: string }) => {
  const { requestMessageTemplateFileName } = templateInfo;
  getDownTemplate(requestMessageTemplateFileName);
};

const getDownTemplate = debounce(async (documentName: string) => {
  await $getOnClient(
    '/gateway/icore-agr-an.compliance/pageConfig/interface/downTemplate',
    { documentName },
    {
      async onResponse({ response }) {
        let fileName = '';
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          if (match) {
            fileName = decodeURI(match[1]);
          }
        }
        downloadBlob(fileData, fileName, fileType);
      },
    },
  );
}, 1000);
const form = ref<
  Partial<
    PlatformBaseType &
      InterfaceBaseType & {
        [props: string]: string;
      }
  >
>({});

watchEffect(async () => {
  if (props.visible) {
    getInterfaceDetail();
    getListCallSceneList();
    getListRequestTypeCodeList();
    getListReqMsgFormatCodeList();
  }
});
</script>
