<template>
  <a-modal :width="pxToRem(800)" wrap-class-name="platform-edit-modal" :after-close="handleAfterClose" :centered="true" :open="visible" :title="innerTitle" @cancel="handleCancel" @ok="handleCancel">
    <a-spin :spinning="loading">
      <a-form ref="formRef" :model="editModalRef" :colon="false" :rules="editRules">
        <div class="text-[14px] mb-[9px] text-[#333]"><VueIcon class="mr-[3px]" :icon="IconInformationFont" /> 基础信息</div>
        <div class="rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28" class="display-ctrl">
            <a-col span="12">
              <a-form-item label="归属平台" name="idParmThrdPtyPlatBase" class="labelClassHack-120">
                <a-select v-model:value="editModalRef.idParmThrdPtyPlatBase" placeholder="请选择" :field-names="{ label: 'platformCodeName', value: 'idParmThrdPtyPlatBase' }" :disabled="editModalRef.idParmThrdPtyPlatInterface ? true : false" :options="platformNameList" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口名称" name="thirdPartyPlatformInterfaceName" class="labelClassHack-120">
                <a-input v-model:value="editModalRef.thirdPartyPlatformInterfaceName" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口代码" name="thirdPartyPlatformInterfaceNo" class="labelClassHack-120">
                <a-input v-model:value="editModalRef.thirdPartyPlatformInterfaceNo" :disabled="editModalRef.idParmThrdPtyPlatInterface ? true : false" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="是否支持修正下发" name="revisionIssueFlag" class="labelClassHack-120">
                <a-radio-group v-model:value="editModalRef.revisionIssueFlag" :options="xiuzhengOptions" />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="客户端标识" name="dockItfcClntIdtf" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.dockItfcClntIdtf" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="客户端身份标识" name="dockItfcClntIdttIdtf" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.dockItfcClntIdttIdtf" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="秘钥" name="dockItfcSectKey" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.dockItfcSectKey" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="平台异步处理标志" name="platAsnyHndlFlag" class="labelClassHack-120">
                <a-radio-group v-model:value="editModalRef.platAsnyHndlFlag">
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="text-[14px] mb-[9px] text-[#333]"><VueIcon class="mr-[3px]" :icon="IconShezhiFont" /> 设置信息</div>
        <div class="rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28" class="display-ctrl">
            <a-col span="12">
              <a-form-item label="实时标志" name="realtimeFlagCode" class="labelClassHack-140">
                <a-select v-model:value="editModalRef.realtimeFlagCode" placeholder="请选择" :options="realtimeFlagCodeOptions" @change="handleRealTimeFlag" />
              </a-form-item>
            </a-col>
            <a-col v-if="editModalRef.realtimeFlagCode === 0" span="12">
              <a-form-item label="定时/非定时" name="scheduleFlagCode" class="labelClassHack-140">
                <a-radio-group v-model:value="editModalRef.scheduleFlagCode" :options="scheduleFlagCodeOptions" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="editModalRef.realtimeFlagCode === 0 && editModalRef.scheduleFlagCode === 1" span="12">
              <a-form-item label="定时执行时间点" name="scheduleExecutionBeginTime" class="labelClassHack-140">
                <TimePicker v-model:value="editModalRef.scheduleExecutionBeginTime" format="HH:mm" value-format="HH:mm" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口执行顺序" name="thirdPartyPlatformInterfaceExecutionSequence" class="labelClassHack-140">
                <a-input-number v-model:value="editModalRef.thirdPartyPlatformInterfaceExecutionSequence" placeholder="请输入" type="number" style="width: 100%" :step="1" :min="0" :max="1000000" :precision="0" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="生效日期" name="effectiveTime" class="labelClassHack-140">
                <a-date-picker v-model:value="editModalRef.effectiveTime" placeholder="请选择" :format="dateFormat" :disabled-date="disabledDate" :value-format="dateFormat" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="失效日期" name="invalidTime" class="labelClassHack-140">
                <a-date-picker v-model:value="editModalRef.invalidTime" placeholder="请选择" :format="dateFormat" :disabled-date="disabledDate" :value-format="dateFormat" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口版本" name="interfaceVersion" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.interfaceVersion" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="acss网关地址" name="pageUrl" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.pageUrl" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="接口服务地址" name="interfaceServiceAddress" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.interfaceServiceAddress" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="连接超时时间" name="connectionTimeoutTime" class="labelClassHack-140">
                <a-input-number v-model:value="editModalRef.connectionTimeoutTime" placeholder="请输入" :min="0" :step="1" :max="10000000" :precision="0" type="number" style="width: 100%" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="调用场景" name="callScenesCode" class="labelClassHack-140">
                <a-select v-model:value="editModalRef.callScenesCode" placeholder="请选择" :field-names="{ label: 'callScenesCodeLabel', value: 'callScenesCode' }" :options="sceneList" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权账号" name="interfaceAuthorizationAccountNo" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.interfaceAuthorizationAccountNo" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权密码" name="interfaceAuthorizationPassword" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.interfaceAuthorizationPassword" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="请求报文格式代码" name="requestMessageFormatCode" class="labelClassHack-140">
                <a-select v-model:value="editModalRef.requestMessageFormatCode" placeholder="请选择" :field-names="{ label: 'requestMessageFormatCodeLabel', value: 'requestMessageFormatCode' }" :options="requestMessageFormatCodeOption" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="请求类型代码" name="requestTypeCode" class="labelClassHack-140">
                <a-select v-model:value="editModalRef.requestTypeCode" placeholder="请选择" :field-names="{ label: 'requestTypeCodeLabel', value: 'requestTypeCode' }" :options="requestTypeCodeOption" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="是否加密" name="encryptionFlag" class="labelClassHack-140">
                <a-select v-model:value="editModalRef.encryptionFlag" placeholder="请选择" :options="encryptionFlagOption" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-if="editModalRef.encryptionFlag === '1'" span="12">
              <a-form-item label="加密标志" name="encryptionSymbol" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.encryptionSymbol" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="图片上传限制大小" name="imageUploadLimitSize" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.imageUploadLimitSize" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="备注信息" name="remark" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.remark" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="报数设置" name="dataReportSetting" class="labelClassHack-140">
                <a-textarea v-model:value="editModalRef.dataReportSetting" allow-clear :rows="4" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-spin>
    <template #footer>
      <a-space>
        <a-button @click="handleDownload">报文模版下载</a-button>
        <a-upload v-model:file-list="fileList" :show-upload-list="false" :before-upload="() => false" @change="handleUploadChange">
          <a-button>报文模版上传</a-button>
        </a-upload>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, toRaw } from 'vue';
import { Form, TimePicker } from 'ant-design-vue';
import { IconInformationFont, IconShezhiFont } from '@pafe/icons-icore-agr-an';
import dayjs, { type Dayjs } from 'dayjs';
import { debounce } from 'lodash-es';
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { isWordDigitValidator } from '@/utils/validators';
import type { CallSceneLis, InterfaceBaseType, PlatformCodeName, DepartmentNoType } from '@/apiTypes/compliance';
import { usePost, useGet, $getOnClient } from '@/composables/request';
import { message } from '@/components/ui/Message';

const { fetchData: getPlatformCodeNameListRes } = await usePost<PlatformCodeName[], DepartmentNoType>('/api/compliance/listPlatformCodeName');
const { fetchData: addInterfaceRes } = await usePost('/api/compliance/addInterface');
const { fetchData: updateInterfaceRes } = await usePost('/api/compliance/updateInterface');
const { fetchData: uploadInterfaceTemplateRes } = await usePost<{ documentName: string; documentAddress: string }>('/api/compliance/uploadInterfaceTemplate');
const { fetchData: queryCallSceneListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryCallSceneList');
const { fetchData: queryReqMsgFormatCodeListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryReqMsgFormatCodeList');
const { fetchData: queryRequestTypeListRes } = await usePost<CallSceneLis[]>('/api/compliance/queryRequestTypeList');
const { fetchData: getInterfaceDetailRes } = await useGet<InterfaceBaseType>('/api/compliance/getInterfaceDetail');

const props = defineProps<{
  visible: boolean;
  id: string;
  type: string;
  deptCode?: string;
}>();
const emit = defineEmits(['update:visible', 'onOk']);

const dateFormat = 'YYYY-MM-DD';
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'day').endOf('day');
const title = '接口配置';
const innerTitle = computed(() => (editModalRef.idParmThrdPtyPlatInterface === '' || editModalRef.idParmThrdPtyPlatInterface === undefined ? '新增' : '修改') + title);
// 表单初始化值
const editModalRef = reactive<InterfaceBaseType>({
  belongBusinessDepartmentNo: '',
  connectionTimeoutTime: null,
  departmentNoAndName: '',
  effectiveTime: '',
  idParmThrdPtyPlatBase: '',
  idParmThrdPtyPlatInterface: '',
  interfaceAuthorizationAccountNo: '',
  interfaceAuthorizationPassword: '',
  pageUrl: '',
  interfaceServiceAddress: '',
  interfaceVersion: '',
  invalidTime: '',
  realtimeFlagCode: undefined,
  requestMessageFormatCode: undefined,
  requestTypeCode: undefined,
  scheduleExecutionBeginTime: '',
  scheduleExecutionEndTime: '',
  scheduleFlagCode: null,
  callScenesCode: undefined,
  thirdPartyPlatformInterfaceExecutionSequence: null,
  thirdPartyPlatformInterfaceName: '',
  thirdPartyPlatformInterfaceNo: '',
  parmThrdPtyPlatItfcReqtMsgTmplDto: {
    requestMessageTemplateFileName: '',
    requestMessageTemplateFileAddress: '',
  },
  encryptionFlag: undefined,
  encryptionSymbol: '',
  thirdPartyPlatformName: '',
  remark: '',
  revisionIssueFlag: '1', // 是否支持修正下发字段
  dockItfcClntIdtf: '', // 客户端标识
  dockItfcClntIdttIdtf: '', // 客户端身份标识
  dockItfcSectKey: '', // 秘钥
  platAsnyHndlFlag: '', // 平台异步处理标志
  imageUploadLimitSize: '', // 上传图片大小限制
  dataReportSetting: '', // 报数设置
});
// 新增编辑表单校验配置
const editRules = ref({
  idParmThrdPtyPlatBase: [{ required: true, message: '请选择归属平台', trigger: 'change' }],
  thirdPartyPlatformInterfaceName: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
    { message: '接口名称最多64位', max: 64, trigger: 'change' },
  ],
  thirdPartyPlatformInterfaceNo: [
    { required: true, message: '请输入接口代码', trigger: 'blur' },
    { message: '接口代码最多50位', max: 50, trigger: 'change' },
    { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'change', validator: isWordDigitValidator },
  ],
  thirdPartyPlatformInterfaceExecutionSequence: [{ required: true, message: '请输入接口执行顺序', trigger: 'change' }],
  realtimeFlagCode: [{ required: true, message: '请选择实时标志', trigger: 'change' }],
  callScenesCode: [{ required: true, message: '请选择调用场景', trigger: 'change' }],
  scheduleFlagCode: [{ required: true, message: '请选择接口类型', trigger: 'change' }],
  scheduleExecutionBeginTime: [{ required: true, message: '请选择定时执行时间点', trigger: 'change' }],
  effectiveTime: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  interfaceVersion: [
    { required: true, message: '请输入接口版本', trigger: 'blur' },
    { message: '接口版本最多16位', max: 16, trigger: 'change' },
  ],
  pageUrl: [
    { required: true, message: '请输入acss网关地址', trigger: 'blur' },
    { message: 'acss网关地址最多255位', max: 255, trigger: 'change' },
  ],
  interfaceServiceAddress: [
    { required: true, message: '请输入接口服务地址', trigger: 'blur' },
    { message: '接口服务地址最多255位', max: 255, trigger: 'change' },
  ],
  connectionTimeoutTime: [{ required: true, message: '请输入连接超时时间', trigger: 'change' }],
  interfaceAuthorizationAccountNo: [
    { required: true, message: '请输入授权账号', trigger: 'blur' },
    { message: '授权账号最多64位', max: 64, trigger: 'change' },
  ],
  interfaceAuthorizationPassword: [
    { required: true, message: '请输入授权密码', trigger: 'blur' },
    { message: '授权密码最多255位', max: 255, trigger: 'change' },
  ],
  requestMessageFormatCode: [{ required: true, message: '请选择请求报文格式代码', trigger: 'change' }],
  requestTypeCode: [{ required: true, message: '请选择请求类型代码', trigger: 'change' }],
  encryptionFlag: [{ required: true, message: '请选择加密标志', trigger: 'change' }],
  remark: [{ message: '备注最多225位', max: 225, trigger: 'change' }],
  revisionIssueFlag: [{ required: true, message: '请选择是否支持修正下发', trigger: 'change' }],
  imageUploadLimitSize: [{ message: '请输入整数', max: 255, trigger: 'change', pattern: /^[1-9]\d*$/ }],
});
const editForm = Form.useForm(editModalRef, editRules);
const { resetFields } = editForm;
const sceneList = ref<Array<CallSceneLis>>([]);
const requestMessageFormatCodeOption = ref<Array<CallSceneLis>>([]);
const requestTypeCodeOption = ref<Array<CallSceneLis>>([]);
const platformNameList = ref<Array<PlatformCodeName>>([]);
// eslint-disable-next-line vue/no-dupe-keys
const deptCode = ref('');

const getPlatformNameList = async () => {
  const { data, code } = (await getPlatformCodeNameListRes({ belongBusinessDepartmentNo: deptCode.value })) || {};
  if (SUCCESS_CODE === code && data) {
    platformNameList.value = data;
    if (props.type === 'add') {
      editModalRef.idParmThrdPtyPlatBase = platformNameList.value?.[0]?.idParmThrdPtyPlatBase;
    }
  }
};
const getListCallSceneList = async () => {
  const { data, code } = (await queryCallSceneListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    sceneList.value = data;
  }
};
const getListReqMsgFormatCodeList = async () => {
  const { data, code } = (await queryReqMsgFormatCodeListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    requestMessageFormatCodeOption.value = data;
  }
};
const getListRequestTypeCodeList = async () => {
  const { data, code } = (await queryRequestTypeListRes()) || {};
  if (SUCCESS_CODE === code && data) {
    requestTypeCodeOption.value = data;
  }
};

// 查询详情
const loading = ref(false);
const getInterfaceDetail = async () => {
  loading.value = true;
  getInterfaceDetailRes({ id: props.id })
    .then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && data) {
        deptCode.value = data.belongBusinessDepartmentNo;
        getPlatformNameList();
        editModalRef.belongBusinessDepartmentNo = data.belongBusinessDepartmentNo;
        editModalRef.connectionTimeoutTime = data.connectionTimeoutTime;
        editModalRef.departmentNoAndName = data.departmentNoAndName;
        editModalRef.effectiveTime = data.effectiveTime;
        editModalRef.idParmThrdPtyPlatBase = data.idParmThrdPtyPlatBase;
        editModalRef.idParmThrdPtyPlatInterface = data.idParmThrdPtyPlatInterface;
        editModalRef.interfaceAuthorizationAccountNo = data.interfaceAuthorizationAccountNo;
        editModalRef.interfaceAuthorizationPassword = data.interfaceAuthorizationPassword;
        editModalRef.pageUrl = data.pageUrl;
        editModalRef.interfaceServiceAddress = data.interfaceServiceAddress;
        editModalRef.interfaceVersion = data.interfaceVersion;
        editModalRef.invalidTime = data.invalidTime;
        editModalRef.realtimeFlagCode = data.realtimeFlagCode;
        editModalRef.requestMessageFormatCode = data.requestMessageFormatCode;
        editModalRef.requestTypeCode = data.requestTypeCode;
        editModalRef.scheduleExecutionBeginTime = data.scheduleExecutionBeginTime;
        editModalRef.scheduleExecutionEndTime = data.scheduleExecutionEndTime;
        editModalRef.scheduleFlagCode = data.scheduleFlagCode;
        editModalRef.callScenesCode = data.callScenesCode;
        editModalRef.thirdPartyPlatformInterfaceExecutionSequence = data.thirdPartyPlatformInterfaceExecutionSequence;
        editModalRef.thirdPartyPlatformInterfaceName = data.thirdPartyPlatformInterfaceName;
        editModalRef.thirdPartyPlatformInterfaceNo = data.thirdPartyPlatformInterfaceNo;
        editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto = data.parmThrdPtyPlatItfcReqtMsgTmplDto;
        editModalRef.encryptionFlag = data.encryptionFlag;
        editModalRef.encryptionSymbol = data.encryptionSymbol;
        editModalRef.thirdPartyPlatformName = data.thirdPartyPlatformName;
        editModalRef.remark = data.remark;
        editModalRef.revisionIssueFlag = data.revisionIssueFlag;
        editModalRef.dockItfcClntIdtf = data.dockItfcClntIdtf;
        editModalRef.dockItfcClntIdttIdtf = data.dockItfcClntIdttIdtf;
        editModalRef.dockItfcSectKey = data.dockItfcSectKey;
        editModalRef.platAsnyHndlFlag = data.platAsnyHndlFlag;
        editModalRef.imageUploadLimitSize = data.imageUploadLimitSize;
        editModalRef.dataReportSetting = data.dataReportSetting || '';
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const encryptionFlagOption = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];
const realtimeFlagCodeOptions = [
  { label: '同步', value: 1 },
  { label: '异步', value: 0 },
];
const scheduleFlagCodeOptions = [
  { label: '定时', value: 1 },
  { label: '非定时', value: 0 },
];
const xiuzhengOptions = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

const handleAfterClose = () => {
  resetFields();
};
const handleDownload = async () => {
  if (!editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto?.idParmThrdPtyPlatItfcReqtMsgTmpl) {
    message.error('请先上传报文模板');
    return;
  }
  if (editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto?.idParmThrdPtyPlatItfcReqtMsgTmpl) {
    const { requestMessageTemplateFileName } = editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto;
    getDownTemplate(requestMessageTemplateFileName);
  }
};
const getDownTemplate = debounce(async (documentName: string) => {
  await $getOnClient(
    '/gateway/icore-agr-an.compliance/pageConfig/interface/downTemplate',
    { documentName },
    {
      async onResponse({ response }) {
        let fileName = '';
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          if (match) {
            fileName = decodeURI(match[1]);
          }
        }
        downloadBlob(fileData, fileName, fileType);
      },
    },
  );
}, 1000);

const fileList = ref([]);
const handleUploadChange = async (e: { file: File; fileList: UploadListType }) => {
  const { file } = e;
  const formData = new FormData();
  formData.append('uploadFile', file);
  const { code, data, msg = '' } = (await uploadInterfaceTemplateRes(formData)) || {};
  if (code === SUCCESS_CODE) {
    message.success('上传成功');
    if (editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto) {
      editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto = {
        idParmThrdPtyPlatItfcReqtMsgTmpl: editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto.idParmThrdPtyPlatItfcReqtMsgTmpl,
        requestMessageTemplateFileName: data?.documentName || '',
        requestMessageTemplateFileAddress: data?.documentAddress || '',
      };
    } else {
      editModalRef.parmThrdPtyPlatItfcReqtMsgTmplDto = {
        requestMessageTemplateFileName: data?.documentName || '',
        requestMessageTemplateFileAddress: data?.documentAddress || '',
      };
    }
  } else {
    message.error(msg);
  }
};

const handleRealTimeFlag = (val: number) => {
  // 同步
  if (val === 1) {
    editModalRef.scheduleFlagCode = null;
    editModalRef.scheduleExecutionBeginTime = '';
  }
};
const formRef = ref();

const handleCancel = () => {
  emit('update:visible', false);
};

const handleOk = () => {
  formRef.value
    .validate()
    .then(() => {
      const obj: Record<string, unknown> = {};
      // 过滤所有数据中的前后空格
      for (const [key, value] of Object.entries(editModalRef)) {
        if (typeof value === 'string') {
          obj[key] = value.trim();
        } else {
          obj[key] = value;
        }
      }
      const typeMap: { [key: string]: () => void } = {
        add: () => {
          addInterfaceRes(toRaw(obj)).then((res) => {
            const { code, msg = '' } = res || {};
            if (SUCCESS_CODE === code) {
              message.success(msg);
              emit('onOk');
            } else {
              message.error(msg);
            }
          });
        },
        edit: () => {
          updateInterfaceRes(toRaw(obj)).then((res) => {
            const { code, msg = '' } = res || {};
            if (SUCCESS_CODE === code) {
              message.success(msg);
              emit('onOk');
            } else {
              message.error(msg);
            }
          });
        },
      };
      typeMap[props.type]();
    })
    .catch((err: { errorFields: { name: string }[] }) => {
      formRef.value.scrollToField(err?.errorFields?.[0].name, document.querySelector('.ant-form-item-has-error')?.scrollIntoView(false));
    });
};

const initHandler = () => {
  const handleMap: { [key: string]: () => void } = {
    add: () => {
      Object.assign(editModalRef, {});
      deptCode.value = props.deptCode || '';
      getPlatformNameList();
    },
    edit: () => getInterfaceDetail(),
  };
  handleMap[props.type]();
};

watchEffect(async () => {
  if (props.visible) {
    initHandler();
    getListCallSceneList();
    getListRequestTypeCodeList();
    getListReqMsgFormatCodeList();
  }
});

watchEffect(() => {
  if (editModalRef.encryptionFlag === '0') {
    editModalRef.encryptionSymbol = '';
  }
});
</script>

<style lang="less">
.platform-edit-modal {
  .labelClassHack-160 {
    .ant-col.ant-form-item-label {
      width: 160px;
    }
  }
  .labelClassHack-100 {
    .ant-col.ant-form-item-label {
      width: 100px;
    }
  }
  .labelClassHack-80 {
    .ant-col.ant-form-item-label {
      width: 80px;
    }
  }
  .labelClassHack-140 {
    .ant-col.ant-form-item-label {
      width: 140px;
    }
  }
}
</style>
