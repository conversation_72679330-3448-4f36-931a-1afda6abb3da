<template>
  <a-modal v-model:open="visible" centered title="查看" :footer="null">
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">参数类型:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parameterClassNo }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">参数类型名称:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parameterClassName }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">参数代码:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parameterCode }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">参数代码名称:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parameterName }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">父级参数类型:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parentParameterClassNo }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">父级参数类型名称:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parentParameterClassName }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">父级参数代码:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parentParameterCode }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">父级参数代码名称:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.parentParameterName }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">展示排序:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.displaySequenceNo }}</div>
    </div>
    <div class="flex mb-[10px]">
      <div class="w-[120px] shrink-0">生效时间:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.effectiveTime }}</div>
    </div>
    <div class="flex">
      <div class="w-[120px] shrink-0">备注:</div>
      <div class="grow text-wrap break-words overflow-hidden">{{ paramter.remark }}</div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { ParamterItem } from '../paramsSet.d';

const visible = defineModel<boolean>('open');
const { id } = defineProps<{ id: string }>();
const { gateWay, service } = useRuntimeConfig().public || {};

const detailId = ref('');

const initParamter: ParamterItem = {
  parameterClassNo: '',
  parameterClassName: '',
  parameterCode: '',
  parameterName: '',
  parentParameterClassNo: '',
  parentParameterClassName: '',
  parentParameterCode: '',
  parentParameterName: '',
  displaySequenceNo: '',
  effectiveTime: '',
  remark: '',
};

const paramter = reactive<ParamterItem>({ ...initParamter });

const resetParamter = () => {
  Object.assign(paramter, initParamter);
};

const queryDetail = async () => {
  const url = gateWay + service.administrate + '/parmCommonParm/queryParmCommonParm';
  try {
    const res = await $post<ParamterItem>(url, { idParmComnParm: detailId.value });
    if (res.code === SUCCESS_CODE) {
      Object.assign(paramter, {
        parameterClassNo: res.data?.parameterClassNo || '', //参数类型
        parameterClassName: res.data?.parameterClassName || '', //参数类型名称
        parameterCode: res.data?.parameterCode || '', //参数代码
        parameterName: res.data?.parameterName || '', //参数名称
        displaySequenceNo: res.data?.displaySequenceNo || '', //排序
        effectiveTime: res.data?.effectiveTime || '', //生效时间
        parentParameterClassNo: res.data?.parentParameterClassNo || '', //父级参数类型
        parentParameterClassName: res.data?.parentParameterClassName || '', //父级参数类型名称
        parentParameterCode: res.data?.parentParameterCode || '', //父级参数代码
        parentParameterName: res.data?.parentParameterName || '', //父级参数代码名称
        remark: res.data?.remark || '', //备注
      });
    } else {
      message.error(res.msg || '请求有误，请稍后重试');
      resetParamter();
    }
  } catch (err) {
    console.log(err);
  }
};

watch(visible, (val) => {
  if (val && id) {
    detailId.value = id;
    queryDetail();
  }
});
</script>
