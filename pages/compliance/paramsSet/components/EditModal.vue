<template>
  <a-modal v-model:open="visible" centered :title="title" :mask-closable="false" @ok="submitForm">
    <a-form ref="formRef" :label-col="{ span: 6 }" :colon="false" :model="formData">
      <a-form-item label="参数类型" name="parameterClassNo" :rules="[{ required: true, message: '参数类型不能为空' }]">
        <a-input v-model:value="formData.parameterClassNo" placeholder="请输入参数类型" />
      </a-form-item>
      <a-form-item label="参数代码" name="parameterCode" :rules="[{ required: true, message: '参数代码不能为空' }]">
        <a-input v-model:value="formData.parameterCode" placeholder="请输入参数代码" />
      </a-form-item>
      <a-form-item label="参数代码名称" name="parameterName" :rules="[{ required: true, message: '参数代码名称不能为空' }]">
        <a-input v-model:value="formData.parameterName" placeholder="请输入参数代码名称" />
      </a-form-item>
      <a-form-item label="父级参数类型" name="paramParents">
        <a-select v-model:value="parentParameterClassNo" placeholder="请选择" :allow-clear="true" :show-search="true" :options="parameterNameList" :filter-option="(val, options) => options.label.includes(val)" />
      </a-form-item>
      <a-form-item label="父级参数代码" name="idParentParmComnParm">
        <a-select v-model:value="formData.idParentParmComnParm" placeholder="请选择" :allow-clear="true" :show-search="true" :options="parentParameterClassNoList" />
        <!-- <a-input v-model:value="formData.idParentParmComnParm" placeholder="请输入父级参数代码" readonly disabled /> -->
      </a-form-item>
      <a-form-item label="生效时间" name="effectiveTime" :rules="[{ required: true, message: '生效时间不能为空' }]">
        <a-date-picker v-model:value="formData.effectiveTime" show-time allow-clear placeholder="选择日期" style="width: 100%" :format="dateFormat" :value-format="dateFormat" />
      </a-form-item>
      <a-form-item label="展示排序" name="displaySequenceNo" :rules="[{ pattern: /^\d*$/, message: '只能输入数字' }]">
        <a-input v-model:value="formData.displaySequenceNo" placeholder="请输入展示排序" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="2" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import type { ParamterFormData, AddParamterData, ParamterItem } from '../paramsSet.d';

const visible = defineModel<boolean>('open');
const { type, id, parameterNameList, searchParamter } = defineProps<{ type: 'add' | 'edit'; id?: string; parameterNameList: { label: string; value: string }[]; searchParamter: string }>();
const dateFormat = 'YYYY-MM-DD HH:mm:ss';
const formRef = ref();
const { gateWay, service } = useRuntimeConfig().public || {};

const editId = ref('');
const parentParameterClassNo = ref('');
const parentParameterClassNoList = ref<{ label: string; value: string }[]>([]);
const emits = defineEmits(['success']);

const formData = reactive<ParamterFormData>({
  parameterClassNo: '', // 参数类型
  parameterCode: '', // 参数编码
  parameterName: '', // 参数码名称
  displaySequenceNo: '', // 展示顺序
  effectiveTime: '', // 生效时间
  idParentParmComnParm: '', // 父级参数代码
  remark: '', // 备注
});

const addParameter = async () => {
  const url = gateWay + service.administrate + (type === 'add' ? '/parmCommonParm/addParmCommonParm' : '/parmCommonParm/editParmCommonParm');
  const params: AddParamterData = toRaw(formData);
  if (editId.value) {
    params.idParmComnParm = editId.value;
  }
  try {
    const res = await $post(url, params);
    if (res.code === SUCCESS_CODE) {
      message.success(res.msg || '成功');
      visible.value = false;
      emits('success', type);
    } else {
      message.error(res.msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

const submitForm = () => {
  formRef.value.validate().then(() => {
    addParameter();
  });
};

const initFormData = {
  parameterClassNo: '', // 参数类型
  parameterCode: '', // 参数编码
  parameterName: '', // 参数码名称
  displaySequenceNo: '', // 展示顺序
  effectiveTime: '', // 生效时间
  idParentParmComnParm: '', // 父级参数代码
  remark: '', // 备注
};

const resetFormData = () => {
  // 清空值
  Object.assign(formData, initFormData);
  // 清空校验结果
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const setFormData = async () => {
  const url = gateWay + service.administrate + '/parmCommonParm/queryParmCommonParm';
  try {
    const res = await $post<ParamterItem>(url, { idParmComnParm: editId.value });
    if (res.code === SUCCESS_CODE) {
      Object.assign(formData, {
        parameterClassNo: res.data?.parameterClassNo || '', //参数类型
        parameterCode: res.data?.parameterCode || '', //参数代码
        parameterName: res.data?.parameterName || '', //参数名称
        displaySequenceNo: res.data?.displaySequenceNo || '', //排序
        effectiveTime: res.data?.effectiveTime || '', //生效时间
        idParentParmComnParm: res.data?.idParentParmComnParm || '', //父级参数代码
        remark: res.data?.remark || '', //备注
      });
      parentParameterClassNo.value = res.data?.parentParameterClassNo || ''; //父级参数类型
    } else {
      message.error(res.msg || '请求有误，请稍后重试');
      // 清空数据
      Object.assign(formData, initFormData);
      formData.idParentParmComnParm = editId.value;
    }
  } catch (err) {
    console.log(err);
    // 清空数据
    Object.assign(formData, initFormData);
    formData.idParentParmComnParm = editId.value;
  }
  // 清空校验结果
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const getParentParameterClassNoList = async (code) => {
  const url = gateWay + service.administrate + '/parmCommonParm/querySelect';
  try {
    const res = await $post(url, [code]);
    if (res.code === SUCCESS_CODE) {
      const map = res.data?.selectMaps || {};
      parentParameterClassNoList.value = (map[code] || []).map((val) => {
        return {
          label: (val.parameterName || '') + '-' + (val.parameterCode || ''),
          value: val.idParmComnParm || '',
        };
      });
      const index = parentParameterClassNoList.value.findIndex((val) => val.value === formData.idParentParmComnParm);
      if (index < 0) {
        formData.idParentParmComnParm = '';
      }
    } else {
      message.error(res.msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

watch(parentParameterClassNo, (val) => {
  if (val) {
    getParentParameterClassNoList(val);
  }
});

const title = ref('');
// 清除上一次数据
watch(visible, (val) => {
  if (!val) return;
  if (type === 'add') {
    // 新增场景清空数据
    resetFormData();
    parentParameterClassNo.value = '';
    parentParameterClassNoList.value = [];
    editId.value = '';
    title.value = '新增';
    formData.parameterClassNo = searchParamter;
  } else {
    title.value = '编辑';
    editId.value = id ? id : '';
    setFormData();
  }
});
</script>
