export interface ParamterFormData {
  parameterClassNo: string; // 参数类型
  parameterClassName?: string; // 参数类型名称
  parameterCode: string; // 参数编码
  parameterName: string; // 参数码名称
  displaySequenceNo: string; // 展示顺序
  effectiveTime: string; // 生效时间
  idParentParmComnParm: string; // 父级参数id
  remark: string; // 备注
}

export interface AddParamterData extends ParamterFormData {
  idParmComnParm?: string; // 父级主键
}

export interface ParamterItem extends ParamterFormData {
  parentParameterClassNo: string; // 父级参数类型
  parentParameterClassName: string; // 父级参数类型名称
  parentParameterCode: string; // 父级参数代码
  parentParameterName: string; // 父级参数名称
  idParmComnParm: string; // 参数id
}
