<template>
  <div class="params-set-manage">
    <div class="mx-[14px] my-[12px] px-[16px] pt-[16px] pb-[20px] bg-[#ffffff] rounded-[6px]">
      <a-form class="flex-grow" :label-col="{ style: { width: pxToRem(90) } }" :colon="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="参数类型名称" name="parameterName">
              <a-select v-model:value="parameterName" placeholder="请选择" :allow-clear="true" :show-search="true" :options="parameterNameList" :filter-option="filterOption" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-center">
        <a-button type="primary" ghost @click="handleQuery(1)">查询</a-button>
      </div>
    </div>
    <div class="mx-[14px] my-[12px] px-[16px] bg-[#ffffff] rounded-[6px]">
      <div class="h-[64px] flex justify-between items-center">
        <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">参数配置</span>
        <div class="text-[14px] space-x-[8px]">
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </div>
      </div>
      <a-table :data-source="dataSource" :columns="columns" :loading="loading" :pagination="pagination" :bordered="false" :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)" :scroll="{ y: '50vh' }">
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
          <template v-if="['thirdPartyPlatformName', 'parameterClassName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="link" size="small" @click="handleUpdate(record)">修改</a-button>
            <a-button type="link" size="small" @click="handleDelete(record)">删除</a-button>
            <a-button type="link" size="small" @click="handleView(record)">查看</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <edit-modal :id="tableData?.idParmComnParm || ''" v-model:open="visible" :type="modelType" :parameter-name-list="parameterNameList" :search-paramter="parameterName" @success="reload" />
  <detail-modal :id="tableData?.idParmComnParm || ''" v-model:open="visibleDetail" />
</template>
<script setup lang="ts">
import { pxToRem } from '@/utils/tools';
import { message } from '@/components/ui/Message';
import { usePagination } from '@/composables/usePagination';
import EditModal from './components/EditModal.vue';
import DetailModal from './components/DetailModal.vue';
import { $get, $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { ParamterItem } from './paramsSet.d';

const parameterName = ref('');
const parameterNameList = ref<{ label: string; value: string }[]>([]);
const dataSource = ref<ParamterItem[]>([]);
const loading = ref(false);
const modelType = ref<'edit' | 'add'>('add');
const { gateWay, service } = useRuntimeConfig().public || {};
const tableData = ref<ParamterItem>();

// 表格每列数据
const columns = [
  {
    title: '参数类型',
    dataIndex: 'parameterClassNo',
  },
  {
    title: '参数代码',
    dataIndex: 'parameterCode',
  },
  {
    title: '参数代码名称',
    dataIndex: 'parameterName',
  },
  {
    title: '父级参数类型',
    dataIndex: 'parentParameterClassNo',
  },
  {
    title: '父级参数类型名称',
    dataIndex: 'parentParameterClassName',
  },
  {
    title: '父级参数代码',
    dataIndex: 'parentParameterCode',
  },
  {
    title: '父级参数代码名称',
    dataIndex: 'parentParameterName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '200',
  },
];

const filterOption = (input, option) => {
  return option.label.includes(input);
};
const initParameterNameList = async () => {
  const res = await $get(gateWay + service.compliance + '/commonParameterType/queryCommonParameterType', { thirdPartyPlatformNo: '' });
  if (res.code === SUCCESS_CODE && Array.isArray(res.data)) {
    parameterNameList.value = res.data.map((val) => {
      return {
        label: val.parameterClassName,
        value: val.parameterClassNo,
      };
    });
    // const list = []; // 测试代码
    // for (const item of parameterNameList.value) {
    //   if (list.findIndex(val => val.value === item.value) < 0) {
    //     list.push(item);
    //   }
    // }
    // parameterNameList.value = list;
  } else {
    parameterNameList.value = [];
  }
};

onMounted(() => {
  initParameterNameList();
});

const handleQuery = async (pageNum: number) => {
  if (parameterName.value) {
    try {
      loading.value = true;
      const res = await $get(gateWay + service.administrate + '/parmCommonParm/queryPageByParameterClassNo', {
        parameterClassNo: parameterName.value,
        pageSize: pagination.pageSize,
        pageNum: pageNum ? pageNum : pagination.current,
      });
      loading.value = false;
      if (res.code === SUCCESS_CODE) {
        const list = res.data?.records || [];
        const { total = 0, current = 1, size = 10 } = res.data || {};
        dataSource.value = list.map((val) => {
          return {
            idParmComnParm: val.idParmComnParm || '',
            parameterClassNo: val.parameterClassNo || '', //参数类型
            parameterCode: val.parameterCode || '', //参数代码
            parameterName: val.parameterName || '', //参数代码名称
            displaySequenceNo: val.displaySequenceNo || '', // 排序
            effectiveTime: val.effectiveTime || '', // 生效日期
            idParentParmComnParm: val.idParentParmComnParm || '',
            parentParameterClassNo: val.parentParameterClassNo || '', //父级参数类型
            parentParameterClassName: val.parentParameterClassName || '', //父级参数类型名称
            parentParameterCode: val.parentParameterCode || '', //父级参数代码
            parentParameterName: val.parentParameterName || '', // 父级参数代码名称
            remark: val.remark || '',
          };
        });
        pagination.total = total;
        pagination.current = current;
        pagination.pageSize = size;
      } else {
        dataSource.value = [];
        pagination.total = 0;
        pagination.current = 1;
        pagination.pageSize = 10;
      }
    } catch (err) {
      loading.value = false;
      console.log(err);
    }
  } else {
    message.error('请先选择参数类型名称');
  }
};
// 分页处理
const { pagination } = usePagination(handleQuery);

const handleUpdate = (item) => {
  tableData.value = item;
  visible.value = true;
  modelType.value = 'edit';
};

const deleteParamter = async (id: string) => {
  try {
    const res = await $post(gateWay + service.administrate + '/parmCommonParm/deleteParmCommonParm', { idParmComnParm: id });
    if (res.code === SUCCESS_CODE) {
      message.success('删除成功');
      pagination.current = 1;
      handleQuery();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (err) {
    console.log(err);
  }
};
const handleDelete = (item) => {
  Modal.confirm({
    title: '温馨提示',
    content: '确定要删除？',
    class: 'bell-bg',
    centered: true,
    onOk: async () => {
      deleteParamter(item.idParmComnParm);
    },
  });
};

const visibleDetail = ref(false);
const handleView = (item) => {
  tableData.value = item;
  visibleDetail.value = true;
};

const visible = ref(false);
const handleAdd = () => {
  visible.value = true;
  modelType.value = 'add';
};

const reload = () => {
  pagination.current = 1;
  handleQuery();
};
</script>
