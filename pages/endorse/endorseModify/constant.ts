type SceneEnum = {
  apply: string;
  modify: string;
  route: string;
};

type SceneEnums = {
  [key: string]: SceneEnum;
};

export const sceneEnums: SceneEnums = {
  // 整单批改
  '90001': {
    apply: 'WHOLE_APPLY',
    modify: 'WHOLE_MODIFY',
    route: 'endorseModify',
  },
  // 退保
  '00006': {
    apply: 'CANCELLATION_APPLY',
    modify: 'CANCELLATION_MODIFY',
    route: 'surrenderModify',
  },
  // 注销
  '00017': {
    apply: 'WRITE_OFF_APPLY',
    modify: 'WRITE_OFF_MODIFY',
    route: 'cancelModify',
  },
  // 保费来源
  '90003': {
    apply: 'PREMIUM_FROM_APPLY',
    modify: 'PREMIUM_FROM_MODIFY',
    route: 'premiumSource',
  },
};
