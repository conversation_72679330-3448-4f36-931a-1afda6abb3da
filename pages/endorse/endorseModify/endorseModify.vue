<template>
  <div ref="scrollWrapper" class="page-container">
    <main ref="endorseModifyRef" class="main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[48px] w-ful bg-image-url">
          <span class="text-[16px] font-semibold text-[#00190c] leading-[48px] ml-[16px]">批改单信息</span>
        </div>
        <a-spin :spinning="loading">
          <div class="grid grid-cols-4 gap-x-16px bg-white h-[46px] items-center pl-[24px] pr-[24px]">
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">批改申请单号：</span>
              <span class="font-number">{{ contractModel.endorseApplyNo || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">保单号：</span>
              <span class="font-number">{{ contractModel.policyNo || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">录单人：</span>
              <span class="font-number">{{ contractModel.edrApplyBaseInfoValue.inputBy || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">批改类型：</span>
              <span class="font-number">{{ contractModel.edrApplySceneInfoValue.sceneName }}</span>
            </div>
          </div>
          <a-form :colon="false">
            <div class="space-y-16px">
              <InfoGroupBox id="modify-content" title="本次批改内容">
                <ModifyInfo ref="modifyContentRef" v-model:data="contractModel.edrApplyBaseInfoValue" v-model:reason="contractModel.edrApplySceneInfoValue" :insurance-begin-date="contractModel?.contractValue?.baseInfo?.insuranceBeginDate" />
              </InfoGroupBox>
              <InfoGroupBox id="basic-info" title="基本信息">
                <BaseInfo ref="baseInfoRef" v-model:data="contractModel.contractValue.baseInfo" />
              </InfoGroupBox>
              <InfoGroupBox id="endorse-formula" title="批改公式">
                <EndorseFormula ref="endorseFormula" v-model:data="contractModel.edrApplyBaseInfoValue" />
              </InfoGroupBox>
              <InfoGroupBox id="publicity-info" title="清单信息">
                <PublicityInfo ref="publicityRef" :farmer-detailed-list="contractModel.insrSpltPlyEdrSumyVO" @modify="farmerModify" />
              </InfoGroupBox>
              <InfoGroupBox id="customer-info" title="客户信息">
                <CustomerInfo ref="customerInfoRef" v-model:organizer-info="contractModel.contractValue.organizerInfo" v-model:insurant-info-list="contractModel.contractValue.insurantInfoList" v-model:applicant-info-list="contractModel.contractValue.applicantInfoList" v-model:beneficary-info-list="contractModel.contractValue.beneficaryInfoList" class="customer-info" :insurance-way="contractModel?.contractValue?.baseInfo?.applyPolicyType" :insurant-filed-disabled="false" :is-anti-money="isAntiMoney" :insure-type="contractModel?.contractValue.riskGroupInfoList?.[0]?.agriculturalRiskObjectClassCode" :sec-dept-code="contractModel?.contractValue?.baseInfo?.secDeptCode" />
              </InfoGroupBox>
              <InfoGroupBox id="target-info" title="标的信息">
                <PlantRisk ref="riskFormRef" v-model:data="contractModel.contractValue.riskGroupInfoList" v-model:address-list="contractModel.contractValue.riskAddressInfoList" :more-risk-flag="contractModel.moreRiskFlag" :department-code="contractModel.contractValue?.baseInfo?.departmentCode" :product-code="contractModel.contractValue?.baseInfo?.productCode" :product-version="contractModel.contractValue?.baseInfo?.productVersion" show-address-tips @change-amount="changeAmount" />
              </InfoGroupBox>
              <InfoGroupBox id="insurance-plan" title="保险方案">
                <InsurancePlan ref="planFormRef" v-model:data="contractModel.contractValue.riskGroupInfoList" :product-code="contractModel.contractValue?.baseInfo?.productCode" :product-version="contractModel.contractValue?.baseInfo?.productVersion" :department-code="contractModel.contractValue?.baseInfo?.departmentCode" :short-time-coefficient="contractModel.contractValue?.baseInfo?.shortTimeCoefficient" :gov-subsidy-type="contractModel.contractValue?.baseInfo.govSubsidyType" :sum-risk-group-amount="contractModel.contractValue?.sumRiskGroupAmount" :insured-number="contractModel.contractValue?.baseInfo?.insuredNumber" :farmers-count="contractModel.contractValue?.baseInfo?.farmersCount" :insured-unit="contractModel.contractValue?.riskGroupInfoList?.[0]?.riskAgrInfo?.insuredUnit" />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel.edrApplyPayInfoListVOList.length > 0" id="fee-plan" title="收费计划">
                <FeePlan ref="payInfoRef" v-model:pay-info-list="contractModel.edrApplyPayInfoListVOList" v-model:base-info="contractModel.contractValue.baseInfo" :inner-coinsurance-mark="contractModel.contractValue?.coinsuranceInfo?.innerCoinsuranceMark" :payer-type-sum-of-pay-info="contractModel.edrApplyPayInfoTypeSumVO" :combined-product-code="contractModel.contractValue?.riskGroupInfoList?.[0]?.combinedProductCode" />
              </InfoGroupBox>
              <InfoGroupBox id="disclaimers-info" title="免赔信息">
                <DisclaimInfo ref="disclaimRef" v-model:disclaim-info="contractModel.contractValue.noclaimInfoList" v-model:base-info="contractModel.contractValue.baseInfo" :risk-group-info-list="contractModel.contractValue.riskGroupInfoList" :sum-risk-group-amount="contractModel.contractValue?.sumRiskGroupAmount" />
              </InfoGroupBox>
              <InfoGroupBox id="special-appoint" title="特别约定">
                <SpecialSet v-model="contractModel.contractValue.specialPromiseList" :department-code="contractModel.contractValue.baseInfo?.departmentCode" :market-product-code="contractModel.contractValue.baseInfo?.productCode" />
              </InfoGroupBox>
              <InfoGroupBox id="fee-info" title="费用信息">
                <CostInfo ref="feeInfoRef" v-model:cost-info="contractModel.contractValue.costInfo" :base-info="contractModel.contractValue.baseInfo" :coinsurance-info="contractModel.contractValue.coinsuranceInfo" :business-source-code="contractModel.contractValue.saleInfo?.businessSourceCode" :sign-data-list="signDataList" :combined-product-code="contractModel.contractValue.riskGroupInfoList?.[0]?.combinedProductCode" :total-actual-premium="contractModel.contractValue?.payerTypeSumOfPayInfo?.payerType5" @get-eoa-data="getEoaData" />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel.contractValue.baseInfo.coinsuranceMark === '1'" id="co-insurance" title="共保信息">
                <CoInsuranceInfo ref="coInsuranceRef" v-model="contractModel.contractValue.coinsuranceInfo" :department-code="contractModel.contractValue.baseInfo.departmentCode" :accept-insurance-flag="contractModel.contractValue.baseInfo.acceptInsuranceFlag" :total-actual-premium="contractModel.contractValue.baseInfo?.totalActualPremium" :apply-policy-no="contractModel.contractValue.baseInfo?.applyPolicyNo" :insurance-begin-date="contractModel.contractValue.baseInfo?.insuranceBeginDate" :product-code="contractModel.contractValue.baseInfo?.productCode" :sale-info="contractModel.contractValue.saleInfo" :sign-data-list="signDataList" @get-eoa-data="getEoaData" />
              </InfoGroupBox>
            </div>
          </a-form>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button :disabled="saveDisabled" type="primary" @click="saveData(true)">暂存</a-button>
      <a-button :disabled="premiumComputeDisabled" type="primary" @click="validateAllForms('1')">保费计算</a-button>
      <a-button :disabled="submitApprovalDisabled" type="primary" @click="validateAllForms('2')">申请核保</a-button>
      <a-button type="primary" @click="goUpload">附件管理</a-button>
    </footer>
    <!-- 错误信息弹窗 -->
    <a-modal v-model:open="errorVisible" :width="pxToRem(480)" :centered="true" :style="{ 'max-height': pxToRem(300), overflow: 'auto' }" @ok="errorVisible = false">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#f03e3e]" :icon="IconCloseCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">温馨提示</span>
      </div>
      <div v-for="(item, i) in confirmErrText" :key="i">{{ item }}</div>
      <template #footer>
        <a-space>
          <a-button type="primary" @click="errorVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal>
    <!-- eoa签报 -->
    <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" title="批改-EOA签报申请页" :eoa-type="eoaTypeValue" :department-code="departmentCode" :show-uploadbtn="true" @ok="handleSubmit" @reset="handleReset">
      <template #content>
        <div>
          <a-form-item label="签报主题" name="eoaTitle">
            <a-input v-model:value="eoaData.eoaTitle" disabled />
          </a-form-item>
          <a-form-item label="签报内容" name="eoaContent">
            <a-textarea v-model:value="eoaData.eoaContent" disabled />
          </a-form-item>
        </div>
      </template>
    </CommonApprovalModal>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import { IconCloseCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import BaseInfo from './components/BaseInfo.vue';
import CustomerInfo from './components/customerInfo/CustomerInfo.vue';
import PlantRisk from './components/PlantRisk.vue';
import PublicityInfo from './components/PublicityInfo.vue';
import InsurancePlan from './components/InsurancePlan.vue';
import ModifyInfo from './components/ModifyInfo.vue';
import FeePlan from './components/FeePlan.vue';
import DisclaimInfo from './components/DisclaimInfo.vue';
import SpecialSet from './components/specialSet.vue';
import CostInfo from './components/CostInfo.vue';
import CoInsuranceInfo from './components/coinsuranceInfo/CoInsuranceInfo.vue';
import EndorseFormula from './components/EndorseFormula.vue';
import type { CustomerInfo as TypeCustomerInfo } from './components/customerInfo/customerInfo.d';
import type { contractModelType, SignData } from './endorseModify.d';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import { pxToRem, getImg, base64toFile } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';

const scrollWrapper = ref();
const endorseModifyRef = ref();
const getContainer = () => scrollWrapper.value || window;
// 错误信息弹窗显示隐藏
const errorVisible = ref<boolean>(false);
// 错误信息描述
const confirmErrText = ref<string[]>([]);
// 是否触发反洗钱
const isAntiMoney = ref<boolean>(false);
const route = useRoute();
const router = useRouter();
const showApproval = ref<boolean>(false); // 是否展示签报弹窗
const departmentCode = ref<string>(route.query.departmentCode as string); // 机构code
const eoaTypeValue = ref<string>(''); // 链路type，获取审批链路人员信息
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签
// 签报数据
const eoaData = ref<Record<string, string>>({
  eoaTitle: '',
  eoaContent: '',
});
// 提交签报
const handleSubmit = async (eoaData: Record<string, string>) => {
  previewOrCreate(eoaData);
};
// 创建签报
const attachmentList = ref();
const previewOrCreate = async (hanlderEoaData: Record<string, string>) => {
  try {
    // 用于提交签报的参数
    const params = {
      endorseCreateEoaVO: {
        relationBusinessNo: contractModel.value.edrApplyBaseInfoValue?.endorseApplyNo, // 批改申请单号
        eoaType: '003', // EOA类型：003:整单批改核保申请、004:注销核保申请、005:退保核保申请
        eoaSubject: eoaData.value.eoaTitle, // 标题  $1:保单号 $2:场景，整单批改、退保等
        eoaBody: eoaData.value.eoaContent, // 批改内容(批文)
        documentGroupId: hanlderEoaData.documentGroupId, // 附件组ID
        productCode: route.query.productCode, // 产品编码
        departmentCode: route.query.departmentCode, // 机构
        approveChainList: hanlderEoaData.approveChainList,
        attachmentList: attachmentList.value,
      },
    };
    const res = await $postOnClient(`${gateWay}${service.endorse}/eoa/createEoa`, params); // 创建eoa审批接口
    if (res && res.code === SUCCESS_CODE) {
      message.success('签报发起成功');
      showApproval.value = false; // 关闭签报弹窗
      deletPageTabListItem('/endorseModify'); // 关闭页签
      deletPageTabListItem('/endorseUpload');
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
    } else {
      message.error(res?.msg as string);
    }
  } catch (error) {
    console.log(error);
  }
};
// 查询eoaType类型用于获取审批链路人员
const queryEoaType = async () => {
  try {
    const params = {
      endorseApplyNo: contractModel.value.edrApplyBaseInfoValue?.endorseApplyNo, // 批改申请单号
      policyNo: route.query.endorseApplyNo, // 保单号
      signType: '003', // 003:整单批改核保申请、005:退保核保申请
    };
    const res = await $postOnClient<string>(`${gateWay}${service.endorse}/eoa/getEoaType`, params); // 获取eoaType类型用于获取审批链路
    if (res?.code === SUCCESS_CODE) {
      eoaTypeValue.value = res?.data;
    }
  } catch (error) {
    console.log(error);
  }
};
// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#modify-content', title: '本次批改内容' },
  { key: '2', href: '#basic-info', title: '基本信息' },
  { key: '3', href: '#endorse-formula', title: '批改公式' },
  { key: '4', href: '#publicity-info', title: '清单信息' },
  { key: '5', href: '#customer-info', title: '客户信息' },
  { key: '6', href: '#target-info', title: '标的信息' },
  { key: '7', href: '#insurance-plan', title: '保险方案' },
  { key: '9', href: '#disclaimers-info', title: '免赔信息' },
  { key: '10', href: '#special-appoint', title: '特别约定' },
  { key: '11', href: '#fee-info', title: '费用信息' },
]);

const initContractModel = (data: contractModelType) => {
  console.log(initCustomer(data.contractValue?.applicantInfoList), 'initCustomer(data.contractValue?.applicantInfoList)--');
  if (!data.edrApplyBaseInfoValue.calculateType) {
    data.edrApplyBaseInfoValue.calculateType = '2';
  }
  return {
    ...data,
    applyPolicyNo: data.applyPolicyNo || '',
    moreRiskFlag: data.moreRiskFlag || '',
    edrApplyBaseInfoValue: data.edrApplyBaseInfoValue || {}, // 基础信息
    edrApplySceneInfoValue: data.edrApplySceneInfoValue || {}, // 场景
    edrApplyPayInfoListVOList: data.edrApplyPayInfoListVOList || [], // 收费计划
    endorseApplyNo: data.endorseApplyNo || '',
    endorseNo: data.endorseNo || '',
    insrSpltPlyEdrSumyVO: data.insrSpltPlyEdrSumyVO || {}, // 清单信息
    policyNo: data.policyNo || '',
    edrApplyAttachmentVO: {
      ...data.edrApplyAttachmentVO,
      documentGroupId: data?.edrApplyAttachmentVO?.documentGroupId || route.query.documentGroupId,
    },
    edrApplyPayInfoTypeSumVO: data?.edrApplyPayInfoTypeSumVO || {},
    contractValue: {
      ...data.contractValue,
      baseInfo: data.contractValue?.baseInfo || {},
      organizerInfo: data.contractValue?.organizerInfo || {}, // 组织者
      applicantInfoList: initCustomer(data.contractValue?.applicantInfoList) || [], // 投保人
      insurantInfoList: initCustomer(data.contractValue?.insurantInfoList) || [], // 被保人
      beneficaryInfoList: initCustomer(data.contractValue?.beneficaryInfoList) || [], // 收益人
      riskAddressInfoList: data.contractValue?.riskAddressInfoList || [], // 标的
      riskGroupInfoList: data.contractValue?.riskGroupInfoList || [], // 标的
      sumRiskGroupAmount: data.contractValue?.sumRiskGroupAmount || {}, // 保费计划合计
      payInfoList: data.contractValue?.payInfoList || [], // 收费计划
      coinsuranceInfo: data.contractValue?.coinsuranceInfo || {}, // 共保
      payerTypeSumOfPayInfo: data.contractValue?.payerTypeSumOfPayInfo || {}, // 收费按类别汇总用于校验
    },
  };
};

const initCustomer = (data) => {
  if (!data?.length) {
    return [];
  }
  if (!data?.[0]?.superviseInfoList?.[0]) {
    return [
      {
        ...data?.[0],
        superviseInfoList: [
          {
            superviseExtendList: [],
          },
        ],
      },
    ];
  } else {
    return data;
  }
};
const data: contractModelType = {
  applyPolicyNo: '',
  edrApplyBaseInfoValue: {
    originPremium: '',
    totalActualPremium: '',
    actualPremiumChange: '',
    applyDate: '',
    effectiveDate: '',
    isSendElectronicEdrDocument: '',
    endorseComment: '',
    cancelReason: '',
    inputBy: '',
    endorseApplyNo: '',
    policyNo: '',
  },
  edrApplySceneInfoValue: {
    cancelType: '',
    sceneName: '',
  },
  edrApplyPayInfoListVOList: [],
  endorseApplyNo: '',
  endorseNo: '',
  insrSpltPlyEdrSumyVO: {},
  policyNo: '',
  edrApplyAttachmentVO: {},
  contractValue: {
    baseInfo: {
      assisterInfoList: [],
      insuranceBeginDate: '',
      insuranceEndDate: '',
      disputedSettleMode: '',
      arbitralDepartment: '',
      shortTimeCoefficient: '1',
      timeRange: '',
      departmentCode: '',
      applyPolicyNo: '',
      totalActualPremium: '',
      deductionDesc: '',
      premiumOwner: '',
      coinsuranceMark: '',
      govSubsidyType: '',
      acceptInsuranceFlag: '',
      applyPolicyType: '',
      productCode: '',
      productVersion: '',
      insuredNumber: '0',
      farmersCount: 0,
    },
    applicantInfoList: [],
    insurantInfoList: [],
    beneficaryInfoList: [],
    riskAddressInfoList: [],
    riskGroupInfoList: [],
    sumRiskGroupAmount: {
      totalInsuredAmount: '',
      totalStandardPremium: '',
      totalAgreePremium: '',
      totalActualPremium: '',
    },
    coinsuranceInfo: {
      innerCoinsuranceMark: '',
      innerCoinsuranceAgreement: '',
      totalPremium: '',
      totalInsuredAmount: '',
      coinsuranceDetailList: [],
      innerCoinsuranceMarkChName: '',
    },
    payInfoList: [],
    payerTypeSumOfPayInfo: {},
    organizerInfo: {},
  },
};

const contractModel = ref(initContractModel(data));

const loading = ref(false);

const customerInfoRef = ref();
const riskFormRef = ref();
const baseInfoRef = ref();
const endorseFormula = ref();
const planFormRef = ref();
const modifyContentRef = ref();
const payInfoRef = ref();
const disclaimRef = ref();
const getValidateArr = (validatePayInfo: boolean) => {
  const validateItems = [
    riskFormRef.value?.validate(),
    baseInfoRef.value?.validate(),
    endorseFormula.value?.validate(),
    customerInfoRef.value?.validate(),
    planFormRef.value?.validate(),
    modifyContentRef.value?.validate(),
    disclaimRef.value?.validate(),
    // 其他子组件的校验方法
  ];
  if (payInfoRef.value && validatePayInfo) {
    validateItems.push(payInfoRef.value?.validate());
  }
  return validateItems;
};

const premiumComputeDisabled = ref(false);
const submitApprovalDisabled = ref(false);
const validateAllForms = async (type: string) => {
  // 1为保费计算  2为提交核保
  if (type === '1') {
    premiumComputeDisabled.value = true;
  } else if (type === '2') {
    submitApprovalDisabled.value = true;
  }
  try {
    const results = await Promise.all(getValidateArr(true));
    const isValid = results.every((result) => result.valid);
    if (isValid) {
      // 所有表单校验通过
      if (type === '1') {
        premiumCompute();
      } else if (type === '2') {
        submitApproval();
      }
    } else {
      premiumComputeDisabled.value = false;
      submitApprovalDisabled.value = false;
      // 有表单校验未通过，提示用户
      console.log('有表单校验未通过', results);
      // 跳转到校验不通过的第一个项目
      const errorDiv = document.querySelector('.ant-form-item-has-error');
      errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  } catch {
    premiumComputeDisabled.value = false;
    submitApprovalDisabled.value = false;
  }
};
// 林业险碳汇触发自动补全金额
const changeAmount = (value: number) => {
  planFormRef?.value.changeAmount(value);
};

// 提交时投保人模块的值
const transCustomerInfo = (customerData: Array<TypeCustomerInfo>, insurantData: Array<TypeCustomerInfo>) => {
  const customerDataClone = cloneDeep(customerData);
  const insurantDataClone = cloneDeep(insurantData);
  // 如果sameInsuredPersons为1，代表和被保险人数据一致，需要把被保人的数据填充并返回
  const isSameInsuredPersons = customerDataClone?.[0]?.sameInsuredPersons !== '0';
  const sourceData = isSameInsuredPersons ? { ...insurantDataClone?.[0], sameInsuredPersons: '1', id: customerDataClone?.[0]?.id, subjectNumber: customerDataClone?.[0]?.subjectNumber } : customerDataClone?.[0];
  if (isSameInsuredPersons && sourceData?.superviseInfoList?.[0]?.superviseExtendList) {
    sourceData.superviseInfoList[0].id = customerData?.[0]?.superviseInfoList?.[0]?.id;
    sourceData.superviseInfoList[0].superviseExtendList = sourceData.superviseInfoList[0].superviseExtendList.map((v, i) => ({ ...v, id: customerData?.[0]?.superviseInfoList?.[0]?.superviseExtendList?.[i]?.id }));
  }
  // 如果有投保人是否书面声明字段，说明是投保人，需要回填isConfirm字段
  if (customerData?.[0]?.isConfirm) {
    sourceData.isConfirm = customerData[0].isConfirm;
  }
  return [sourceData];
};
// 提交的VO数据
const submitData = computed(() => {
  const tempData = {
    ...contractModel.value,
    contractValue: {
      ...contractModel.value.contractValue,
      beneficaryInfoList: transCustomerInfo(contractModel.value.contractValue.beneficaryInfoList, contractModel.value.contractValue.insurantInfoList),
      applicantInfoList: transCustomerInfo(contractModel.value.contractValue.applicantInfoList, contractModel.value.contractValue.insurantInfoList),
      riskGroupInfoList: planFormRef.value.transformData(contractModel.value.contractValue?.riskGroupInfoList),
    },
  };
  // 提交前数据处理
  return tempData;
});

// 暂存
const saveDisabled = ref(false);
const saveData = async (showMsg?: boolean) => {
  const params = {
    ...submitData.value,
  };
  try {
    saveDisabled.value = true;
    const res = await $postOnClient<{ endorseApplyNo: string }>(`${gateWay}${service.endorse}/web/endorse/temp-storage`, params);
    saveDisabled.value = false;
    if (res && res.code === SUCCESS_CODE) {
      if (showMsg) {
        message.success('保存成功');
      }
      contractModel.value.endorseApplyNo = res.data.endorseApplyNo;
      router.push({
        name: 'endorseModify',
        query: {
          ...route.query,
          qryScene: 'WHOLE_MODIFY',
        },
      });
      getInit('WHOLE_MODIFY');
    } else {
      message.error(res?.msg || '操作失败');
    }
  } catch (error) {
    saveDisabled.value = false;
    console.log(error);
  }
};

// 保费计算
const premiumCompute = async () => {
  loading.value = true;
  try {
    const fetchUrl = `${gateWay}${service.endorse}/web/endorse/quote`;
    const params = {
      ...submitData.value,
    };
    const res = await $postOnClient<{ endorseApplyNo: string }>(fetchUrl, params);
    loading.value = false;
    premiumComputeDisabled.value = false;
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      isAntiMoney.value = false;
      contractModel.value.endorseApplyNo = res?.data?.endorseApplyNo || '';
      router.push({
        name: 'endorseModify',
        query: {
          ...route.query,
          qryScene: 'WHOLE_MODIFY',
        },
      });
      getInit('WHOLE_MODIFY');
    } else if (code === 'PEF1001') {
      message.error(msg);
      // PEF1001 代表后端触发反洗钱校验
      isAntiMoney.value = true;
      // 跳转到客户信息
      const customerDiv = document.querySelector('.customer-info');
      customerDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      // 错误提示
      handleHint(msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
    premiumComputeDisabled.value = false;
  }
};
// 接口返回错误信息提示
const handleHint = (msg: string) => {
  errorVisible.value = true;
  confirmErrText.value = msg?.split(';');
};
// 收费计划数据处理
const changePayInfoData = () => {
  if (contractModel.value?.edrApplyPayInfoListVOList?.length > 0) {
    // 缴费起至时间处理为数组
    contractModel.value.edrApplyPayInfoListVOList.forEach((item: { paymentPeriod: (string | Dayjs)[]; paymentBeginDate: string | Dayjs; paymentEndDate: string | Dayjs }) => {
      item.paymentPeriod = [];
      const today = dayjs(new Date()).format('YYYY-MM-DD');
      item.paymentPeriod[0] = item.paymentBeginDate || dayjs(today);
      item.paymentBeginDate = item.paymentBeginDate || dayjs(today);
      item.paymentPeriod[1] = item.paymentEndDate || '';
    });
    // payInfoList有数据，右侧锚点显示收费计划
    const index = anchorItems.value.findIndex((item) => item.href === '#fee-plan');
    if (index === -1) {
      anchorItems.value.splice(7, 0, {
        key: '8',
        href: '#fee-plan',
        title: '收费计划',
      });
    }
  } else {
    // payInfoList没数据，右侧锚点不显示收费计划
    anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
  }
};
// 标的信息数据处理
const setRiskData = () => {
  if (contractModel.value?.contractValue.riskGroupInfoList?.length > 0) {
    contractModel.value?.contractValue.riskGroupInfoList.forEach((item: RiskGroupInfo) => {
      item.defaultRisk = item.combinedProductCode;
    });
  }
};
const getInit = async (sceneName?: string) => {
  loading.value = true;
  try {
    const res = await $postOnClient(`${gateWay}${service.endorse}/web/endorse/detail`, {
      policyNo: route.query.endorseApplyNo,
      qryScene: sceneName || route.query.qryScene || 'WHOLE_APPLY',
      endorseSceneCode: '90001',
    });
    if (res && res.code === SUCCESS_CODE) {
      loading.value = false;
      contractModel.value = initContractModel((res.data as contractModelType) || {});
      changePayInfoData();
      setRiskData();
      // 免赔信息：免赔项目级联选择器回显数据处理
      if (contractModel.value.contractValue.noclaimInfoList?.length > 0) {
        contractModel.value.contractValue.noclaimInfoList = contractModel.value.contractValue.noclaimInfoList.map((item: { noclaimItem: string; planCode: string; dutyCode: string; noclaimItemArr: string[] }) => {
          item.noclaimItemArr = [];
          if (item.noclaimItem) {
            item.noclaimItemArr.push(item.noclaimItem);
            if (item.planCode) {
              item.noclaimItemArr.push(item.planCode);
              if (item.dutyCode) {
                item.noclaimItemArr.push(item.dutyCode);
              }
            }
          }
          return item;
        });
      }
      // 如果共保信息存在，右侧大纲显示共保信息
      const addCoinsurance = anchorItems.value.findIndex((item) => item.href === '#co-insurance') === -1;
      if (contractModel.value.contractValue.baseInfo.coinsuranceMark === '1' && addCoinsurance) {
        anchorItems.value.push({ key: '12', href: '#co-insurance', title: '共保信息' });
      }
      if (contractModel.value?.edrApplyPayInfoListVOList) {
        for (const [index, item] of contractModel.value.edrApplyPayInfoListVOList.entries()) {
          if (item.bankHeadquartersCode) {
            await payInfoRef.value?.getBankBranchList(item.bankHeadquartersCode, index);
          }
        }
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

// 提交核保
const submitApproval = async () => {
  loading.value = true;
  const params = {
    ...submitData.value,
    edrEoaVO: {
      eoaStatus: 'E', // 后端说会有其他场景传其他参数
    },
  };
  try {
    const fetchUrl = gateWay + service.endorse + '/web/endorse/apply';
    const res = await $postOnClient<{ resultEoa: string }>(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      // 触发审批链
      if (res.data?.resultEoa === 'Y') {
        loading.value = false;
        // 先获取图片 浏览器空闲时会回调的异步函数
        requestIdleCallback(() => {
          getImg(endorseModifyRef.value).then((result) => {
            if (result) {
              const file = base64toFile(result, '整单批改核保申请详情');
              const formData = new FormData();
              formData.append('file', file);
              formData.append('bizNo', contractModel.value.edrApplyBaseInfoValue?.endorseApplyNo);
              formData.append('bizType', 'docTypeEndorse');
              formData.append('documentGroupId', '');
              formData.append('fileType', '');
              const fetchUrl = `${gateWay}${service.administrate}/attachment/document/upload`;
              if (formData) {
                $postOnClient(fetchUrl, formData).then((res) => {
                  if (res && res.code === SUCCESS_CODE) {
                    const {
                      uploadPath = '',
                      documentName = '',
                      documentSize = '',
                      documentFormat = '',
                    } = res?.data as {
                      uploadPath: string;
                      documentName: string;
                      documentSize: string;
                      documentFormat: string;
                    };
                    attachmentList.value = [
                      {
                        attachUrl: uploadPath,
                        docoldName: documentName,
                        fileSize: documentSize,
                        contentType: documentFormat,
                      },
                    ];
                  }
                });
              }
            }
          });
        });
        await queryEoaType(); // 获取审批链路类型
        eoaData.value.eoaTitle = `关于保单[${contractModel.value.edrApplyBaseInfoValue?.policyNo}]进行[${contractModel.value?.edrApplySceneInfoValue?.sceneName}]的申请 `; // 签报主题
        eoaData.value.eoaContent = contractModel.value.edrApplyBaseInfoValue.endorseComment; // 签报内容
        showApproval.value = true;
      } else {
        message.success(res?.msg);
        deletPageTabListItem('/endorseModify'); // 关闭页签
        deletPageTabListItem('/endorseUpload');
        // 返回上一页或者首页
        if (pageTabList.value.length) {
          router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
        } else {
          router.push('/home');
        }
      }
    } else {
      message.error(res?.msg || '提交核保失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
    submitApprovalDisabled.value = false;
  }
};

// 跳转附件管理
const goUpload = () => {
  router.push({
    name: 'endorseUpload',
    query: {
      ...route.query,
      endorseApplyNo: contractModel?.value?.endorseApplyNo,
      documentGroupId: contractModel?.value?.edrApplyAttachmentVO?.documentGroupId,
      policyNo: contractModel?.value?.policyNo,
    },
  });
};

// 修改清单前保存当前页面信息生成批改申请单号
const farmerModify = async (queryData) => {
  loading.value = true;
  const params = {
    ...submitData.value,
  };
  try {
    const res = await $postOnClient<{ endorseApplyNo: string }>(`${gateWay}${service.endorse}/web/endorse/insrSpltEdrListModify`, params);
    if (res && res.code === SUCCESS_CODE) {
      contractModel.value.endorseApplyNo = res.data.endorseApplyNo;
      // 跳转当前页面改变当前页面路由参数
      router.push({
        name: 'endorseModify',
        query: {
          ...route.query,
          qryScene: 'WHOLE_MODIFY',
        },
      });
      loading.value = false;
      router.push({
        name: 'listModify',
        query: {
          ...queryData,
          endorseApplyNo: res.data.endorseApplyNo,
        },
      });
    } else {
      message.error(res?.msg || '操作失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 关闭审批弹窗获取详情
const handleReset = () => {
  getInit('WHOLE_MODIFY');
};

onActivated(() => {
  getInit();
});

const { gateWay, service } = useRuntimeConfig().public || {};

// 签报数据
const signDataList = ref<SignData[]>([]);
// 获取eoa签报数据
const getEoaData = async () => {
  const res = await $getOnClient<SignData[]>(`${gateWay}${service.accept}/accept/eoa/queryApplyPolicySignDataList`, { applyPolicyNo: route.query.applyPolicyNo });
  if (res && res.code === SUCCESS_CODE) {
    signDataList.value = res.data || [];
  }
};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 16px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
