<template>
  <a-modal v-model:open="visible" title="清单信息修改" :width="pxToRem(800)" centered :confirm-loading="confirmLoading" :mask-closable="false" @ok="InfoModify">
    <div class="bg-gray-100 rounded p-14px">
      <a-form ref="formRef" :model="modifyInfo" :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
        <a-form-item label="标的地址" name="villageCode" required>
          <RegionSelect v-model:province="modifyInfo.provinceCode" v-model:city="modifyInfo.cityCode" v-model:county="modifyInfo.countyCode" v-model:town="modifyInfo.townCode" v-model:village="modifyInfo.villageCode" :init-change="true" @change-selected="regionChange" />
          <div class="mt-[12px]">
            <a-input v-model:value="wholeAddress" disabled placeholder="请输入" />
          </div>
        </a-form-item>
        <a-form-item label="清单名称" name="farmerListName" required>
          <a-input v-model:value="modifyInfo.farmerListName" placeholder="请输入清单名称" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { pxToRem } from '@/utils/tools';
import { $post } from '@/utils/request';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import type { RegionType } from '../endorseModify.d';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  farmerlistName: string;
  riskAddressCodeCity: string;
  riskAddressCodeCounty: string;
  riskAddressCodeProvince: string;
  riskAddressCodeTown: string;
  riskAddressCodeVillage: string;
  refresh: () => void;
}>();

const { gateWay, service } = useRuntimeConfig().public || {};

const route = useRoute();

// 信息修改表单
const modifyInfo = ref({
  provinceCode: '',
  cityCode: '',
  countyCode: '',
  townCode: '',
  villageCode: '',
  provinceName: '',
  cityName: '',
  countyName: '',
  townName: '',
  villageName: '',
  farmerListName: '',
});

const wholeAddress = computed(() => modifyInfo.value.provinceName + modifyInfo.value.cityName + modifyInfo.value.countyName + modifyInfo.value.townName + modifyInfo.value.villageName);

const regionChange = (selected: { value: string; label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    modifyInfo.value[`${type}Name`] = selected?.label || '';
  }
};

const formRef = ref();
const confirmLoading = ref(false);
const InfoModify = async () => {
  await formRef.value.validate();
  try {
    confirmLoading.value = true;
    const res = await $post(`${gateWay}${service.farmer}/insrSpltPlyEdrSumy/updateNameAndRiskAddress`, {
      riskAddressCodeProvince: modifyInfo.value.provinceCode,
      riskAddressCodeCity: modifyInfo.value.cityCode,
      riskAddressCodeCounty: modifyInfo.value.countyCode,
      riskAddressCodeTown: modifyInfo.value.townCode,
      riskAddressCodeVillage: modifyInfo.value.villageCode,
      farmerlistName: modifyInfo.value.farmerListName,
      endorseApplyNo: route.query.endorseApplyNo || '',
    });
    confirmLoading.value = false;
    if (res?.code === SUCCESS_CODE) {
      visible.value = false;
      props.refresh();
    } else {
      message.error(res?.msg || '');
    }
  } catch {
    confirmLoading.value = false;
  }
};

const getInitModifyInfo = () => {
  modifyInfo.value.provinceCode = props.riskAddressCodeProvince;
  modifyInfo.value.cityCode = props.riskAddressCodeCity;
  modifyInfo.value.countyCode = props.riskAddressCodeCounty;
  modifyInfo.value.townCode = props.riskAddressCodeTown;
  modifyInfo.value.villageCode = props.riskAddressCodeVillage;
  modifyInfo.value.farmerListName = props.farmerlistName;
};

onMounted(() => {
  getInitModifyInfo();
});
</script>
