<template>
  <a-form ref="formRef" :model="riskList" :colon="false">
    <div class="flex items-center justify-end">
      <a-button v-if="isMultiTarget" :disabled="riskList.length === multiOption.length" type="primary" size="small" @click="addItem">新增</a-button>
    </div>
    <!-- 单标的 -->
    <template v-if="riskList.length > 0">
      <div v-for="(item, index) in riskList" :key="index">
        <div class="flex items-center justify-between">
          <div class="text-[#404442] text-[14px] font-semibold flex items-center mb-[16px]">
            <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
            {{ `标的${index + 1}` }}
          </div>
          <a-button v-if="isMultiTarget && riskList.length !== 1" type="link" size="small" @click="delItem(index)">删除</a-button>
        </div>
        <div class="grid grid-cols-3 gap-x-16px">
          <a-form-item required label="标的明细" :name="[index, 'combinedProductCode']" class="col-span-2" :label-col="{ style: { width: '90px' } }" :rules="[{ validator: (rule, val) => combinedProductValidator(rule, val, index) }]">
            <RiskCodeSelect :ref="(el) => setItemRef(el, index)" :show-search="false" :default-value="item.defaultRisk" :default-mul-risk="item.defaultRisk" :department-code="departmentCode" :is-multi-target="isMultiTarget" :multi-option="multiOption" :is-modify-page="true" :allow-edit="item.addRiskFlag === '1'" :disabled="addIsDisabled(item.addRiskFlag)" @change-value="(value, level, option) => changeRiskCode(value, level, option, index)" />
          </a-form-item>
          <a-form-item label="保险数量" :name="[index, 'riskAgrInfo', 'insuredNumber']" :rules="[{ required: true, validator: () => checkInsured(index) }]" :label-col="{ style: { width: '90px' } }">
            <a-input-group compact>
              <a-input v-model:value="riskList[index].riskAgrInfo.insuredNumber" :disabled="addIsDisabled(item.addRiskFlag)" style="width: 70%" @change="(e) => changeInsuredNumber(e, index)" />
              <a-select v-model:value="riskList[index].riskAgrInfo.insuredUnit" placeholder="请选择" :disabled="addIsDisabled(item.addRiskFlag)" :options="insuredNumberOptions" style="width: 30%" @change="(value, option) => handleRiskUnit(index, option)" />
            </a-input-group>
          </a-form-item>
          <a-form-item label="保险金额" :name="[index, 'riskAgrInfo', 'insuredAmount']" :label-col="{ style: { width: '90px' } }">
            <a-input v-model:value="riskList[index].riskAgrInfo.insuredAmount" :disabled="addIsDisabled(item.addRiskFlag)" />
          </a-form-item>
          <a-form-item label="保费金额" :name="[index, 'riskAgrInfo', 'premium']" :label-col="{ style: { width: '90px' } }" :disabled="addIsDisabled(item.addRiskFlag)">
            <a-input v-model:value="riskList[index].riskAgrInfo.premium" name="premium" :rules="[{ required: true }]" :disabled="addIsDisabled(item.addRiskFlag)" />
          </a-form-item>
          <a-form-item required label="参保农户数" :name="[index, 'riskAgrInfo', 'farmersCount']" :rules="[{ required: true }]" :label-col="{ style: { width: '90px' } }">
            <a-input v-model:value="riskList[index].riskAgrInfo.farmersCount" :disabled="addIsDisabled(item.addRiskFlag)" />
          </a-form-item>
          <a-form-item v-if="!isMultiTarget" required class="col-span-3" :label-col="{ style: { width: '90px' } }">
            <template #label>
              <span>标的地址</span>
              <a-tooltip class="ml-[3px]" placement="right">
                <template #title>
                  <span>需要修改标的地址，请前往批改清单编辑页面</span>
                </template>
                <VueIcon v-if="showAddressTips" :icon="IconErrorCircleFilledFont" class="text-[#666]" />
              </a-tooltip>
            </template>
            <CompareRender v-if="readonly" :value="addressList[0].address" value-path="contract.riskGroupInfoList[0].address" />
            <div v-else class="flex gap-x-8px">
              <RegionSelect v-model:province="addressList[0].province" v-model:city="addressList[0].city" v-model:county="addressList[0].county" v-model:town="addressList[0].town" v-model:village="addressList[0].village" style="width: 65%" :disabled="isDisabled || showAddressTips" @change-selected="changeRiskAddress" />
              <a-input v-model:value="addressList[0].address" placeholder="请输入" style="width: 35%" :disabled="isDisabled || showAddressTips" />
            </div>
          </a-form-item>
        </div>
        <div v-if="riskList[index].riskAgrInfo.agrRiskAttribute?.length" class="grid grid-cols-3 gap-x-16px" :class="{ 'divide-line': !isMultiTarget }">
          <template v-for="(obj, idx) in riskList[index].riskAgrInfo.agrRiskAttribute">
            <!-- 单位保险产量及单位 -->
            <a-form-item
              v-if="obj.bussinessKey === 'unitInsureCount'"
              :key="obj.bussinessKey"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                { validator: () => checkUnitInsureCount(obj.notRequiredFlag, index) },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过20位，小数位不超过2位',
                },
              ]"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
            >
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" :disabled="isDisabled" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="unitInsureCountOptions" style="width: 30%" :disabled="isDisabled" />
              </a-input-group>
            </a-form-item>
            <!-- 约定价格（指数）及单位 -->
            <a-form-item v-if="obj.bussinessKey === 'priceAgreed'" :key="obj.bussinessKey" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :rules="[{ validator: () => checkPriceAgreed(obj.notRequiredFlag, index) }, { pattern: /^\d{1,20}(\.\d{1,2})?$/, message: '输入整数且整数位不超过20位，小数位不超过2位' }]">
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" :disabled="isDisabled" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="priceAgreedOptions" style="width: 30%" :disabled="isDisabled" />
              </a-input-group>
            </a-form-item>
            <!-- 周期 -->
            <a-form-item v-if="obj.bussinessKey === 'claimsCycle'" :key="obj.bussinessKey" required :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :rules="[{ required: true }]">
              <a-select v-model:value="obj.bussinessValue" :options="claimsCycleOptions" :disabled="isDisabled" />
            </a-form-item>
            <!-- 每亩PH值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'agreedPh'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="`每${unitName || '亩'}PH值`"
              :rules="[
                { required: true },
                {
                  pattern: /^\d{1,12}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过12位，小数位不超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 每亩有机质值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'organicValue'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="`每${unitName || '亩'}有机质值`"
              :rules="[
                { required: true },
                {
                  pattern: /^\d{1,12}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过12位，小数位不超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 疾病观察期 -->
            <a-form-item
              v-if="obj.bussinessKey === 'diseaseObservation' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^[0-9]\d*$/,
                  message: '请输入非负整数',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" @change="(e) => changeDiseaseObservation(e, index)" />
            </a-form-item>
            <!-- 投保比例 -->
            <a-form-item
              v-if="obj.bussinessKey === 'insureProportion'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              required
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^(100|[1-9]?\d)(\.\d{1,4})?$/,
                  message: '请输入0~100范围内的数字且小数位不能超过4位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 养殖方式 -->
            <a-form-item v-if="obj.bussinessKey === 'farmingMethods'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :rules="[{ required: true }]">
              <a-select v-model:value="obj.bussinessValue" :options="farmingMethodsOptions" :disabled="isDisabled" />
            </a-form-item>
            <!-- 约定周期保险数量（头/只） -->
            <a-form-item
              v-if="obj.bussinessKey === 'cycleInsuredNumber'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^[1-9]\d*$/,
                  message: '请输入正整数',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 预计出栏日期 -->
            <a-form-item v-if="obj.bussinessKey === 'estimateSlaughterDate'" :key="obj.bussinessKey" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']" :required="!obj.notRequiredFlag" :label="obj.bussinessType">
              <a-date-picker v-model:value="obj.bussinessValue" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="isDisabled" />
            </a-form-item>
            <!-- 养殖数量(存栏量) -->
            <a-form-item
              v-if="obj.bussinessKey === 'breedAmount' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,9}(\.\d{1,2})?$/,
                  message: '输入整数且整数位不超过9位，小数位不超过2位',
                },
                { validator: () => checkBreedAmountInput(obj.notRequiredFlag, index) },
              ]"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
            >
              <a-input-group compact>
                <a-input v-model:value="obj.bussinessValue" style="width: 70%" :disabled="isDisabled" />
                <a-select v-model:value="riskList[index].riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue" allow-clear :options="unitOptions" style="width: 30%" :disabled="isDisabled" />
              </a-input-group>
            </a-form-item>
            <!-- 圈舍面积 -->
            <a-form-item
              v-if="obj.bussinessKey === 'pigstyArea' && !obj?.notShowFlag"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d+(\.\d{1,2})?$/,
                  message: '小数部分不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="平方米" :disabled="isDisabled" />
              <span v-if="checkPigstyArea(obj.bussinessValue, index)" class="text-[#FAAD14]">实际承保数量少于理论存栏数，请注意</span>
            </a-form-item>
            <!-- 养殖险end -->
            <!-- 林业险start -->
            <!-- 树龄（年） -->
            <a-form-item
              v-if="obj.bussinessKey === 'variousCountForest'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '最多20位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 平均密度（棵/亩） -->
            <a-form-item
              v-if="obj.bussinessKey === 'primeCostDensity2'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '最多20位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" :disabled="isDisabled" />
            </a-form-item>
            <!-- 林木属性 -->
            <a-form-item v-if="obj.bussinessKey === 'forestAttribute'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="forestAttributeOptions" :disabled="isDisabled" />
            </a-form-item>
            <!-- 林木用途 -->
            <a-form-item v-if="obj.bussinessKey === 'forestBreed'" :key="obj.bussinessKey" :required="!obj.notRequiredFlag" :label="obj.bussinessType" :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']">
              <a-select v-model:value="obj.bussinessValue" :options="forestBreedOptions" :disabled="isDisabled" />
            </a-form-item>
            <!-- 每亩约定碳汇目标值 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkTargetValue'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '最多20位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="吨" :disabled="isDisabled" @change="(e) => handleCarbonChange(e, index)" />
            </a-form-item>
            <!-- 约定碳汇单价 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkPrice'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '最多20位正数且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="元/吨" :disabled="isDisabled" @change="(e) => handlePriceChange(e, index)" />
            </a-form-item>
            <!-- 约定碳汇目标增速 -->
            <a-form-item
              v-if="obj.bussinessKey === 'carbonSinkTargetRate'"
              :key="obj.bussinessKey"
              :name="[index, 'riskAgrInfo', 'agrRiskAttribute', idx, 'bussinessValue']"
              :required="!obj.notRequiredFlag"
              :label="obj.bussinessType"
              :rules="[
                { required: !obj.notRequiredFlag },
                {
                  pattern: /^\d{1,20}(\.\d{1,2})?$/,
                  message: '请输入0~100范围内的数字且小数位不能超过2位',
                },
              ]"
            >
              <a-input v-model:value="obj.bussinessValue" addon-after="%" :disabled="isDisabled" />
            </a-form-item>
            <!-- 林业险结束 -->
          </template>
        </div>
      </div>
      <div class="divide-line"></div>
      <a-form-item v-if="isMultiTarget" required class="col-span-3" :label-col="{ style: { width: '90px' } }">
        <template #label>
          <span>标的地址</span>
          <a-tooltip class="ml-[3px]" placement="right">
            <template #title>
              <span>需要修改标的地址，请前往批改清单编辑页面</span>
            </template>
            <VueIcon v-if="showAddressTips" :icon="IconErrorCircleFilledFont" class="text-[#666]" />
          </a-tooltip>
        </template>
        <CompareRender v-if="readonly" :value="addressList[0].address" value-path="contract.riskGroupInfoList[0].address" />
        <div v-else class="flex gap-x-8px">
          <RegionSelect v-model:province="addressList[0].province" v-model:city="addressList[0].city" v-model:county="addressList[0].county" v-model:town="addressList[0].town" v-model:village="addressList[0].village" style="width: 65%" :disabled="isDisabled || showAddressTips" @change-selected="changeRiskAddress" />
          <a-input v-model:value="addressList[0].address" placeholder="请输入" style="width: 35%" :disabled="isDisabled || showAddressTips" />
        </div>
      </a-form-item>
    </template>
    <a-empty v-else :image="simpleImage" />
  </a-form>
  <a-modal v-model:open="infoVisible" :closable="false" :width="pxToRem(400)">
    <div class="flex items-center">当前录入疾病观察期超30天，请核实是否正确，如误录会造成后续理赔无法正常进行</div>
    <template #footer>
      <a-button @click="goEdit">返回修改</a-button>
      <a-button type="primary" @click="infoVisible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import type { RiskAddressInfo, RiskGroupInfo } from '../endorseModify.d';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { $getOnClient, $post, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, multiply } from '@/utils/tools';
import CompareRender from '@/pages/review/endorseVerifyDetail/components/CompareRender.vue';
import type { SelectOptions } from '~/apiTypes/apiCommon';
import { cloneDeep } from 'lodash-es';
import type { DefaultOptionType } from 'ant-design-vue/es/select';
import type { Rule } from 'ant-design-vue/es/form';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps<{
  departmentCode: string;
  productVersion?: string;
  productCode: string;
  readonly?: boolean;
  pageFrom?: string; // endorseVerifyDetail-批单审核详情与整单批改共用此组件
  moreRiskFlag?: string;
  showAddressTips?: boolean;
}>();
const emits = defineEmits(['changeAmount']);

const unitOptions = [
  {
    value: '头',
    label: '头',
  },
  {
    value: '只',
    label: '只',
  },
  {
    value: '张',
    label: '张',
  },
];

const formRef = ref();
const addressList = defineModel<RiskAddressInfo[]>('addressList', {
  default: [],
});
const riskList = defineModel<RiskGroupInfo[]>('data', { default: [] });

// 是否为多标的
const isMultiTarget = computed(() => {
  return props.moreRiskFlag === '1';
});
// 保险数量，单位校验
const checkInsured = (index: number) => {
  if (!riskList.value[index].riskAgrInfo.insuredNumber) {
    return Promise.reject('请录入保险数量');
  }
  if (!riskList.value[index].riskAgrInfo.insuredUnit) {
    return Promise.reject('请录入保险数量单位');
  }
  return Promise.resolve();
};

// 单位保险数量校验
const checkUnitInsureCount = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const value = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'unitInsureCount');
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'unitInsureCountUnit');
    if (!value?.bussinessValue) {
      return Promise.reject('请输入单位保险产量');
    }
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入单位保险产量单位');
    }
  }

  return Promise.resolve();
};

// 价格指数校验
const checkPriceAgreed = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const value = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'priceAgreed');
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'priceAgreedUnit');
    if (!value?.bussinessValue) {
      return Promise.reject('请输入价格指数');
    }
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入价格指数单位');
    }
  }

  return Promise.resolve();
};
// 养殖数量校验
const checkBreedAmountInput = (notRequiredFlag: boolean, index: number) => {
  if (notRequiredFlag) {
    return Promise.resolve();
  }
  if (Array.isArray(riskList.value?.[index]?.riskAgrInfo?.agrRiskAttribute)) {
    const unitValue = riskList.value[index].riskAgrInfo.agrRiskAttribute.find((item) => item.bussinessKey === 'breedUnit');
    if (!unitValue?.bussinessValue) {
      return Promise.reject('请输入养殖数量单位');
    }
  }

  return Promise.resolve();
};
// 每亩约定碳汇目标值改变
const handleCarbonChange = (e: Event, index: number) => {
  // 约定碳汇单价
  const carbonSinkPrice = riskList?.value?.[index]?.riskAgrInfo?.agrRiskAttribute?.filter((item) => item.bussinessKey === 'carbonSinkPrice')?.[0]?.bussinessValue;
  // 林业险碳汇自动根据每亩约定碳汇目标值和约定碳汇单价 自动计算出主险单位保险金额
  if ((e.target as HTMLInputElement).value && carbonSinkPrice) {
    riskList?.value?.[index].planInfoList.forEach((subItem) => {
      if (subItem.isMain === '1') {
        subItem.unitInsuredAmount = Number(multiply(String(carbonSinkPrice), String((e.target as HTMLInputElement).value))).toFixed(2);
        emits('changeAmount', subItem.unitInsuredAmount);
      }
    });
  }
};
// 约定碳汇单价改变
const handlePriceChange = (e: Event, index: number) => {
  // 约定碳汇单价
  const carbonSinkTargetValue = riskList?.value?.[index]?.riskAgrInfo?.agrRiskAttribute?.filter((item) => item.bussinessKey === 'carbonSinkTargetValue')?.[0]?.bussinessValue;
  // 林业险碳汇自动根据每亩约定碳汇目标值和约定碳汇单价 自动计算出主险单位保险金额
  if ((e.target as HTMLInputElement).value && carbonSinkTargetValue) {
    riskList?.value?.[index].planInfoList.forEach((subItem) => {
      if (subItem.isMain === '1') {
        subItem.unitInsuredAmount = Number(multiply(String(carbonSinkTargetValue), String((e.target as HTMLInputElement).value))).toFixed(2);
        emits('changeAmount', subItem.unitInsuredAmount);
      }
    });
  }
};
// 修改保险数量, 重新计算保险金额
const changeInsuredNumber = (e: Event, index: number) => {
  if ((e.target as HTMLInputElement).value) {
    riskList?.value?.[index].planInfoList.forEach((subItem) => {
      if (subItem.isMain === '1' && subItem.unitInsuredAmount !== undefined && (e.target as HTMLInputElement).value !== undefined) {
        subItem.totalInsuredAmount = Number(multiply(String(subItem.unitInsuredAmount), String((e.target as HTMLInputElement).value))).toFixed(2);
      }
    });
  }
};
defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
  clearValidate: async () => {
    if (formRef.value) {
      await formRef.value.clearValidate();
    }
  },
});

const { gateWay, service } = useRuntimeConfig().public || {};
const claimsCycleOptions = ref([]); // 周期下拉
const insuredNumberOptions = ref<SelectOptions[]>([]); // 保险数量单位
const unitInsureCountOptions = ref<SelectOptions[]>([]); // 单位保险数量下拉
const priceAgreedOptions = ref<SelectOptions[]>([]); // 价格指数下拉
const farmingMethodsOptions = ref<SelectOptions[]>([]); // 养殖方式下拉
const forestAttributeOptions = ref<SelectOptions[]>([]); // 林木属性下拉
const forestBreedOptions = ref<SelectOptions[]>([]); // 林木用途下拉

try {
  const res = await $post(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, ['AGRLPZQ', 'AGRYZCB', 'forestAttribute', 'forestBreed']);
  if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
    // 周期下拉列表
    claimsCycleOptions.value = res.data.find((item) => item.value === 'AGRLPZQ')?.children || [];
    // 养殖方式下拉列表
    farmingMethodsOptions.value = res.data.find((item) => item.value === 'AGRYZCB')?.children || [];
    // 林木属性下拉
    forestAttributeOptions.value = res.data.find((item) => item.value === 'forestAttribute')?.children || [];
    // 林木用途下拉
    forestBreedOptions.value = res.data.find((item) => item.value === 'forestBreed')?.children || [];
  }
} catch (e) {
  console.log(e, 'init error');
}

watch(
  () => [props.productCode, props.productVersion],
  async () => {
    if (props.productCode && props.productVersion) {
      const params = {
        productCode: props.productCode,
        productVersion: props.productVersion,
      };
      const numberRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryRiskNumberUnitList`, params);
      if (numberRes && numberRes?.code === SUCCESS_CODE) {
        insuredNumberOptions.value = numberRes.data || [];
      }
      const unitRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryProductionUnitList`, params);
      if (unitRes && unitRes?.code === SUCCESS_CODE) {
        unitInsureCountOptions.value = unitRes.data || [];
      }
      const indexRes = await $getOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/public/queryPriceIndexUnitList`, params);
      if (indexRes && indexRes?.code === SUCCESS_CODE) {
        priceAgreedOptions.value = indexRes.data || [];
      }
      changeProduct();
    }
  },
  {
    immediate: true,
  },
);

const isDisabled = computed(() => {
  return props.pageFrom === 'endorseVerifyDetail';
});

const addIsDisabled = (addRiskFlag: string | undefined) => {
  return isDisabled.value || addRiskFlag !== '1';
};

type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';
// 选择标的地址
const changeRiskAddress = async (option: { label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    addressList.value[0][`${type}Name`] = option?.label || '';
  }

  // 拼接地址时过滤掉undefined/null值，避免出现NaN
  const addressParts = [addressList.value[0]?.provinceName, addressList.value[0]?.cityName, addressList.value[0]?.countyName, addressList.value[0]?.townName, addressList.value[0]?.villageName].filter(Boolean);

  addressList.value[0].address = addressParts.join('') || '';
};
// 新增标的
const addItem = () => {
  const copyItem = cloneDeep(riskList.value[0].planInfoList[0]);
  const dutyInfoItem = copyItem.dutyInfoList?.map((item) => {
    const dutyAttrItem = item?.dutyAttributeInfoList?.map((attrItem) => {
      return {
        ...attrItem,
        id: '',
      };
    });
    return {
      ...item,
      dutyAttributeInfoList: dutyAttrItem,
      id: '',
    };
  });
  const planInfoItem = [{ ...copyItem, id: '', eachCompensationMaxAmount: '', unitInsuredAmount: '', totalInsuredAmount: '', expectPremiumRate: '', unitPrimium: '', totalActualPremium: '', totalAgreePremium: '', totalStandardPremium: '', totalVATExcludedPremium: '', totalValueAddedTax: '', dutyInfoList: dutyInfoItem }];
  const riskItem = cloneDeep(riskList.value[0]);
  const agrRiskAttributeItem = riskItem.riskAgrInfo.agrRiskAttribute?.map((item) => {
    return {
      ...item,
      bussinessValue: '',
      id: '',
    };
  });
  const newRiskItem: RiskGroupInfo = {
    ...riskItem,
    combinedProductCode: riskItem.combinedProductCode.slice(0, 4),
    defaultRisk: riskItem.combinedProductCode.slice(0, 4),
    combinedProductName: '',
    addRiskFlag: '1',
    planInfoList: planInfoItem,
    riskAgrInfo: {
      ...riskItem.riskAgrInfo,
      planAttributeSameMain: riskItem.riskAgrInfo.planAttributeSameMain,
      reductionCoefficient: riskItem.riskAgrInfo.reductionCoefficient,
      substitute: riskItem.riskAgrInfo.substitute,
      fundingSource: riskItem.riskAgrInfo.fundingSource,
      agrRiskAttribute: agrRiskAttributeItem,
      insuredNumber: '',
      insuredUnit: '',
      insuredAmount: '',
      premium: '',
      farmersCount: '',
      id: '',
    },
    id: '',
    totalActualPremium: '',
    totalAgreePremium: '',
    totalInsuredAmount: '',
    totalStandardPremium: '',
    totalVATExcludedPremium: '',
    totalValueAddedTax: '',
    isNew: true,
  };
  riskList.value.push(newRiskItem);
};
// 删除标的
const delItem = (index: number) => {
  riskList.value.splice(index, 1);
  setOptionDisabled();
};
// 保险数量单位
const handleRiskUnit = (index: number, option: DefaultOptionType) => {
  riskList.value[index].riskAgrInfo.insuredUnit = option.value as string;
};
const itemRefs = ref<Element[]>([]); // 设置ref回调函数
// 标的校验
const combinedProductValidator = (rule: Rule, val: string, index: number) => {
  const forthValue = itemRefs.value[index]?.valueList?.[3];
  if (!val || !forthValue) {
    return Promise.reject('请选择');
  }
  const itemArr = riskList.value.filter((item) => item.combinedProductCode === val);

  if (itemArr.length > 1) {
    return Promise.reject('标的不能重复选择');
  }
  return Promise.resolve();
};
const setItemRef = (el: Element, index: number) => {
  if (el) {
    itemRefs.value[index] = el;
  }
};
// 多标的option
const multiOption = ref<DefaultOptionType>([]);
// 获取标的最后一级下拉框数据
const changeProduct = async () => {
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const data = {
    productCode: props.productCode,
    version: props.productVersion,
  };
  const res = await $postOnClient<{ targetTypeList: DefaultOptionType }>(url, data);
  if (res && res.code === SUCCESS_CODE) {
    // 是否为多标产品
    if (res?.data?.targetTypeList?.length > 1) {
      multiOption.value = res?.data?.targetTypeList.map((item: DefaultOptionType) => ({
        encodeValue: `${item.fourLevTargetName}/${item.fiveLevTargetName}`,
        encodeKey: item.fiveLevTargetType,
      }));
      setOptionDisabled();
    } else {
      multiOption.value = [];
    }
  }
};
const setOptionDisabled = () => {
  multiOption.value.forEach((item: DefaultOptionType) => {
    item.disabled = false;
    riskList.value.forEach((rskItem) => {
      if (rskItem.combinedProductCode === item.encodeKey) {
        item.disabled = true;
      }
    });
  });
};
// 选择标的
const changeRiskCode = (value: string, level: string, option: DefaultOptionType, index: number) => {
  if (isMultiTarget.value) {
    // 选择最后一级 或者 清除最后一级时，赋值并重置下拉框disabled值
    if (String(level) === '4' || (String(level) === '3' && !option)) {
      riskList.value[index].defaultRisk = value;
      riskList.value[index].combinedProductCode = value;
      riskList.value[index].combinedProductName = option?.encodeValue || '';
      setOptionDisabled();
    }
  }
};
// 返回修改疾病观察期
const goEdit = () => {
  // 疾病观察期重置为0
  const index = riskList.value[currentIndex.value].riskAgrInfo.agrRiskAttribute.findIndex((item) => item.bussinessKey === 'diseaseObservation');
  riskList.value[currentIndex.value].riskAgrInfo.agrRiskAttribute[index].bussinessValue = '';
  infoVisible.value = false;
};
const unitName = computed(() => {
  return insuredNumberOptions.value.find((item) => item.value === riskList.value[0].riskAgrInfo.insuredUnit)?.label;
});
const infoVisible = ref<boolean>(false);
const currentIndex = ref<number>(0);
// 修改疾病观察期
const changeDiseaseObservation = (e: Event, index: number) => {
  // 疾病观察期超30天，弹窗提示
  if (Number((e.target as HTMLInputElement).value) > 30) {
    infoVisible.value = true;
    currentIndex.value = index;
  }
};
// 改变圈舍面积
const checkPigstyArea = (value: string, index: number) => {
  // 育肥猪  1.5  BB010000 能繁母猪 2 BB010100
  let culNum = 0;
  if (riskList.value[index].combinedProductCode === 'BB010000') {
    culNum = 1.5;
  } else if (riskList.value[index].combinedProductCode === 'BB010100') {
    culNum = 2;
  } else {
    return false;
  }

  // 实际承保数量（保险数量）
  const reality = Number(riskList.value[index].riskAgrInfo.insuredNumber);
  // 输入值为空时不校验
  if (!reality || !value) {
    return false;
  }
  // 理论存栏数
  const theory = Number(value) / culNum;
  // 实际承保数量小于理论存栏数时提示
  return reality / theory < 1;
};

// 寻找 priceAgreedUnit, unitInsureCountUnit, breedUnit, 插入对应值 priceAgreed unitInsureCount, breedAmount 下面
watch(riskList, () => {
  if (riskList.value) {
    for (let i = 0; i < riskList.value.length; i++) {
      const item = riskList.value[i].riskAgrInfo?.agrRiskAttribute;
      if (item) {
        // 1. 找到 priceAgreed unitInsureCount, breedAmount 的索引
        const priceAgreedIndex = item.findIndex((item) => item.bussinessKey === 'priceAgreed');
        const unitInsureCountIndex = item.findIndex((item) => item.bussinessKey === 'unitInsureCount');
        const breedAmountIndex = item.findIndex((item) => item.bussinessKey === 'breedAmount');
        // 2. 找到 priceAgreedUnit, unitInsureCountUnit, breedUnit 的对象
        const priceAgreedUnitItem = item.find((item) => item.bussinessKey === 'priceAgreedUnit');
        const unitInsureCountUnitItem = item.find((item) => item.bussinessKey === 'unitInsureCountUnit');
        const breedUnitItem = item.find((item) => item.bussinessKey === 'breedUnit');
        // 3. 如果 priceAgreed 存在，并且 priceAgreedUnit 也存在，就将 priceAgreedUnit 插入到 priceAgreed 后面
        if (priceAgreedIndex !== -1 && priceAgreedUnitItem) {
          // 先从数组中移除 priceAgreedUnit
          const priceAgreedUnitIndex = item.findIndex((it) => it.bussinessKey === 'priceAgreedUnit');
          if (priceAgreedUnitIndex !== -1) {
            const [priceAgreedUnit] = item.splice(priceAgreedUnitIndex, 1);
            // 插入到 priceAgreed 后面
            if (priceAgreedUnit) {
              item.splice(priceAgreedIndex + 1, 0, priceAgreedUnit);
            }
          }
        }
        // 4. 如果 unitInsureCount 存在，并且 unitInsureCountUnit 也存在，就将 unitInsureCountUnit 插入到 unitInsureCount 后面
        if (unitInsureCountIndex !== -1 && unitInsureCountUnitItem) {
          // 先从数组中移除 unitInsureCountUnit
          const unitInsureCountUnitIndex = item.findIndex((it) => it.bussinessKey === 'unitInsureCountUnit');
          if (unitInsureCountUnitIndex !== -1) {
            const [unitInsureCountUnit] = item.splice(unitInsureCountUnitIndex, 1);
            // 插入到 unitInsureCount 后面
            if (unitInsureCountUnit) {
              item.splice(unitInsureCountIndex + 1, 0, unitInsureCountUnit);
            }
          }
        }
        // 5. 如果 breedAmount 存在，并且 breedUnit 也存在，就将 breedUnit 插入到 breedAmount 后面
        if (breedAmountIndex !== -1 && breedUnitItem) {
          // 先从数组中移除 breedUnit
          const breedUnitIndex = item.findIndex((it) => it.bussinessKey === 'breedUnit');
          if (breedUnitIndex !== -1) {
            const [breedUnit] = item.splice(breedUnitIndex, 1);
            // 插入到 breedAmount 后面
            if (breedUnit) {
              item.splice(breedAmountIndex + 1, 0, breedUnit);
            }
          }
        }
      }
    }
  }
});
</script>

<style lang="less">
.divide-line {
  margin-top: 5px;
  padding-top: 16px;
  border-top: 1px solid #e6e8eb;
}
</style>
