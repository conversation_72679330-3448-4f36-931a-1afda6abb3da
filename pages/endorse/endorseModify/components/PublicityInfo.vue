<template>
  <a-table :columns="columns" :data-source="dataList" :pagination="false">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'insureArea'">
        <span>{{ record.riskAddressNameProvince || '' }}</span>
        <span>{{ record.riskAddressNameCity || '' }}</span>
        <span>{{ record.riskAddressNameCounty || '' }}</span>
        <span>{{ record.riskAddressNameTown || '' }}</span>
        <span>{{ record.riskAddressNameVillage || '' }}</span>
      </template>
      <template v-if="column.dataIndex === 'operation'">
        <span class="text-[14px] text-[#576B95] font-normal cursor-pointer" @click="modify(record.endorseApplyNo, record.policyNo, record.farmerlistNo, record.farmersCount)">修改</span>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import { pxToRem } from '@/utils/tools';

const emits = defineEmits(['modify']);
const { farmerDetailedList } = defineProps<{
  farmerDetailedList: Record<string, string>;
}>();
const dataList = computed(() => {
  return Object.keys(farmerDetailedList).length ? [farmerDetailedList] : [];
});
const columns: TableColumnsType = [
  { title: '批改清单编号', dataIndex: 'farmerlistNo', key: 'farmerlistNo' },
  { title: '清单名称', dataIndex: 'farmerlistName', key: 'farmerlistName' },
  { title: '承保数量', dataIndex: 'insuredNumber', key: 'insuredNumber', width: pxToRem(80) },
  { title: '承保户数', dataIndex: 'farmersCount', key: 'farmersCount', width: pxToRem(80) },
  { title: '导入条数', dataIndex: 'farmersCount', key: 'farmersCount', width: pxToRem(80) },
  { title: '清单标的地址', dataIndex: 'insureArea', key: 'insureArea' },
  { title: '操作', dataIndex: 'operation', key: 'operation', fixed: 'right', width: pxToRem(80) },
];
const modify = (endorseApplyNo: string, policyNo: string, farmerListNo: string, farmersCount: string) => {
  const queryData = {
    endorseApplyNo,
    policyNo,
    farmerListNo,
    farmersCount,
  };
  emits('modify', queryData);
};
</script>

<style>
.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  width: fit-content;

  &::before {
    position: absolute;
    left: 0;
    top: 6px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
</style>
