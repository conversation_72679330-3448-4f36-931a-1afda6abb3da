<template>
  <div class="insurance-plan space-y-16px">
    <a-form ref="formRef" :model="data" :colon="false">
      <template v-if="data.length > 0">
        <div v-for="(item, mainIndex) in data" :key="mainIndex" class="bg-[#ffffff] p-16px rounded">
          <div class="text-[#404442] text-[14px] font-semibold flex items-center">
            <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
            {{ `方案${data?.length > 1 ? mainIndex + 1 : ''}` }}
          </div>
          <div class="flex items-center justify-between text-[rgba(0,0,0,0.55)] font-normal mb-[16px]">
            <div>险种列表</div>
            <a-button v-show="!readonly" type="primary" @click="addInsurance(mainIndex)">添加附加险</a-button>
          </div>
          <div class="table-wrap">
            <a-table :columns="columns" :data-source="item.planInfoList" :pagination="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : '')">
              <template #bodyCell="{ text, column, record, index }">
                <template v-if="column.dataIndex === 'number'">{{ index + 1 }}</template>
                <template v-if="column.dataIndex === 'combinedProductName'">{{ data[mainIndex]?.combinedProductName }}</template>
                <template v-if="column.dataIndex === 'isMain'">{{ text === '1' ? '主险' : '附加险' }}</template>
                <template v-if="column.dataIndex === 'planName'">
                  <span v-if="record?.isMain === '1'">{{ text }}</span>
                  <span v-else-if="isDisabled">{{ text }}</span>
                  <!-- 管中星提出 暂时这样处理：当前页面新增可修改，暂存后不可修改 -->
                  <a-form-item v-else-if="record.isModify" :name="[mainIndex, 'planInfoList', index, 'planName']">
                    <a-select v-model:value="item.planInfoList[index].planCode" class="w-[120px]" @select="(_value: any, option: any) => handleSelect(mainIndex, index, option)">
                      <template v-for="option in ridersOptions" :key="option.planCode">
                        <a-select-option :value="option.planCode" :title="option.planName" :disabled="item.planInfoList.filter((plan) => plan.planCode === option.planCode)?.length > 0">{{ option.planName }}</a-select-option>
                      </template>
                    </a-select>
                  </a-form-item>
                  <span v-else>{{ text }}</span>
                </template>
                <template v-if="column.dataIndex === 'eachCompensationMaxAmount'">
                  <a-form-item :name="[mainIndex, 'planInfoList', index, 'eachCompensationMaxAmount']" :rules="[{ validator: (_rule, value) => limitValidator(value, record.totalInsuredAmount) }]">
                    <a-input-number v-model:value="item.planInfoList[index].eachCompensationMaxAmount" :disabled="isDisabled" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'unitInsuredAmount'">
                  <a-form-item :name="[mainIndex, 'planInfoList', index, 'unitInsuredAmount']" :rules="[{ validator: unitAmountValidator }]">
                    <a-input-number v-model:value="item.planInfoList[index].unitInsuredAmount" :disabled="isDisabled" @change="(value) => amountChange(mainIndex, index, value as number)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'expectPremiumRate'">
                  <a-form-item :name="[mainIndex, 'planInfoList', index, 'expectPremiumRate']" :rules="[{ validator: centralFinanceValidator }]">
                    <a-input-number v-model:value="item.planInfoList[index].expectPremiumRate" :disabled="isDisabled" @change="(value) => rateChange(mainIndex, index, value as number)" />
                  </a-form-item>
                </template>
                <!-- 单位保费 -->
                <template v-if="column.dataIndex === 'unitPrimium'">
                  <a-form-item :name="[mainIndex, 'planInfoList', index, 'unitPrimium']" :rules="[{ validator: amountValidator }]">
                    <a-input-number v-model:value="item.planInfoList[index].unitPrimium" :disabled="isDisabled" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'totalActualPremium'">
                  <a-form-item
                    :name="[mainIndex, 'planInfoList', index, 'totalActualPremium']"
                    :rules="[
                      { required: true, message: '不能为空' },
                      { pattern: /^(?!0{13,})(0|[1-9]\d{0,11})(\.\d{1,2})?$/, message: '整数部分最多12位，小数部分最多2位' },
                    ]"
                  >
                    <a-input-number v-model:value="item.planInfoList[index].totalActualPremium" :disabled="isDisabled" :min="0" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <span v-if="!isDisabled && record?.isMain !== '1'" class="text-[14px] text-[#576B95] font-normal cursor-pointer" @click="deleteInsurance(mainIndex, index)">删除</span>
                </template>
              </template>
            </a-table>
          </div>
          <div class="bg-[#E6E8EB] h-[1px] w-full my-[17px]" />
          <!-- 保费来源 只有最后一项的时候展示，兼容多标的 -->
          <div v-if="mainIndex === data.length - 1">
            <div class="flex items-center justify-between text-[rgba(0,0,0,0.55)] font-normal mb-[16px]">
              <div>保费来源</div>
              <div v-show="!readonly" class="flex items-center">
                <span>附加险保费来源：</span>
                <a-form-item :name="[mainIndex, 'riskAgrInfo', 'planAttributeSameMain']">
                  <span class="text-[rgba(0,0,0,0.55)] font-normal">与主险一致：</span>
                  <a-radio-group v-model:value="item.riskAgrInfo.planAttributeSameMain" disabled>
                    <a-radio value="Y">
                      <span class="text-[rgba(0,0,0,0.55)] font-normal">是</span>
                    </a-radio>
                    <a-radio value="N">
                      <span class="text-[rgba(0,0,0,0.55)] font-normal">否</span>
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </div>
            </div>
            <a-table :columns="sourceColumns" :data-source="sourceList" :pagination="false">
              <template #bodyCell="{ column, index }">
                <template v-if="column.dataIndex === 'centralFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'centralFinance']" :rules="[{ validator: centralFinanceValidator }]">
                    <a-input-number v-model:value="sourceList[index].centralFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'cityFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'cityFinance']" :rules="[{ validator: financeValidator }]">
                    <a-input-number v-model:value="sourceList[index].cityFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'countyFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'countyFinance']" :rules="[{ validator: financeValidator }]">
                    <a-input-number v-model:value="sourceList[index].countyFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'provincialFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'provincialFinance']" :rules="[{ validator: financeValidator }]">
                    <a-input-number v-model:value="sourceList[index].provincialFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'farmersFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'farmersFinance']" :rules="[{ validator: financeValidator }]">
                    <a-input-number v-model:value="sourceList[index].farmersFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'otherFinance'">
                  <a-form-item class="w-[100px]" :name="[mainIndex, 'planInfoList', index, 'otherFinance']" :rules="[{ validator: (_rule, value) => otherFinanceValidator(value, mainIndex, index) }]">
                    <a-input-number v-model:value="sourceList[index].otherFinance" disabled @change="() => handleFinanceChange(mainIndex, index)" />
                  </a-form-item>
                </template>
              </template>
            </a-table>
            <div class="grid grid-cols-3 gap-x-10px my-[16px]">
              <a-form-item label="农户自缴减免系数" required :name="[mainIndex, 'riskAgrInfo', 'reductionCoefficient']" :rules="[{ validator: (rule: Rule, value: number) => coefficientValidator(value, mainIndex) }]">
                <a-input-number v-model:value="item.riskAgrInfo.reductionCoefficient" :disabled="isDisabled" :style="{ width: '100%' }" placeholder="输入介于0-农户自缴上限" />
              </a-form-item>
              <a-form-item label="农户保费是否代缴" required :name="[mainIndex, 'riskAgrInfo', 'substitute']">
                <a-select v-model:value="item.riskAgrInfo.substitute" :disabled="isDisabled" class="w-full" @change="(value) => substituteChange(value as string, mainIndex)">
                  <a-select-option value="Y">是</a-select-option>
                  <a-select-option value="N">否</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item v-if="item.riskAgrInfo.substitute === 'Y'" label="代缴保费资金来源" required :name="[mainIndex, 'riskAgrInfo', 'fundingSource']">
                <a-select v-model:value="item.riskAgrInfo.fundingSource" :options="sourceOptions" :disabled="isDisabled" />
              </a-form-item>
            </div>
          </div>
        </div>
      </template>
      <a-empty v-else :image="simpleImage" />
    </a-form>
    <div class="p-16px leading-[22px] text-[rgba(0, 0, 0, 0.9)]">
      <div>总计：保单保险金额={{ sumRiskGroupAmount?.totalInsuredAmount || 0 }} ｜ 减免前保单保费金额={{ sumRiskGroupAmount?.totalStandardPremium || 0 }}｜ 减免后保单保费金额={{ sumRiskGroupAmount?.totalAgreePremium || 0 }} ｜ 保单复核保费={{ sumRiskGroupAmount?.totalActualPremium || 0 }}</div>
      <div v-if="data.length === 1">保险数量：{{ props.insuredNumber || 0 }}{{ unitName }} ｜ 参保户次：{{ props.farmersCount || 0 }}户</div>
      <div v-if="data.length > 1">
        <template v-for="(item, index) in data" :key="index">
          <div>标的名称：{{ item.combinedProductName }} ｜ 保险数量：{{ item.riskAgrInfo.insuredNumber || 0 }}{{ item.riskAgrInfo.insuredUnitChName }} ｜ 参保户次：{{ item.riskAgrInfo.farmersCount || 0 }}户</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import type { ColumnType } from 'ant-design-vue/es/table/interface';
import { Empty } from 'ant-design-vue';
import type { RiskGroupInfo, SumRiskGroupAmount, InsurancePlanDetail, PlanInfoListItem, PlanInfo } from '../endorseModify.d';
import { usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { useIndexDB } from '@/composables/useIndexDB';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { getCacheData } = useIndexDB();
const data = defineModel<Array<RiskGroupInfo>>('data', { default: [] });
const props = defineProps<{
  productVersion: string; // 产品版本
  productCode: string; // 产品编码
  departmentCode: string; // 机构编码
  shortTimeCoefficient: string; // 年限系数
  govSubsidyType: string; // 商业补贴类型
  sumRiskGroupAmount: SumRiskGroupAmount; // 保险方案合计
  insuredNumber: string; // 保险数量
  farmersCount: number; // 参保户次
  insuredUnit: string; // 单位
  readonly?: boolean;
  pageFrom?: string; // endorseVerifyDetail-批单审核详情与整单批改共用此组件
}>();
const { gateWay, service } = useRuntimeConfig().public || {};
// 预设保险方案list
const insuranceList = ref<Array<InsurancePlanDetail>>([]);

const formRef = ref();

// 附加险下拉选项
const ridersOptions = ref<Array<PlanInfo>>([]);
// 代缴保费资金来源下拉选项
const sourceOptions = ref<SelectOptions[]>([]);
// 产品主险数据
const mainInsuranceData = ref<Array<PlanInfo>>([]);
// 单位下拉选项
const unitOptions = ref<SelectOptions[]>([]);

// 保险方案表格columns
const columns: Array<ColumnType> = [
  {
    title: '序号',
    dataIndex: 'number',
    key: 'number',
    width: pxToRem(56),
  },
  {
    title: '标的名称',
    dataIndex: 'combinedProductName',
    key: 'combinedProductName',
  },
  {
    title: '标识',
    dataIndex: 'isMain',
    key: 'isMain',
    width: pxToRem(140),
  },
  {
    title: '险种码',
    dataIndex: 'planCode',
    key: 'planCode',
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: pxToRem(140),
  },
  {
    title: '每次赔偿限额',
    dataIndex: 'eachCompensationMaxAmount',
    key: 'eachCompensationMaxAmount',
    width: pxToRem(140),
  },
  {
    title: '单位保险金额',
    dataIndex: 'unitInsuredAmount',
    key: 'unitInsuredAmount',
    width: pxToRem(140),
  },
  {
    title: '保险金额',
    dataIndex: 'totalInsuredAmount',
    key: 'totalInsuredAmount',
    width: pxToRem(140),
  },
  {
    title: '费率',
    dataIndex: 'expectPremiumRate',
    key: 'expectPremiumRate',
    width: pxToRem(140),
  },
  {
    title: '单位保费',
    dataIndex: 'unitPrimium',
    key: 'unitPrimium',
    width: pxToRem(140),
  },
  {
    title: '基准保费金额',
    dataIndex: 'totalStandardPremium',
    key: 'totalStandardPremium',
    width: pxToRem(140),
  },
  {
    title: '减免后保费金额',
    dataIndex: 'totalAgreePremium',
    key: 'totalAgreePremium',
    width: pxToRem(140),
  },
  {
    title: '复核保费',
    dataIndex: 'totalActualPremium',
    key: 'totalActualPremium',
    width: pxToRem(120),
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
    width: pxToRem(80),
  },
];
// 保费来源政策性columns
const policySourceColumns = [
  {
    title: '险种代码',
    dataIndex: 'planCode',
    key: 'planCode',
    width: pxToRem(120),
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: pxToRem(120),
  },
  {
    title: '中央补贴(%)',
    dataIndex: 'centralFinance',
    key: 'centralFinance',
  },
  {
    title: '省级补贴(%)',
    dataIndex: 'provincialFinance',
    key: 'provincialFinance',
  },
  {
    title: '地方补贴(%)',
    dataIndex: 'cityFinance',
    key: 'cityFinance',
  },
  {
    title: '县级补贴(%)',
    dataIndex: 'countyFinance',
    key: 'countyFinance',
  },
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
  },
];
// 保费来源商业性columns
const businesSSourceColumns = [
  {
    title: '险种代码',
    dataIndex: 'planCode',
    key: 'planCode',
    width: pxToRem(120),
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
    key: 'planName',
    width: pxToRem(120),
  },
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
  },
];

// 保费来源表格datasource
const sourceList = computed(() => {
  if (data.value?.length === 1) {
    // 单标的
    return data.value[data.value.length - 1].riskAgrInfo.planAttributeSameMain === 'Y' ? [data.value[data.value.length - 1].planInfoList?.[0]] : data.value[data.value.length - 1].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    data.value.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return data.value[data.value.length - 1].riskAgrInfo.planAttributeSameMain === 'Y' ? [result?.[0]] : result;
  }
});
const sourceColumns = computed(() => {
  // 03代表商业险
  if (props?.govSubsidyType === '3') {
    return businesSSourceColumns;
  } else {
    return policySourceColumns;
  }
});

// 每次限额校验
const limitValidator = (value: number, insuranceValue: number) => {
  if (!value) {
    return Promise.resolve();
  }
  // 不能大于保险金额
  if (Number(value) > Number(insuranceValue)) {
    return Promise.reject('不能大于保险金额');
  }

  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');

  if (integerPart && integerPart.length > 12) {
    return Promise.reject('整数部分不能超过12位');
  }

  if (decimalPart && decimalPart.length > 2) {
    return Promise.reject('小数部分不能超过2位');
  }

  return Promise.resolve();
};

// 单位保险金额校验
const unitAmountValidator = (_rule: Rule, value: number) => {
  if (!value && value != 0) {
    return Promise.reject('不能为空');
  }
  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');

  if (integerPart && integerPart.length > 12) {
    return Promise.reject('整数部分不能超过12位');
  }

  if (decimalPart && decimalPart.length > 2) {
    return Promise.reject('小数部分不能超过2位');
  }

  return Promise.resolve();
};

// 金额校验
const amountValidator = (_rule: Rule, value: number) => {
  if (!value && value != 0) {
    return Promise.reject('不能为空');
  }
  // 整数12位，小数2位
  const valueStr = value.toString();
  const [integerPart, decimalPart] = valueStr.split('.');
  console.log('integerPart: ', integerPart);
  // if (integerPart && integerPart.length > 9) {
  //   return Promise.reject('整数部分不能超过9位');
  // }

  if (decimalPart && decimalPart.length > 6) {
    return Promise.reject('小数部分不能超过6位');
  }

  return Promise.resolve();
};

// 农户自缴减免系数校验
const coefficientValidator = (value: number, mainIndex: number) => {
  if (Number(value) < 0) {
    return Promise.reject('不能小于0');
  }
  // 最大值为险种中农户自缴比例的最小值 farmersFinance
  const farmersFinanceMin = Math.min(...data.value[mainIndex].planInfoList.map((item) => Number(item.farmersFinance)));

  if (Number(value * 100) > farmersFinanceMin) {
    return Promise.reject('不能大于最小农户自缴系数');
  }
  return Promise.resolve();
};

// 比例校验，比例录入范围0-100范围内的数字且小数位不能超过4位
const financeValidator = (_rule: Rule, value: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 中央政策来源 如果是中央政策补贴则必填
const centralFinanceValidator = (_rule: Rule, value: number) => {
  if (!value && value !== 0) {
    return Promise.reject('不能为空');
  }
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 校验是否等于100
const otherFinanceValidator = (value: number, mainIndex: number, index: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }
  const num1 = parseFloat(data.value[mainIndex].planInfoList[index].centralFinance as string) || 0;
  const num2 = parseFloat(data.value[mainIndex].planInfoList[index].provincialFinance as string) || 0;
  const num3 = parseFloat(data.value[mainIndex].planInfoList[index].cityFinance as string) || 0;
  const num4 = parseFloat(data.value[mainIndex].planInfoList[index].countyFinance as string) || 0;
  const num5 = parseFloat(data.value[mainIndex].planInfoList[index].farmersFinance as string) || 0;
  const num6 = parseFloat(data.value[mainIndex].planInfoList[index].otherFinance as string) || 0;

  // 地方政策补贴则 省市区不能都为空
  if (props?.govSubsidyType === '2') {
    if (!num2 && !num3 && !num4) {
      return Promise.reject('省、市、区不能都为空');
    }
  }

  // 将数值乘以10000转换为整数进行计算
  let sum = 0;
  if (props.govSubsidyType === '3') {
    sum = (num5 * 10000 + num6 * 10000) / 10000;
  } else {
    sum = (num1 * 10000 + num2 * 10000 + num3 * 10000 + num4 * 10000 + num5 * 10000 + num6 * 10000) / 10000;
  }

  // 校验总和是否等于100
  if (sum !== 100) {
    if (props.govSubsidyType === '3') {
      return Promise.reject('农户自缴和其他补贴总和必须等于100');
    } else {
      return Promise.reject('中央补贴、省级补贴、地方补贴、县级补贴、农户自缴、其他补贴总和必须等于100');
    }
  }
  return Promise.resolve();
};

// 补贴类型为商业险时 置空省市区数据
const handleFinanceChange = (mainIndex: number, index: number) => {
  if (props.govSubsidyType === '3') {
    data.value[mainIndex].planInfoList[index].centralFinance = '';
    data.value[mainIndex].planInfoList[index].provincialFinance = '';
    data.value[mainIndex].planInfoList[index].cityFinance = '';
    data.value[mainIndex].planInfoList[index].countyFinance = '';
  }
  // 触发是否等于100比例的校验
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'otherFinance']]);
  // 触发农户自缴系数不能大于最小农户自缴比例的校验
  formRef.value.validateFields([[mainIndex, 'riskAgrInfo', 'reductionCoefficient']]);
};

// 添加附加险
const addInsurance = (mainIndex: number) => {
  // 获取主险
  const mainData = data.value[mainIndex].planInfoList.find((item) => item.isMain === '1');
  // 添加附加险保费来源默认值设置为主险的保费来源
  data.value[mainIndex].planInfoList.push({
    isMain: '0',
    isModify: true,
    centralFinance: mainData?.centralFinance,
    cityFinance: mainData?.cityFinance,
    countyFinance: mainData?.countyFinance,
    farmersFinance: mainData?.farmersFinance,
    otherFinance: mainData?.otherFinance,
    provincialFinance: mainData?.provincialFinance,
  });
};

// 删除附加险
const deleteInsurance = (mainIndex: number, index: number) => {
  data.value[mainIndex].planInfoList.splice(index, 1);
};

// 选择险种名称
const handleSelect = (mainIndex: number, index: number, option: { title: string | undefined; value: string }) => {
  data.value[mainIndex].planInfoList[index].planName = option.title;
  // 回填dutyInfoList到险种，后端需要
  const dutyInfoList = ridersOptions.value.filter((item) => item.planCode === option.value)?.[0]?.dutyInfoList;
  data.value[mainIndex].planInfoList[index].dutyInfoList = dutyInfoList;
};
const roundToDecimal = (num: number, decimal: number) => {
  const factor = Math.pow(10, decimal);
  return Math.round(num * factor) / factor;
};
// 单位保险金额改变
const amountChange = (mainIndex: number, index: number, value: number) => {
  // 确保输入值为有效数字，如果为NaN则转换为0
  const insuredNumber = Number(data.value[mainIndex].riskAgrInfo.insuredNumber) || 0;
  const expectPremiumRate = isNaN(parseFloat(data.value[mainIndex].planInfoList[index].expectPremiumRate as string)) ? 0 : parseFloat(data.value[mainIndex].planInfoList[index].expectPremiumRate as string) / 100;

  // 保险金额=单位保险金额*保险数量
  data.value[mainIndex].planInfoList[index].totalInsuredAmount = String(roundToDecimal(insuredNumber * value, 2));
  // 单位保费=单位保额*费率
  data.value[mainIndex].planInfoList[index].unitPrimium = String(roundToDecimal(expectPremiumRate * value, 2));
  // 基准保费金额 减免后保费金额 复核保费 如果没值 初始化为0
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalStandardPremium) {
    data.value[mainIndex].planInfoList[index].totalStandardPremium = '0';
  }
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalAgreePremium) {
    data.value[mainIndex].planInfoList[index].totalAgreePremium = '0';
  }
  if (!data?.value?.[mainIndex]?.planInfoList?.[index]?.totalActualPremium) {
    data.value[mainIndex].planInfoList[index].totalActualPremium = '0';
  }

  // 触发对填写金额字段的单独校验
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'eachCompensationMaxAmount']]);
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'unitPrimium']]);
  formRef.value.validateFields([[mainIndex, 'planInfoList', index, 'totalActualPremium']]);
};

// 费率改变
const rateChange = (mainIndex: number, index: number, value: number) => {
  // 确保输入值为有效数字，如果为NaN则转换为0
  const rate = isNaN(value) ? 0 : value / 100;
  const unitInsuredAmount = Number(data.value[mainIndex].planInfoList[index].unitInsuredAmount) || 0;

  // 单位保费=单位保额*费率
  data.value[mainIndex].planInfoList[index].unitPrimium = String(roundToDecimal(unitInsuredAmount * rate, 2));
};
// 处理数据
const transformData = (data: Array<RiskGroupInfo>) => {
  // 多标数据合并
  data.forEach((item) => {
    item.planInfoList = item.planInfoList.map((plan) => {
      const sourceItem = sourceList.value.find((source) => source.planCode === plan.planCode);
      if (sourceItem) {
        return {
          ...plan,
          centralFinance: props.govSubsidyType === '3' ? '' : sourceItem.centralFinance,
          provincialFinance: props.govSubsidyType === '3' ? '' : sourceItem.provincialFinance,
          cityFinance: props.govSubsidyType === '3' ? '' : sourceItem.cityFinance,
          countyFinance: props.govSubsidyType === '3' ? '' : sourceItem.countyFinance,
          farmersFinance: sourceItem.farmersFinance,
          otherFinance: sourceItem.otherFinance,
        };
      }
      return plan;
    });
    // 把所有项的 riskAgrInfo对象中的reductionCoefficient substitute planAttributeSameMain字段重置为最后一项的值
    const lastItem = data[data.length - 1].riskAgrInfo;
    item.riskAgrInfo = {
      ...item.riskAgrInfo,
      reductionCoefficient: lastItem.reductionCoefficient,
      substitute: lastItem.substitute,
      planAttributeSameMain: lastItem.planAttributeSameMain,
    };
  });
  // 附加险与主险一致，把附加险的保费来源数据处理成与主险的一致
  data.forEach((item) => {
    if (item?.riskAgrInfo?.planAttributeSameMain === 'Y') {
      const mainInsurance = item.planInfoList.filter((item) => item.isMain === '1');
      if (item.planInfoList.length > 1) {
        item.planInfoList = item.planInfoList.map((plan) => {
          if (plan.isMain !== '1') {
            return {
              ...plan,
              centralFinance: mainInsurance?.[0].centralFinance,
              cityFinance: mainInsurance?.[0].cityFinance,
              countyFinance: mainInsurance?.[0].countyFinance,
              otherFinance: mainInsurance?.[0].otherFinance,
              farmersFinance: mainInsurance?.[0].farmersFinance,
              provincialFinance: mainInsurance?.[0].provincialFinance,
            };
          } else {
            return plan;
          }
        });
      }
    }
  });
  return data;
};
// 林业险碳汇填完碳汇单价要自动带出主险单位保险金额及后续金额
const changeAmount = (value: number) => {
  formRef.value.validateFields([[0, 'planInfoList', 0, 'unitInsuredAmount']]);
  amountChange(0, 0, value);
};

defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
  clearValidate: async () => {
    if (formRef.value) {
      await formRef.value.clearValidate();
    }
  },
  transformData,
  changeAmount: (value: number) => changeAmount(value),
});

const getProductList = async () => {
  const params = {
    productCode: props.productCode,
    version: props.productVersion,
  };
  const res = await getProductInfo.fetchData(params);
  if (res && res?.code === SUCCESS_CODE) {
    // 附加险
    const extraInsurance = res.data.planList.filter((item: PlanInfoListItem) => item.isMain === '0');
    // 主险
    const mainInsurance = res.data.planList.filter((item: PlanInfoListItem) => item.isMain === '1');
    ridersOptions.value = extraInsurance as PlanInfo[];
    mainInsuranceData.value = mainInsurance as PlanInfo[];
  }
};

// 获取方案预设列表
const getPlanList = async () => {
  const params = {
    departmentNo: props.departmentCode,
    marketProductNo: props.productCode,
    productVersionNo: props.productVersion,
  };
  const res = await getPlanData.fetchData(params);
  if (res && res?.code === SUCCESS_CODE) {
    if (res?.data.records?.length > 0) {
      insuranceList.value = res.data.records || '';
    } else {
      insuranceList.value = [];
    }
  }
};

// 获取产品列表
const getProductInfo = await usePost<InsurancePlanDetail>(`${gateWay}${service.accept}/web/applicationForm/productInfos`);
// 获取方案预设列表
const getPlanData = await usePost<{ records: Array<InsurancePlanDetail> }>(`${gateWay}${service.accept}/sys/insurance/qryPlanList`);
const initOptions = async () => {
  const params = ['fundingSource', 'AGRZZJGZSBXDW'];
  const requestParams = {
    url: `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`,
    params,
  };
  const res = (await getCacheData('optionData', requestParams)) as SelectOptions[];
  sourceOptions.value = res.find((item: { value: string }) => item.value === 'fundingSource')?.children || [];
  unitOptions.value = res.find((item: { value: string }) => item.value === 'AGRZZJGZSBXDW')?.children || [];
};
onMounted(() => {
  // 获取基础信息
  initOptions();
});

// 农户自缴系数改变，刷新方案对应的金额
// const handleBlur = (mainIndex: number) => {
// data.value[mainIndex].planInfoList.forEach((item, index: number) => {
//   if (item?.unitInsuredAmount) {
//     amountChange(mainIndex, index, parseFloat(item.unitInsuredAmount || ''));
//   }
// });
// };

// 获取产品信息
watch(
  () => [props.productCode, props.productVersion],
  () => {
    if (props.productCode && props.productVersion) {
      getProductList();
    }
  },
);

// 获取预设保险方案名称列表
watch(
  () => [props.productCode, props.productVersion, props.departmentCode],
  () => {
    if (props.productCode && props.productVersion && props.departmentCode) {
      getPlanList();
    }
  },
);

const isDisabled = computed(() => {
  return props.pageFrom === 'endorseVerifyDetail';
});
// 农户是否代缴选择否清空资金来源
const substituteChange = (value: string, index: number) => {
  if (value === 'N') {
    data.value[index].riskAgrInfo.fundingSource = '';
  }
};
// 总计保险数量单位
const unitName = computed(() => {
  return unitOptions.value.find((item) => item.value === props.insuredUnit)?.label;
});
</script>

<style lang="less" scoped>
.insurance-plan {
  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }
  .table-wrap {
    width: calc(100vw - 420px);
  }
}
</style>
