<template>
  <div class="space-y-16px">
    <!-- 投保组织者 -->
    <OrganizerInfo v-if="isOrganization" ref="organizerRef" v-model:data="organizerInfo" :options-data="optionsData" />
    <!-- 被保人信息 -->
    <CustomerDetail ref="insurantInfoRef" v-model:data="insurantInfoList[0]" v-model:insurance-relationship="insuranceRelationship" customer-type="insurantInfo" :options-data="optionsData" :insurant-filed-disabled="insurantFiledDisabled" :is-anti-money="isAntiMoney" :sec-dept-code="secDeptCode" />
    <a-divider />
    <!-- 投保人信息 -->
    <CustomerDetail ref="applicantInfoRef" v-model:data="applicantInfoList[0]" v-model:insurance-relationship="insuranceRelationship" customer-type="applicantInfo" :options-data="optionsData" :is-anti-money="isAntiMoney" :is-forestry="isForestry" />
    <a-divider />
    <!-- 受益人信息 -->
    <CustomerDetail ref="beneficaryRef" v-model:data="beneficaryInfoList[0]" customer-type="beneficaryInfo" :options-data="optionsData" :is-anti-money="isAntiMoney" />
  </div>
</template>

<script setup lang="ts">
import OrganizerInfo from './OrganizerInfo.vue';
import CustomerDetail from './CustomerDetail.vue';
import type { CustomerInfo, OrganizerInfoData, OptionsData } from './customerInfo';
// import { useIndexDB } from '@/composables/useIndexDB';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

// const { getCacheData } = useIndexDB();
const { gateWay, service } = useRuntimeConfig().public || {};
const props = defineProps<{
  insuranceWay: string; // 投保方式2-组织投保，1-非组织投保
  insurantFiledDisabled: boolean; // 是否需要置灰被保人名称、证件类型、证件号码、联系电话
  isAntiMoney: boolean; // 是否触发反洗钱
  insureType: string; // 保险类型 C为林业险
  secDeptCode: string; // 二级机构
}>();

const organizerRef = ref(); // 投保组织者ref
const insurantInfoRef = ref(); // 被保险人信息ref
const applicantInfoRef = ref(); // 投保人信息ref
const beneficaryRef = ref(); // 受益人ref
const insuranceRelationship = ref(); // 被保人与投保人是否一致，true为一致

const organizerInfo = defineModel<OrganizerInfoData>('organizerInfo', {
  default: {},
});
// 被保险人数据
const insurantInfoList = defineModel<Array<CustomerInfo>>('insurantInfoList', {
  default: [],
});
// 投保人信息数据
const applicantInfoList = defineModel<Array<CustomerInfo>>('applicantInfoList', {
  default: [],
});
// 受益人信息数据
const beneficaryInfoList = defineModel<Array<CustomerInfo>>('beneficaryInfoList', {
  default: [],
});

// 是否为林业险
const isForestry = computed(() => {
  return props.insureType === 'C';
});
// 下拉选项集合
const optionsData = reactive<OptionsData>({
  sex: [],
  relation: [],
  relationGroup: [],
  profession: [],
  poorHouse: [],
  poorDetailType: [],
  personalCertificate: [],
  groupCertificate: [],
  industry: [],
  farmerMain: [],
  enterprise: [],
  country: [],
  organizationType: [],
  applyOrg: [],
  benefitMode: [],
  AGRLYZTLX: [],
});

// 是否为组织投保
const isOrganization = computed(() => props.insuranceWay === '2');

const validate = async () => {
  const validateArr = [applicantInfoRef.value?.validate(), beneficaryRef.value?.validate(), insurantInfoRef.value?.validate()];
  if (isOrganization.value) {
    validateArr.push(organizerRef.value?.validate());
  }
  const results = await Promise.all(validateArr);

  const isValid = results.every((result) => result?.valid);
  if (isValid) {
    // 所有表单校验通过
    return { valid: true, errors: [] };
  } else {
    return { valid: false, errors: ['校验不通过'] };
  }
};

defineExpose({
  validate,
});

const initBaseData = async () => {
  const params: Array<string> = ['sex', 'relation', 'profession', 'poorHouse', 'poorDetailType', 'personalCertificate', 'groupCertificate', 'industry', 'farmerMain', 'enterprise', 'country', 'organizationType', 'applyOrg', 'benefitMode', 'relationGroup', 'AGRLYZTLX'];
  // const requestParams = {
  //   url: `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`,
  //   params,
  // };
  // const res = await getCacheData('optionData', requestParams) as SelectOptions[];
  const res = await $postOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, params);
  // const res = await getBaseData.fetchData(params);
  if (res?.code === SUCCESS_CODE) {
    params.forEach((field) => {
      // 职业和行业为级联
      if (field !== 'industry' && field !== 'profession') {
        optionsData[field as keyof OptionsData] = res.data.filter((item) => item.value === field)?.[0]?.children;
      } else {
        const filterData = res.data.filter((item) => item.value === field)?.[0]?.children || [];
        optionsData[field] = filterData.map((item) => {
          return {
            label: item.label || '',
            value: item.value || '',
            children: item.children
              ? item.children.map((subItem: SelectOptions) => {
                  return {
                    label: subItem.label || '',
                    value: subItem.value || '',
                  };
                })
              : [],
          };
        });
      }
    });
  }
};
onMounted(() => {
  initBaseData();
});
</script>

<style>
.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  width: fit-content;

  &::before {
    position: absolute;
    left: 0;
    top: 6px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
</style>
