import type { ValueType } from 'ant-design-vue/es/vc-cascader/Cascader';
import type { CascaderOptionType } from 'ant-design-vue/es/cascader';
import type { DefaultOptionType } from 'ant-design-vue/es/select';

// 投保组织者
export interface OrganizerInfoData {
  organizerName?: string; // 名称
  certificateType?: string; // 证件类型
  certificateNo?: string; // 证件号码
  certificateIssueDate?: string; // 证件有效期开始日期
  certificateValidDate?: string; // 证件有效期截止日期
  province?: string; // 省
  city?: string; // 市
  county?: string; // 县区
  address?: string; // 地址
  postalCode?: string; // 邮政编码
  contactTelephone?: string; // 移动电话
  contactPerson?: string; // 联系人名称
  nationality?: string; // 国籍
  email?: string; // 邮箱
  businessScope?: string; // 经营范围
  industryCode?: ValueType | undefined; // 行业代码
  industryCodeLevel2?: ValueType | undefined; // 二级行业代码
  organizationType?: string; // 组织机构类型
}
// 客户信息 （投被受）
export interface CustomerInfo {
  personnelType?: string; // 客户属性 [1个人，0团体]
  name?: string; // 名称
  certificateType?: string; // 证件类型
  certificateNo?: string; // 证件号码
  certificateIssueDate?: string; // 证件有效期开始日期
  certificateValidDate?: string; // 证件有效期截止日期
  address?: string; // 地址
  postcode?: string; // 邮政编码
  mobileTelephone?: string; // 移动电话
  email?: string; // 电子邮箱
  subjectNumber?: string; // 农业主体类型代码
  sexCode?: string; // 性别 0男，1女
  nationality?: string; // 国籍
  professionCode?: ValueType | undefined; // 职业代码
  professionCodeLevel2?: ValueType | undefined; // 二级行业代码
  professionName?: string; // 职业名称
  relationshipWithApplicant?: string; // 投保人与被保险人关系
  relationshipWithInsured?: string; // 受益人与被保人关系
  sameInsuredPersons?: string; // 是否与被保险人保持一致 0不一致，1一致
  isConfirm?: string; // 投保人是否已书面确定必须选 投保人声明 是12 否1
  belongOrganizationNo?: string; // 企业归属
  certifyResult?: string; // 认证状态 0-未认证 1-已认证
  cisUuid?: string; // CIS返回的UUID
  hasUploadImage?: string; // 是否有上传证件 0-无证件上传 1-有证件上传
  ocrIdentifyResult?: string; // 证件OCR识别结果   0-识别失败 1-识别成功
  province?: string; // 省
  city?: string; // 市
  county?: string; // 县区
  poorSymbol?: string; // 贫困户标识
  poorDetailType?: string; // 贫困户细分
  industryCode?: ValueType | undefined; // 行业
  industryCodeLevel2?: ValueType | undefined; // 二级行业代码
  businessScope?: string; // 国有属性
  superviseInfoList: Array<SuperviseInfoList>; // 监管人信息
  id?: string; // 唯一id
  cid?: string; // 团体客户名称id
}

// 监管信息
export interface SuperviseInfoList {
  businessScope?: string; // 经营范围
  enterpriseType?: string; // 国有属性
  registeredFund?: string; // 注册资本金币种
  yearlySalaries?: string; // 年收入
  companyName?: string; // 工作单位名称
  superviseExtendList: Array<SuperviseExtendList>;
}

// 监管者信息
export interface SuperviseExtendList {
  name?: string; // 姓名
  benefitMode?: string; // 判定收益所有人方式
  equityRatio?: string; // 收益所有人持股数量或表决权占比
  address?: string; // 地址
  companyRelationType?: string; // 公司相关人员信息类型（1.控股股东/实际控制人、2.法定代表人/负责人、3.授权办理业务人、4.受益所有人）
  certificateNo?: string; // 证件类型
  certificateType?: string; // 证件号码
  certificateIssueDate?: string; // 证件有效期开始日期
  certificateValidDate?: string; // 证件有效期截止日期
  provinceName?: string; // 省名字
  province?: string; // 省code
  cityName?: string; // 市名字
  city?: string; // 市code
  countyName?: string; // 区名字
  county?: string; // 区code
}
export interface OptionsData {
  personalCertificate: DefaultOptionType[] | undefined;
  groupCertificate: DefaultOptionType[] | undefined;
  farmerMain: DefaultOptionType[] | undefined;
  poorHouse: DefaultOptionType[] | undefined;
  poorDetailType: DefaultOptionType[] | undefined;
  sex: DefaultOptionType[] | undefined;
  country: DefaultOptionType[] | undefined;
  profession: CascaderOptionType[] | undefined;
  relation: DefaultOptionType[] | undefined;
  applyOrg: DefaultOptionType[] | undefined;
  industry: CascaderOptionType[] | undefined;
  enterprise: DefaultOptionType[] | undefined;
  relationGroup: DefaultOptionType[] | undefined;
  benefitMode: DefaultOptionType[] | undefined;
  organizationType: DefaultOptionType[] | undefined;
  AGRLYZTLX: DefaultOptionType[] | undefined;
}

// 选择团体时 团体公司信息
export interface companyInfo {
  address: string; // 公司地址
  clientName: string; // 公司名称
  companyId: string; // 公司id
  creditCode: string; // 社会信用码
  orgCodeNo: string; // 组织机构码
  uuid: string; // 唯一标识
}
