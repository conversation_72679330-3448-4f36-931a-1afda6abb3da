<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <a-row :gutter="16">
      <a-col :span="8">
        <a-form-item class="text-[#333]" label="起保终保日期" :label-col="{ style: { width: pxToRem(96) } }">
          <span class="font-number text-[#333]">{{ (data.insuranceBeginDate ?? '-') + ' 至 ' + (data.insuranceEndDate ?? '-') }}</span>
        </a-form-item>
      </a-col>
      <a-col :span="4">
        <a-form-item class="text-[#333]" label="起止时间" :label-col="{ style: { width: pxToRem(68) } }">
          <span class="font-number text-[#333]">{{ data.timeRange === '0' ? '0-24时' : data.timeRange === '1' ? '12-12时' : '-' }}</span>
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item required label="年限系数" class="text-[#333]" name="shortTimeCoefficient" :label-col="{ style: { width: pxToRem(110) } }" :rules="[{ validator: coefficientValidator }]">
          <span v-if="readonly">{{ data.shortTimeCoefficient }}</span>
          <a-input-number v-else v-model:value="data.shortTimeCoefficient" class="text-[#333] font-number" style="width: 60%" placeholder="请输入" />
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item required name="disputedSettleMode" class="text-[#333]" label="争议处理方式" :label-col="{ style: { width: pxToRem(130) } }">
          <CompareRender v-if="readonly" :value="findLabel(selectOption, data.disputedSettleMode)" value-path="contract.baseInfo.disputedSettleMode" />
          <a-select v-else v-model:value="data.disputedSettleMode" class="text-[#333] font-number w-full" :options="selectOption" @change="resolutionChange" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row a-col :span="6">
      <a-form-item v-if="data.disputedSettleMode === '2'" required label="仲裁机构" name="arbitralDepartment" :label-col="{ style: { width: pxToRem(96) } }" :rules="[{ max: 100, message: '不能超过100个字符' }]">
        <span v-if="readonly">{{ data.arbitralDepartment }}</span>
        <a-input v-else v-model:value="data.arbitralDepartment" placeholder="请输入" />
      </a-form-item>
    </a-row>
  </a-form>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { SelectProps } from 'ant-design-vue';
import type { BaseInfo } from '../endorseModify.d';
import { pxToRem } from '@/utils/tools';
import { useIndexDB } from '@/composables/useIndexDB';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import CompareRender from '@/pages/review/endorseVerifyDetail/components/CompareRender.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const { getCacheData } = useIndexDB();

const data = defineModel<BaseInfo>('data', { default: {} });
const { readonly = false } = defineProps<{ readonly?: boolean }>();

// 争议处理方式options
const selectOption = ref<SelectProps['options']>([]);

const formRef = ref();

const findLabel = (options: [], value: string) => {
  if (options?.length > 0 && value) {
    return options.find((k) => k.value === value)?.label;
  }
  return '';
};

// 年限系数校验
const coefficientValidator = (_rule: Rule, value: number) => {
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!reg.test(String(value))) {
    return Promise.reject('请输入数字且小数位不能超过2位！');
  }
  if (value <= 0) {
    return Promise.reject('年限系数要大于0');
  }
  if (value >= 100) {
    return Promise.reject('年限系数要小于100');
  }
  return Promise.resolve();
};

// 争议方式改变清空仲裁机构
const resolutionChange = () => {
  data.value.arbitralDepartment = '';
};

defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
});

// 获取基础信息数据
const queryBaseData = async () => {
  const requestParams = {
    url: `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`,
    params: ['disputeType'],
  };
  const res = (await getCacheData('optionData', requestParams)) as SelectOptions[];
  selectOption.value = res?.[0]?.children;
};
onMounted(() => {
  queryBaseData();
});
</script>

<style scoped>
.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  width: fit-content;

  &::before {
    position: absolute;
    left: 0;
    top: 6px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
:deep(.ant-form-item) {
  margin: 0;
}
</style>
