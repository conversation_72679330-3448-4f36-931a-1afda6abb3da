<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 bg-[#F2F3F5] border-right-line">
        <div class="h-[48px] w-ful bg-image-url">
          <span class="text-[16px] text-[#00190c] leading-[48px] ml-[24px]">批改单信息</span>
        </div>
        <a-spin :spinning="loading">
          <a-form :colon="false">
            <div class="space-y-[12px]">
              <div class="flex items-center text-[14px] h-[46px] space-x-[24px] px-[24px] mb-[12px] bg-white rounded-b-[4px]">
                <div>批改申请单号：{{ contractModel?.edrApplyBaseInfoValue?.endorseApplyNo }}</div>
                <div>保单号：{{ contractModel?.edrApplyBaseInfoValue?.policyNo }}</div>
                <div>录单人：{{ contractModel?.edrApplyBaseInfoValue?.inputBy }}</div>
                <div>批改类型：{{ contractModel?.edrApplySceneInfoValue?.sceneName }}</div>
              </div>
              <InfoGroup id="modify-content" title="本次批改内容">
                <ModifyInfo ref="modifyContentRef" v-model:data="contractModel.edrApplyBaseInfoValue" v-model:reason="contractModel.edrApplySceneInfoValue" :insurance-begin-date="contractModel?.contractValue?.baseInfo?.insuranceBeginDate" />
              </InfoGroup>
              <ImageUpload ref="imageRef" :endorse-apply-no="contractModel?.edrApplyBaseInfoValue?.endorseApplyNo" :document-group-id="contractModel?.edrApplyAttachmentVO?.documentGroupId" :policy-no="contractModel?.policyNo" type-name="注销" @refresh-document-id="refreshDocumentId" />
            </div>
          </a-form>
        </a-spin>
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" @click="saveData(true)">暂存</a-button>
      <a-button type="primary" @click="validateAllForms('1')">保费计算</a-button>
      <a-button type="primary" @click="validateAllForms('2')">申请核保</a-button>
    </footer>
    <!-- 错误信息弹窗 -->
    <a-modal v-model:open="errorVisible" :width="pxToRem(480)" :centered="true" :style="{ 'max-height': pxToRem(300), overflow: 'auto' }" @ok="errorVisible = false">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#f03e3e]" :icon="IconCloseCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">温馨提示</span>
      </div>
      <div v-for="(item, i) in confirmErrText" :key="i">{{ item }}</div>
      <template #footer>
        <a-space>
          <a-button type="primary" @click="errorVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal>
    <!-- eoa签报 -->
    <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" title="批改-EOA签报申请页" :eoa-type="eoaTypeValue" :department-code="departmentCode" :show-uploadbtn="true" @ok="handleSubmit" @reset="handleReset">
      <template #content>
        <div>
          <a-form-item label="签报主题" name="eoaTitle">
            <a-input v-model:value="eoaData.eoaTitle" disabled />
          </a-form-item>
          <a-form-item label="签报内容" name="eoaContent">
            <a-textarea v-model:value="eoaData.eoaContent" disabled />
          </a-form-item>
        </div>
      </template>
    </CommonApprovalModal>
  </div>
</template>

<script setup lang="ts">
import { IconCloseCircleFilledFont } from '@pafe/icons-icore-agr-an';
import ImageUpload from '../imageUpload/imageUpload.vue';
import ModifyInfo from './components/ModifyInfo.vue';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
// 错误信息弹窗显示隐藏
const errorVisible = ref<boolean>(false);
// 错误信息描述
const confirmErrText = ref<string[]>([]);
// 是否触发反洗钱
const isAntiMoney = ref<boolean>(false);
const route = useRoute();
const router = useRouter();
const imageRef = ref();
const showApproval = ref<boolean>(false); // 是否展示签报弹窗
const departmentCode = ref<string>(route.query.departmentCode as string); // 机构code
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签
const eoaTypeValue = ref<string>(''); // 链路type，获取审批链路人员信息
// 签报数据
const eoaData = ref<Record<string, string>>({
  eoaTitle: '',
  eoaContent: '',
});
const refreshDocumentId = (id: string) => {
  contractModel.value.edrApplyAttachmentVO.documentGroupId = id;
};
// 提交签报
const handleSubmit = async (eoaData: Record<string, string>) => {
  previewOrCreate(eoaData);
};
// 创建退保签报
const previewOrCreate = async (hanlderEoaData: Record<string, string>) => {
  try {
    // 用于提交签报的参数
    const params = {
      endorseCreateEoaVO: {
        relationBusinessNo: contractModel.value.edrApplyBaseInfoValue?.endorseApplyNo, // 批改申请单号
        eoaType: '004', // EOA类型：003:整单批改核保申请、004:注销核保申请、005:退保核保申请
        eoaSubject: eoaData.value.eoaTitle, // 标题  $1:保单号 $2:场景，整单批改、退保等
        eoaBody: eoaData.value.eoaContent, // 批改内容(批文)
        documentGroupId: hanlderEoaData.documentGroupId, // 附件组ID
        productCode: route.query.productCode, // 产品编码
        departmentCode: route.query.departmentCode, // 机构
        approveChainList: hanlderEoaData.approveChainList,
        attachmentList: hanlderEoaData.fileInfos,
      },
    };
    const res = await $postOnClient(`${gateWay}${service.endorse}/eoa/createEoa`, params); // 创建eoa审批接口
    if (res && res.code === SUCCESS_CODE) {
      message.success('签报发起成功');
      showApproval.value = false; // 关闭签报弹窗
      deletPageTabListItem('/cancelModify'); // 关闭页签
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
    } else {
      message.error(res?.msg as string);
    }
  } catch (error) {
    console.log(error);
  }
};
// 查询eoaType类型用于获取审批链路人员
const queryEoaType = async () => {
  try {
    const params = {
      endorseApplyNo: contractModel.value.edrApplyBaseInfoValue?.endorseApplyNo, // 批改申请单号
      policyNo: route.query.endorseApplyNo, // 保单号
      signType: '004', // 004:注销核保申请
    };
    const res = await $postOnClient<string>(`${gateWay}${service.endorse}/eoa/getEoaType`, params); // 获取eoaType类型用于获取审批链路
    if (res?.code === SUCCESS_CODE) {
      eoaTypeValue.value = res?.data;
    }
  } catch (error) {
    console.log(error);
  }
};
const initContractModel = (data) => {
  return {
    ...data,
    edrApplyBaseInfoValue: data.edrApplyBaseInfoValue || {},
    edrApplySceneContentVO: data.edrApplySceneContentVO || {},
    edrApplyAttachmentVO: data.edrApplyAttachmentVO || {},
  };
};

// !!!类型晚点补
const contractModel = ref(initContractModel({}));

const loading = ref(false);

const modifyContentRef = ref();
const getValidateArr = () => {
  const validateItems = [
    modifyContentRef.value?.validate(),
    // 其他子组件的校验方法
  ];
  return validateItems;
};
const validateAllForms = async (type: string) => {
  console.log(contractModel, 'contractModel');
  const results = await Promise.all(getValidateArr());
  const isValid = results.every((result) => result.valid);
  if (isValid) {
    // 所有表单校验通过
    // 1为保费计算 2为提交核保
    if (type === '1') {
      premiumCompute();
    } else if (type === '2') {
      submitApproval();
    }
  } else {
    // 有表单校验未通过，提示用户
    console.log('有表单校验未通过', results);
    // 跳转到校验不通过的第一个项目
    const errorDiv = document.querySelector('.ant-form-item-has-error');
    errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 暂存
const saveData = async (showMsg?: boolean) => {
  loading.value = true;
  const result = imageRef.value?.submit();
  if (result === 'N') {
    message.error('存在未分类的附件信息，请调整。');
    loading.value = false;
    return;
  }
  const params = {
    ...contractModel.value,
  };
  try {
    const res = await $postOnClient(`${gateWay}${service.endorse}/web/endorse/temp-storage`, params);
    if (res && res.code === SUCCESS_CODE) {
      if (showMsg) {
        message.success('保存成功');
      }
      contractModel.value.endorseApplyNo = res.data.endorseApplyNo;
      loading.value = false;
      router.push({
        name: 'cancelModify',
        query: {
          ...route.query,
          qryScene: 'WRITE_OFF_MODIFY',
        },
      });
      getInit('WRITE_OFF_MODIFY');
    } else {
      message.error(res?.msg || '操作失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

// 保费计算
const premiumCompute = async () => {
  loading.value = true;
  const result = imageRef.value?.submit();
  if (result === 'N') {
    message.error('存在未分类的附件信息，请调整。');
    loading.value = false;
    return;
  }
  try {
    const fetchUrl = `${gateWay}${service.endorse}/web/endorse/quote`;
    const params = {
      ...contractModel.value,
    };
    const res = await $postOnClient(fetchUrl, params);
    loading.value = false;
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      isAntiMoney.value = false;
      contractModel.value.endorseApplyNo = res.data.endorseApplyNo;
      router.push({
        name: 'cancelModify',
        query: {
          ...route.query,
          qryScene: 'WRITE_OFF_MODIFY',
        },
      });
      getInit('WRITE_OFF_MODIFY');
    } else {
      // 错误提示
      handleHint(msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 接口返回错误信息提示
const handleHint = (msg: string) => {
  errorVisible.value = true;
  confirmErrText.value = msg?.split(';');
};
const getInit = async (sceneName?: string) => {
  loading.value = true;
  try {
    const res = await $postOnClient(`${gateWay}${service.endorse}/web/endorse/detail`, {
      policyNo: route.query.endorseApplyNo,
      qryScene: sceneName || route.query.qryScene || 'WRITE_OFF_APPLY',
      endorseSceneCode: '00006',
    });
    if (res && res.code === SUCCESS_CODE) {
      loading.value = false;
      contractModel.value = initContractModel(res.data || {});
      nextTick(() => {
        // if (contractModel?.value?.edrApplyAttachmentVO?.documentGroupId) {
        imageRef?.value?.getDocList();
        // } else {
        //   imageRef?.value?.reset();
        // }
      });
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

// 提交核保
const submitApproval = async () => {
  loading.value = true;
  const params = {
    ...contractModel.value,
    edrEoaVO: {
      eoaStatus: 'E', // 后端说会有其他场景传其他参数
    },
  };
  try {
    const fetchUrl = gateWay + service.endorse + '/web/endorse/apply';
    const res = await $postOnClient<{ resultEoa: string }>(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      // 触发审批链
      if (res.data?.resultEoa === 'Y') {
        await queryEoaType(); // 获取审批链路类型
        eoaData.value.eoaTitle = `关于保单[${contractModel.value.edrApplyBaseInfoValue?.policyNo}]进行[${contractModel.value?.edrApplySceneInfoValue?.sceneName}]的申请 `; // 签报主题
        eoaData.value.eoaContent = contractModel.value.edrApplyBaseInfoValue.endorseComment; // 签报内容
        showApproval.value = true;
        loading.value = false;
      } else {
        message.success(res?.msg);
        deletPageTabListItem('/cancelModify'); // 关闭页签
        // 返回上一页或者首页
        if (pageTabList.value.length) {
          router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
        } else {
          router.push('/home');
        }
      }
    } else {
      message.error(res?.msg || '提交核保失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

// 关闭审批弹窗获取详情
const handleReset = () => {
  getInit('WRITE_OFF_MODIFY');
};

onActivated(() => {
  getInit();
});

const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }
  }
  :deep(.info-group-card .card-content) {
    background: #fff;
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
