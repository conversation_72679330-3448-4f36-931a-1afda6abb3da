interface RiskAgrInfo {
  insuredNumber: string; // 保险数量
  insuredUnit: string; // 保险数量单位
  insuredAmount: string; // 保险金额
  premium: string; // 保费金额
  farmersCount: string; // 参保农户数
  planAttributeSameMain: string; // 保费来源是否与主险一致
  reductionCoefficient: string; // 减免系数
  substitute: string; // 农户保费是否代缴 Y：是，N：否
  planConfigId: string; // 保险方案配置ID（保险方案配置表ID）
  fundingSource: string; // 代缴保费资金来源 '财政补贴'-'01','扶贫资金'-'02','社会捐赠'-'03','公司代缴'-'04','组合'-'05'
  riskAddressInfo: {
    province: string; // 标的地址-省
    city: string; // 标的地址-市
    county: string; // 标的地址-县
    town: string; // 标的地址-城镇
    village: string; // 标的地址-村
    address: string; // 完整地址
  };
  agrRiskAttribute: {
    bussinessType: string; // label
    bussinessKey: string; // keyword
    bussinessValue: string; // value
    notRequiredFlag: boolean; // 是否必填
    notShowFlag: boolean; // 是否显示
  }[];
  insuredUnitChName: string;
}

export interface RiskGroupInfo {
  defaultRisk: string | undefined;
  combinedProductCode: string; // 标的明细
  combinedProductName: string; // 标的名称
  riskAgrInfo: RiskAgrInfo;
  planInfoList: Array<PlanInfoListItem>;
  addRiskFlag?: string | undefined; // 是否新增标的
}

export interface RiskAddressInfo {
  province: string; // 省
  provinceName: string;
  city: string; // 市
  cityName: string;
  county: string; // 区
  countyName: string;
  town: string; // 镇
  townName: string;
  village: string; // 村
  villageName: string;
  address: string; // 完整地址
}
// 基本信息
export interface EndorseBaseInfo {
  originPremium: string; // 原保费
  totalActualPremium: string; // 现保费
  actualPremiumChange: string; // 保费变化
  applyDate: string; // 批改日期
  effectiveDate: string; // 生效日期
  isSendElectronicEdrDocument: string; // 是否发送电子批单
  endorseComment: string; // 批文
  cancelReason?: string; // 批改原因
  inputBy: string; // 录单人
  endorseApplyNo: string; // 批改申请单号
  policyNo: string;
  calculateType: string;
}

// 基本信息
export interface BaseInfo {
  assisterInfoList: Array<AssisterInfo>; // 协办员列表
  insuranceBeginDate: string; // 保险起期
  insuranceEndDate: string; // 保险止期
  disputedSettleMode: string; // 争议处理方式 1:诉讼 2: 仲裁
  arbitralDepartment?: string; // 仲裁机构
  shortTimeCoefficient: string; // 年限系数
  timeRange: string; // 起止时间
  departmentCode: string; // 机构编码
  applyPolicyNo: string; // 保单号
  totalActualPremium: string; // 整单总保费
  deductionDesc: string; // 免赔描述
  premiumOwner: string; // 责任人
  coinsuranceMark: string; // 共保标志
  insuranceEndDate: string; // 保险止期
  govSubsidyType: string; // 补贴类型
  acceptInsuranceFlag: string; // 是否主承
  applyPolicyType: string;
  productCode: string;
  productVersion: string;
  insuredNumber: string;
  farmersCount: number;
  secDeptCode: string;
}
// 责任
export interface DutyInfoListItem {
  dutyCode: string; // 责任编码
  dutyName: string; // 责任名称
}

// 方案
export interface PlanInfoListItem {
  expectPremiumRate?: string; // 费率
  isMain?: string; // 是否主险,是否主险 1-主险 0-附加险
  planCode?: string; // 险种编码
  planEnglishName?: string; // 英文险种名称
  planName?: string; // 险种名称
  unitInsuredAmount?: string; // 单位保险金额
  unitPrimium?: string; // 单位保费
  centralFinance?: string; // 中央来源比例
  cityFinance?: string; // 市财政来源
  countyFinance?: string; // 区县财政来源
  farmersFinance?: string; // 农户来源
  otherFinance?: string; // 其他来源
  provincialFinance?: string; // 省财政来源
  totalActualPremium?: string; // 实交保费合计,保险方案页面叫做 复核保费
  totalAgreePremium?: string; // 应交保费合计,保险方案页面叫做 减免后保费金额
  totalInsuredAmount?: string; // 保单保险金额,保险方案页面叫做 保险金额
  totalStandardPremium?: string; // 减免前保单保费金额,保险方案页面叫做 基准保费金额
  eachCompensationMaxAmount?: string; // 每次赔偿限额
  feesInfoSameFlag?: string; // 附加险是否与主险一致
  dutyInfoList?: Array<DutyInfoListItem>; // 责任list
  flag?: string; // 判断是否为新增附加险
  isModify?: boolean;
}
// 保险方案详情
export interface InsurancePlanDetail {
  insuranceProposalName: string; // 保险方案名称
  marketProductName: string; // 产品名称
  departmentName: string; // 机构名称
  planAttributeSameMain: string; // 是否与主险一致
  planList: Array<PlanInfoListItem>; // 方案产品数据
  id: string; // 方案id
  fileInfos: Array<FileInfo>; // 文件列表
}

// 保额总计
export interface SumRiskGroupAmount {
  totalInsuredAmount: string; // 合计保单保险金额
  totalStandardPremium: string; // 合计减免前保单保费金额
  totalAgreePremium: string; // 合计应交保费
  totalActualPremium: string; // 合计实交保费
}

// 文件
export interface FileInfo {
  fileName: string; // 文件名称
  fileType: string; // 文件类型
  fileKey: string; // 文件id
}

// 附加险下拉选项
export interface PlanInfo {
  planName: string; // 险种名称
  planCode: string; // 险种id
  dutyInfoList?: Array<DutyInfoListItem>; // 责任列表
}

// 提交核保返回校验信息
export interface ExceptionPayload {
  exceptionPayload: {
    formPayloadList: FormPayload[];
  };
}

export interface FormPayload {
  code: string; // 提示code
  msg: string; // 提示信息
  type: string; // 提示类型
}

// 收费计划
export interface payInfo {
  branchBankOptions: Record<string, string>[]; // 银行分支选项
  bankCode: string; // 银行分支code
  bankHeadquartersCode: string; // 银行总行code
  bankDetail: string; // 银行明细
  bankAccountNo: string; // 银行帐号
  bankAttribute: string; // 账户类型
  certificateNo: string; // 身份证
  paymentPath: string; // 途径
  termNo: string; // 期次
  payerType: string; // 补贴类型
  paymentPersonName: string; // 付款人名称
  actualPremium: string; // 应收保费
  paymentPeriod: [string, string]; // 缴费起止期
  paymentBeginDate: string; // 缴费起期
  paymentEndDate: string; // 缴费止期
  receivableDate: string; // 预计收回日期
  otherPayerTypeDesc: string; // 备注
  noclaimRate: string; // 免赔率
  noclaimAmount: string; // 免赔额
  typeDisabled?: boolean; // 是否新增行
  flag?: boolean; // 判断是否为新增行
}
// 是否需要eoa签报返回类型
export interface CheckEoa {
  resultCode: string; // 结果判断标识
  code: string; // 状态码
}

// 共保信息
export interface CoinsuranceType {
  innerCoinsuranceMark: string; // 共保属性:0-系统外共保,1-系统内共保
  innerCoinsuranceAgreement: string; // 共保协议码
  totalPremium: string; // 总保费
  totalInsuredAmount: string; // 总保额
  coinsuranceDetailList: CoinsuranceDetailType[];
  innerCoinsuranceMarkChName: string;
}
// 共保列表
export interface CoinsuranceDetailType {
  reinsureCompanyCode: string; // 共保公司编码
  reinsureCompanyName: string; // 共保公司名称
  reinsureScaleBigDecimal: string; // 共保比例
  insuredAmountBigDecimal: string; // 保额
  premiumBigDecimal: string; // 保费
  onlyselfScaleBigDecimal: string; // 净自留比例
  callPremiumBigDecimal: string; // 复核保费
  acceptInsuranceFlag: string; // 是 // 主承保：0 // 否,1是
  employeeCode: string; // 业务员
  idchannelSourceCode: string; // 渠道
  channelSourceName: string; // 渠道名称
  coinsuranceInsureFeeRatioBigDecimal: string; // 共保出单费比例
  coinsureCompanyFinanceDeptCode: string; // 分出管理机构编码
  coinsureCompanyMgDeptCode: string; // 分出财务机构编码
  employeeName: string; // 业务员名称
  totalValueAddedTaxBigDecimal: string; // 增值税
  totalVATExcludedPremiumBigDecimal: string; // 不含增值税保费
}
// 投保人、被保险人、受益人
export interface ApplicantInfo {
  [key: string | number]: string | number;
  personnelTypeChName: string;
  name: string;
  certificateNo: string;
  certificateIssueDate: string;
  certificateValidDate: string;
  address: string;
  postcode: string;
  sexCodeChName: string;
  mobileTelephone: string;
  poorSymbolChName: string;
  professionName: string;
  belongOrganizationNoChName: string;
  subjectNumberName: string;
  nationalityZh: string;
  personnelType: string;
  superviseInfoList: SuperviseExtend[];
}
interface SuperviseExtend {
  superviseExtendList: Record<string, string>[];
  yearlySalaries: string;
  companyName: string;
  businessScope: string;
  enterpriseTypeChName: string;
  registeredFund: string;
}

interface IDisclaimInfo {
  dutyCode: string; // 免赔编码（免赔项目所选值最后一个元素）
  noclaimAmount: string; // 免赔额
  noclaimItem: string; // 免赔项目一级
  noclaimItemArr: string[]; // 免赔项目所选值
  noclaimRate: string; // 免赔率
  noclaimType: string; // 免赔类型
  planCode: string; // 险种编码
}

interface ISpecialPromise {
  promiseCode: string;
  promiseDesc: string;
  promiseType: string;
}

interface ICostInfo {
  assisterCharge: string; // 协办费
  assisterChargeLimit: string;
  calamitySecurityRate: string; // 防灾防损费
  calamitySecurityRateLimit: string;
  coinsuranceInsureFeeRatio: string; // 共保出单费
  coinsuranceInsureFeeRatioLimit: string;
  commissionBrokerChargeProportion: string; // 手续费/经纪费
  commissionBrokerChargeProportionLimit: string;
  isPolicyBeforePayfee: string; // 财务标识
  isPolicyBeforePayfeeChName: string;
  managementFees: string; // 工作经费
  managementFeesLimit: string;
  performanceValue1Default: string; // 农险补贴
  performanceValue1DefaultLimit: string;
  totalCostRate: string;
  totalSumFeeLimit: string; // 费用比例之和阈值
}

interface ISaleInfo {
  businessSourceCode: string;
}

interface ContractValue {
  baseInfo: BaseInfo;
  applicantInfoList: ApplicantInfo[];
  insurantInfoList: ApplicantInfo[];
  beneficaryInfoList: ApplicantInfo[];
  riskAddressInfoList: RiskAddressInfo[];
  riskGroupInfoList: riskGroupInfo[];
  sumRiskGroupAmount: SumRiskGroupAmount;
  coinsuranceInfo: CoinsuranceType;
  payInfoList: payInfo[];
  payerTypeSumOfPayInfo: Record<string, string>;
  organizerInfo: Record<string, string>;
  noclaimInfoList: IDisclaimInfo[];
  specialPromiseList: ISpecialPromise[];
  costInfo: ICostInfo;
  saleInfo: ISaleInfo;
}
interface EdrApplySceneInfoValue {
  cancelType: string;
  sceneName: string;
}
// 总集合
export interface contractModelType {
  moreRiskFlag: string;
  applyPolicyNo: string; // 投保单号
  edrApplyBaseInfoValue: EndorseBaseInfo; // 基础信息
  edrApplySceneInfoValue: EdrApplySceneInfoValue; // 场景
  edrApplyPayInfoListVOList: payInfo[]; // 收费计划
  endorseApplyNo: string;
  endorseNo: string;
  insrSpltPlyEdrSumyVO: Record<string, string>; // 清单信息
  policyNo: string;
  edrApplyAttachmentVO: Record<string, string>;
  contractValue: ContractValue;
  edrApplyPayInfoTypeSumVO: Record<string, string>;
}
export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
export interface SearchFormState {
  certificateNo: string; // 证件号码
  mobileTelephone: string; // 联系电话
  farmerName: string; // 被保险人名称
}

export interface SignData {
  signType: string; // 签报类型
  signDesc: string; // 签报类型说明
  signStatus: string; // 签报状态
  signStatusDesc: string; // 签报状态说明
  eoaNo: string; // 签报号
  eoaDetailUrl: string; // 签报详情url
}

export interface FarmerList {
  index: string;
  farmerlistNo: string;
  farmerName: string;
  certificateNo: string;
  certificateType: string;
  executeFlag: boolean;
  idFarmerlistCustomInfo: string;
}

export interface ErrorFarmerList extends FarmerList {
  ruleContent: string;
  farmerListNo: string;
}

export interface RiskFarmerList extends FarmerList {
  ruleContent: string;
  farmerListNo: string;
}

export interface IFarmerInfo {
  farmerlistName: string;
  riskAddressCodeCity: string;
  riskAddressCodeCounty: string;
  riskAddressCodeProvince: string;
  riskAddressCodeTown: string;
  riskAddressCodeVillage: string;
}

export type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';
