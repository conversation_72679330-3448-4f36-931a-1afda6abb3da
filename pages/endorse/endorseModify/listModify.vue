<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(110) } }">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="分户被保险人" name="farmerName">
                <a-input v-model:value="searchFormState.farmerName" placeholder="请输入分户被保险人" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="证件号码" name="certificateNo">
                <a-input v-model:value="searchFormState.certificateNo" placeholder="请输入证件号码" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="联系方式" name="mobileTelephone">
                <a-input v-model:value="searchFormState.mobileTelephone" placeholder="请输入联系方式" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px mt-[4px]">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-[8px]">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-10px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
        <div class="flex align-center space-x-[8px]">
          <a-button type="primary" :disabled="hasData || onlyView" @click="modifyListInfoVisible = true">清单信息修改</a-button>
          <a-button type="primary" :loading="loadingEffect" :disabled="!effectStatus || onlyView" @click="takeEffect">生效</a-button>
          <a-button type="primary" :disabled="onlyView" @click="handleMulEndorse">批量批改</a-button>
          <a-button type="primary" :disabled="onlyView" @click="handlerEditOrCheck('add')">添加分户被保险人</a-button>
          <a-button v-if="false" type="primary" :disabled="onlyView" @click="handleToLandMap({ farmerListNo: '', addressName: '' })">按图指认</a-button>
          <a-button type="primary" :loading="exportLoading" :disabled="onlyView" @click="handleExportList()">导出清单数据</a-button>
        </div>
      </div>
      <div v-show="tipShow" class="flex space-x-[12px] mb-[8px]">
        <div>清单编号： {{ tipState.farmerListNo }}</div>
        <div>清单户数： {{ tipState.farmersCount }}</div>
        <div>
          错误户数： <span :class="['text-[red]', { 'cursor-pointer': tipState.errorCount }]" @click="handleToErrorFarmerList(tipState.errorCount)">{{ tipState.errorCount }}</span>
        </div>
        <div>
          风险户数： <span :class="['text-[red]', { 'cursor-pointer': tipState.riskCount }]" @click="handleToDangerFarmerList(tipState.riskCount)">{{ tipState.riskCount }}</span>
        </div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="tableLoading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, index, text }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.key === 'statusList'">
            <div v-if="executeFlag">执行中</div>
            <div v-else-if="FailedFlag">执行失败</div>
            <div v-else class="flex">
              <template v-for="item in record.statusList" :key="item">
                <div v-if="item === '02'" class="flex items-center bg-[#FFECEB] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#DB3939] opacity-65" :icon="IconErrorCircleFilledFont" />
                  <span class="ml-[4px] text-[#DB3939]">错误</span>
                </div>
                <div v-else-if="item === '03'" class="flex items-center bg-[#FFF9EB] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#E6AD1C] opacity-65" :icon="IconErrorCircleFilledFont" />
                  <span class="ml-[4px] text-[#E6AD1C]">风险</span>
                </div>
                <div v-else class="flex items-center bg-[#F2FFF9] rounded-[4px] px-[8px] py-[3px] mx-[1px]">
                  <VueIcon class="text-[12px] text-[#07C160] opacity-65" :icon="IconCheckCircleFilledFont" />
                  <span class="ml-[4px] text-[#07C160]">正确</span>
                </div>
              </template>
            </div>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <!-- 操作区域 -->
            <a-space :size="1">
              <a-button v-if="!onlyView" type="link" :disabled="executeFlag" @click="handlerEditOrCheck('edit', record.idFarmerlistCustomInfo)">批改</a-button>
              <a-button type="link" :disabled="executeFlag" @click="handlerEditOrCheck('view', record.idFarmerlistCustomInfo)">查看</a-button>
              <a-button v-if="!onlyView" type="link" :disabled="executeFlag" @click="handleDelete(record as FarmerList)">删除</a-button>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'insuranceNums'">
            <a-tooltip v-if="record.farmerRiskList?.length > 1" placement="top">
              <template #title>
                <div v-for="risk in record.farmerRiskList" :key="risk.riskCode">
                  {{ `${risk.riskName} ${risk.insuranceNums}${risk.insuranceNumsUnit}` }}
                </div>
              </template>
              <span>{{ text }}</span>
            </a-tooltip>
            <span v-else>{{ text }}</span>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <!-- 批量批改弹窗 -->
  <MulEndorse v-if="mulEndorseVisible" v-model:visible="mulEndorseVisible" :refresh="submit" :endorse-apply-no="endorseApplyNo" />
  <ModifyListInfo v-if="modifyListInfoVisible" v-model:visible="modifyListInfoVisible" :refresh="submit" :farmerlist-name="farmerInfo.farmerlistName" :risk-address-code-city="farmerInfo.riskAddressCodeCity" :risk-address-code-county="farmerInfo.riskAddressCodeCounty" :risk-address-code-province="farmerInfo.riskAddressCodeProvince" :risk-address-code-town="farmerInfo.riskAddressCodeTown" :risk-address-code-village="farmerInfo.riskAddressCodeVillage" />
</template>

<script setup lang="ts">
import { IconCheckCircleFilledFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import type { DataType, SearchFormState, FarmerList, IFarmerInfo } from './endorseModify.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { usePost, $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import MulEndorse from './modal/mulEndorse.vue';
import ModifyListInfo from './modal/modifyListInfo.vue';

const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签
const router = useRouter();
const route = useRoute();

const { gateWay, service } = useRuntimeConfig().public || {};

const modifyListInfoVisible = ref<boolean>(false);

const getListReq = await usePost<DataType>(`${gateWay}${service.farmer}/edrCust/getEdrCustByPage`); // 查询列表请求
// 定义表格列
const listColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'index' },
  { title: '分户被保险人', dataIndex: 'farmerName' },
  { title: '证件号码', dataIndex: 'certificateNo' },
  { title: '标的名称', dataIndex: 'riskName' },
  { title: '保险数量', dataIndex: 'insuranceNums' },
  { title: '联系电话', dataIndex: 'mobileTelephone' },
  { title: '状态', dataIndex: 'statusList', key: 'statusList' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 查询表单和状态
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  certificateNo: '', // 证件号码
  mobileTelephone: '', // 联系方式
  farmerName: '', // 被保险人名称
});

const endorseApplyNo = computed(() => {
  return (route.query.endorseApplyNo || '') as string;
});

const executeFlag = computed(() => {
  if (dataSource.value?.length > 0) {
    // -1-执行中 -3-执行失败 02-错误 03-风险
    return dataSource.value[0].statusList.includes('-1');
  } else {
    return false;
  }
});

const FailedFlag = computed(() => {
  if (dataSource.value?.length > 0) {
    // -1-执行中 -3-执行失败 02-错误 03-风险
    return dataSource.value[0].statusList.includes('-3');
  } else {
    return false;
  }
});

const onlyView = computed(() => {
  return route.query?.onlyView === 'Y';
});

const dataSource = ref<Record<string, string>[]>([]); // 表格数据
const tableLoading = ref<boolean>(false); // 表格loading
// 查询
const search = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
  fetchTip();
  getEffectStatus();
};
// 列表分页查询
const refresh = async () => {
  tableLoading.value = true;
  try {
    // 接口所需要参数
    const params = {
      ...searchFormState,
      endorseApplyNo: route.query.endorseApplyNo, // 第一次批改 ，传保单号，批改后传批改单号
      policyNo: route.query.policyNo,
      type: route.query.endorseApplyNo ? '02' : '01', //01 保单 02 批单
      pageNum: pagination.current, // 页码
      pageSize: pagination.pageSize, // 每页数
    };
    dataSource.value = [];
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    }
  } catch (error) {
    console.log(error);
  } finally {
    tableLoading.value = false;
  }
};
// 分页处理
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value.resetFields();
  refresh();
};
// 编辑和查看
const handlerEditOrCheck = async (type: string, idFarmerlistCustomInfo?: string) => {
  router.push({
    name: 'endorseFarmerInfo',
    query: {
      mode: type,
      farmerListNo: route.query.farmerListNo,
      endorseApplyNo: route.query.endorseApplyNo,
      idFarmerlistCustomInfo,
      t: new Date().getTime(),
    },
  });
};
// 生效
const loadingEffect = ref(false);
const takeEffect = async () => {
  const params = {
    endorseApplyNo: route.query.endorseApplyNo,
  };
  loadingEffect.value = true;
  const res = await $postOnClient(`${gateWay}${service.farmer}/insrSpltPlyEdrSumy/takeEffect`, params);
  loadingEffect.value = false;
  if (res && res.code === SUCCESS_CODE) {
    message.success(res?.msg);
    deletPageTabListItem('/listModify'); // 关闭页签
    // 返回上一页或者首页
    if (pageTabList.value.length) {
      router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
    } else {
      router.push('/home');
    }
  } else {
    message.error(res?.msg || '');
  }
};
const effectStatus = ref<boolean>(false);
// 查询生效状态
const getEffectStatus = async () => {
  const params = {
    endorseApplyNo: route.query.endorseApplyNo,
    type: '02',
  };
  const res = await $postOnClient<{ takeEffect?: string }>(`${gateWay}${service.farmer}/insrSpltPlyEdrSumy/queryInsrSpltEdrChangeList`, params);
  if (res && res.code === SUCCESS_CODE) {
    if (res.data?.takeEffect === '0') {
      effectStatus.value = true;
    } else {
      effectStatus.value = false;
    }
  }
};

// 批量批改
const mulEndorseVisible = ref<boolean>(false);
const handleMulEndorse = () => {
  mulEndorseVisible.value = true;
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
  fetchTip();
  getEffectStatus();
};

const handleToLandMap = ({ farmerListNo, addressName }: { farmerListNo: string; addressName: string }) => {
  router.push({
    path: 'landEdit',
    query: {
      farmerListNo,
      addressName,
    },
  });
};

const exportLoading = ref(false);
const handleExportList = async () => {
  try {
    exportLoading.value = true;
    const params = {
      endorseApplyNo: route.query.endorseApplyNo, //批改单号
    };
    await $getOnClient<{ takeEffect?: string }>(`${gateWay}${service.farmer}/file/endorseFarmerListExport`, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
    exportLoading.value = false;
  } catch {
    exportLoading.value = false;
  }
};

const handleToErrorFarmerList = (errorCount: number) => {
  if (errorCount) {
    router.push({
      name: 'endorseErrorFarmerList',
      query: {
        endorseApplyNo: route.query.endorseApplyNo,
      },
    });
  }
};

const handleToDangerFarmerList = (riskCount: number) => {
  if (riskCount) {
    router.push({
      name: 'endorseDangerFarmerList',
      query: {
        endorseApplyNo: route.query.endorseApplyNo,
      },
    });
  }
};

const handleDelete = async (record: { farmerName: string; idFarmerlistCustomInfo: string }) => {
  Modal.confirm({
    title: '提醒',
    content: `是否确认删除分户被保险人【${record.farmerName}】`,
    async onOk() {
      try {
        const params = {
          idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
          endorseApplyNo: route.query.endorseApplyNo, //批改单号
        };
        const res = await $postOnClient(`${gateWay}${service.farmer}/edrList/deleteFarmerList`, params);
        if (res && res.code === SUCCESS_CODE) {
          message.success(res.msg);
          refresh();
          fetchTip();
          getEffectStatus();
        } else if (res) {
          message.error(res.msg);
        }
      } catch (e) {
        console.log(e);
      }
    },
  });
};

const tipState = ref({ farmerListNo: '-', farmersCount: 0, errorCount: 0, riskCount: 0 });
const tipShow = ref(false);
const farmerInfo = ref<IFarmerInfo>({
  farmerlistName: '',
  riskAddressCodeCity: '',
  riskAddressCodeCounty: '',
  riskAddressCodeProvince: '',
  riskAddressCodeTown: '',
  riskAddressCodeVillage: '',
});
const hasData = ref(false);
const fetchTip = async () => {
  try {
    const url = `${gateWay}${service.farmer}/insrSpltPlyEdrSumy/getEdrFarmerListSummaryAndRuleInfo`;
    const formData = new FormData();
    if (route?.query?.endorseApplyNo) {
      formData.append('endorseApplyNo', route.query.endorseApplyNo as string);
    }
    hasData.value = false;
    farmerInfo.value = {
      farmerlistName: '',
      riskAddressCodeCity: '',
      riskAddressCodeCounty: '',
      riskAddressCodeProvince: '',
      riskAddressCodeTown: '',
      riskAddressCodeVillage: '',
    };
    const res = await $postOnClient<{ farmerlistNo: string; farmersCount: number; errorCount: number; riskCount: number } & IFarmerInfo>(url, formData);
    if (res && res.data) {
      tipState.value.farmerListNo = res?.data.farmerlistNo;
      tipState.value.farmersCount = res?.data.farmersCount;
      tipState.value.errorCount = res?.data.errorCount;
      tipState.value.riskCount = res?.data.riskCount;
      farmerInfo.value = res?.data;
      tipShow.value = true;
    } else if (res && !res.data) {
      hasData.value = true;
    } else {
      tipShow.value = false;
    }
  } catch (e) {
    console.log(e);
  }
};
onMounted(() => {
  search();
});

onActivated(() => {
  getEffectStatus();
  refresh();
  fetchTip();
});
</script>
