<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="创建日期" name="createDate" :label-col="{ style: { width: pxToRem(110) } }">
                <a-range-picker v-model:value="searchFormState.createDate" :disabled="disabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保单号" name="policyNo">
                <a-input v-model:value.trim="searchFormState.policyNo" placeholder="请输入" allow-clear @blur="() => blurInput(searchFormState.policyNo)" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="8">
              <a-form-item label="被保险人" name="insuredName">
                <a-input v-model:value="searchFormState.insuredName" :disabled="disabled" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="16">
              <a-form-item label="标的类型" name="lastRiskType">
                <RiskCodeSelect v-model:value="searchFormState.lastRiskType" :disabled="disabled" :department-code="searchFormState.departmentCode" :show-search="false" />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-row v-show="expand" :gutter="24">
            <a-col :span="24">
              <a-form-item label="验标状态" name="riskCheckStatus" class="mr-[10px]">
                <check-box-group v-model:checked-list="searchFormState.riskCheckStatus" :options="verifyTargetOptions" />
              </a-form-item>
            </a-col>
          </a-row> -->
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="批改状态" name="edrStatusCodeList" class="mr-[10px]">
                <check-box-group v-model:checked-list="searchFormState.edrStatusCodeList" :disabled="disabled" :options="approveModificationsOptions" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8" style="padding-right: 0px">
              <a-form-item label="批改申请单号/批单号" name="endorseNoOrApplyNo" :label-col="{ style: { width: 'none' } }">
                <a-input v-model:value.trim="searchFormState.endorseNoOrApplyNo" placeholder="请输入" allow-clear @blur="() => blurInput(searchFormState.endorseNoOrApplyNo)" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-10px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'insuranceBeginDate'">{{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }}</template>
          <!-- 批改单号/批改申请单号 -->
          <template v-if="column.dataIndex === 'endorseApplyNo'">
            <CopyLink :text="record?.endorseNo || record?.endorseApplyNo" @click="openEndorseDetail(record?.endorseNo || record?.endorseApplyNo, record.endorseApplyNo)" />
          </template>
          <template v-if="['marketProductName', 'insuredName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <!-- 操作区域 -->
            <a-space :size="1">
              <!-- 李伟刚、邱楚丹提出已生成批单、失效不可修改 -->
              <AuthButton code="endorseTrackEdit" type="link" :disabled="!['01', '03', '06'].includes(record.endorseStatus)" @click="handlerEdit(record)">修改</AuthButton>
              <AuthButton code="endorseTrackDel" type="link" @click="handleDelete(record.endorseApplyNo)">删除</AuthButton>
              <AuthButton code="endorsePrint" type="link" :disabled="record.endorseStatus !== '08'" @click="handlePrint(record)">打印</AuthButton>
              <AuthButton code="endorseRevoke" type="link" :disabled="!record.revocable" @click="recall(record.endorseApplyNo)">撤回</AuthButton>
            </a-space>
          </template>
        </template>
      </a-table>
      <PrintModal v-model:visible="printVisible" type="02" :is-endorse="true" :select-obj="selectObj as unknown as insuranceTrackingType" />
      <!-- 确认删除弹窗 -->
      <a-modal v-model:open="open" :width="pxToRem(450)" :centered="true" @ok="handleOk">
        <div>
          <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
          <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
        </div>
        <div class="mt-[10px]">{{ confirmText }}</div>
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { SearchFormState, DataType, Key } from './endorseTracking.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $getOnClient, $postOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { pxToRem } from '@/utils/tools';
import FormFold from '@/components/ui/FormFold.vue';
import PrintModal from '@/pages/insure/insuranceTracking/components/PrintModal.vue';
import type { insuranceTrackingType } from '@/pages/insure/insuranceTracking/insuranceTracking.d';
import CopyLink from '@/components/ui/CopyLink.vue';
import AuthButton from '@/components/ui/AuthButton.vue';

const confirmText = ref<string>('');
const router = useRouter();
// 接口相关配置
const { gateWay, service } = useRuntimeConfig().public || {};
const getListReq = await usePost<DataType>(`${gateWay}${service.endorse}/web/endorse/list`); // 查询列表请求
const deleteEndorseApplyNumber = await usePost<DataType>(`${gateWay}${service.endorse}/web/endorse/delEndorseApplyNo`); // 删除数据
// 定义表格列
const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'insureDepartmentName' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '批改申请单号/批改单号', dataIndex: 'endorseApplyNo' },
  { title: '产品名称', dataIndex: 'marketProductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '批改场景', dataIndex: 'endorseSceneName' },
  { title: '批改状态', dataIndex: 'endorseStatusName' },
  { title: '申请日期', dataIndex: 'inputTime' },
  { title: '申请人', dataIndex: 'createdBy' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 验标状态选项
// const verifyTargetOptions = ref <{ label: string; value: string }[]> ([
//   { label: '验标中', value: '1' },
//   { label: '验标完成', value: '2' },
//   { label: '未验标', value: '3' },
// ]);
// 搜索项是否展开
const expand = ref(false);
// 批改状态选项
const approveModificationsOptions = ref<{ label: string; value: string }[]>([
  { label: '待报价', value: '01' },
  { label: '待审批', value: '02' },
  { label: '待申请', value: '03' },
  { label: '待审核', value: '05' },
  { label: '待修改', value: '06' },
  { label: '待缴费', value: '07' },
  { label: '待生成批单', value: '18' },
  { label: '已生成批单', value: '08' },
  { label: '失效', value: '09' },
  { label: '承保失败', value: '19' },
]);
const { userInfo } = useUserStore(); // 默认用户信息
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : '')); // 默认机构编码
// 查询表单和状态
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containLowDepartment: true, // 是否包含下级
  lastRiskType: '', // 标的类型
  createDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 创建日期
  // riskCheckStatus: [], // 验标状态
  insuredName: '', // 被保险人名称
  policyNo: '', // 保单号
  edrStatusCodeList: ['01', '03', '06'], // 批改状态
  endorseNoOrApplyNo: '', // 申请单号
});

// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
  searchForm.value.validate(['departmentCode']);
};
const dataSource = ref<Record<string, string>[]>([]); // 表格数据
const loading = ref<boolean>(false); // 表格loading
const dataId = ref<Key[]>([]);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    dataId.value = []; // 初始化单选按钮值
    refresh();
  } catch (e) {
    console.log(e);
  }
};
// 跳转批改信息详情页面 ENDORSE_APPLY_NO/ENDORSE_NO
const openEndorseDetail = (voucherNo: string, endorseApplyNo: string) => {
  router.push({
    path: '/approvalTraceCheck',
    query: {
      documentNo: voucherNo,
      documentType: voucherNo.slice(0, 1) === '3' ? 'ENDORSE_NO' : 'ENDORSE_APPLY_NO',
      t: new Date().getTime(),
      endorseApplyNo,
    },
  });
};
// 列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    // 接口所需要参数
    const params = {
      ...searchFormState,
      inputStartDate: searchFormState.createDate?.[0] || '', // 创建时间开始时间
      inputEndDate: searchFormState.createDate?.[1] || '', // 创建时间开结束时间
      pageNum: pagination.current, // 页码
      pageSize: pagination.pageSize, // 每页数
    };
    dataSource.value = [];
    const res = await getListReq.fetchData({ endorseTraceListReq: params });
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 分页处理
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value.resetFields();
  disabled.value = false;
  refresh();
};
const confirmType = ref<string>(''); // 1 删除 2 撤回
// 二次确认弹窗确定
const handleOk = () => {
  if (confirmType.value === '1') {
    // 删除
    handleDeleteOk();
  } else {
    // 撤回
    handleRecallOk();
  }
};
// 删除
const open = ref<boolean>(false);
const endorseApplyNo = ref<string>('');
const handleDelete = (id: string) => {
  confirmText.value = '是否删除该批单申请？';
  confirmType.value = '1';
  open.value = true;
  endorseApplyNo.value = id;
};

// 删除功能
const handleDeleteOk = async () => {
  try {
    const deleteResult = await deleteEndorseApplyNumber.fetchData({ applicationNo: endorseApplyNo.value });
    if (deleteResult && deleteResult.code === '000000') {
      message.success('删除成功');
      open.value = false;
      refresh();
    } else {
      message.error(deleteResult?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 判断是否可进入批改
const allowModifyReq = await usePost<{ isEndorsable: boolean; reason: string }>(`${gateWay}${service.endorse}/web/endorse/enter-into-endorse`);
// 修改功能
const handlerEdit = async (record: Record<string, string>) => {
  const scenes = {
    '90001': { scene: 'WHOLE_MODIFY', route: 'endorseModify' },
    '00006': { scene: 'CANCELLATION_MODIFY', route: 'surrenderModify' },
    '00017': { scene: 'WRITE_OFF_MODIFY', route: 'cancelModify' },
    '90003': { scene: 'PREMIUM_FROM_MODIFY', route: 'premiumSource' },
  };

  const currentScene = scenes[record.endorseScene as keyof typeof scenes];

  if (currentScene) {
    try {
      // 接口所需要参数
      const params = {
        policyNo: record.policyNo, // 保单号
        enterIntoEndorseScene: currentScene.scene, // 场景
      };

      const res = await allowModifyReq.fetchData(params);

      if (res && res.code === SUCCESS_CODE) {
        if (res.data?.isEndorsable) {
          router.push({
            name: currentScene.route,
            query: {
              endorseApplyNo: record.policyNo,
              endorseScene: record.endorseScene,
              qryScene: currentScene.scene,
              departmentCode: record.insureDepartmentNo, // 机构
              productCode: record.marketProductNo, // 产品编码
            },
          });
        } else {
          // 不进入批改
          message.warning(res?.data?.reason);
        }
      } else {
        message.warning(res?.msg || '');
      }
    } catch (error) {
      console.log(error);
    }
  }
};
const disabled = ref<boolean>(false);
// 单号输入后调接口获取单号类型和机构,禁用其他组件
const blurInput = async (val) => {
  if (val) {
    disabled.value = true;
    searchFormState.createDate = ['', ''];
    searchFormState.insuredName = '';
    searchFormState.lastRiskType = '';
    searchFormState.edrStatusCodeList = ['01', '02', '03', '05', '06', '07', '08', '09', '18', '19'];
    const fetchUrl = gateWay + service.administrate + '/public/getBizTypeByBizNo';
    const params = {
      bizNo: val,
    };
    const res = await $getOnClient<Record<string, string>>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      searchFormState.departmentCode = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    searchFormState.departmentCode = defaultDeptCode.value;
    searchFormState.createDate = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    searchFormState.edrStatusCodeList = ['01', '03', '06'];
  }
};
// 打印信息弹窗
const printVisible = ref<boolean>(false);
// 点击打印获取需要传给后端的数据
const selectObj = ref<Record<string, string>>({});
// 点击打印按钮
const handlePrint = (record: Record<string, string>) => {
  printVisible.value = true;
  selectObj.value = record;
};
// 撤回
const recallEndorseApplyNo = ref<string>('');
const recall = (id: string) => {
  confirmText.value = '是否撤回该批单申请？';
  confirmType.value = '2';
  open.value = true;
  recallEndorseApplyNo.value = id;
};
// 撤回接口
const handleRecallOk = async () => {
  const res = await $postOnClient(gateWay + service.endorse + '/web/endorse/revoke', { endorseApplyNo: recallEndorseApplyNo.value });
  if (res && res?.code === SUCCESS_CODE) {
    message.success(res.msg);
    open.value = false;
    refresh();
  } else {
    message.error(res?.msg || '失败');
  }
};
const route = useRoute();
onMounted(() => {
  // 批单待申请核保 跳转 批改跟踪
  if (route.query.fromHome === '1') {
    searchFormState.edrStatusCodeList = ['01', '03', '06'];
    if (route.query.startTime && route.query.endTime) {
      searchFormState.createDate = [String(route.query.startTime), String(route.query.endTime)];
    }
  }
  search();
});

onActivated(() => {
  refresh();
});
watch(
  () => route.query,
  (val) => {
    if (val) {
      // 首页投保单待申请核保 跳转 投保跟踪
      if (val.fromHome === '1') {
        searchFormState.edrStatusCodeList = ['01', '03', '06'];
        if (route.query.startTime && route.query.endTime) {
          searchFormState.createDate = [String(route.query.startTime), String(route.query.endTime)];
        }
        search();
      }
      // 任务中心跳转过来带入投保单号
      if (val.bizNo) {
        searchFormState.policyNo = val.bizNo as string;
        blurInput(searchFormState.policyNo);
        search();
      }
    }
  },
  { deep: true, immediate: true },
);
</script>
