<template>
  <div ref="scrollWrapper" class="page-container">
    <a-spin :spinning="loading">
      <main class="main-wrapper">
        <div class="flex-1">
          <div class="h-[48px] w-ful bg-image-url">
            <span class="text-[16px] font-semibold text-[#00190c] leading-[48px] ml-[16px]">批单明细</span>
          </div>
          <div class="flex items-center text-[14px] h-[46px] space-x-[24px] px-[24px] bg-white rounded-b-[4px]">
            <div><span class="text-[rgba(0,0,0,.6)]">批改申请单号：</span>{{ contractModel?.edrApplySceneInfoValue?.endorseApplyNo }}</div>
            <div><span class="text-[rgba(0,0,0,.6)]">保单号：</span>{{ contractModel?.edrApplySceneInfoValue?.policyNo }}</div>
            <div><span class="text-[rgba(0,0,0,.6)]">录单人：</span>{{ contractModel?.edrApplyBaseInfoValue?.inputBy }}</div>
            <div><span class="text-[rgba(0,0,0,.6)]">批改类型：</span>{{ contractModel?.edrApplySceneInfoValue?.sceneName }}</div>
          </div>
          <div class="relative mt-[14px] form-content">
            <InfoGroupBox id="policy-info" title="批改内容">
              <ModifyInfo ref="modifyContentRef" v-model:data="contractModel.edrApplyBaseInfoValue" v-model:reason="contractModel.edrApplySceneInfoValue" :readonly="true" />
            </InfoGroupBox>
            <template v-if="!insuranceSourceShow">
              <InfoGroupBox v-if="isShow" id="surrender-info" title="退保信息">
                <div>退保金额：{{ positiveActualPremiumChange }}元</div>
              </InfoGroupBox>
            </template>
            <div v-if="!isShow">
              <template v-if="!insuranceSourceShow">
                <InfoGroupBox id="basic-info" title="基础信息">
                  <a-descriptions>
                    <a-descriptions-item label="农保ID">
                      <span v-for="(item, index) in contractModel?.baseInfo?.assisterInfoList" :key="item.assisterId" class="font-number">
                        {{ item.assisterId }}
                        <span v-if="index !== contractModel.baseInfo.assisterInfoList.length - 1">、</span>
                      </span>
                    </a-descriptions-item>
                  </a-descriptions>
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="保险起期">{{ contractModel?.baseInfo?.insuranceBeginDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="保险期限">{{ contractModel?.baseInfo?.insuranceBeginEndDateType || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="保险止期">{{ contractModel?.baseInfo?.insuranceEndDate }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="起止时间">{{ contractModel?.baseInfo?.timeRange === '0' ? '0-24时' : '12-12时' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="年限系数">{{ contractModel?.baseInfo?.shortTimeCoefficient || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="争议处理方式">{{ contractModel?.baseInfo?.disputedSettleModeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.baseInfo?.disputedSettleMode === '2'" class="font-number" label="仲裁机构">{{ contractModel?.baseInfo?.arbitralDepartment || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="投保方式">{{ contractModel?.baseInfo?.applyPolicyTypeChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                </InfoGroupBox>
                <InfoGroupBox id="list-info" title="清单信息">
                  <a-table :columns="listColumns" :data-source="farmerDataList" :pagination="false">
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'insureArea'">
                        {{ record.riskAddressNameCity + record.riskAddressNameCounty + record.riskAddressNameProvince + record.riskAddressNameTown + record.riskAddressNameVillage }}
                      </template>
                      <template v-if="column.dataIndex === 'operation'">
                        <span class="text-[14px] text-[#576B95] font-normal cursor-pointer" @click="view(record.endorseApplyNo, record.policyNo, record.farmerlistNo, record.farmersCount)">查看</span>
                      </template>
                    </template>
                  </a-table>
                  <a-button type="primary" :loading="listLoading" class="mt-[8px]" @click="farmerListExport">导出清单数据</a-button>
                </InfoGroupBox>
                <InfoGroupBox id="customer-info" title="客户信息">
                  <!-- 投保组织者 -->
                  <div v-if="contractModel?.baseInfo?.applyPolicyType === '2'" class="form-title">
                    <span>投保组织者</span>
                  </div>
                  <a-descriptions v-if="contractModel?.baseInfo?.applyPolicyType === '2'">
                    <a-descriptions-item class="font-number" label="名称">{{ contractModel?.organizerInfo?.organizerName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件类型">{{ contractModel?.organizerInfo?.certificateTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件号码">{{ contractModel?.organizerInfo?.certificateNo || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效起期">{{ contractModel?.organizerInfo?.certificateIssueDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效止期">{{ contractModel?.organizerInfo?.certificateValidDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="地址">{{ contractModel?.organizerInfo?.address || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="邮政编码">{{ contractModel?.organizerInfo?.postalCode || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="联系人">{{ contractModel?.organizerInfo?.contactPerson || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="联系电话">{{ contractModel?.organizerInfo?.contactTelephone || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="电子邮箱">{{ contractModel?.organizerInfo?.email || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="经营范围">{{ contractModel?.organizerInfo?.businessScope || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="行业">{{ contractModel?.organizerInfo?.completeIndustryChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="组织机构类型">{{ contractModel?.organizerInfo?.organizationTypeChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <div v-if="contractModel?.baseInfo?.applyPolicyType === '2'" class="h-px bg-[#E6E8EB] mb-[18px]" />
                  <div class="form-title">
                    <span>被保险人信息</span>
                  </div>
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="客户属性">{{ contractModel?.insurantInfoList[0]?.personnelTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="名称">{{ contractModel?.insurantInfoList[0]?.name || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件类型">{{ contractModel?.insurantInfoList[0]?.certificateTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件号码">{{ contractModel?.insurantInfoList[0]?.certificateNo || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效起期">{{ contractModel?.insurantInfoList[0]?.certificateIssueDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效止期">{{ contractModel?.insurantInfoList[0]?.certificateValidDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="地址">{{ contractModel?.insurantInfoList[0]?.address || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="邮政编码">{{ contractModel?.insurantInfoList[0]?.postcode || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="联系电话">{{ contractModel?.insurantInfoList[0]?.mobileTelephone || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="电子邮箱">{{ contractModel?.insurantInfoList[0]?.email || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="农业主体类型">{{ contractModel?.insurantInfoList[0]?.subjectNumberName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="贫困户标识">{{ contractModel?.insurantInfoList[0]?.poorSymbolChName || '-' }}</a-descriptions-item>
                    <template v-if="contractModel?.insurantInfoList[0]?.poorSymbol === '1' && contractModel?.baseInfo?.secDeptCode === '219'">
                      <a-descriptions-item class="font-number" label="贫困户细分">{{ contractModel?.insurantInfoList[0]?.poorDetailTypeCnName || '-' }}</a-descriptions-item>
                    </template>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'" class="font-number" label="性别">{{ contractModel?.insurantInfoList[0]?.sexCodeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'" class="font-number" label="国籍">{{ contractModel?.insurantInfoList[0]?.nationalityZh || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'" class="font-number" label="职业">{{ contractModel?.insurantInfoList[0]?.professionName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'" class="font-number" label="年收入(元)">{{ contractModel?.insurantInfoList[0]?.superviseInfoList?.[0].yearlySalaries || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '1'" class="font-number" label="工作单位名称">{{ contractModel?.insurantInfoList[0]?.superviseInfoList?.[0].companyName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="企业归属">{{ contractModel?.insurantInfoList[0]?.belongOrganizationNoChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="行业">{{ contractModel?.insurantInfoList[0]?.completeIndustryChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="经营范围">{{ contractModel?.insurantInfoList[0]?.superviseInfoList?.[0]?.businessScope || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="国有属性">{{ contractModel?.insurantInfoList[0]?.superviseInfoList?.[0]?.enterpriseTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="注册资本金(元)">{{ contractModel?.insurantInfoList[0]?.superviseInfoList?.[0]?.registeredFund || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'" class="font-number" label="投保人与被保人关系">{{ contractModel?.insurantInfoList[0]?.relationshipWithApplicantChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <!-- 被保人信息团体需要展示以下模块 -->
                  <div v-if="contractModel?.insurantInfoList[0]?.personnelType === '0'">
                    <template v-for="(item, index) in contractModel?.insurantInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
                      <!-- 授权办理业务人员 -->
                      <div v-if="item.companyRelationType === '3'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>授权办理业务人员</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 法定代办人/负责人 -->
                      <div v-if="item.companyRelationType === '2'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>法定代办人/负责人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 控股股东/实际控制人 -->
                      <div v-if="item.companyRelationType === '1'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>控股股东/实际控制人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 受益所有人 -->
                      <div v-if="item.companyRelationType === '4'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>受益所有人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="判定受益所有人方式">{{ item.benefitModeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="受益所有人持股数量或表决权占比（%）">{{ item.equityRatio || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                    </template>
                  </div>
                  <!-- 投保人信息 -->
                  <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                  <div class="form-title">
                    <span>投保人信息</span>
                  </div>
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="客户属性">{{ contractModel?.applicantInfoList[0]?.personnelTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="名称">{{ contractModel?.applicantInfoList[0]?.name || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件类型">{{ contractModel?.applicantInfoList[0]?.certificateTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件号码">{{ contractModel?.applicantInfoList[0]?.certificateNo || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效起期">{{ contractModel?.applicantInfoList[0]?.certificateIssueDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效止期">{{ contractModel?.applicantInfoList[0]?.certificateValidDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="地址">{{ contractModel?.applicantInfoList[0]?.address || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="邮政编码">{{ contractModel?.applicantInfoList[0]?.postcode || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="联系电话">{{ contractModel?.applicantInfoList[0]?.mobileTelephone || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="电子邮箱">{{ contractModel?.applicantInfoList[0]?.email || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'" class="font-number" label="性别">{{ contractModel?.applicantInfoList[0]?.sexCodeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'" class="font-number" label="国籍">{{ contractModel?.applicantInfoList[0]?.nationalityZh || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'" class="font-number" label="职业">{{ contractModel?.applicantInfoList[0]?.professionName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'" class="font-number" label="年收入(元)">{{ contractModel?.applicantInfoList[0]?.superviseInfoList?.[0].yearlySalaries || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '1'" class="font-number" label="工作单位名称">{{ contractModel?.applicantInfoList[0]?.superviseInfoList?.[0].companyName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'" class="font-number" label="企业归属">{{ contractModel?.applicantInfoList[0]?.belongOrganizationNoChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'" class="font-number" label="行业">{{ contractModel?.applicantInfoList[0]?.completeIndustryChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'" class="font-number" label="经营范围">{{ contractModel?.applicantInfoList[0]?.superviseInfoList?.[0]?.businessScope || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'" class="font-number" label="国有属性">{{ contractModel?.applicantInfoList[0]?.superviseInfoList?.[0]?.enterpriseTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'" class="font-number" label="注册资本金(元)">{{ contractModel?.applicantInfoList[0]?.superviseInfoList?.[0]?.registeredFund || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <!-- 投保人信息团体需要展示以下模块 -->
                  <div v-if="contractModel?.applicantInfoList[0]?.personnelType === '0'">
                    <template v-for="(item, index) in contractModel?.applicantInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
                      <!-- 授权办理业务人员 -->
                      <div v-if="item.companyRelationType === '3'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>授权办理业务人员</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 法定代办人/负责人 -->
                      <div v-if="item.companyRelationType === '2'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>法定代办人/负责人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 控股股东/实际控制人 -->
                      <div v-if="item.companyRelationType === '1'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>控股股东/实际控制人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 受益所有人 -->
                      <div v-if="item.companyRelationType === '4'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>受益所有人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="判定受益所有人方式">{{ item.benefitModeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="受益所有人持股数量或表决权占比（%）">{{ item.equityRatio || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                    </template>
                  </div>
                  <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                  <div class="form-title">
                    <span>受益人信息</span>
                  </div>
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="客户属性">{{ contractModel?.beneficaryInfoList[0]?.personnelTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="名称">{{ contractModel?.beneficaryInfoList[0]?.name || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件类型">{{ contractModel?.beneficaryInfoList[0]?.certificateTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件号码">{{ contractModel?.beneficaryInfoList[0]?.certificateNo || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效起期">{{ contractModel?.beneficaryInfoList[0]?.certificateIssueDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="证件有效止期">{{ contractModel?.beneficaryInfoList[0]?.certificateValidDate || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="地址">{{ contractModel?.beneficaryInfoList[0]?.address || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="邮政编码">{{ contractModel?.beneficaryInfoList[0]?.postcode || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="联系电话">{{ contractModel?.beneficaryInfoList[0]?.mobileTelephone || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="电子邮箱">{{ contractModel?.beneficaryInfoList[0]?.email || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '1'" class="font-number" label="性别">{{ contractModel?.beneficaryInfoList[0]?.sexCodeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '1'" class="font-number" label="国籍">{{ contractModel?.beneficaryInfoList[0]?.nationalityZh || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '1'" class="font-number" label="职业">{{ contractModel?.beneficaryInfoList[0]?.professionName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0'" class="font-number" label="企业归属">{{ contractModel?.beneficaryInfoList[0]?.belongOrganizationNoChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0'" class="font-number" label="行业">{{ contractModel?.beneficaryInfoList[0]?.completeIndustryChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0'" class="font-number" label="经营范围">{{ contractModel?.beneficaryInfoList[0]?.superviseInfoList?.[0]?.businessScope || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="受益人与被保人关系">{{ contractModel?.beneficaryInfoList[0]?.relationshipWithInsuredChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <!-- 受益人信息团体需要展示以下模块 -->
                  <div v-if="contractModel?.beneficaryInfoList[0]?.personnelType === '0'">
                    <template v-for="(item, index) in contractModel?.beneficaryInfoList?.[0]?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
                      <!-- 授权办理业务人员 -->
                      <div v-if="item.companyRelationType === '3'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>授权办理业务人员</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 法定代办人/负责人 -->
                      <div v-if="item.companyRelationType === '2'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>法定代办人/负责人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                      <!-- 控股股东/实际控制人 -->
                      <div v-if="item.companyRelationType === '1'">
                        <div class="h-px bg-[#E6E8EB] mb-[18px]" />
                        <div class="form-title">
                          <span>控股股东/实际控制人/受益所有人</span>
                        </div>
                        <a-descriptions>
                          <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                    </template>
                  </div>
                </InfoGroupBox>
                <InfoGroupBox v-if="contractModel?.extendInfo !== null" id="project-info" title="项目信息">
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="项目来源">{{ contractModel?.extendInfo?.isTenderBusinessChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="项目名称">{{ contractModel?.extendInfo?.tenderBusinessName || '-' }}</a-descriptions-item>
                    <a-descriptions-item />
                    <a-descriptions-item class="font-number" label="客户类型">{{ contractModel?.extendInfo?.customerTypeChName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="客户简称">{{ contractModel?.extendInfo?.customerAbbrName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="风险程度（%）">{{ contractModel?.extendInfo?.lossRate || '-' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.extendGroupInfo?.productFactoryPlanType === '05'" class="font-number" label="期货公司名称">{{ contractModel?.extendInfo?.futureCompanyName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                </InfoGroupBox>
                <InfoGroupBox v-if="contractModel?.baseInfo !== null" id="renewal-info" title="续保信息">
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="是否属于续保">{{ contractModel?.baseInfo?.renewalTypeName || '-' }}</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="被续保保单号">{{ contractModel?.baseInfo?.lastPolicyNo || '-' }}</a-descriptions-item>
                  </a-descriptions>
                </InfoGroupBox>
                <InfoGroupBox id="target-info" title="标的信息">
                  <template v-for="(riskItem, index) in contractModel.riskGroupInfoList" :key="index">
                    <div class="text-[#404442] text-[14px] font-semibold flex items-center mb-[16px]">
                      <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
                      {{ `标的${index + 1}` }}
                    </div>
                    <a-descriptions>
                      <a-descriptions-item class="font-number" label="标的明细">{{ riskItem?.combinedProductName || '-' }}</a-descriptions-item>
                      <a-descriptions-item class="font-number" label="保险数量">{{ riskItem?.riskAgrInfo?.insuredNumber || '-' }} {{ riskItem.riskAgrInfo?.insuredUnitChName }}</a-descriptions-item>
                      <a-descriptions-item class="font-number" label="保险金额">{{ riskItem?.riskAgrInfo?.insuredAmount || '-' }}元</a-descriptions-item>
                      <a-descriptions-item class="font-number" label="保费金额">{{ riskItem?.riskAgrInfo?.premium || '-' }}元</a-descriptions-item>
                      <a-descriptions-item class="font-number" label="参保农户数">{{ riskItem?.riskAgrInfo?.farmersCount || '-' }}</a-descriptions-item>
                      <a-descriptions-item class="font-number" label="标的地址">{{ contractModel?.riskAddressInfoList?.[0]?.address || '-' }}</a-descriptions-item>
                      <template v-for="(obj, idx) in riskItem?.riskAgrInfo?.agrRiskAttribute" :key="obj.businessType">
                        <a-descriptions-item v-if="obj.bussinessKey === 'unitInsureCount'" class="font-number" label="单位保险产量及单位">{{ obj.bussinessValue || '-' }} {{ riskItem?.riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValueChName }}</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'priceAgreed'" class="font-number" label="约定价格（指数）">{{ obj.bussinessValue || '-' }} {{ riskItem?.riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValueChName }}</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'breedAmount'" class="font-number" :label="obj.bussinessType">{{ obj.bussinessValue || '-' }} {{ riskItem?.riskAgrInfo.agrRiskAttribute[idx + 1].bussinessValue }}</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'pigstyArea'" class="font-number" :label="obj.bussinessType">{{ obj.bussinessValue || '-' }} 平方米</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'carbonSinkTargetValue'" class="font-number" :label="obj.bussinessType">{{ obj.bussinessValue || '-' }} 吨</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'carbonSinkPrice'" class="font-number" :label="obj.bussinessType">{{ obj.bussinessValue || '-' }} 元/吨</a-descriptions-item>
                        <a-descriptions-item v-else-if="obj.bussinessKey === 'carbonSinkTargetRate'" class="font-number" :label="obj.bussinessType">{{ obj.bussinessValue || '-' }} %</a-descriptions-item>
                        <!-- 单位不需要展示 -->
                        <template v-else-if="obj.bussinessKey === 'breedUnit'" />
                        <template v-else-if="obj.bussinessKey === 'unitInsureCountUnit'" />
                        <template v-else-if="obj.bussinessKey === 'priceAgreedUnit'" />
                        <a-descriptions-item v-else class="font-number" :label="obj.bussinessType">{{ obj?.bussinessValueChName || obj.bussinessValue || '-' }}</a-descriptions-item>
                      </template>
                    </a-descriptions>
                    <div class="divide-line" />
                  </template>
                  <a-descriptions-item>
                    <a-button type="primary" :loading="btnLoading" :disabled="contractModel?.baseInfo?.farmerlistNo" @click="singleFarmerListExport">导出清单</a-button>
                  </a-descriptions-item>
                </InfoGroupBox>
                <InfoGroupBox id="insurance-plan" title="保险方案">
                  <template v-for="(riskGroupInfo, riskIndex) in contractModel.riskGroupInfoList" :key="riskIndex">
                    <div class="form-title">
                      <span>{{ `方案${contractModel.riskGroupInfoList?.length > 1 ? riskIndex + 1 : ''}` }}</span>
                    </div>
                    <div class="mb-[16px]">
                      <a-table :columns="insurancePlanColumns" :data-source="riskGroupInfo?.planInfoList" :pagination="false">
                        <template #bodyCell="{ column, index, text }">
                          <template v-if="column.dataIndex === 'number'">
                            {{ index + 1 }}
                          </template>
                          <template v-if="column.dataIndex === 'combinedProductName'">
                            {{ riskGroupInfo?.combinedProductName }}
                          </template>
                          <template v-if="column.dataIndex === 'isMain'">
                            {{ text === '1' ? '主险' : '附加险' }}
                          </template>
                        </template>
                      </a-table>
                    </div>
                  </template>
                  <div class="py-[12px]">保费来源</div>
                  <a-table class="mb-[12px]" :columns="sourceColumns" :data-source="sourceList" :pagination="false" />
                  <a-descriptions>
                    <a-descriptions-item label="农户自缴减免系数">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.reductionCoefficient || '-' }}</a-descriptions-item>
                    <a-descriptions-item label="农户保费是否代缴">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y' ? '是' : '否' }}</a-descriptions-item>
                    <a-descriptions-item v-if="contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y'" label="代缴保费资金来源">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo.fundingSourceChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <div class="mt-16px leading-[22px] text-[rgba(0, 0, 0, 0.9)]">
                    <div>总计：保单保险金额={{ contractModel?.sumRiskGroupAmount?.totalInsuredAmount || 0 }} ｜ 减免前保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalStandardPremium || 0 }}｜ 减免后保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalAgreePremium || 0 }} ｜ 保单复核保费={{ contractModel?.sumRiskGroupAmount?.totalActualPremium || 0 }}</div>
                    <div v-if="contractModel.riskGroupInfoList.length === 1">保险数量：{{ contractModel.baseInfo?.insuredNumber || 0 }}{{ contractModel.riskGroupInfoList?.[0]?.riskAgrInfo?.insuredUnitChName }} ｜ 参保户次：{{ contractModel.baseInfo?.farmersCount || 0 }}户</div>
                    <div v-if="contractModel.riskGroupInfoList.length > 1">
                      <template v-for="(item, index) in contractModel.riskGroupInfoList" :key="index">
                        <div>标的名称：{{ item.combinedProductName }} ｜ 保险数量：{{ item.riskAgrInfo.insuredNumber || 0 }}{{ item.riskAgrInfo.insuredUnitChName }} ｜ 参保户次：{{ item.riskAgrInfo.farmersCount || 0 }}户</div>
                      </template>
                    </div>
                  </div>
                </InfoGroupBox>
              </template>
              <InfoGroupBox id="premium-source" title="保费来源">
                <InsuranceSource ref="premiumSourceRef" v-model:change-data="contractModel.premiumSourceChange" v-model:data="contractModel.riskGroupInfoList" :product-code="contractModel.baseInfo?.productCode" :product-version="contractModel.baseInfo?.productVersion" :gov-subsidy-type="contractModel.baseInfo.govSubsidyType" disabled />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel?.payInfoList?.length > 0 || contractModel?.payInfoList !== null" id="fee-plan" title="收费计划">
                <a-table :columns="feePlanColumns" :data-source="contractModel.payInfoList" :pagination="false">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'paymentPeriod'">
                      {{ record.paymentBeginDate + ' 至 ' + record.paymentEndDate }}
                    </template>
                    <template v-if="column.dataIndex === 'certificateNo'">
                      <span class="break-all">{{ record.certificateNo }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'bankAccountNo'">
                      <span class="break-all">{{ record.bankAccountNo }}</span>
                    </template>
                  </template>
                </a-table>
              </InfoGroupBox>
              <template v-if="!insuranceSourceShow">
                <InfoGroupBox v-if="contractModel.noclaimInfoList?.length > 0" id="disclaimers-info" title="免赔信息">
                  <a-table :columns="noClaimColumns" :data-source="contractModel.noclaimInfoList" :pagination="false" />
                </InfoGroupBox>
                <InfoGroupBox id="special-appoint" title="特别约定">
                  <a-table v-if="contractModel.specialPromiseList.length > 0" :columns="specialColumns" :data-source="contractModel.specialPromiseList" :pagination="false" />
                  <a-table v-else :columns="specialColumns" :data-source="specialDataDefault" :pagination="false">
                    <template #bodyCell="{ column }">
                      <template v-if="column.dataIndex === 'promiseType'">通用特约</template>
                    </template>
                  </a-table>
                </InfoGroupBox>
                <InfoGroupBox id="fee-info" title="费用信息">
                  <a-descriptions>
                    <a-descriptions-item label="财务标识">{{ contractModel?.costInfo?.isPolicyBeforePayfeeChName || '-' }}</a-descriptions-item>
                  </a-descriptions>
                  <a-descriptions>
                    <a-descriptions-item class="font-number" label="农险补贴">{{ contractModel?.costInfo?.performanceValue1Default || '-' }}%</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="协办费">{{ contractModel?.costInfo?.assisterCharge || '-' }}%</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="手续费/经纪费">{{ contractModel?.costInfo?.commissionBrokerChargeProportion || '-' }}%</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="工作经费">{{ contractModel?.costInfo?.managementFees || '-' }}%</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="防灾防损费">{{ contractModel?.costInfo?.calamitySecurityRate || '-' }}%</a-descriptions-item>
                    <a-descriptions-item class="font-number" label="共保出单费">{{ contractModel?.costInfo?.coinsuranceInsureFeeRatio || '-' }}%</a-descriptions-item>
                  </a-descriptions>
                </InfoGroupBox>
                <!-- 再次共保 -->
                <InfoGroupBox id="again-coinsurance-info" title="再/共保信息">
                  <AgainCoinsurance :contract-model="contractModel" />
                </InfoGroupBox>
                <!-- 渠道信息 -->
                <InfoGroupBox id="channel-info" title="渠道信息">
                  <ChannelInfo :contract-model="contractModel" />
                </InfoGroupBox>
              </template>
            </div>
          </div>
        </div>
        <!-- 侧边锚点导航 -->
        <div v-if="showSceneList" class="right-sider">
          <div class="sticky top-[25px]">
            <span class="text-[#404442] font-semibold">大纲</span>
          </div>
          <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
        </div>
      </main>
    </a-spin>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" ghost @click="handleGoLink">附件管理</a-button>
      <!-- <a-button type="primary" ghost @click="goInspectionInfo">验标信息</a-button> -->
      <a-button
        type="primary"
        ghost
        @click="
          processOpen = true;
          queryUnderwriteAssistant();
        "
        >核保轨迹</a-button
      >
    </footer>
    <!-- 点击核保信息按钮 -->
    <InsureTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
  </div>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import ModifyInfo from '@/pages/endorse/endorseModify/components/ModifyInfo.vue';
import AgainCoinsurance from '@/pages/policy/policyDetails/components/AgainCoinsurance.vue';
import ChannelInfo from '@/pages/policy/policyDetails/components/ChannelInfo.vue';
import InsureTrack from '@/pages/review/insureProcess/components/InsureTrack.vue';
import InsuranceSource from '@/pages/endorse/premiumSource/components/InsuranceSource.vue';

// 点击核保信息按钮，打开弹窗
const processOpen = ref<boolean>(false);
const getContainer = () => scrollWrapper.value || window;
const scrollWrapper = ref();
const route = useRoute();
// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#policy-info', title: '批改内容' },
  { key: '14', href: '#surrender-info', title: '退保信息' },
  { key: '2', href: '#basic-info', title: '基础信息' },
  { key: '13', href: '#list-info', title: '清单信息' },
  { key: '3', href: '#customer-info', title: '客户信息' },
  { key: '4', href: '#project-info', title: '项目信息' },
  { key: '5', href: '#renewal-info', title: '续保信息' },
  { key: '6', href: '#target-info', title: '标的信息' },
  { key: '7', href: '#insurance-plan', title: '保险方案' },
  { key: '17', href: '#premium-source', title: '保费来源' },
  { key: '8', href: '#fee-plan', title: '收费计划' },
  { key: '9', href: '#disclaimers-info', title: '免赔信息' },
  { key: '10', href: '#special-appoint', title: '特别约定' },
  { key: '11', href: '#fee-info', title: '费用信息' },
  { key: '16', href: '#again-coinsurance-info', title: '再/共保信息' },
  { key: '15', href: '#channel-info', title: '渠道信息' },
]);

const showSceneList = computed(() => {
  // 整单批改、保费来源
  return ['90001', '90003'].includes(contractModel.value?.edrApplyBaseInfoValue?.sceneList);
});

// 免赔信息表头
const noClaimColumns: TableColumnsType = [
  { title: '免赔项目', dataIndex: 'noclaimItemZh', key: 'noclaimItemZh', width: pxToRem(400) },
  { title: '免赔类型', dataIndex: 'noclaimTypeZh', key: 'noclaimTypeZh', width: pxToRem(136) },
  { title: '免赔率(%)', dataIndex: 'noclaimRate', key: 'noclaimRate' },
  { title: '免赔额', dataIndex: 'noclaimAmount', key: 'noclaimAmount' },
];
// 收费计划表头
const feePlanColumns: TableColumnsType = [
  { title: '期次', dataIndex: 'termNo', key: 'termNo', width: '5%' },
  { title: '补贴类型', dataIndex: 'payerTypeName', key: 'payerTypeName', width: pxToRem(80) },
  { title: '付款人名称', dataIndex: 'paymentPersonName', key: 'paymentPersonName', width: pxToRem(110) },
  { title: '收付途径', dataIndex: 'paymentPathName', key: 'paymentPathName', width: pxToRem(100) },
  { title: '应收保费', dataIndex: 'actualPremium', key: 'actualPremium', width: pxToRem(100) },
  { title: '缴费起止期', dataIndex: 'paymentPeriod', key: 'paymentPeriod', width: pxToRem(200) },
  { title: '账户类型', dataIndex: 'bankAttributeName', key: 'bankAttributeName', width: pxToRem(100) },
  { title: '身份证', dataIndex: 'certificateNo', key: 'certificateNo', width: pxToRem(80) },
  { title: '银行总行', dataIndex: 'bankHeadquartersName', key: 'bankHeadquartersName', width: pxToRem(100) },
  { title: '银行分行', dataIndex: 'bankName', key: 'bankName', width: pxToRem(100) },
  { title: '开户行明细', dataIndex: 'bankDetail', key: 'bankDetail', width: pxToRem(125) },
  { title: '银行账号', dataIndex: 'bankAccountNo', key: 'bankAccountNo', width: pxToRem(100) },
  { title: '预计收回日期', dataIndex: 'receivableDate', key: 'receivableDate', width: pxToRem(100) },
  { title: '备注', dataIndex: 'otherPayerTypeDesc', key: 'otherPayerTypeDesc', width: pxToRem(80) },
];
// 保险方案表头
const insurancePlanColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'number', key: 'number', width: pxToRem(56) },
  { title: '标的名称', dataIndex: 'combinedProductName', key: 'combinedProductName', width: pxToRem(86) },
  { title: '主险/附加险标识', dataIndex: 'isMain', key: 'isMain', width: pxToRem(145) },
  { title: '险种码', dataIndex: 'planCode', key: 'planCode' },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(140) },
  { title: '每次赔偿限额', dataIndex: 'eachCompensationMaxAmount', key: 'eachCompensationMaxAmount', width: pxToRem(140) },
  { title: '单位保险金额', dataIndex: 'unitInsuredAmount', key: 'unitInsuredAmount', width: pxToRem(140) },
  { title: '保险金额', dataIndex: 'totalInsuredAmount', key: 'totalInsuredAmount', width: pxToRem(140) },
  { title: '费率(%)', dataIndex: 'expectPremiumRate', key: 'expectPremiumRate', width: pxToRem(140) },
  { title: '单位保费', dataIndex: 'unitPrimium', key: 'unitPrimium', width: pxToRem(140) },
  { title: '基准保费金额', dataIndex: 'totalStandardPremium', key: 'totalStandardPremium', width: pxToRem(140) },
  { title: '减免后保费金额', dataIndex: 'totalAgreePremium', key: 'totalAgreePremium', width: pxToRem(140) },
  { title: '复核保费', dataIndex: 'totalActualPremium', key: 'totalActualPremium', width: pxToRem(120) },
];
// 保费来源columns
const sourceColumns: TableColumnsType = [
  { title: '险种代码', dataIndex: 'planCode', key: 'planCode', width: pxToRem(120) },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(120) },
  { title: '中央补贴(%)', dataIndex: 'centralFinance', key: 'centralFinance' },
  { title: '省级补贴(%)', dataIndex: 'provincialFinance', key: 'provincialFinance' },
  { title: '地方补贴(%)', dataIndex: 'cityFinance', key: 'cityFinance' },
  { title: '县级补贴(%)', dataIndex: 'countyFinance', key: 'countyFinance' },
  { title: '农户自缴(%)', dataIndex: 'farmersFinance', key: 'farmersFinance' },
  { title: '其他补贴(%)', dataIndex: 'otherFinance', key: 'otherFinance' },
];
// 特约表头
const specialColumns: TableColumnsType = [
  { title: '特约类型', dataIndex: 'promiseTypeChName', width: pxToRem(100) },
  { title: '特约内容', dataIndex: 'promiseDesc' },
];
// 特约无数据时默认数据
const specialDataDefault = [
  {
    promiseCode: '', // 编码
    promiseDesc: '无其他特别约定', // 特约内容
    promiseType: '',
  },
];
// 清单信息表头
const listColumns: TableColumnsType = [
  { title: '批改清单编号', dataIndex: 'farmerlistNo', key: 'farmerlistNo' },
  { title: '清单名称', dataIndex: 'farmerlistName', key: 'farmerlistName' },
  { title: '承保数量', dataIndex: 'insuredNumber', key: 'insuredNumber', width: pxToRem(80) },
  { title: '承保户数', dataIndex: 'farmersCount', key: 'farmersCount', width: pxToRem(80) },
  { title: '导入条数', dataIndex: 'importCount', key: 'importCount', width: pxToRem(80) },
  { title: '投保区域', dataIndex: 'insureArea', key: 'insureArea' },
  { title: '操作', dataIndex: 'operation', key: 'operation', fixed: 'right', width: pxToRem(80) },
];
// 清单信息数据
const farmerDataList = computed(() => {
  if (contractModel.value?.insrSpltPlyEdrSumyVO) {
    return Object.keys(contractModel.value?.insrSpltPlyEdrSumyVO).length ? [contractModel.value?.insrSpltPlyEdrSumyVO] : [];
  } else {
    return [];
  }
});
// 查看
const view = (endorseApplyNo: string, policyNo: string, farmerListNo: string, farmersCount: string) => {
  router.push({
    name: 'listModify',
    query: {
      endorseApplyNo,
      policyNo,
      farmerListNo,
      farmersCount,
      onlyView: 'Y',
    },
  });
};
// 退保和注销显示页面一样，整单批改显示全部数据
const isShow = computed(() => ['00017', '00006'].includes(contractModel.value?.edrApplyBaseInfoValue?.sceneList));
// 保费来源只展示批文内容、保费来源、收费计划
const insuranceSourceShow = computed(() => ['90003'].includes(contractModel.value?.edrApplyBaseInfoValue?.sceneList));
// 保费来源表格datasource
const sourceList = computed(() => {
  if (contractModel.value.riskGroupInfoList?.length === 1) {
    // 单标的
    return contractModel.value.riskGroupInfoList[0].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    contractModel.value.riskGroupInfoList.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return result;
  }
});
const initContractModel = (data) => {
  return {
    ...{
      baseInfo: data?.contractValue?.baseInfo || {}, // 基础信息
      extendInfo: data?.contractValue?.extendInfo || {},
      organizerInfo: data?.contractValue?.organizerInfo || {}, // 组织者
      applicantInfoList: data?.contractValue?.applicantInfoList || [], // 投保人
      insurantInfoList: data?.contractValue?.insurantInfoList || [], // 被保人
      beneficaryInfoList: data?.contractValue?.beneficaryInfoList || [], // 收益人
      riskAddressInfoList: data?.contractValue?.riskAddressInfoList || [], // 标的
      riskGroupInfoList: data?.contractValue?.riskGroupInfoList || [], // 标的
      sumRiskGroupAmount: data?.contractValue?.sumRiskGroupAmount || {}, // 保费计划合计
      coinsuranceInfo: data?.contractValue?.coinsuranceInfo || {}, // 共保
      noclaimInfoList: data?.contractValue?.noclaimInfoList || [], // 免赔
      payInfoList: data?.edrApplyPayInfoListVOList || [], // 收费计划 -- 编辑页取data.edrApplyPayInfoListVOList，要与编辑页保持一致
      costInfo: data?.contractValue?.costInfo || {}, // 费用信息
      specialPromiseList: data?.contractValue?.specialPromiseList || [], // 特约
      saleInfo: data?.contractValue?.saleInfo || {},
      payerTypeSumOfPayInfo: data?.contractValue?.payerTypeSumOfPayInfo || {}, // 收费按类别汇总用于校验
      extendGroupInfo: data?.contractValue?.extendGroupInfo || {},
    },
    edrApplyAttachmentVO: data?.edrApplyAttachmentVO,
    edrApplyBaseInfoValue: data?.edrApplyBaseInfoValue,
    edrApplySceneContentVO: data?.edrApplySceneContentVO,
    edrApplySceneInfoValue: data?.edrApplySceneInfoValue,
    endorseApplyNo: data?.endorseApplyNo,
    insrSpltPlyEdrSumyVO: data?.insrSpltPlyEdrSumyVO,
    premiumSourceChange: data?.premiumSourceChange,
  };
};
// 退费金额
const positiveActualPremiumChange = computed(() => Math.abs(contractModel.value.edrApplyBaseInfoValue.actualPremiumChange || 0) || '');
const btnLoading = ref<boolean>(false);
// 导出清单数据
const singleFarmerListExport = async () => {
  try {
    btnLoading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/file/singleFarmerListExport`;
    await $getOnClient(
      fetchUrl,
      { position: '3', farmerListNo: contractModel.value?.baseInfo?.farmerlistNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('导出成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
// !!!类型晚点补
const contractModel = ref(initContractModel({}));

const loading = ref(false);
const getInit = async () => {
  loading.value = true;
  try {
    const res = await $postOnClient(`${gateWay}${service.endorse}/web/endorse/detail/readonly`, { documentNo: route.query.documentNo, documentType: route.query.documentType });
    if (res && res.code === SUCCESS_CODE) {
      contractModel.value = initContractModel(res.data || {});
      if (contractModel.value.payInfoList?.length > 0) {
        // payInfoList有数据，右侧锚点显示收费计划
        const index = anchorItems.value.findIndex((item) => item.href === '#fee-plan');
        if (index === -1) {
          anchorItems.value.splice(8, 0, {
            key: '8',
            href: '#fee-plan',
            title: '收费计划',
          });
        }
      } else {
        // payInfoList没数据，右侧锚点不显示收费计划
        anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
      }
      if (contractModel.value?.edrApplyBaseInfoValue?.sceneList !== '00006') {
        anchorItems.value.splice(1, 1);
      }
      if (contractModel.value?.noclaimInfoList?.length === 0) {
        anchorItems.value = anchorItems.value.filter((item) => item.href !== '#disclaimers-info');
      }
      // 保费来源只展示批改内容、保费来源、收费计划
      if (contractModel.value?.edrApplyBaseInfoValue?.sceneList === '90003') {
        anchorItems.value = anchorItems.value.filter((item) => item.href === '#policy-info' || item.href === '#premium-source' || item.href === '#fee-plan');
      } else {
        anchorItems.value = anchorItems.value.filter((item) => item.href !== '#premium-source');
      }
    } else {
      message.error(res?.msg || '请求失败');
    }
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};
const router = useRouter();
// 跳转附件管理页面
const handleGoLink = () => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: contractModel.value?.endorseApplyNo || '',
      bizType: 'docViewTreeEndorse',
    },
  });
};
// 跳转验标页面
// const goInspectionInfo = () => {
//   router.push({
//     path: '/inspectionInfo',
//     query: {
//       applyPolicyNo: route.query.applyPolicyNo,
//       isHiddenProcess: 'Y',
//     },
//   });
// };
// 批单信息
interface QueryApprovalTaskList {
  approveStatus: string;
  approveResultDesc: string;
  approveUm: string;
  approveTime: string;
  createdBy: string;
  createdDate: string;
}
const insureInfo = ref<QueryApprovalTaskList[]>([]);
const chainStr = ref<string>('');
const queryUnderwriteAssistant = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/evntApprovalTaskRecord/queryApprovalTaskList`;
    const res = await $getOnClient<{ taskRecordVOList: QueryApprovalTaskList[]; chainList: Record<string, string>[] }>(fetchUrl, { voucherNo: route.query.endorseApplyNo });
    const { taskRecordVOList = [], chainList = [] } = res?.data || {};
    if (res?.code === SUCCESS_CODE) {
      insureInfo.value = taskRecordVOList || [];
      chainStr.value = chainList.join('->');
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
// 主介绍人
const showIntroducer = ref(false);
watchEffect(() => {
  const channelSourceCode = contractModel.value?.saleInfo?.channelSourceCode;
  const channelSourceDetailCode = contractModel.value?.saleInfo?.channelSourceDetailCode?.split(',')?.[0] ?? '';
  if ((channelSourceCode === '7' && ['D', 'L'].includes(channelSourceDetailCode)) || (channelSourceCode === 'C' && channelSourceDetailCode === 'G')) {
    showIntroducer.value = true;
  } else {
    showIntroducer.value = false;
  }
});
const listLoading = ref<boolean>(false);
// 导出清单数据
const farmerListExport = async () => {
  try {
    listLoading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/file/endorseFarmerListExport`;
    await $getOnClient(
      fetchUrl,
      { endorseApplyNo: contractModel.value?.endorseApplyNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('导出成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
    listLoading.value = false;
  } catch (error) {
    console.log(error);
    listLoading.value = false;
  }
};
onActivated(() => {
  getInit();
});

const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    min-height: calc(100vh - 40px - 50px - 28px);
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
    :deep(.ant-descriptions) {
      .ant-descriptions-item {
        padding-bottom: 12px;
        color: rgba(0, 0, 0, 0.6);
        .ant-descriptions-item-content {
          color: #333333;
        }
      }
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    width: 100%;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
  :deep(.ant-descriptions-item-label) {
    color: rgba(0, 0, 0, 0.6);
  }
  :deep(.css-dev-only-do-not-override-1h2b86a.ant-form-item .ant-form-item-label > label) {
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
