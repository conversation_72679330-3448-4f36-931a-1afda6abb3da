<template>
  <div class="my-16px flex gap-x-[12px] pd-[56px] relative">
    <div v-show="showFileUploadLoading" class="absolute top-0 left-0 w-full h-full bg-white/50 flex justify-center items-center z-[1000]">
      <a-spin :tip="showFileUploadLoadingTips" />
    </div>
    <div class="rounded bg-white w-[291px] p-12px container-height">
      <a-upload :multiple="true" :before-upload="(file, fileList) => handleNoclassifyFileChange(file, fileList, noclassify.typeCode, noclassify.fileDetailList)" :show-upload-list="false">
        <div class="upload-box">
          <div class="flex flex-col items-center">
            <img class="w-[46px] h-[54px]" :src="uploadImg" />
            <div class="text-xs text-[#333333] mt-[8px] mb-[4px]">可直接拖拽文件到这里，或点击添加</div>
            <div class="text-xs text-[rgba(0,0,0,0.6)]">单次不超过<span class="font-number-medium text-[#07c160]">200&nbsp;</span>份</div>
          </div>
        </div>
      </a-upload>
      <div class="flex items-center justify-between mt-[16px] mb-[8px]">
        <div class="text-rgba(0,0,0,0.9) text-[16px] font-bold">未分类文件</div>
        <a-checkbox v-model:checked="noclassify.checkedAll" :indeterminate="noclassify.indeterminate" @change="(e) => handleAllCheckedChange(e, noclassify)">
          <span class="text-xs text-[#606060]">全选</span>
        </a-checkbox>
      </div>
      <div class="grid grid-cols-2 gap-[8px] noclassify-height">
        <!-- 可拖拽文件 -->
        <div v-for="file in noclassify.fileDetailList" :key="file.documentId" class="file-box" draggable="true" @dragstart="dragStart(file, noclassify)">
          <a-checkbox v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(noclassify)" />
          <div class="h-full flex flex-col justify-center items-center">
            <img v-if="file.thumbnail && isImage(file.documentFormat)" class="h-[80px] w-[70px]" :src="file.thumbnail" />
            <div v-else class="text-[50px] h-[80px] flex justify-center items-center">
              <VueIcon :icon="getFileIcon(file.documentName)" />
            </div>
            <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" :title="file.documentName">{{ file.documentName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="rounded bg-white flex-grow p-12px container-height">
      <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold ml-12px">
        {{ typeName }}资料({{ totalCount }})
        <a-button class="ml-[6px]" type="primary" @click="() => (allDeleteModal = true)"> 删除文件 </a-button>
      </div>
      <div class="flex justify-between mb-8px">
        <div class="flex items-center gap-x-16px text-[rgba(0,0,0,0.6)] ml-12px">
          <span>注：1.单个附件大小不超过10M</span>
          <span class="gap-[5px]">
            2.图片格式
            <a-tooltip placement="top">
              <VueIcon :icon="IconErrorCircleFilledFont" />
              <template #title>
                <span>图片附件格式为：BMP, DIB, JPG, JPEG, JPE, JFIF, GIF, TIF, TIFF, PNG</span>
              </template>
            </a-tooltip>
          </span>
          <span class="gap-[5px]"
            >3.文档格式
            <a-tooltip placement="top">
              <VueIcon :icon="IconErrorCircleFilledFont" />
              <template #title>
                <span>文档附件格式为：DOC, XLS, PDF, MSG, HTM, PPT, PPTX, RAR, ZIP, TXT, XLSX, DOCX</span>
              </template>
            </a-tooltip></span
          >
        </div>
        <div class="flex items-center gap-x-8px">
          <a-select v-model:value="docTypeValue" class="w-[277px]" placeholder="请选择资料类型" allow-clear show-search option-filter-prop="label" :options="docTypeList" />
          <a-button @click="addDocType">
            <span class="text-[#5a5a5a]">
              <VueIcon :icon="IconAddFont" />
              添加资料类型
            </span>
          </a-button>
        </div>
      </div>
      <div class="grid grid-cols-2 gap-12px">
        <!-- 可拖入区域 -->
        <div v-for="classify in classifyList" :key="classify.typeCode" class="classify-container" @dragover.prevent @drop="drop(classify)">
          <div class="flex items-center justify-between mb-[8px]">
            <div class="flex items-center">
              <a-checkbox v-model:checked="classify.checkedAll" :indeterminate="classify.indeterminate" @change="(e) => handleAllCheckedChange(e, classify)">
                <span class="sub-title">{{ classify.typeName }}</span>
              </a-checkbox>
              <!-- 必传数量 -->
              <span v-if="['01', '02', '03'].includes(classify.docLimitFlag) && classify.fileTotalNeedNum" class="text-xs text-[rgba(0,0,0,0.6)] ml-[5px]">{{ `必传数量 (${classify.localTotalNum}/${classify.fileTotalNeedNum || 0})` }}</span>
            </div>
            <div class="text-xs text-[rgba(0,0,0,0.6)] flex items-center">
              <a-tooltip title="暂无数据">
                <span class="mr-16px">资料说明</span>
              </a-tooltip>
              <a-tooltip title="暂无数据">
                <span>查看示例</span>
              </a-tooltip>
              <span class="inline-block w-[1px] h-[16px] bg-[#d4d6d9] mx-[12px]" />
              <span v-if="classify.docLimitFlag === '00'" class="text-[#576B95] cursor-pointer" @click="handleDeleteDoc(classify)"><VueIcon :icon="IconTongyongShanchuFont" /><span class="ml-[4px]">删除分类</span></span>
              <span v-if="classify.docLimitFlag === '01'" class="green-tag">出单必录</span>
              <span v-if="classify.docLimitFlag === '02'" class="blue-tag">出单可缓</span>
              <span v-if="classify.docLimitFlag === '03'" class="blue-tag">纸质归档</span>
            </div>
          </div>
          <ScrollLoadAttachments v-model:current-page-num="classify.pageNum" v-model:page-size="classify.pageSize" v-model:total-num="classify.totalNum" v-model:loading="classify.loading" class="grid-container" @load-more="loadMore(classify)">
            <a-upload :multiple="true" :before-upload="(file, fileList) => handleFileChange(file, fileList, classify.typeCode, classify.fileDetailList)" :show-upload-list="false">
              <div class="file-box flex justify-center items-center gap-x-[2px] text-[#606060]">
                <VueIcon :icon="IconUploadFont" />
                上传文件
              </div>
            </a-upload>
            <div v-for="file in classify.fileDetailList" :key="file.documentId" class="file-box" draggable="true" @dragstart="dragStart(file, classify)">
              <a-checkbox v-model:checked="file.selected" class="absolute top-[6px] right-[6px]" @click.stop @change="handleCheckedChange(classify)" />
              <div class="h-full flex flex-col justify-center items-center">
                <img v-if="file.thumbnail && isImage(file.documentFormat)" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file, classify.fileDetailList)" />
                <div v-else class="text-[50px] h-[80px] flex justify-center items-center" @click="handleClickFile(file, classify.fileDetailList)">
                  <VueIcon :icon="getFileIcon(file.documentName)" />
                </div>
                <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" :title="file.documentName">{{ file.documentName }}</div>
              </div>
            </div>
          </ScrollLoadAttachments>
        </div>
      </div>
    </div>
    <a-modal v-model:open="deleteModal" title="提醒" @ok="deleteDocType">
      <div>确认要将资料类型及其类型下所有资料删除？</div>
    </a-modal>
    <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleDeleteFile">
      <div>确认删除？</div>
    </a-modal>
    <ImageViewer ref="viewRef" :list="viewList" :current="viewCurrent" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { IconUploadFont, IconAddFont, IcPdfColor, IcWordColor, IcDianziqingdanColor, IcWenjianColor, IconErrorCircleFilledFont, IconTongyongShanchuFont, IcYingxiangColor } from '@pafe/icons-icore-agr-an';
import type { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import type { FileModule, UploadFile } from './imageUpload';
import uploadImg from '@/assets/images/ic-upload.png';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { $postOnClient } from '@/composables/request';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import { useUploadFiles } from '@/composables/useUploadFiles';
import ScrollLoadAttachments from '@/components/ui/ScrollLoadAttachments.vue';
import { type ApiResult, $post } from '@/utils/request';
import { isImage, isUnPreviewImage } from '@/utils/tools';

const { gateWay, service } = useRuntimeConfig().public || {};
const props = defineProps<{
  endorseApplyNo: string; // 批改申请单号
  documentGroupId: string; // 文件唯一id
  policyNo: string; // 保单号
  typeName: string; // 批改类型名称
}>();
const emit = defineEmits(['refreshDocumentId']);
// 还需补充
const fileTypeMap = new Map([
  ['XLSX', IcDianziqingdanColor],
  ['XLS', IcDianziqingdanColor],
  ['DOC', IcWordColor],
  ['DOCX', IcWordColor],
  ['PDF', IcPdfColor],
]);

const totalCount = ref(0);
const docTypeValue = ref('');
const docTypeList = ref<{ label: string; value: string }[]>([]);

const getDocTypeList = async () => {
  try {
    const fetchUrl = gateWay + service.administrate + '/attachment/documentType/list';
    const { data, code } = await $postOnClient(fetchUrl, {
      bizType: 'docTypeEndorse',
    });
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      docTypeList.value = data.map((item) => ({ label: item.parameterName, value: item.parameterCode }));
    }
  } catch (e) {
    console.log(e);
  }
};
getDocTypeList();

// 未分类文件
const noclassify = reactive<FileModule>({
  typeName: '未分类文件',
  typeCode: '',
  fileDetailList: [],
  checkedAll: false,
  indeterminate: false,
  docLimitFlag: '',
});
// 已分类列表
const classifyList = ref<Array<FileModule>>([]);

const getDocList = async () => {
  const fetchUrl = gateWay + service.administrate + '/attachment/endorse/documentGroup/query';
  try {
    const { code, data, msg } = await $postOnClient(fetchUrl, { bizNo: props.endorseApplyNo, bizType: 'docTypeEndorse', policyNo: props.policyNo });
    if (code === SUCCESS_CODE && data) {
      totalCount.value = data.fileTotalSize;
      classifyList.value = data.fileTypeList.map((item) => ({
        ...item,
        checkedAll: false,
        indeterminate: false,
        pageNum: item.pageNum || 1, // 赋值
        pageSize: item.pageSize || 20,
        totalNum: item.total || 0,
        localTotalNum: item.total || 0, // 本地展示的当前文件总数量
        loading: false,
      }));
      noclassify.fileDetailList = data.noTypeFileList?.[0]?.fileDetailList || [];
    } else {
      message.error(msg || '');
    }
  } catch (e) {
    console.log(e);
  }
};

const handleAllCheckedChange = (e: CheckboxChangeEvent, target: FileModule) => {
  target.indeterminate = false;
  target.fileDetailList.forEach((el) => (el.selected = e.target.checked || false));
};

const handleCheckedChange = (target: FileModule) => {
  target.checkedAll = target.fileDetailList.every((i) => i.selected);
  if (target.fileDetailList.some((i) => i.selected)) {
    target.indeterminate = !target.fileDetailList.every((i) => i.selected);
  } else {
    target.indeterminate = false;
  }
};
// 记录拖拽文件
const dragFile = ref<UploadFile>();
// 记录待拖拽文件所属对象
const moduleSnapshot = ref<FileModule>();
const dragStart = (targetFile: UploadFile, fileModule: FileModule) => {
  dragFile.value = targetFile;
  moduleSnapshot.value = fileModule;
};

const removeFile = async (file: UploadFile, fileList: UploadFile[]) => {
  const removeIndex = fileList.findIndex((item) => item.documentId === file.documentId);
  fileList.splice(removeIndex, 1);
};

const drop = (targetModule: FileModule) => {
  if (moduleSnapshot.value && dragFile.value) {
    const selectedList = moduleSnapshot.value?.fileDetailList.filter((i) => i.selected);
    if (selectedList.length > 1 && dragFile.value.selected) {
      selectedList.forEach((current) => {
        current.selected = false;
        targetModule.fileDetailList.unshift(current);
        removeFile(current, moduleSnapshot.value?.fileDetailList || []);
        targetModule.localTotalNum = targetModule.localTotalNum + 1; // 更新拖入文件总数
        moduleSnapshot.value.localTotalNum = moduleSnapshot.value.localTotalNum - 1; // 更新拖出文件总数
      });
    } else {
      targetModule.fileDetailList.unshift(dragFile.value);
      removeFile(dragFile.value, moduleSnapshot.value.fileDetailList);
      targetModule.localTotalNum = targetModule.localTotalNum + 1; // 更新拖入文件总数
      moduleSnapshot.value.localTotalNum = moduleSnapshot.value.localTotalNum - 1; // 更新拖出文件总数
    }
    handleCheckedChange(targetModule);
    moduleSnapshot.value.checkedAll = false;
    moduleSnapshot.value.indeterminate = false;
  }
};

const acceptType = ['BMP', 'DIB', 'JPG', 'JPEG', 'JPE', 'JFIF', 'GIF', 'TIF', 'TIFF', 'PNG', 'PSD', 'PCX', 'EXIF', 'DOC', 'XLS', 'PDF', 'MSG', 'HTM', 'PPT', 'PPTX', 'RAR', 'ZIP', 'TXT', 'XLSX', 'DOCX'];
const MAX_SIZE = 10 * 1024 * 1024; // 10MB
// 文件验证
const validateFile = (file: File): boolean => {
  const fileExtension = file.name.split('.').pop()?.toUpperCase();

  if (!fileExtension || !acceptType.includes(fileExtension)) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }

  if (file.size > MAX_SIZE) {
    message.warning(`${file.name} 大小超过10MB`);
    return false;
  }

  const isDuplicate = (list: UploadFile[]) => list.some((_file) => _file.documentName === file.name);
  if (isDuplicate(noclassify.fileDetailList || []) || classifyList.value.some((classify) => isDuplicate(classify.fileDetailList || []))) {
    message.warning(`${file.name}附件已存在，不能再上传`);
    return false;
  }

  return true;
};

const { upload, showFileUploadLoading, showFileUploadLoadingTips, uploadedFile } = useUploadFiles({ validate: validateFile });
const targetUploadList = ref<UploadFile>([]);

// 上传成功回调
watch(uploadedFile, (val) => {
  targetUploadList.value.unshift({ ...val, selected: false });
  emit('refreshDocumentId', val.documentGroupId);
  // 更新对应类型的文件总数量
  classifyList.value.forEach((classify) => {
    if (classify.typeCode === val?.documentType) {
      classify.localTotalNum = classify?.localTotalNum + 1;
    }
  });
});

const handleFileChange = async (file: File & { uid: string }, fileList: Array<File & { uid: string }>, targetType: string, targetList: UploadFile[]) => {
  // if (fileList.length > 200) {
  //   message.error('一次上传最多不能超过200张，请重新选择要上传的附件！');
  //   return false;
  // }
  // // 文件类型控制
  // const fileExtension = file.name.slice(((file.name.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  // if (acceptType.indexOf(fileExtension) === -1) {
  //   message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
  //   return false;
  // }
  // // 文件大小控制
  // const MAX_SIZE = 10 * 1024 * 1024; // 4MB
  // if (file.size > MAX_SIZE) {
  //   message.warning('文件大小不能超过10MB');
  //   return false;
  // }
  // // 检查未分类是否存在同名
  // const noclassNameIndex = noclassify.fileDetailList?.findIndex((_file) => _file.documentName === file.name);
  // if (noclassNameIndex !== -1) {
  //   message.warning(`${file.name}附件已存在，不能再上传`);
  //   return false;
  // }
  // // 检查已分类是否存在同名
  // for (const classify of classifyList.value) {
  //   const nameIndex = classify.fileDetailList?.findIndex((_file) => _file.documentName === file.name);
  //   if (nameIndex !== -1) {
  //     message.warning(`${file.name}附件已存在，不能再上传`);
  //     return false;
  //   }
  // }
  // try {
  //   const fetchUrl = gateWay + service.administrate + '/attachment/document/upload';
  //   const formData = new FormData();
  //   formData.append('file', file);
  //   formData.append('bizNo', props.endorseApplyNo || '');
  //   formData.append('bizType', 'docTypeEndorse');
  //   formData.append('fileType', targetType);
  //   formData.append('policyNo', props.policyNo || '');
  //   formData.append('documentGroupId', props.documentGroupId || '');
  //   const { data, code, msg } = await $postOnClient(fetchUrl, formData);
  //   if (code === SUCCESS_CODE) {
  //     targetList.push({
  //       ...data,
  //       selected: false,
  //     });
  //     emit('refreshDocumentId', data.documentGroupId);
  //   } else {
  //     message.error(msg || '文件上传失败，请重试');
  //   }
  // } catch (e) {
  //   console.log(e);
  // }
  // return false;

  targetUploadList.value = targetList;
  return upload(file, fileList, {
    bizNo: props.endorseApplyNo || '',
    bizType: 'docTypeEndorse',
    fileType: targetType,
    policyNo: props.policyNo || '',
    documentGroupId: props.documentGroupId || '',
  });
};

// 未分类文件上传
const handleNoclassifyFileChangeTimes = ref(0); // handleNoclassifyFileChange方法执行次数
const handleNoclassifyFileChange = async (file: File & { uid: string }, fileList: Array<File & { uid: string }>, targetType: string, targetList: UploadFile[]) => {
  // 未分类文件不存在分页，最多可存200条数据，已和产品沟通
  handleNoclassifyFileChangeTimes.value += 1;
  if (targetList.length + fileList.length >= 201) {
    if (handleNoclassifyFileChangeTimes.value === fileList.length) {
      // 仅提示一次
      message.error('未分类文件已达上限，请把未分类的文件移到其他分类中，再进行上传');
      handleNoclassifyFileChangeTimes.value = 0;
    }
    return false;
  }
  // return false;
  handleFileChange(file, fileList, targetType, targetList);
};

const getFileIcon = (fileName: string) => {
  const fileExtension = fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  return isUnPreviewImage(fileExtension) ? IcYingxiangColor : fileTypeMap.get(fileExtension) || IcWenjianColor;
};

const handleDeleteFile = async () => {
  const noclassifySelected = noclassify.fileDetailList.filter((item) => item.selected);
  let classifySelected: UploadFile[] = [];
  classifyList.value.forEach((classify) => {
    const selectedList = classify.fileDetailList.filter((item) => item.selected);
    classifySelected = [...classifySelected, ...selectedList];
  });
  if (noclassifySelected?.length === 0 && classifySelected?.length === 0) {
    message.error('请至少勾选1个要删除的文件');
  } else {
    const deleteList = [...noclassifySelected.map((k) => k.documentId), ...classifySelected.map((v) => v.documentId)];
    const url = gateWay + service.administrate + '/attachment/document/delete';
    try {
      const { code } = await $postOnClient(url, { bizType: 'docTypeEndorse', bizNo: props.endorseApplyNo, documentId: deleteList.join(',') });
      if (code === SUCCESS_CODE) {
        noclassify.fileDetailList = noclassify.fileDetailList.filter((item) => !item.selected);
        noclassify.checkedAll = false;
        noclassify.indeterminate = false;
        classifyList.value.forEach((classify) => {
          const delNum = classify.fileDetailList.filter((item) => item.selected).length; // 删除的数量
          classify.localTotalNum = classify.localTotalNum - delNum; // 更新当前文件总数
          classify.fileDetailList = classify.fileDetailList.filter((item) => !item.selected);
          classify.checkedAll = false;
          classify.indeterminate = false;
        });
        allDeleteModal.value = false;
        // getDocList();
      } else {
        message.error('删除失败，请重试!');
      }
    } catch (e) {
      message.error('删除失败，请重试!');
      console.log(e);
    }
  }
};

const viewRef = ref(null);
const viewList = ref<
  {
    bucketName: string;
    uploadPath: string;
    thumbnail: string; // 缩略图url
    documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
    documentName?: string;
    id?: string;
  }[]
>([]);
const viewCurrent = ref(0);
const handleClickFile = async (file: UploadFile, fileList: UploadFile[]) => {
  // 全部都到预览页展示
  viewCurrent.value = fileList.findIndex((k) => k.documentId === file.documentId);
  viewList.value = fileList.map((v) => ({
    bucketName: v.bucketName,
    uploadPath: v.uploadPath,
    thumbnail: v.thumbnail,
    documentFormat: v.documentFormat,
    documentName: v.documentName,
    id: v.documentId,
  }));
  if (viewRef.value) {
    viewRef.value?.openPreview();
  }
  // 如果点击图片，则预览，否则下载
  // if (file.thumbnail) {
  //   viewCurrent.value = fileList.findIndex((k) => k.documentId === file.documentId);
  //   viewList.value = fileList.map((v) => ({
  //     bucketName: v.bucketName,
  //     uploadPath: v.uploadPath,
  //     thumbnail: v.thumbnail,
  //     documentFormat: v.documentFormat,
  //     documentName: v.documentName,
  //     id: v.documentId,
  //   }));
  //   if (viewRef.value) {
  //     viewRef.value?.openPreview();
  //   }
  // } else {
  //   const { data } =
  //     (await $postOnClient('/api/iobs/getInIobsUrl', {
  //       iobsBucketName: file.bucketName,
  //       fileKey: file.uploadPath,
  //       storageTypeCode: file.storageType,
  //     })) || {};
  //   if (data?.fileUrl) {
  //     window.open(data?.fileUrl);
  //   }
  // }
};

const addDocType = () => {
  if (!docTypeValue.value) {
    message.warning('请选择要添加的资料类型');
    return;
  }
  const selected = docTypeList.value.find((doc) => doc.value === docTypeValue.value);
  const hasAdd = classifyList.value.some((item) => item.typeCode === selected?.value);
  if (selected && !hasAdd) {
    classifyList.value.push({ typeName: selected.label, fileDetailList: [], checkedAll: false, indeterminate: false, typeCode: selected.value, docLimitFlag: '00' });
  } else {
    message.warning('该类型文件已存在，请勿重新添加');
  }
};

const deleteModal = ref(false);
const waitDeleteDocType = ref<string>();
const handleDeleteDoc = async (target: FileModule) => {
  waitDeleteDocType.value = target.typeCode;
  deleteModal.value = true;
};
const allDeleteModal = ref(false);
const deleteDocType = async () => {
  const fetchurl = gateWay + service.administrate + '/attachment/documentGroup/deleteByDocType';
  try {
    const { code } = await $postOnClient(fetchurl, {
      bizType: 'docTypeEndorse',
      bizNo: props.endorseApplyNo,
      docType: waitDeleteDocType.value,
      documentGroupId: props.documentGroupId,
    });
    if (code === SUCCESS_CODE) {
      classifyList.value = classifyList.value.filter((classify) => classify.typeCode !== waitDeleteDocType.value);
    }
  } catch (e) {
    console.log(e);
  } finally {
    deleteModal.value = false;
  }
};
const reset = () => {
  classifyList.value = [];
  totalCount.value = 0;
  noclassify.fileDetailList = [];
};

const saveList = async () => {
  const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/save';
  const fileList: Array<{ typeCode: string; documentId: string }> = [];
  classifyList.value.forEach((classify) => {
    classify.fileDetailList.forEach((file) => {
      fileList.push({
        typeCode: classify.typeCode,
        documentId: file.documentId,
      });
    });
  });
  try {
    if (props.endorseApplyNo || props.documentGroupId) {
      const { code } = await $postOnClient(fetchUrl, { bizNo: props.endorseApplyNo, bizType: 'docTypeEndorse', documentGroupId: props.documentGroupId, saveFileDetailVOS: fileList });
      if (code === SUCCESS_CODE) {
        // 刷新页面
        getDocList();
      }
    }
  } catch (e) {
    console.log(e);
  }
};

const submit = () => {
  if (noclassify.fileDetailList.length > 0) {
    return 'N';
  } else {
    saveList();
    return 'Y';
  }
};

const loadMore = async (item: FileModule) => {
  // console.log('loadmore', item);
  // 加载下一页数据
  item.loading = true;
  const { typeCode, pageNum } = item;
  const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/queryPageSubType';
  // const fetchUrl = gateWay + service.administrate + '/attachment/documentGroup/query';
  try {
    const { code, data } = await $post<ApiResult<DocumentGroupQueryResponse>>(fetchUrl, { bizNo: props.endorseApplyNo, documentType: typeCode, pageNum, bizType: 'docTypeEndorse' });
    // const { code, data } = await $post<ApiResult<DocumentGroupQueryResponse>>(fetchUrl, { bizNo: route.query.applyPolicyNo, bizType: 'docTypeApply' });
    item.loading = false;
    if (code === SUCCESS_CODE && data) {
      item.pageNum = data.fileTypeList[0].pageNum;
      item.pageSize = data.fileTypeList[0].pageSize;
      item.totalNum = data.fileTypeList[0].total;
      item.fileDetailList = [...item.fileDetailList, ...data.fileTypeList[0].fileDetailList];
    }
  } catch (e) {
    item.loading = false;
    console.log(e);
  }
};

defineExpose({
  getDocList,
  reset,
  submit,
});
</script>

<style lang="less" scoped>
.container-height {
  height: calc(100vh - 120px);
  overflow-y: scroll;
}
.noclassify-height {
  max-height: calc(100% - 200px);
  overflow-y: auto;
  overflow-x: hidden;
}
.sub-title {
  position: relative;
  font-size: 14px;
  color: #404442;
  font-weight: 600;
  padding-left: 8px;

  &::before {
    position: absolute;
    left: 0px;
    top: 4px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
.upload-box {
  width: 279px;
  height: 140px;
  background: rgba(7, 193, 96, 0.05);
  border: 1px dashed rgba(7, 193, 96, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.file-box {
  cursor: pointer;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  width: 130px;
  height: 130px;
  position: relative;
  background: #ffffff;
}
.classify-container {
  background: #fafafc;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  padding: 12px;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 默认是3列布局 */
  gap: 8px; /* 网格间距 */
  max-height: 580px;
  overflow-y: auto;
  overflow-x: hidden;
}
@media (min-width: 1920px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr); /* 1920及以上为4列布局 */
  }
}
.green-tag {
  background: rgba(7, 193, 96, 0.05);
  border: 1px solid rgba(7, 193, 96, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #07c160;
  display: flex;
  justify-content: center;
  align-items: center;
}
.blue-tag {
  background: rgba(61, 126, 255, 0.05);
  border: 1px solid rgba(61, 126, 255, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #3d7eff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.orange-tag {
  background: rgba(255, 91, 0, 0.05);
  border: 1px solid rgba(255, 91, 0, 0.4);
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 12px;
  color: #ff5b00;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footer-boxshadow {
  box-shadow: 0px 2px 12px 0px rgba(212, 214, 217, 1);
}
</style>
