<template>
  <div ref="premiumSourceScrollWrapper" class="premium-source-container">
    <main class="premium-source-main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[48px] w-ful bg-image-url">
          <span class="text-[16px] font-semibold text-[#00190c] leading-[48px] ml-[16px]">批改单信息</span>
        </div>
        <a-spin :spinning="mainLoading">
          <div class="grid grid-cols-4 gap-x-16px bg-white h-[46px] items-center pl-[24px] pr-[24px]">
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">批改申请单号：</span>
              <span class="font-number">{{ contractModel.endorseApplyNo || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">保单号：</span>
              <span class="font-number">{{ contractModel.policyNo || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">录单人：</span>
              <span class="font-number">{{ contractModel.edrApplyBaseInfoValue.inputBy || '-' }}</span>
            </div>
            <div class="text-[#333]">
              <span class="text-[rgba(0,0,0,.6)] w-[98px] inline-block text-right">批改类型：</span>
              <span class="font-number">{{ contractModel.edrApplySceneInfoValue.sceneName }}</span>
            </div>
          </div>
          <a-form :colon="false">
            <div class="space-y-16px">
              <InfoGroupBox id="modify-content" title="本次批改内容">
                <ModifyInfo ref="modifyContentRef" v-model:data="contractModel.edrApplyBaseInfoValue" v-model:reason="contractModel.edrApplySceneInfoValue" :insurance-begin-date="contractModel?.contractValue?.baseInfo?.insuranceBeginDate" />
              </InfoGroupBox>
              <InfoGroupBox id="premium-source" title="保费来源">
                <InsuranceSource ref="premiumSourceRef" v-model:change-data="contractModel.premiumSourceChange" v-model:data="contractModel.contractValue.riskGroupInfoList" :gov-subsidy-type="contractModel.contractValue.baseInfo.govSubsidyType" />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel.edrApplyPayInfoListVOList.length > 0" id="fee-plan" title="收费计划">
                <FeePlan ref="payInfoRef" v-model:pay-info-list="contractModel.edrApplyPayInfoListVOList" v-model:base-info="contractModel.contractValue.baseInfo" :inner-coinsurance-mark="contractModel.contractValue?.coinsuranceInfo?.innerCoinsuranceMark" :payer-type-sum-of-pay-info="contractModel.edrApplyPayInfoTypeSumVO" :combined-product-code="contractModel.contractValue?.riskGroupInfoList?.[0]?.combinedProductCode" />
              </InfoGroupBox>
            </div>
          </a-form>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" :disabled="saveDisabled" :loading="saveDisabled" @click="saveData()">暂存</a-button>
      <a-button type="primary" :disabled="premiumComputeDisabled" :loading="premiumComputeDisabled" @click="validateAllForms('1')">保费计算</a-button>
      <a-button type="primary" :disabled="submitApprovalDisabled" :loading="submitApprovalDisabled" @click="validateAllForms('2')">申请核保</a-button>
      <a-button type="primary" @click="goUpload">附件管理</a-button>
    </footer>
    <!-- 错误信息弹窗 -->
    <a-modal v-model:open="errorVisible" :width="pxToRem(480)" :centered="true" :style="{ 'max-height': pxToRem(300), overflow: 'auto' }" @ok="errorVisible = false">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#f03e3e]" :icon="IconCloseCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">温馨提示</span>
      </div>
      <div v-for="(item, i) in confirmErrText" :key="i">{{ item }}</div>
      <template #footer>
        <a-space>
          <a-button type="primary" @click="errorVisible = false">确定</a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { IconCloseCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import type { contractModelType } from './premiumSource.d';
import { dataModel } from './const';
import ModifyInfo from './components/ModifyInfo.vue';
import InsuranceSource from './components/InsuranceSource.vue';
import FeePlan from './components/FeePlan.vue';
import { pxToRem } from '@/utils/tools';

const route = useRoute();
const router = useRouter();
const premiumSourceScrollWrapper = ref();
const premiumSourceRef = ref();
const modifyContentRef = ref();
const payInfoRef = ref();
const mainLoading = ref(false);
// 错误信息弹窗显示隐藏
const errorVisible = ref<boolean>(false);
// 错误信息描述
const confirmErrText = ref<string[]>([]);
const getContainer = () => premiumSourceScrollWrapper.value || window;
const { gateWay, service } = useRuntimeConfig().public || {};
const { deletPageTabListItem, pageTabList } = inject('pageTab') as {
  deletPageTabListItem: (id: string) => void;
  pageTabList: Ref<{ fullPath: string }[]>;
}; // 关闭页签

// 定位锚点
const anchorItems = ref([
  { key: '1', href: '#modify-content', title: '本次批改内容' },
  { key: '2', href: '#premium-source', title: '保费来源' },
]);

const initContractModel = (data: contractModelType) => {
  if (!data.edrApplyBaseInfoValue.calculateType) {
    data.edrApplyBaseInfoValue.calculateType = '2';
  }
  return {
    ...data,
    applyPolicyNo: data.applyPolicyNo || '',
    moreRiskFlag: data.moreRiskFlag || '',
    edrApplyBaseInfoValue: data.edrApplyBaseInfoValue || {}, // 基础信息
    edrApplySceneInfoValue: data.edrApplySceneInfoValue || {}, // 场景
    edrApplyPayInfoListVOList: data.edrApplyPayInfoListVOList || [], // 收费计划
    endorseApplyNo: data.endorseApplyNo || '',
    endorseNo: data.endorseNo || '',
    insrSpltPlyEdrSumyVO: data.insrSpltPlyEdrSumyVO || {}, // 清单信息
    policyNo: data.policyNo || '',
    edrApplyAttachmentVO: {
      ...data.edrApplyAttachmentVO,
      documentGroupId: data?.edrApplyAttachmentVO?.documentGroupId || route.query.documentGroupId,
    },
    edrApplyPayInfoTypeSumVO: data?.edrApplyPayInfoTypeSumVO || {},
    contractValue: {
      ...data.contractValue,
      baseInfo: data.contractValue?.baseInfo || {},
      riskGroupInfoList: data.contractValue?.riskGroupInfoList || [], // 标的
      sumRiskGroupAmount: data.contractValue?.sumRiskGroupAmount || {}, // 保费计划合计
      payInfoList: data.contractValue?.payInfoList || [], // 收费计划
      coinsuranceInfo: data.contractValue?.coinsuranceInfo || {}, // 共保
    },
  };
};

const contractModel = ref(initContractModel(dataModel));

const getInit = async (sceneName?: string) => {
  mainLoading.value = true;
  try {
    const res = await $post(`${gateWay}${service.endorse}/web/endorse/detail`, {
      policyNo: route.query.endorseApplyNo,
      qryScene: sceneName || route.query.qryScene || 'PREMIUM_FROM_APPLY',
      endorseSceneCode: route.query.endorseScene, // 场景编码
    });
    if (res && res.code === SUCCESS_CODE) {
      contractModel.value = initContractModel((res.data as contractModelType) || {});
      changePayInfoData();
    }
  } catch (error) {
    console.log(error);
  } finally {
    mainLoading.value = false;
  }
};

// 收费计划数据处理
const changePayInfoData = () => {
  if (contractModel.value?.edrApplyPayInfoListVOList?.length > 0) {
    // 缴费起至时间处理为数组
    contractModel.value.edrApplyPayInfoListVOList.forEach((item: { paymentPeriod: (string | Dayjs)[]; paymentBeginDate: string | Dayjs; paymentEndDate: string | Dayjs }) => {
      item.paymentPeriod = [];
      const today = dayjs(new Date()).format('YYYY-MM-DD');
      item.paymentPeriod[0] = item.paymentBeginDate || dayjs(today);
      item.paymentBeginDate = item.paymentBeginDate || dayjs(today);
      item.paymentPeriod[1] = item.paymentEndDate || '';
    });
    // payInfoList有数据，右侧锚点显示收费计划
    const index = anchorItems.value.findIndex((item) => item.href === '#fee-plan');
    if (index === -1) {
      anchorItems.value.splice(2, 0, {
        key: '3',
        href: '#fee-plan',
        title: '收费计划',
      });
    }
  } else {
    // payInfoList没数据，右侧锚点不显示收费计划
    anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
  }
};

// 跳转附件管理
const goUpload = () => {
  router.push({
    name: 'endorseUpload',
    query: {
      ...route.query,
      endorseApplyNo: contractModel?.value?.endorseApplyNo,
      documentGroupId: contractModel?.value?.edrApplyAttachmentVO?.documentGroupId,
      policyNo: contractModel?.value?.policyNo,
    },
  });
};

// 提交的VO数据
const submitData = computed(() => {
  const tempData = {
    ...contractModel.value,
    contractValue: {
      ...contractModel.value.contractValue,
      riskGroupInfoList: premiumSourceRef.value.transformData(contractModel.value.contractValue?.riskGroupInfoList),
    },
  };
  // 提交前数据处理
  return tempData;
});

// 暂存
const saveDisabled = ref(false);
const saveData = async () => {
  const params = {
    ...submitData.value,
  };
  try {
    saveDisabled.value = true;
    const res = await $post<{ endorseApplyNo: string }>(`${gateWay}${service.endorse}/web/endorse/temp-storage`, params);
    saveDisabled.value = false;
    if (res && res.code === SUCCESS_CODE) {
      message.success('保存成功');
      contractModel.value.endorseApplyNo = res?.data?.endorseApplyNo || '';
      router.push({
        name: 'premiumSource',
        query: {
          ...route.query,
          qryScene: 'PREMIUM_FROM_MODIFY',
        },
      });
      getInit('PREMIUM_FROM_MODIFY');
    } else {
      message.error(res?.msg || '操作失败');
    }
  } catch (error) {
    saveDisabled.value = false;
    console.log(error);
  }
};

const getValidateArr = (validatePayInfo: boolean) => {
  const validateItems = [modifyContentRef.value?.validate(), premiumSourceRef.value?.validate()];
  if (payInfoRef.value && validatePayInfo) {
    validateItems.push(payInfoRef.value?.validate());
  }
  return validateItems;
};

const premiumComputeDisabled = ref(false);
const submitApprovalDisabled = ref(false);
const validateAllForms = async (type: string) => {
  // 1为保费计算  2为提交核保
  if (type === '1') {
    premiumComputeDisabled.value = true;
  } else if (type === '2') {
    submitApprovalDisabled.value = true;
  }
  try {
    const results = await Promise.all(getValidateArr(true));
    const isValid = results.every((result) => result.valid);
    if (isValid) {
      // 所有表单校验通过
      if (type === '1') {
        premiumCompute();
      } else if (type === '2') {
        submitApproval();
      }
    } else {
      premiumComputeDisabled.value = false;
      submitApprovalDisabled.value = false;
      // 有表单校验未通过，提示用户
      console.log('有表单校验未通过', results);
      // 跳转到校验不通过的第一个项目
      const errorDiv = document.querySelector('.ant-form-item-has-error');
      errorDiv?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  } catch {
    premiumComputeDisabled.value = false;
    submitApprovalDisabled.value = false;
  }
};

// 保费计算
const premiumCompute = async () => {
  mainLoading.value = true;
  try {
    const fetchUrl = `${gateWay}${service.endorse}/web/endorse/quote`;
    const params = {
      ...submitData.value,
    };
    const res = await $post<{ endorseApplyNo: string }>(fetchUrl, params);
    mainLoading.value = false;
    premiumComputeDisabled.value = false;
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      contractModel.value.endorseApplyNo = res?.data?.endorseApplyNo || '';
      router.push({
        name: 'premiumSource',
        query: {
          ...route.query,
          qryScene: 'PREMIUM_FROM_MODIFY',
        },
      });
      getInit('PREMIUM_FROM_MODIFY');
    } else {
      // 错误提示
      handleHint(msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    mainLoading.value = false;
    premiumComputeDisabled.value = false;
  }
};

// 接口返回错误信息提示
const handleHint = (msg: string) => {
  errorVisible.value = true;
  confirmErrText.value = msg?.split(';');
};

// 提交核保
const submitApproval = async () => {
  mainLoading.value = true;
  const params = {
    ...submitData.value,
    edrEoaVO: {
      eoaStatus: 'E', // 后端说会有其他场景传其他参数
    },
  };
  try {
    const fetchUrl = gateWay + service.endorse + '/web/endorse/apply';
    const res = await $post<{ resultEoa: string }>(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      deletPageTabListItem('/premiumSource'); // 关闭页签
      deletPageTabListItem('/endorseUpload');
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
    } else {
      message.error(res?.msg || '提交核保失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    mainLoading.value = false;
    submitApprovalDisabled.value = false;
  }
};

onActivated(() => {
  getInit();
});
</script>

<style lang="less" scoped>
.premium-source-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .premium-source-main-wrapper {
    padding: 16px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
