<template>
  <div class="insurance-source space-y-16px bg-[#ffffff]">
    <a-form ref="formRef" :model="changeData" :colon="false">
      <div v-for="(item, mainIndex) in changeData.sourcePlanChangeVOList" :key="mainIndex" class="bg-[#ffffff] rounded">
        <div class="flex items-center justify-between text-[rgba(0,0,0,0.55)] font-normal mb-[6px]">
          <div class="text-[rgba(0,0,0,0.60)] text-[14px] font-semibold flex items-center">
            <div class="bg-[#07C160] w-[3px] h-[10px] mr-[4px]" />
            {{ item?.isMain === '1' ? `主险：${item?.planCode + item?.planName}` : `附加险：${item?.planCode + item?.planName}` }}
          </div>
          <div v-if="changeData?.sourcePlanChangeVOList?.length > 1 && mainIndex === 0" class="flex items-center">
            <span>附加险保费来源：</span>
            <a-form-item name="samePrimaryInsurance">
              <span class="text-[rgba(0,0,0,0.55)] font-normal">与主险一致：</span>
              <a-radio-group v-model:value="changeData.samePrimaryInsurance" :disabled="data?.length > 1 || disabled" @change="handleChange">
                <a-radio value="1">
                  <span class="text-[rgba(0,0,0,0.55)] font-normal">是</span>
                </a-radio>
                <a-radio value="0">
                  <span class="text-[rgba(0,0,0,0.55)] font-normal">否</span>
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </div>
        </div>
        <div class="text-[14px] text-[#333333] font-normal mb-[12px]">保费来源</div>
        <div class="mb-[16px]">
          <a-table v-if="changeData?.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO" :columns="columns" :data-source="dataSource?.[mainIndex]" :pagination="false">
            <template #bodyCell="{ column, index }">
              <template v-if="column.dataIndex === 'centralFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newCentralFinance']" :rules="[{ validator: centralFinanceValidator }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newCentralFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'cityFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newCityFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newCityFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'countyFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newCountyFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newCountyFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'provincialFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newProvinceFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newProvinceFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'farmersFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newFarmerFinance']" :rules="[{ validator: financeValidator }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newFarmerFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex, true)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'otherFinance' && index === 1">
                <a-form-item class="w-[100px]" :name="['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newOtherFinance']" :rules="[{ validator: (_rule, value) => otherFinanceValidator(value, mainIndex) }]">
                  <a-input-number v-model:value="changeData.sourcePlanChangeVOList[mainIndex].newSourceRatioVO.newOtherFinance" :min="0" :disabled="(mainIndex > 0 && changeData.samePrimaryInsurance === '1') || disabled" @change="() => handleFinanceChange(mainIndex)" />
                </a-form-item>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-form>
    <a-form ref="farmerFormRef" :model="data" :colon="false">
      <div v-if="data?.length > 0" class="grid grid-cols-3 gap-x-10px mb-[16px]">
        <a-form-item label="农户自缴减免系数" required :name="[0, 'riskAgrInfo', 'reductionCoefficient']" :rules="[{ validator: (rule: Rule, value: number) => coefficientValidator(value, 0) }]">
          <a-input-number v-model:value="data[0].riskAgrInfo.reductionCoefficient" :style="{ width: '100%' }" placeholder="输入介于0-农户自缴上限" :min="0" disabled />
        </a-form-item>
        <a-form-item label="农户保费是否代缴" required :name="[0, 'riskAgrInfo', 'substitute']">
          <a-select v-model:value="data[0].riskAgrInfo.substitute" class="w-full" disabled @change="(value) => substituteChange(value as string, 0)">
            <a-select-option value="Y">是</a-select-option>
            <a-select-option value="N">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="data[0].riskAgrInfo.substitute === 'Y'" label="代缴保费资金来源" required :name="[0, 'riskAgrInfo', 'fundingSource']">
          <a-select v-model:value="data[0].riskAgrInfo.fundingSource" :options="sourceOptions" disabled />
        </a-form-item>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import type { ColumnType } from 'ant-design-vue/es/table/interface';
import { cloneDeep } from 'lodash-es';
import type { RiskGroupInfo } from '@/pages/insure/insuranceFormFill/insuranceFormFill';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { add } from '@/utils/tools';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import type { PremiumSourceChange } from '../premiumSource';
import { policySourceColumns, businessSourceColumns } from '../const';

const data = defineModel<Array<RiskGroupInfo>>('data', { default: [] });
const changeData = defineModel<PremiumSourceChange>('changeData', { default: {} });
const props = defineProps<{
  govSubsidyType: string; // 商业补贴类型
  disabled?: boolean; // 是否仅展示
}>();
const { gateWay, service } = useRuntimeConfig().public || {};

const formRef = ref();
const farmerFormRef = ref();

// 单位下拉选项
const unitOptions = ref<SelectOptions[]>([]);
// 代缴保费资金来源下拉选项
const sourceOptions = ref<SelectOptions[]>([]);

// 构建表格数据源
const dataSource = computed(() => {
  if (changeData?.value?.sourcePlanChangeVOList?.length < 1) {
    return [];
  } else {
    const tempData = changeData.value.sourcePlanChangeVOList;
    return tempData.map((item) => {
      return [
        {
          key: 'old',
          label: '旧比例',
          centralFinance: item?.oldSourceRatioVO?.oldCentralFinance,
          provincialFinance: item?.oldSourceRatioVO?.oldProvinceFinance,
          cityFinance: item?.oldSourceRatioVO?.oldCityFinance,
          countyFinance: item?.oldSourceRatioVO?.oldCountyFinance,
          farmersFinance: item?.oldSourceRatioVO?.oldFarmerFinance,
          otherFinance: item?.oldSourceRatioVO?.oldOtherFinance,
        },
        {
          key: 'new',
          label: '新比例',
          centralFinance: item?.newSourceRatioVO?.newCentralFinance,
          provincialFinance: item?.newSourceRatioVO?.newProvinceFinance,
          cityFinance: item?.newSourceRatioVO?.newCityFinance,
          countyFinance: item?.newSourceRatioVO?.newCountyFinance,
          farmersFinance: item?.newSourceRatioVO?.newFarmerFinance,
          otherFinance: item?.newSourceRatioVO?.newOtherFinance,
        },
        {
          key: 'change',
          label: '保费变化',
          centralFinance: item?.planSourceChangeVO?.centralPremiumChange,
          provincialFinance: item?.planSourceChangeVO?.provincePremiumChange,
          cityFinance: item?.planSourceChangeVO?.cityPremiumChange,
          countyFinance: item?.planSourceChangeVO?.countyPremiumChange,
          farmersFinance: item?.planSourceChangeVO?.farmerPremiumChange,
          otherFinance: item?.planSourceChangeVO?.otherPremiumChange,
        },
      ];
    });
  }
});

// 构建完整的 columns 配置
const columns = computed((): Array<ColumnType> => {
  // 03代表商业险
  if (props?.govSubsidyType === '3') {
    return [
      {
        title: '', // 第一列标题为空
        dataIndex: 'label',
        key: 'label',
        align: 'center',
      },
      ...businessSourceColumns,
    ];
  } else {
    return [
      {
        title: '', // 第一列标题为空
        dataIndex: 'label',
        key: 'label',
        align: 'center',
      },
      ...policySourceColumns,
    ];
  }
});

// 农户自缴减免系数校验
const coefficientValidator = (value: number, mainIndex: number) => {
  if (Number(value) < 0) {
    return Promise.reject('不能小于0');
  }
  // 最大值为险种中农户自缴比例的最小值 farmersFinance
  const farmersFinanceMin = changeData.value.sourcePlanChangeVOList[mainIndex]?.newSourceRatioVO?.newFarmerFinance;

  if (Number(value * 100) > Number(farmersFinanceMin)) {
    return Promise.reject('不能大于最小农户自缴系数');
  }
  return Promise.resolve();
};

// 比例校验，比例录入范围0-100范围内的数字且小数位不能超过4位
const financeValidator = (_rule: Rule, value: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 中央政策来源 如果是中央政策补贴则必填
const centralFinanceValidator = (_rule: Rule, value: number) => {
  if (!value && value !== 0) {
    return Promise.reject('不能为空');
  }
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};

// 校验是否等于100
const otherFinanceValidator = (value: number, mainIndex: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }
  const num1 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newCentralFinance || '0');
  const num2 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newCityFinance || '0');
  const num3 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newCountyFinance || '0');
  const num4 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newProvinceFinance || '0');
  const num5 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newFarmerFinance || '0');
  const num6 = String(changeData.value.sourcePlanChangeVOList?.[mainIndex]?.newSourceRatioVO?.newOtherFinance || '0');

  // 地方政策补贴则 省市区不能都为空
  if (props?.govSubsidyType === '2') {
    if (!num2 && !num3 && !num4) {
      return Promise.reject('省、市、区不能都为空');
    }
  }

  let sum = '';
  if (props.govSubsidyType === '3') {
    sum = add(num5, num6);
  } else {
    sum = [num1, num2, num3, num4, num5, num6].reduce((acc, num) => add(acc, num), '0');
  }

  // 校验总和是否等于100
  if (parseFloat(sum) !== 100) {
    if (props.govSubsidyType === '3') {
      return Promise.reject('农户自缴和其他补贴总和必须等于100');
    } else {
      return Promise.reject('中央补贴、省级补贴、地方补贴、县级补贴、农户自缴、其他补贴总和必须等于100');
    }
  }
  return Promise.resolve();
};

// 补贴类型为商业险时 置空省市区数据
const handleFinanceChange = (mainIndex: number, isFarmer?: boolean) => {
  // 如果同主险一致，就同时更新数据
  if (changeData?.value?.samePrimaryInsurance === '1' && changeData?.value?.sourcePlanChangeVOList?.length > 1) {
    const newValue = changeData?.value?.sourcePlanChangeVOList.filter((item) => item.isMain === '1')?.[0]?.newSourceRatioVO;
    changeData.value.sourcePlanChangeVOList.forEach((item, index) => {
      if (item.isMain === '0') {
        item.newSourceRatioVO = cloneDeep(newValue);
      }
      formRef.value?.validateFields([['sourcePlanChangeVOList', index, 'newSourceRatioVO', 'newOtherFinance']]);
    });
  }
  // 触发是否等于100比例的校验
  formRef.value?.validateFields([['sourcePlanChangeVOList', mainIndex, 'newSourceRatioVO', 'newOtherFinance']]);
  // 如果是农户改变，触发农户自缴系数不能大于最小农户自缴比例的校验
  if (isFarmer) {
    farmerFormRef.value?.validate([[0, 'riskAgrInfo', 'reductionCoefficient']]);
  }
};

// 农户是否代缴选择否清空资金来源
const substituteChange = (value: string, index: number) => {
  if (value === 'N') {
    data.value[index].riskAgrInfo.fundingSource = '';
  }
};

// 修改为同主险一致时 需要把数据也重置
const handleChange = (e) => {
  if (e?.target?.value === '1') {
    if (changeData?.value?.sourcePlanChangeVOList?.length > 1) {
      const newValue = changeData?.value?.sourcePlanChangeVOList.filter((item) => item.isMain === '1')?.[0]?.newSourceRatioVO;
      changeData.value.sourcePlanChangeVOList.forEach((item, index) => {
        if (item.isMain === '0') {
          item.newSourceRatioVO = cloneDeep(newValue);
        }
        formRef.value?.validateFields([['sourcePlanChangeVOList', index, 'newSourceRatioVO', 'newOtherFinance']]);
      });
    }
  }
};
// 处理数据 把多标的农户自缴系数、农户是否代缴、代缴来源字段重新赋值
const transformData = (data: Array<RiskGroupInfo>) => {
  // 多标数据合并
  data.forEach((item) => {
    // 把所有项的 riskAgrInfo对象中的reductionCoefficient substitute planAttributeSameMain字段重置为最后一项的值
    const firstItem = data?.[0].riskAgrInfo;
    item.riskAgrInfo = {
      ...item.riskAgrInfo,
      reductionCoefficient: firstItem.reductionCoefficient,
      substitute: firstItem.substitute,
      planAttributeSameMain: firstItem.planAttributeSameMain,
    };
  });
  return data;
};
const validate = async () => {
  if (formRef?.value) {
    try {
      await formRef?.value.validateFields();
      if (farmerFormRef.value) {
        await farmerFormRef.value.validateFields();
      }
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({
  validate,
  transformData,
});

const initOptions = async () => {
  const params = ['fundingSource', 'AGRZZJGZSBXDW'];
  const res = await $postOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, params);
  if (res && res?.code === SUCCESS_CODE) {
    sourceOptions.value = res.data.find((item: { value: string }) => item.value === 'fundingSource')?.children || [];
    unitOptions.value = res.data.find((item: { value: string }) => item.value === 'AGRZZJGZSBXDW')?.children || [];
  }
};
onMounted(() => {
  // 获取基础信息
  initOptions();
});
</script>

<style lang="less" scoped>
.insurance-source {
  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }
  .table-wrap {
    width: calc(100vw - 420px);
  }
}
</style>
