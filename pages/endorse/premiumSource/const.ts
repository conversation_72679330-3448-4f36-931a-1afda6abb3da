import type { contractModelType } from './premiumSource';

export const dataModel: contractModelType = {
  moreRiskFlag: '',
  applyPolicyNo: '',
  edrApplyBaseInfoValue: {
    originPremium: '',
    totalActualPremium: '',
    actualPremiumChange: '',
    applyDate: '',
    effectiveDate: '',
    isSendElectronicEdrDocument: '',
    endorseComment: '',
    cancelReason: '',
    inputBy: '',
    endorseApplyNo: '',
    policyNo: '',
    calculateType: '',
  },
  edrApplySceneInfoValue: {
    cancelType: '',
    sceneName: '',
  },
  edrApplyPayInfoListVOList: [],
  endorseApplyNo: '',
  endorseNo: '',
  insrSpltPlyEdrSumyVO: {},
  policyNo: '',
  edrApplyAttachmentVO: {},
  contractValue: {
    baseInfo: {
      assisterInfoList: [],
      insuranceBeginDate: '',
      insuranceEndDate: '',
      disputedSettleMode: '',
      arbitralDepartment: '',
      shortTimeCoefficient: '1',
      timeRange: '',
      departmentCode: '',
      applyPolicyNo: '',
      totalActualPremium: '',
      deductionDesc: '',
      premiumOwner: '',
      coinsuranceMark: '',
      govSubsidyType: '',
      acceptInsuranceFlag: '',
      applyPolicyType: '',
      productCode: '',
      productVersion: '',
      insuredNumber: '0',
      farmersCount: 0,
    },
    applicantInfoList: [],
    insurantInfoList: [],
    beneficaryInfoList: [],
    riskGroupInfoList: [],
    sumRiskGroupAmount: {
      totalInsuredAmount: '',
      totalStandardPremium: '',
      totalAgreePremium: '',
      totalActualPremium: '',
    },
    coinsuranceInfo: {
      innerCoinsuranceMark: '',
      innerCoinsuranceAgreement: '',
      totalPremium: '',
      totalInsuredAmount: '',
      coinsuranceDetailList: [],
      innerCoinsuranceMarkChName: '',
    },
    payInfoList: [],
  },
  edrApplyPayInfoTypeSumVO: {},
  premiumSourceChange: {
    samePrimaryInsurance: '',
    sourcePlanChangeVOList: [
      {
        govSubsidyType: '', // 补贴类型
        isMain: '', // 是否为主险
        planCode: '', // 险种编码
        planId: '', // 险种id
        planName: '', // 险种名称
        newSourceRatioVO: {
          newCentralFinance: '',
          newCityFinance: '',
          newCountyFinance: '',
          newFarmerFinance: '',
          newOtherFinance: '',
          newProvinceFinance: '',
        },
        oldSourceRatioVO: {
          oldCentralFinance: '',
          oldCityFinance: '',
          oldCountyFinance: '',
          oldFarmerFinance: '',
          oldOtherFinance: '',
          oldProvinceFinance: '',
        },
        planSourceChangeVO: {
          centralPremiumChange: '',
          cityPremiumChange: '',
          countyPremiumChange: '',
          farmerPremiumChange: '',
          otherPremiumChange: '',
          provincePremiumChange: '',
        },
      },
    ],
  },
};

// 保费来源政策性columns
export const policySourceColumns = [
  {
    title: '中央补贴(%)',
    dataIndex: 'centralFinance',
    key: 'centralFinance',
  },
  {
    title: '省级补贴(%)',
    dataIndex: 'provincialFinance',
    key: 'provincialFinance',
  },
  {
    title: '地方补贴(%)',
    dataIndex: 'cityFinance',
    key: 'cityFinance',
  },
  {
    title: '县级补贴(%)',
    dataIndex: 'countyFinance',
    key: 'countyFinance',
  },
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
  },
];
// 保费来源商业性columns
export const businessSourceColumns = [
  {
    title: '农户自缴(%)',
    dataIndex: 'farmersFinance',
    key: 'farmersFinance',
    width: '25%',
  },
  {
    title: '其他补贴(%)',
    dataIndex: 'otherFinance',
    key: 'otherFinance',
    width: '25%',
  },
];
