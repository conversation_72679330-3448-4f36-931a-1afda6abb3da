<template>
  <div class="edit-farmer-list p-[14px]">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: pxToRem(110) } }">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="分户被保险人" name="farmerName">
                <a-input v-model:value="formState.farmerName" placeholder="请输入分户被保险人" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="证件号码" name="certificateNo">
                <a-input v-model:value="formState.certificateNo" placeholder="请输入证件号码" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="联系方式" name="mobileTelephone">
                <a-input v-model:value="formState.mobileTelephone" placeholder="请输入联系方式" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px mt-[4px]">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="submit">查询</a-button>
      </div>
    </div>
    <div class="p-[16px] bg-[white] rounded-[6px] mt-[14px]">
      <div class="pb-[16px] flex items-center">
        <div class="text-[16px] font-bold">查询结果</div>
      </div>
      <div class="mb-[14px] text-[rgba(0,0,0,.6)]">涉及客户信息问题{{ tipState.customerRuleCount || 0 }}户，地块信息问题{{ tipState.landRuleCount || 0 }}户，其他问题{{ tipState.otherRuleCount || 0 }}户</div>
      <a-table :data-source="dataSource" :columns="columns" :pagination="pagination" :bordered="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key === 'farmerName'">
            <div>{{ text }} <a-tag v-if="record.accurateBigFarmer === 'Y'" color="green">大户</a-tag></div>
          </template>
          <template v-if="column.key === 'ruleContent'">
            <a-tooltip placement="topLeft" arrow-point-at-center>
              <template #title>
                <div v-html="(text || '').replace(/\n/g, '<br>')" />
              </template>
              <div class="max-w-[700px] truncate">{{ (text || '').replace(/\n/g, '') }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'operation'">
            <AuthButton code="dangerEdit" type="link" size="small" @click="handleEdit(record as RiskFarmerList)">批改</AuthButton>
            <AuthButton code="dangerView" type="link" size="small" @click="handleView(record as RiskFarmerList)">查看</AuthButton>
            <AuthButton code="dangerDel" type="link" size="small" @click="handleDel(record as RiskFarmerList)">删除</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TableColumnProps } from 'ant-design-vue';
import type { SearchFormState, RiskFarmerList } from '../endorseModify/endorseModify.d';
import { $postOnClient, usePost } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { message } from '@/components/ui/Message';
import { pxToRem } from '@/utils/tools';
import { SUCCESS_CODE } from '~/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';

const getListReq = await usePost<{ list: RiskFarmerList[]; total: number; size: number; current: number }>('/api/endorse/getFarmerRiskRuleList');

const { gateWay, service } = useRuntimeConfig().public || {};

const route = useRoute();
const formState = reactive<SearchFormState>({
  certificateNo: '', // 证件号码
  mobileTelephone: '', // 联系方式
  farmerName: '', // 被保险人名称
});
const formReset = ref(0);
const columns: TableColumnProps[] = [
  { title: '序号', dataIndex: 'index', key: 'index' },
  { title: '分户被保险人', dataIndex: 'farmerName', key: 'farmerName' },
  { title: '证件号码', dataIndex: 'certificateNo', key: 'certificateNo' },
  { title: '风险提示内容', dataIndex: 'ruleContent', key: 'ruleContent' },
  { title: '操作', width: pxToRem(160), dataIndex: 'operation', key: 'operation', fixed: 'right' },
];

const dataSource = ref<RiskFarmerList[]>([]);

const refresh = async () => {
  await nextTick();
  getList();
};

const { pagination } = usePagination(refresh);

const getList = async () => {
  const params = {
    endorseApplyNo: route.query.endorseApplyNo as string,
    farmerName: formState.farmerName || '',
    certificateNo: formState.certificateNo || '',
    mobileTelephone: formState.mobileTelephone || '',
    pageSize: pagination.pageSize || 10,
    pageNum: pagination.current || 1,
  };
  try {
    const res = await getListReq.fetchData(params);
    if (res?.code === '000000') {
      dataSource.value = res?.data?.list || [];
      pagination.current = res?.data?.current || 1;
      pagination.pageSize = res?.data?.size || 10;
      pagination.total = res?.data?.total || 0;
    } else {
      message.error(res?.msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

const reset = () => {
  formState.farmerName = '';
  formState.certificateNo = '';
  formState.mobileTelephone = '';
  formReset.value = formReset.value + 1;
};

const submit = () => {
  pagination.current = 1;
  refresh();
};
const router = useRouter();
const handleEdit = (record: RiskFarmerList) => {
  router.push({
    path: '/endorseFarmerInfo',
    query: {
      mode: 'edit',
      farmerlistNo: record.farmerlistNo,
      endorseApplyNo: route.query.endorseApplyNo,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
      ruleType: '0',
    },
  });
};
const handleView = (record: RiskFarmerList) => {
  router.push({
    path: '/endorseFarmerInfo',
    query: {
      mode: 'view',
      farmerlistNo: record.farmerlistNo,
      endorseApplyNo: route.query.endorseApplyNo,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
      ruleType: '0',
    },
  });
};
const handleDel = async (record: RiskFarmerList) => {
  Modal.confirm({
    title: '提醒',
    content: `是否确认删除分户被保险人【${record.farmerName}】`,
    async onOk() {
      try {
        const params = {
          idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
          endorseApplyNo: route.query.endorseApplyNo, //批改单号
        };
        const res = await $postOnClient(`${gateWay}${service.farmer}/edrList/deleteFarmerList`, params);
        if (res?.code === SUCCESS_CODE) {
          message.success(res.msg);
          refresh();
          fetchTip();
        } else {
          message.error(res?.msg || '删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
  });
};
const tipState = reactive({
  customerRuleCount: 0,
  landRuleCount: 0,
  otherRuleCount: 0,
});
const fetchTip = async () => {
  try {
    const url = `${gateWay}${service.farmer}/insrSpltPlyEdrSumy/getEdrFarmerListSummaryAndRuleInfo`;
    const formData = new FormData();
    formData.append('endorseApplyNo', route.query.endorseApplyNo as string);
    formData.append('ruleType', '0');
    const res = await $postOnClient<{ riskFarmerRuleInfo: { customerRuleCount: number; landRuleCount: number; otherRuleCount: number } }>(url, formData);
    if (res && res.code === SUCCESS_CODE) {
      tipState.customerRuleCount = res.data?.riskFarmerRuleInfo?.customerRuleCount;
      tipState.landRuleCount = res.data?.riskFarmerRuleInfo?.landRuleCount;
      tipState.otherRuleCount = res.data?.riskFarmerRuleInfo?.otherRuleCount;
    }
  } catch (e) {
    console.log(e);
  }
};

await getList();
await fetchTip();

onActivated(() => {
  reset();
  pagination.current = 1;
  refresh();
  fetchTip();
});
</script>
