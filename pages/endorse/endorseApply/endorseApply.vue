<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row>
            <a-col :span="8">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保险起期" name="insureDate" :label-col="{ style: { width: pxToRem(110) } }">
                <a-range-picker v-model:value="searchFormState.insureDate" :disabled="disabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保单号" name="policyNo">
                <a-input v-model:value.trim="searchFormState.policyNo" placeholder="请输入" allow-clear @blur="blurInput" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col v-show="expand" :span="8">
              <a-form-item label="被保险人" name="insuredName">
                <a-input v-model:value.trim="searchFormState.insuredName" :disabled="disabled" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col v-show="expand" :span="16">
              <a-form-item label="标的" name="lastRiskType">
                <RiskCodeSelect v-model:value="searchFormState.lastRiskType" :disabled="disabled" :department-code="searchFormState.departmentCode" :show-search="false" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col v-show="expand" :span="8">
              <a-form-item label="创建日期" name="createDate">
                <a-range-picker v-model:value="searchFormState.createDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="disabled" />
              </a-form-item>
            </a-col>
            <a-col v-show="expand" :span="12">
              <a-form-item label="验标状态" name="riskCheckStatus" class="mr-[10px]">
                <check-box-group v-model:checked-list="searchFormState.riskCheckStatus" :disabled="disabled" :options="verifyTargetOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-10px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'insuranceBeginDate'">{{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }} </template>
          <template v-if="['marketproductName', 'riskWholeAddress', 'insuredName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <!-- 操作区域 -->
            <a-space :size="1">
              <a-button type="link" @click="handlerEnterCorrecting(record)">进入批改</a-button>
              <a-button type="link" @click="handlerNewInsurance(record.applyPolicyNo, record.policyNo)">最新保单</a-button>
              <a-button type="link" @click="handlerCorrectingHistory(record.policyNo)">批改历史</a-button>
              <a-button type="link" @click="handlerClaimsRecord(record.policyNo)">理赔记录</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 弹窗 -->
    <a-modal v-model:open="correctingModal" title="按模块批改" @ok="handleCorrectingOk">
      <a-radio-group v-model:value="moduleValue" class="w-[100%]">
        <a-radio v-for="(item, index) in sceneList" :key="index" :value="item.endorseScene">{{ item.endorseSceneName }}</a-radio>
      </a-radio-group>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import { sceneEnums } from '../endorseModify/constant';
import type { SearchFormState, DataType } from './endorseApply.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $postOnClient, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { pxToRem } from '@/utils/tools';
import FormFold from '@/components/ui/FormFold.vue';
// 定义表格列
const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentName' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '产品名称', dataIndex: 'marketproductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '标的信息', dataIndex: 'riskTypeName' },
  { title: '整单保费', dataIndex: 'totalActualPremium' },
  { title: '保险起止期', dataIndex: 'insuranceBeginDate' },
  { title: '标的地址', dataIndex: 'riskWholeAddress' },
  { title: '批改次数', dataIndex: 'endorsementTimes' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 验标状态选项
const verifyTargetOptions = ref<{ label: string; value: string }[]>([
  { label: '验标中', value: '2' },
  { label: '验标完成', value: '3' },
  { label: '未验标', value: '0' },
  { label: '验标审核中', value: '1' },
]);
const { userInfo } = useUserStore(); // 默认用户信息
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : '')); // 默认机构编码
const correctingModal = ref<boolean>(false); // 批改弹窗
const moduleValue = ref<string>(''); // 批改模块选择值
const router = useRouter();
// 查询表单和状态
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containLowDepartment: true, // 是否包含下级
  lastRiskType: '', // 标的类型
  insureDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 保单生成日期
  riskCheckStatus: ['0', '1', '2', '3'], // 验标状态
  insuredName: '', // 被保险人名称
  policyNo: '', // 保单号
  createDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 创建日期
});
// 接口相关配置
const { gateWay, service } = useRuntimeConfig().public || {};
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
  searchForm.value.validate(['departmentCode']);
};
const dataSource = ref<Record<string, string>[]>([]); // 表格数据
const loading = ref<boolean>(false); // 表格loading
const currentItemInfo = ref<Record<string, string>>({}); // 点击当前列表数据对象
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
// 搜索项是否展开
const expand = ref(true);
// 列表请求
const getListReq = await usePost<DataType>(`${gateWay}${service.endorse}/web/policy/list`);
// 列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    // 接口所需要参数
    const params = {
      policyNo: searchFormState.policyNo, // 投保单号
      containLowDepartment: searchFormState.containLowDepartment, // 是否包含下级
      departmentCode: searchFormState.departmentCode, // 机构编码
      insuredName: searchFormState.insuredName, // 被保险人
      inputStartDate: searchFormState.createDate?.[0] || '', // 创建时间开始时间
      inputEndDate: searchFormState.createDate?.[1] || '', // 创建时间结束时间
      riskCheckStatus: searchFormState.riskCheckStatus, // 验标状态
      insuranceStartDate: searchFormState.insureDate?.[0] || '', // 保险开始时间
      insuranceStartDateEnd: searchFormState.insureDate?.[1] || '', // 保险开结束时间
      lastRiskType: searchFormState.lastRiskType, // 选中的最后一级标的类型
      pageNum: pagination.current, // 页码
      pageSize: pagination.pageSize, // 每页数
    };
    const res = await getListReq.fetchData({ policyQueryReq: params });
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 分页处理
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value.resetFields();
  disabled.value = false;
  refresh();
};
// 场景列表
const sceneList = ref<Record<string, string>[]>([]);
// 进入批改
const handlerEnterCorrecting = async (currentInfo: Record<string, string>) => {
  const fetchUrl = gateWay + service.endorse + '/web/metadata/endorseScene/list';
  const params = {
    endorseSceneQueryReq: {
      marketproductCode: currentInfo.marketproductCode, // 产品编码
      productClass: currentInfo.productClass, // 产品大类
    },
  };
  const res = await $postOnClient<Record<string, string>[]>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    sceneList.value = res.data;
    moduleValue.value = '';
    correctingModal.value = true;
    currentItemInfo.value = currentInfo;
  } else {
    message.warning(res?.msg);
  }
};
// 最新保单
const handlerNewInsurance = (applyPolicyNo: string, policyNo: string) => {
  router.push({
    path: '/policyDetails',
    query: { applyPolicyNo, policyNo },
  });
};
// 批改历史
const handlerCorrectingHistory = (policyNo: string) => {
  router.push({
    name: 'endorseHistory',
    query: {
      policyNo,
    },
  });
};
// 理赔记录
const handlerClaimsRecord = (policyNo: string) => {
  router.push({
    name: 'claimRecord',
    query: { policyNo },
  });
};
// 关闭弹窗
const handleCorrectingOk = () => {
  correctingModal.value = false;
  allowModify();
};
// 判断是否可进入批改
const allowModifyReq = await usePost<{ isEndorsable: boolean; reason: string }>(`${gateWay}${service.endorse}/web/endorse/enter-into-endorse`);
const allowModify = async () => {
  loading.value = true;
  try {
    // 接口所需要参数
    const params = {
      policyNo: currentItemInfo.value.policyNo, // 保单号
      enterIntoEndorseScene: sceneEnums?.[moduleValue.value]?.apply, // 场景
    };

    const res = await allowModifyReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      // 进入批改
      if (res.data?.isEndorsable) {
        const routes: { [key: string]: string } = {
          '90001': 'endorseModify', // 整单批改
          '00006': 'surrenderModify', // 退保
          '00017': 'cancelModify', // 注销
          '90003': 'premiumSource', // 保费来源
        };
        const routeName = routes[moduleValue.value];
        if (routeName) {
          router.push({
            name: routeName,
            query: {
              endorseApplyNo: currentItemInfo.value.policyNo,
              endorseScene: moduleValue.value,
              departmentCode: currentItemInfo.value.departmentCode, // 机构
              productCode: currentItemInfo.value.marketproductCode, // 产品编码
            },
          });
        } else {
          message.warning('该功能暂未开发');
        }
      } else {
        message.warn(res?.data?.reason);
      }
    } else {
      message.warn(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const disabled = ref<boolean>(false);
// 单号输入后调接口获取单号类型和机构,禁用其他组件
const blurInput = async () => {
  if (searchFormState.policyNo) {
    disabled.value = true;
    searchFormState.insureDate = ['', ''];
    searchFormState.createDate = ['', ''];
    searchFormState.insuredName = '';
    searchFormState.lastRiskType = '';
    searchFormState.riskCheckStatus = ['0', '1', '2', '3'];
    const fetchUrl = gateWay + service.administrate + '/public/getBizTypeByBizNo';
    const params = {
      bizNo: searchFormState.policyNo,
    };
    const res = await $getOnClient<Record<string, string>>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      searchFormState.departmentCode = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    searchFormState.departmentCode = defaultDeptCode.value;
    searchFormState.insureDate = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    searchFormState.createDate = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  }
};
const route = useRoute();
watch(
  () => route.query,
  (val) => {
    if (val?.bizNo) {
      searchFormState.policyNo = val.bizNo as string;
      blurInput();
    }
  },
  { deep: true, immediate: true },
);
onMounted(() => {
  search();
});

onActivated(() => {
  refresh();
});
</script>

<style lang="less" scoped>
.ant-radio-wrapper {
  padding: 20px 10px;
  display: block;
  background-color: #f9fafa;
  width: 100%;
  margin-bottom: 10px;
}
</style>
