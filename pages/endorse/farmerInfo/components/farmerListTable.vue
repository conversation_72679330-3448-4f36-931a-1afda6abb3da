<template>
  <div class="relative">
    <div ref="containerDom" class="w-full h-[300px] relative" />
    <div id="customer_tooltips" class="bg-white text-[#333] py-[6px] px-[8px] inline-block rounded-[4px] text-[14px] leading-[18px] absolute hidden border border-[#E5E5E5] border-solid">ssss</div>
  </div>
  <div v-if="pageMode !== 'view'" class="mt-[10px] text-[#576B95] cursor-pointer w-fit" style="border-bottom: none" @click="addTagRecord">
    <VueIcon :icon="IconTongyongXinzengFont" class="cursor-pointer" />
    <span class="ml-[4px]">新增</span>
  </div>
</template>

<script lang="ts" setup>
import { ListTable, type CustomRenderFunctionArg, register } from '@visactor/vtable';
import type { TableColumnProps } from 'ant-design-vue';
import { createGroup } from '@visactor/vtable/es/vrender';
import { onMounted, shallowRef } from 'vue';
import { cloneDeep } from 'lodash-es';
import { IconTongyongXinzengFont } from '@pafe/icons-icore-agr-an';
import type { RenderTableTxtCellParams } from './farmerListTable.d';
import { validStrMax, validStrRequire, validInsuredQuantity, validAnimalAge, validEarTagNo, validUnitInsuredAmount, validTreeAge } from './farmerListTableRules';
import { theme } from '@/components/ui/Vtable/theme';
import kong from '@/assets/images/table/ic-ziliao-kong.svg';
import { createRedStar, createHeaderText, createLinkBtn, createBodyText, createCircleIcon } from '@/components/ui/Vtable/vrender';
import { FramerInputEditor } from '@/components/ui/Vtable/inputEditor';
import { FramerListEditor } from '@/components/ui/Vtable/listEditor';
import { FramerInputAndListEditor } from '@/components/ui/Vtable/inputAndListEditor';
import { getStringLength, getElementWidth } from '@/utils/tools';

const CIRCLE_FILLED_PATH = 'M512 64C264.58 64 64 264.58 64 512s200.58 448 448 448 448-200.58 448-448S759.42 64 512 64zm32 544h-64V256.02h64V608zm6.03 96v76.8h-76.8V704h76.8z';

type Options = { label: string; value: string };
type PageMode = 'view' | 'edit' | 'add';
const {
  columns = [],
  dataSource,
  unitOptions = [],
  farmerLandOptions = [],
  planCodeOptions = [],
  pageMode = 'view',
  landSourceType = [],
} = defineProps<{
  columns: TableColumnProps[];
  dataSource: unknown[];
  unitOptions: Options[];
  farmerLandOptions: Options[];
  planCodeOptions: Options[];
  pageMode: PageMode;
  landSourceType: Options[];
}>();
const vTableInstance = shallowRef(null);
const inputEditorVal = shallowRef(null);
const listEditorVal = shallowRef(null);
const inputAndListEditorVal = shallowRef(null);
const emit = defineEmits(['drawLand', 'handleApply']);
const route = useRoute();
const containerDom = ref(null);

const createCenterGroup = ({ width, height }: { width: number; height: number }) => {
  return createGroup({
    height,
    width,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
  });
};

// 基础渲染函数
const renderBaseCell = ({ container, content, errorTips = '', status = '', height = 32, width, disable = false }) => {
  if (status === 'warning' || status === 'error') {
    const insideContainer = createGroup({
      height,
      width,
      display: 'flex',
      flexDirection: 'column',
      flexWrap: 'nowrap',
    });

    insideContainer.add(content);

    const tips = createBodyText({
      text: errorTips,
      config: {
        boundsPadding: [2, 10, 2, 10],
        fill: status === 'warning' ? '#faad14' : '#f03e3e',
      },
      disable,
    });

    insideContainer.add(tips);
    container.add(insideContainer);
  } else {
    container.add(content);
  }
};

// 文本单元格渲染
const renderTableTxtCell = ({ args, key }: RenderTableTxtCellParams) => {
  const { table, row, col, rect } = args;
  const { height, width } = rect || table.getCellRect(col, row);
  const record = table.getRecordByRowCol(col, row);

  const outsideContainer = createGroup({
    height,
    width,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
  });

  const content = createBodyText({
    text: record[key].value || ' ',
    config: {
      boundsPadding: [2, 10], // 写法同css的padding
    },
    disable: record[key].disable,
  });

  renderBaseCell({
    container: outsideContainer,
    content,
    errorTips: record[key].statusTips,
    status: record[key].status,
    width,
    height,
    disable: record[key].disable,
  });

  return {
    rootContainer: outsideContainer,
    renderDefault: false,
  };
};

// 下拉选择单元格渲染
const renderTableSelectCell = ({ args, key, options }: { args: CustomRenderFunctionArg; key: string; options: Options[] }) => {
  const { table, row, col, rect } = args;
  const { height, width } = rect || table.getCellRect(col, row);
  const record = table.getRecordByRowCol(col, row);

  const outsideContainer = createGroup({
    height,
    width,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
  });

  const unit = options.find((val) => val.value === record[key].value);
  const content = createBodyText({
    text: unit?.label || ' ', // 空格占一行，错误提示不会往上飘
    config: {
      boundsPadding: [2, 10],
    },
  });

  renderBaseCell({
    container: outsideContainer,
    content,
    errorTips: record[key].statusTips,
    status: record[key].status,
    width,
    height,
    disable: record[key].disable,
  });

  return {
    rootContainer: outsideContainer,
    renderDefault: false,
  };
};

// 数量输入单元格渲染
const renderTableInsuredQuantityCell = (args: CustomRenderFunctionArg, key: string, unitOptions, disable = false) => {
  const { table, row, col, rect } = args;
  const { height, width } = rect || table.getCellRect(col, row);
  const record = table.getRecordByRowCol(col, row);

  const outsideContainer = createGroup({
    height,
    width,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
  });

  const content = createGroup({
    height: height - 20,
    width,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  });

  // 数量
  const txt = createBodyText({
    text: record[key].value || ' ', // 用一个空格作占位符，不然渲染有问题
    config: {
      boundsPadding: [0, 2, 0, 10],
    },
    disable,
  });

  // 单位
  const unit = unitOptions.find((val) => val.value === record[key].unitValue);
  const unitTxt = createBodyText({
    text: unit?.label || ' ',
    config: {
      boundsPadding: [0, 10, 0, 0],
    },
    disable,
  });

  content.add(txt);
  content.add(unitTxt);

  renderBaseCell({
    container: outsideContainer,
    content,
    errorTips: record[key].statusTips,
    status: record[key].status,
    width,
    height,
    disable,
  });

  return {
    rootContainer: outsideContainer,
    renderDefault: false,
  };
};

const defaultTableOption = {
  theme: Object.assign({ scrollStyle: { visible: 'always' } }, theme),
  widthMode: 'standard',
  autoFillWidth: true, // 列数不够，自动撑宽列宽，使得表格占满整个容器
  editCellTrigger: 'click', // 单击进入编辑状态
  frozenColCount: 1,
  rightFrozenColCount: 1, // 右侧冻结列
  emptyTip: {
    text: '暂无数据',
    textStyle: {
      color: 'rgba(0,0,0,0.60)',
    },
    icon: {
      width: 86,
      height: 86,
      image: kong,
    },
  },
  menu: {
    contextMenuItems: ['修改单元格'],
  },
  columns: [],
  records: [],
};

// 删除某一行
const deleteRecordsByColRow = ({ row, col }: { row: number; col: number }) => {
  if (vTableInstance.value.records.length < 2) {
    message.warning('至少保留一行');
    return;
  }
  const recordIndex = vTableInstance.value.getRecordShowIndexByCell(col, row);
  vTableInstance.value.deleteRecords([recordIndex]);
};

// 重新渲染表格
const reRenderCells = () => {
  vTableInstance.value.renderWithRecreateCells();
};

// customerLayout不支持使用vtable注册的icon（官方文档有误，作者已在github上澄清）
// 自己实现一个简单的tooltips
const showTips = async ({ top, right, text }: { top: number; right: number; text: string }) => {
  const dom = document.getElementById('customer_tooltips');
  dom.innerHTML = text;
  dom.style.display = 'block';
  dom.style.visibility = 'hidden';
  const rect = dom.getBoundingClientRect();
  dom.style.left = right - Math.floor(rect.width) + 'px';
  dom.style.top = top - Math.floor(rect.height) - 10 + 'px';
  dom.style.visibility = 'visible';
};

const hiddenTips = () => {
  const dom = document.getElementById('customer_tooltips');
  dom.style = '';
};

// 获取勾选
const getCheckboxSelected = () => {
  // 1.4.1版本之后getCheckboxState将不再支持field参数，可能是bug，暂时使用getCheckedIndexes过滤
  const infos = vTableInstance.value.getCheckboxState('isCheck') || [];
  const length = infos.length;
  const records = vTableInstance.value.records || [];
  const idList = [];
  for (let i = 0; i < length; i++) {
    if (infos[i]) {
      idList.push(records[i]);
    }
  }
  return idList;
};

// 手动校验规则
const validate = () => {
  return new Promise((resolve, reject) => {
    const records = vTableInstance.value.records;
    const columns = vTableInstance.value.columns;
    let isTrue = true;
    for (const item of records) {
      for (const col of columns) {
        if (item[col.field] && col.rule) {
          for (const rule of col.rule) {
            const res = rule.validator(item[col.field]);
            if ((res !== true && !res.valid) || !res) {
              const message = res.message || rule.message;
              item[col.field].status = 'error';
              item[col.field].statusTips = message;
              isTrue = false;
              break;
            }
          }
        }
      }
    }
    if (isTrue) {
      resolve({ valid: true, message: '校验通过' });
    } else {
      vTableInstance.value.setRecords(records);
      reject({ valid: false, message: '清单信息表格字段校验不通过，请仔细检查或拖动下滚动条进行检查' });
    }
  });
};

const getRecords = () => {
  const recordes = cloneDeep(vTableInstance.value.records);
  // 删除vtable新增的字段
  const list = recordes.map((val) => {
    if (val.id !== undefined) {
      delete val.id;
    }
    if (val.isCheck) {
      delete val.isCheck;
    }
    return val;
  });
  return list;
};

defineExpose({
  getCheckboxSelected,
  validate,
  getRecords,
});

const detaultWidth = {
  action: 150,
  landCode: 120,
  mapAreaCode: 200,
  landName: 150,
  agrLandClassification: 150,
  authName: 150,
  authCertificateNo: 180,
  confirmationLandCode: 150,
  landArea: 120,
  insuredQuantity: 210,
  animalAge: 100,
  startEarTagNo: 150,
  endEarTagNo: 150,
  planCode: 150,
  unitInsuredAmount: 150,
  forestCertificationNo: 150,
  forestSpotCode: 150,
  treeAge: 100,
  forestAverageDensity: 100,
  landSource: 120,
};

// 根据单元格内容和默认宽，确定单元格的宽度
const getLargestWidth = (dataSource, dataIndex) => {
  let maxWidth = 0;
  for (const item of dataSource) {
    const text = item[dataIndex].value;
    const textLength = getStringLength(text);
    const statusTipsLength = getStringLength(item[dataIndex].statusTips);
    if (textLength > maxWidth) {
      maxWidth = textLength;
    }
    if (statusTipsLength > maxWidth) {
      maxWidth = statusTipsLength;
    }
  }
  return maxWidth;
};

const setDefaultWidth = (columns, dataSource) => {
  const dataSourceLength = (dataSource || []).length;
  const nuewColumns = columns.map((val) => {
    if (dataSourceLength <= 1000) {
      // 行数小于1000时，设置width=auto，不会很影响性能，并且单元格宽度能随内容长度变宽
      val.minWidth = val.dataIndex ? detaultWidth[val.dataIndex] : 200; // 设置minWidth，编辑单元格完后会刷新当前列的数据，其他列不会刷新，如果不设置，则会整个表格的单元格的刷新，width设置了auto，最好加下minWidth
      val.width = 'auto'; // 如果直接设置了width具体值，编辑单元格完后会刷新当前单元格的数据，性能可进一步提升
    } else {
      // 大于1000时，设置width=auto则会很耗性能，单元格宽度不能随内容长度变宽
      // 需要固定住width，使得编辑单元格完后会刷新当前单元格的数据，其他单元格不会刷新
      // 根据内容最长值设置width
      if (['action'].includes(val.dataIndex)) {
        val.minWidth = detaultWidth[val.dataIndex];
        val.width = detaultWidth[val.dataIndex];
      } else {
        const width = getLargestWidth(dataSource, val.dataIndex);
        val.minWidth = Math.max(width * 10 + 20, detaultWidth[val.dataIndex]);
        val.width = Math.max(width * 10 + 20, detaultWidth[val.dataIndex]);
      }
    }
    return val;
  });

  if (dataSource.length > 1000) {
    // 验证表格宽度是否已经撑满整个容器了，如果没有则需要重新调整宽度
    const total = nuewColumns.reduce((sum, current) => {
      // 确保当前值是数字类型
      const num = Number(current.width);
      // 如果是有效数字则加入总和，否则忽略
      return sum + (Number.isNaN(num) ? 0 : num);
    }, 0);
    const domWidth = getElementWidth(containerDom.value);
    if (domWidth - total > 0) {
      // 说明有剩余空间
      const num = domWidth - total;
      const columnsLength = nuewColumns.length;
      const average = Math.floor(num / columnsLength);
      nuewColumns.forEach((val) => {
        if (['action'].includes(val.dataIndex)) {
          val.width = val.width + num - average * (columnsLength - 1);
          val.minWidth = val.minWidth + num - average * (columnsLength - 1);
        } else {
          val.width = val.width + average;
          val.minWidth = val.minWidth + average;
        }
      });
    }
  }

  return nuewColumns;
};

const setTableColumns = (newColumns: TableColumnProps[]) => {
  const tableColumns = newColumns.map((val) => {
    const newVal = {
      field: val.dataIndex,
      title: val.title,
      minWidth: val.minWidth,
      width: val.width,
    };

    if (val.require) {
      // 必填，渲染红星星
      newVal.headerCustomLayout = (args: CustomRenderFunctionArg) => {
        const { table, row, col, rect } = args;
        const { height, width } = rect || table.getCellRect(col, row);
        const container = createCenterGroup({ width, height });
        const redStar = createRedStar([0, 4, 0, 0]);
        const title = createHeaderText(args.dataValue);
        container.add(redStar);
        container.add(title);

        return {
          rootContainer: container,
          renderDefault: false,
          enableCellPadding: true,
        };
      };
    }
    switch (val.dataIndex) {
      case 'action':
        newVal.disableHeaderSelect = true;
        newVal.disableSelect = true;
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          console.log('action');
          const { table, row, col, rect } = args;
          const { height, width } = rect || table.getCellRect(col, row);
          const record = table.getRecordByRowCol(col, row);
          const container = createCenterGroup({ width, height });
          const delBtn = createLinkBtn({ text: '删除' });
          delBtn.addEventListener('click', () => {
            deleteRecordsByColRow({ row, col });
            // 强制更新
            // 节点会被缓存
            reRenderCells();
          });
          container.add(delBtn);

          if (record?.landDeviationsStatus?.value === '1') {
            const setBtn = createLinkBtn({ text: '调整地块' });
            setBtn.addEventListener('click', () => {
              emit('drawLand', record?.landCode?.value || '');
            });
            container.add(setBtn);
          }
          if (record?.authStatus?.value === 0) {
            const btnContainer = createCenterGroup({ width: 90, height: 14 });
            const setBtn = createLinkBtn({ text: '发起流转' });
            const iconElement = createCircleIcon({ path: CIRCLE_FILLED_PATH });
            btnContainer.add(setBtn);
            btnContainer.add(iconElement);
            setBtn.addEventListener('click', () => {
              emit('handleApply', [record]);
            });
            iconElement.on('mouseenter', () => {
              const { top, right } = vTableInstance.value.getCellRelativeRect(col, row);
              showTips({ top, right, text: '该地块确权人与当前分户被保人不同，请确认土地流转情况' });
            });
            iconElement.on('mouseleave', () => {
              hiddenTips();
            });
            container.add(btnContainer);
            // container.appendChild(popTip);
          } else if (record.authStatus && (record.authStatus.value === 1 || record.authStatus.value === 3)) {
            // 流转按钮描述-流转状态（0 发起流转,1 流转中,2 流转退回,3,已流转,4 数据初始状态）
            const text = record.authStatus.value === 1 ? '流转中' : '已流转';
            const setBtn = createLinkBtn({ text });
            // !!!!!感觉这里有问题
            if (record.authStatus.value === 0) {
              setBtn.addEventListener('click', () => {
                emit('handleApply', record.authCertificateNo.value, record.landCode.value, record.authStatus.value);
              });
            }
            if (record.authStatus.value === 3) {
              setBtn.addEventListener('click', () => {
                emit('handleApply', [record], true);
              });
            }
            container.add(setBtn);
          }

          // 新增编辑按钮，提示用户编辑方法
          // const setEditBtn = createLinkBtn({ text: '编辑' });
          // setEditBtn.on('mouseenter', () => {
          //   const { top, right } = vTableInstance.value.getCellRelativeRect(col, row);
          //   showTips({ top, right, text: '单击单元格或右键单元格即可进行编辑' });
          // });
          // setEditBtn.on('mouseleave', () => {
          //   hiddenTips();
          // });
          // container.add(setEditBtn);

          return {
            rootContainer: container,
            renderDefault: false,
          };
        };

        break;
      case 'landCode':
        // 地块编码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'landCode' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(32, val.require), message: '最多32个字符' }];
        break;
      case 'mapAreaCode':
        // 遥感地图编码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'mapAreaCode' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(32, val.require), message: '最多32个字符' }];
        break;
      case 'landName':
        // 地块名称
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'landName' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(100, val.require), message: '最多100个字符' }];
        break;
      case 'agrLandClassification':
        // 农业用地分类
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableSelectCell({ args, key: 'agrLandClassification', options: farmerLandOptions });
        };
        newVal.editor = pageMode !== 'view' ? 'list_editor' : undefined;
        newVal.options = farmerLandOptions;
        if (val.require) {
          newVal.rule = [{ validator: validStrRequire, message: '请选择农业用地分类' }];
        }
        break;
      case 'landSource':
        // 土地来源
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableSelectCell({ args, key: 'landSource', options: landSourceType });
        };
        newVal.editor = pageMode !== 'view' ? 'list_editor' : undefined;
        newVal.options = landSourceType;
        if (val.require) {
          newVal.rule = [{ validator: validStrRequire, message: '请选择土地来源' }];
        }
        break;
      case 'authName':
        // 地块确权人
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'authName' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(100, val.require), message: '最多100个字符' }];
        break;
      case 'authCertificateNo':
        // 确权人证件号码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'authCertificateNo' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(32, val.require), message: '最多32个字符' }];
        break;
      case 'confirmationLandCode':
        // 确权人确权编码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'confirmationLandCode' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(32, val.require), message: '最多32个字符' }];
        break;
      case 'landArea':
        // 地块面积
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'landArea' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        if (val.require) {
          newVal.rule = [{ validator: validStrRequire, message: '请选择地块面积' }];
        }
        break;
      case 'insuredQuantity':
        // 投保数量
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableInsuredQuantityCell(args, 'insuredQuantity', unitOptions);
        };
        newVal.editor = pageMode !== 'view' ? 'input_list_editor' : undefined;
        newVal.options = unitOptions;
        newVal.rule = [{ validator: validInsuredQuantity(val.require) }];
        break;
      case 'animalAge':
        // 畜龄
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'animalAge' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validAnimalAge(val.require), message: '仅支持录入整数，最大3位' }];
        break;
      case 'startEarTagNo':
        // 起耳标号
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'startEarTagNo' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validEarTagNo(val.require) }];
        break;
      case 'endEarTagNo':
        // 止耳标号
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'endEarTagNo' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validEarTagNo(val.require) }];
        break;
      case 'planCode':
        // 险种编码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableSelectCell({ args, key: 'planCode', options: planCodeOptions });
        };
        newVal.editor = pageMode !== 'view' ? 'list_editor' : undefined;
        newVal.options = planCodeOptions;
        if (val.require) {
          newVal.rule = [{ validator: validStrRequire, message: '请输入险种编码' }];
        }
        break;
      case 'unitInsuredAmount':
        // 单位保额
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'unitInsuredAmount' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validUnitInsuredAmount(val.require) }];
        break;
      case 'forestCertificationNo':
        // 林权证号
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'forestCertificationNo' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(64, val.require), message: '最多64个字符' }];
        break;
      case 'forestSpotCode':
        // 林斑编码
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'forestSpotCode' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validStrMax(64, val.require), message: '最多32个字符' }];
        break;
      case 'treeAge':
        // 树龄
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'treeAge' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validTreeAge(val.require) }];
        break;
      case 'forestAverageDensity':
        // 平均密度
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'forestAverageDensity' });
        };
        newVal.editor = pageMode !== 'view' ? 'input_editor' : undefined;
        newVal.rule = [{ validator: validTreeAge(val.require) }];
        break;
      case 'farmerRiskIndexNo':
        // 农户标的索引编号
        newVal.customLayout = (args: CustomRenderFunctionArg) => {
          return renderTableTxtCell({ args, key: 'farmerRiskIndexNo' });
        };
        newVal.editor = undefined;
        break;
      default:
        break;
    }

    return newVal;
  });

  tableColumns.unshift({
    field: 'isCheck',
    title: '',
    maxWidth: 40,
    headerType: 'checkbox',
    cellType: 'checkbox',
    disableHeaderSelect: true,
    disableSelect: true,
  });
  const tableOptions = Object.assign({}, defaultTableOption, { columns: tableColumns });

  vTableInstance.value.updateOption(tableOptions);

  return tableOptions;
};

const setTableRecord = () => {
  const dataSourceCopy = cloneDeep(dataSource);
  // 设置不可编辑状态
  dataSourceCopy.forEach((val) => {
    const keys = Object.keys(val);
    // 是否四川模版(有土地来源)
    const hasLandSource = keys.includes('landSource');
    for (const item of keys) {
      if ((item === 'authName' && !hasLandSource) || item === 'authCertificateNo' || item === 'confirmationLandCode' || item === 'landArea') {
        val[item].disable = !!val.idFarmerlistBaseInfo?.value;
      }
    }
    // 新增一个isCheck字段
    const isDisable = !!(val.authStatus && val.authStatus.value?.toString() !== '0') || route.query.mode === 'add' || (val.authCertificateNo && !val.authCertificateNo.value);
    val.isCheck = {
      checked: false,
      disable: isDisable,
    };
  });
  vTableInstance.value.setRecords(dataSourceCopy);
};

watch(
  [() => columns, vTableInstance, () => dataSource, () => unitOptions, () => farmerLandOptions, () => planCodeOptions],
  ([columns, vTableInstance, dataSource, unitOptions, farmerLandOptions, planCodeOptions]) => {
    if (columns && columns.length && vTableInstance && Array.isArray(dataSource) && unitOptions && unitOptions.length && farmerLandOptions && farmerLandOptions.length && planCodeOptions && planCodeOptions.length) {
      const newColumns = setDefaultWidth(columns, dataSource);
      const options = setTableColumns(newColumns);
      inputEditorVal.value.stateColumns(options.columns);
      listEditorVal.value.stateColumns(options.columns);
      inputAndListEditorVal.value.stateColumns(options.columns);
      setTableRecord();
    }
  },
  {
    immediate: true,
    deep: 1,
  },
);

const resetCheckboxStatus = (col) => {
  for (let rowI = vTableInstance.value.columnHeaderLevelCount; rowI < vTableInstance.value.rowCount; rowI++) {
    const record = vTableInstance.value.getCellOriginRecord(col, rowI);
    if (record.isCheck.disable) {
      // 更新checkbox状态
      vTableInstance.value.stateManager.setCheckedState(col, rowI, 'isCheck', false);
      // 更新checkbox展示
      try {
        // 表格是动态加载，可能节点不存在
        vTableInstance.value.scenegraph.getCell(col, rowI).getChildByName('checkbox', true).setAttribute('checked', false);
      } catch (err) {
        console.error(err);
      }
    }
  }
};

const copyOnRecord = (index?: number) => {
  const records = vTableInstance.value.records;
  const copyTagRecord = cloneDeep(records[index || 0]);
  // 特殊处理,手动新增的允许编辑
  const keys = Object.keys(copyTagRecord);
  for (const item of keys) {
    if (copyTagRecord[item]?.disable) {
      copyTagRecord[item].disable = false;
    }
  }
  return copyTagRecord;
};

const addEmptyRecord = () => {
  const record = copyOnRecord();
  Object.keys(record)?.forEach((key) => {
    if (key === 'isCheck') {
      record[key].checked = false;
      record[key].disable = true;
    } else if (record[key]) {
      record[key].value = null;
      record[key].status = '';
      record[key].statusTips = '';
      record[key].unitValue = '';
    }
  });

  return record;
};

const addTagRecord = () => {
  const length = vTableInstance.value.records.length;
  const record = addEmptyRecord();
  vTableInstance.value.addRecord(record, length);
  vTableInstance.value.scrollToCell({ row: length });
};

onMounted(() => {
  vTableInstance.value = new ListTable(containerDom.value, defaultTableOption);

  const inputEditor = new FramerInputEditor();
  const listEditor = new FramerListEditor();
  const inputAndListEditor = new FramerInputAndListEditor();

  inputEditorVal.value = inputEditor;
  listEditorVal.value = listEditor;
  inputAndListEditorVal.value = inputAndListEditor;

  register.editor('input_editor', inputEditor);
  register.editor('list_editor', listEditor);
  register.editor('input_list_editor', inputAndListEditor);

  vTableInstance.value.on('dropdown_menu_click', (args) => {
    // const recordIndex = vTableInstance.value.getRecordShowIndexByCell(args.col, args.row);
    if (args.menuKey === '修改单元格') {
      vTableInstance.value.startEditCell(args.col, args.row);
    }
  });

  // 监听checkbox选中变化
  // 当点击了全选按钮，checkbox是disble的，则实时改为不勾选
  // 此方法是vtable作者推荐的写法，其中stateManager，scenegraph，getCell，getChildByName等API官方文档没有描述
  vTableInstance.value.on('checkbox_state_change', ({ col, row, checked }) => {
    const isHeader = row < vTableInstance.value.columnHeaderLevelCount;

    if (isHeader && checked) {
      // 点击的是表头的全选checkbox
      resetCheckboxStatus(col);
    }
  });

  // 单元格使用了layoutcustom自定义渲染，设置tooltip配置无效，需要手动展示tooktips
  // 当行数超过1000后，为了性能考虑，会固定单元格的宽度，导致单元格无法随内容的长度而变化，间接使用tooltip让用户看到全部信息
  vTableInstance.value.on('mouseenter_cell', ({ row, col }) => {
    if (row > 0) {
      const rect = vTableInstance.value.getVisibleCellRangeRelativeRect({ row, col });
      const record = vTableInstance.value.getCellOriginRecord(col, row);
      const column = vTableInstance.value.options.columns[col];
      if (['landCode', 'mapAreaCode', 'landName', 'authName', 'authCertificateNo', 'landArea', 'animalAge', 'startEarTagNo', 'endEarTagNo', 'unitInsuredAmount', 'forestCertificationNo', 'forestSpotCode', 'treeAge', 'forestAverageDensity'].includes(column.field)) {
        const text = record[column.field] ? record[column.field].value || '' : '';
        if (text) {
          vTableInstance.value.showTooltip(col, row, {
            content: text,
            referencePosition: { rect, placement: 'right' },
          });
        }
      }
    }
  });
});
</script>
