<template>
  <div>
    <template v-for="(item, index) in farmerList" :key="index">
      <div v-if="allowAddRiskFlag" class="flex items-center justify-between" :style="{ marginTop: index !== 0 ? pxToRem(10) : '0' }">
        <div class="title-tag">标的{{ index + 1 }}</div>
        <div v-if="farmerList && farmerList.length > 1 && route.query.mode !== 'view'" class="mt-[10px] text-[#576B95] cursor-pointer w-fit" @click="removeTargetRecord(index)">
          <VueIcon :icon="IconTongyongShanchuFont" />
          <span class="ml-[5px] mr-[16px]">删除</span>
        </div>
      </div>
      <div class="rounded p-16px bg-white relative z-10">
        <div class="grid grid-cols-3 gap-x-16px">
          <!-- 标的名称 -->
          <template v-if="item.riskName">
            <proxyFormItem v-slot="{ clearStatus }" :name="['farmerList', index, 'riskName', 'value']" :label-col="labelCol" :data="item.riskName" :options="riskNameOptions">
              <a-select v-model:value="item.riskName.value" :options="riskNameOptions" :disabled="!allowAddRiskFlag" @change="clearStatus" />
            </proxyFormItem>
          </template>
          <!-- 标的品种 -->
          <template v-if="item.riskBreed">
            <proxyFormItem v-slot="{ clearStatus }" :name="['farmerList', index, 'riskBreed', 'value']" :label-col="labelCol" :data="item.riskBreed" :options="findRiskChildren(item.riskName.value)">
              <a-select v-if="findRiskChildren(item.riskName.value).length > 0" v-model:value="item.riskBreed.value" :options="findRiskChildren(item.riskName.value)" allow-clear :loading="!riskNameOptions.value || !riskNameOptions.value.length" @change="clearStatus" />
              <a-input v-else v-model:value="item.riskBreed.value" @change="clearStatus" />
            </proxyFormItem>
          </template>
          <!-- 养殖地点 -->
          <template v-if="item.breedAddress">
            <proxyFormItem v-slot="{ clearStatus }" :name="['farmerList', index, 'breedAddress', 'value']" :label-col="labelCol" :data="item.breedAddress" :extra-rules="[{ max: 200, trigger: 'change' }]">
              <a-input v-model:value.trim="item.breedAddress.value" @change="clearStatus" />
            </proxyFormItem>
          </template>
          <!-- 养殖方式 -->
          <template v-if="item.breedMethod">
            <proxyFormItem v-slot="{ clearStatus }" :name="['farmerList', index, 'breedMethod', 'value']" :label-col="labelCol" :data="item.breedMethod" :options="farmer_breedtype_unit_code">
              <a-select v-model:value="item.breedMethod.value" :options="farmer_breedtype_unit_code" @change="clearStatus" />
            </proxyFormItem>
          </template>
          <!-- 养殖用途 -->
          <template v-if="item.breedPurpose">
            <proxyFormItem v-slot="{ clearStatus }" :name="['farmerList', index, 'breedPurpose', 'value']" :label-col="labelCol" :data="item.breedPurpose" :options="farmer_breedpurp_unit_code">
              <a-select v-model:value.trim="item.breedPurpose.value" :options="farmer_breedpurp_unit_code" allow-clear @change="clearStatus" />
            </proxyFormItem>
          </template>
          <div>
            <a-button v-if="false && allowCheckMapFlag" class="w-[100px]" type="primary" @click="jump2Map">查看地图</a-button>
            <a-button v-if="route.query.mode !== 'add' && !item.riskName.value?.startsWith('B')" class="ml-[8px]" type="primary" :loading="anDisabled" @click="openLandModal">发起土地流转</a-button>
          </div>
        </div>
        <ClientOnly>
          <FarmerListTable ref="farmerTable" :columns="columns" :data-source="item.tag" :unit-options="farmer_unit_code" :farmer-land-options="farmer_land_unit_code" :plan-code-options="planCodeOptions" :land-source-type="land_source_type" :page-mode="pageMode" @draw-land="drawLand" @handle-apply="handleApply" />
        </ClientOnly>
        <!-- <div v-if="route.query.mode !== 'view'" class="mt-[10px] text-[#576B95] cursor-pointer w-fit" style="border-bottom: none" @click="addTagRecord(item.tag)">
          <VueIcon :icon="IconTongyongXinzengFont" class="cursor-pointer" />
          <span class="ml-[4px]">新增</span>
        </div> -->
        <!-- 爱农宝流转确认 -->
        <a-modal v-model:open="anbVisible" title="爱农宝流转详情" :width="pxToRem(600)" :mask-closable="false" centered @ok="handleAnbOk">
          <div class="flex">
            <div class="pr-[16px]">分户被保险人：张丹丹</div>
            <!-- <VueIcon class="text-[12px] text-[#07C160] opacity-65" :icon="IconCheckCircleFilledFont" />
            <span class="ml-[4px] text-[#07C160]">已确认</span> -->
            <!-- <a-tag :bordered="false" :color="tagMap[text].color">
              <template #icon>
                <VueIcon :icon="tagMap[text].icon" />
              </template>
              {{ tagMap[text].text }}
            </a-tag> -->
            <a-tag :bordered="false" color="success">
              <template #icon>
                <VueIcon :icon="IconCheckCircleFilledFont" />
              </template>
              已确认
            </a-tag>
          </div>
          <div class="flex mt-[12px]">
            <div class="pr-[16px]">土地所有人：郭靖</div>
            <!-- <VueIcon class="text-[12px] text-[#E6AD1C] opacity-65" :icon="IconErrorCircleFilledFont" />
            <span class="ml-[4px] text-[#E6AD1C]">确认中</span> -->
            <a-tag :bordered="false" color="success">
              <template #icon>
                <VueIcon :icon="IconCheckCircleFilledFont" />
              </template>
              已确认
            </a-tag>
            <!-- <a-button type="link">撤回</a-button>
            <a-button type="link">重新发起</a-button> -->
            <span class="text-[#576B95] text-[14px] pl-[16px]" @click="handleRevoke(1)">撤回</span>
            <span class="text-[#576B95] text-[14px] pl-[16px]" @click="handleRevoke(2)">重新发起</span>
          </div>
        </a-modal>
        <a-modal v-model:open="openVisible" :width="pxToRem(450)" :ok-text="okText" @ok="handleConfirmOk">
          <div class="mb-[10px]">
            <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
            <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
          </div>
          <div class="text-[14px] text-[#333333] mb-[10px]" v-html="confirmText" />
        </a-modal>
      </div>
    </template>
    <div v-if="allowAddRiskFlag" class="bg-white mx-[-16px] mb-[-20px] pl-16px pt-14px">
      <a-button type="primary" ghost @click="addTargetRecord">新增一条新的标的</a-button>
    </div>
    <slot />
  </div>
</template>

<script setup lang="ts">
import { IconTongyongShanchuFont, IconCheckCircleFilledFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { cloneDeep, get, set } from 'lodash-es';
import type { TableColumnProps } from 'ant-design-vue';
import type { TargetRecordType } from '../../farmer.d';
import proxyFormItem from '../components/proxyFormItem.vue';
import FarmerListTable from '../components/farmerListTable.vue';
import { $getOnClient } from '@/composables/request';
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
// import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
// const tagMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
//   0: { color: 'success', text: '已确认', icon: IconCheckCircleFilledFont },
//   1: { color: 'orange', text: '已撤回', icon: IconErrorCircleFilledFont },
//   2: { color: 'processing', text: '确认中', icon: IconErrorCircleFilledFont },
//   3: { color: 'orange', text: '已退回', icon: IconErrorCircleFilledFont },
// };
const anbVisible = ref<boolean>(false);
const openVisible = ref<boolean>(false);
const confirmText = ref<string>('确定撤回流转吗？');
const okText = ref<string>('撤回');
// type Key = string | number;
const labelCol = { style: { width: pxToRem(80) } };
const farmerList = defineModel<{ [key: string]: { value: string | number; tag: unknown[] } }[]>('formerList');
// const selectedRow = defineModel<TargetRecordType[]>('selectedRow', { default: [] });
// const selectedRowKey = defineModel<Key[]>('selectedRowKey', { default: [] });
defineProps<{ allowAddRiskFlag: boolean; allowCheckMapFlag: boolean }>();
const pageMode = inject('pageMode');
const route = useRoute();
const farmerTable = ref(null);

const findRiskChildren = (_currentBreed: string) => {
  if (!Array.isArray(riskNameOptions.value)) {
    return [];
  }

  const parent = riskNameOptions.value.find((k) => k.value === _currentBreed);
  if (parent && parent.children) {
    return parent.children;
  }
  return [];
};

// const hasRiskBreedOptions = computed(() => Array.isArray(riskBreedOptions.value) && riskBreedOptions.value.length > 0);
const handleAnbOk = () => {};
const handleConfirmOk = () => {};
const handleRevoke = (type: number) => {
  if (type === 1) {
    confirmText.value = '确定撤回流转吗？';
    okText.value = '撤回';
  } else {
    confirmText.value = '是否重新发起流转？';
    okText.value = '确定';
  }
  openVisible.value = true;
};
// const addTagRecord = (target: RefMapType[]) => {
//   const copyTagRecord = cloneDeep(target[0]);
//   Object.keys(copyTagRecord)?.forEach((key) => {
//     if (copyTagRecord[key]) {
//       copyTagRecord[key].value = null;
//       copyTagRecord[key].status = '';
//       copyTagRecord[key].statusTips = '';
//       copyTagRecord[key].unitValue = '';
//     }
//   });
//   target.push(copyTagRecord);
// };
const emit = defineEmits(['handleApply']);
// 点击流转按钮
const handleApply = (authCertificateNo: string, landCode: string, status: string, idFarmerlistBaseInfo: string) => {
  emit('handleApply', authCertificateNo, landCode, status, idFarmerlistBaseInfo);
};

const validate = () => {
  if (!farmerTable.value || !farmerTable.value.length) return;
  return Promise.all(farmerTable.value.map((item) => item.validate()));
};

const getRecords = () => {
  const newFarmerList = cloneDeep(farmerList.value || []);
  for (let i = 0; i < newFarmerList.length; i++) {
    newFarmerList[i].tag = farmerTable.value[i].getRecords();
  }
  return newFarmerList;
};

defineExpose({
  validate,
  getRecords,
});

// const removeTagRecord = (target: [], position: number) => {
//   target.splice(position, 1);
// };
// 表格的select选择框，如果状态不为则禁用选项, 状态不为0则禁用，或者不为新增状态，或者确权人证件号为空禁用
// const rowSelection: TableProps['rowSelection'] = {
//   onChange: (selectedRowKeys: Key[], selectedRows: TargetRecordType[]) => onSelectChange(selectedRowKeys, selectedRows),
//   getCheckboxProps: (record: TargetRecordType) => ({
//     disabled: (record.authStatus && (record.authStatus.value)?.toString() !== '0') || route.query.mode === 'add' || (record.authCertificateNo && !record.authCertificateNo.value),
//     authStatus: record.authStatus && record.authStatus.value,
//   }),
// };
// 选择数据
// const onSelectChange = (selectedRowKeys: Key[], selectedRows: TargetRecordType[]) => {
//   selectedRow.value = selectedRows;
//   selectedRowKey.value = selectedRowKeys;
// };
type ListType = {
  landCode: string;
  certificateNo: string;
  farmerlistNo: string;
};
const anDisabled = defineModel<boolean>('confirmLoading', { required: true, default: false });
// 爱农宝批量选择
const list = ref<ListType[]>([]);
// 打开土地流转弹窗
const openLandModal = () => {
  // 获取勾选数
  const selectList = (farmerTable.value[0] || farmerTable.value).getCheckboxSelected();
  if (selectList?.length === 0) {
    message.warning('请勾选至少一条数据');
  } else {
    emit('handleApply', selectList);
    list.value = selectList?.map((item: TargetRecordType) => item.idFarmerlistBaseInfo.value);
  }
};

const addTargetRecord = () => {
  const copyTargetRecord = cloneDeep(farmerList.value && farmerList.value[0]) as TargetRecordType;
  Object.keys(copyTargetRecord).forEach((key) => {
    if (key !== 'tag') {
      copyTargetRecord[key].value = '';
      copyTargetRecord[key].status = '';
      copyTargetRecord[key].statusTips = '';
    } else {
      const copyTagRecord = cloneDeep(get(copyTargetRecord, 'tag[0]'));
      Object.keys(copyTagRecord).forEach((key) => {
        set(copyTagRecord, `${key}.value`, '');
        set(copyTagRecord, `${key}.status`, '');
        set(copyTagRecord, `${key}.statusTips`, '');
      });
      set(copyTargetRecord, 'tag', [copyTagRecord]);
    }
  });
  const list = getRecords() || []; // 那最新的数据
  list.push(copyTargetRecord);
  farmerList.value = list;
};

const removeTargetRecord = (index: number) => {
  // 保存一次数据，因为删除了某一个，表格会被刷新
  const list = getRecords();
  list.splice(index, 1);
  farmerList.value = list;
};
// 流转按钮描述-流转状态（0 发起流转,1 流转中,2 流转退回,3,已流转,4 数据初始状态）
// const btnText = (type: number) => {
//   return type === 1 ? '流转中' : type === 2 ? '流转退回' : type === 3 ? '已流转' : '';
// };

// 构造tag列
const columns = computed(() => {
  const firstData = get(farmerList, 'value[0].tag[0]');
  if (Array.isArray(farmerList.value) && firstData) {
    const list: { order: number; title: unknown; dataIndex: unknown; require: unknown }[] = [];
    Object.keys(firstData).forEach((key) => {
      const tempData = firstData[key];
      if (get(tempData, 'show')) {
        list.push({ order: get(tempData, 'columnIndex'), title: get(tempData, 'label'), dataIndex: key, require: get(tempData, 'require') });
      }
    });
    if (get(pageMode, 'value') === 'view') {
      return [
        // { title: '序号', dataIndex: 'orderNum', fixed: 'left' },
        ...list.sort((a, b) => a.order - b.order).map(({ title, dataIndex, require }) => ({ title, dataIndex, require })),
      ] as TableColumnProps[];
    } else {
      return [
        // { title: '序号', dataIndex: 'orderNum', fixed: 'left' },
        ...list.sort((a, b) => a.order - b.order).map(({ title, dataIndex, require }) => ({ title, dataIndex, require })),
        { title: '操作', dataIndex: 'action', fixed: 'right' },
      ] as TableColumnProps[];
    }
  } else {
    return [] as TableColumnProps[];
  }
});

const farmer_breedtype_unit_code = ref<{ label: string; value: string }[]>([]); // 养殖方式
const farmer_breedpurp_unit_code = ref<{ label: string; value: string }[]>([]); // 养殖用途
const farmer_unit_code = ref<{ label: string; value: string }[]>([]); // 投保数量单位
const farmer_land_unit_code = ref<{ label: string; value: string }[]>([]);
const land_source_type = ref<{ label: string; value: string }[]>([]); // 土地来源

try {
  const res = await $post('/api/farmer/getSelect', ['farmer_breedtype_unit_code', 'farmer_breedpurp_unit_code', 'farmer_unit_code', 'farmer_land_unit_code', 'land_source_type'], { key: 'farmer_breed_select' });
  if (res && res.code === SUCCESS_CODE) {
    const tempData = res?.data;
    farmer_breedtype_unit_code.value = get(tempData, 'farmer_breedtype_unit_code', []);
    farmer_breedpurp_unit_code.value = get(tempData, 'farmer_breedpurp_unit_code', []);
    farmer_unit_code.value = get(tempData, 'farmer_unit_code', []);
    farmer_land_unit_code.value = get(tempData, 'farmer_land_unit_code', []);
    land_source_type.value = get(tempData, 'land_source_type', []);
  } else {
    farmer_breedtype_unit_code.value = [];
    farmer_breedpurp_unit_code.value = [];
    farmer_unit_code.value = [];
    farmer_land_unit_code.value = [];
  }
} catch {
  farmer_breedtype_unit_code.value = [];
  farmer_breedpurp_unit_code.value = [];
  farmer_unit_code.value = [];
}

// 险种编码
const planCodeOptions = ref([]);
// const planCodeView = (code: string) => {
//   if (planCodeOptions.value.length) {
//     const current = planCodeOptions.value.find((item: { [key: string]: unknown }) => item.value === code);
//     if (current) {
//       return get(current, 'label');
//     }
//   }
//   return '';
// };
// 标的品种
// const riskBreedOptions = ref([]);
// 标的名称
const riskNameOptions = ref([]);

const router = useRouter();
const jump2Map = () => {
  router.push({
    path: 'landEdit',
    query: {
      farmerListNo: route.query.farmerListNo,
      idFarmerlistCustomInfo: route.query.idFarmerlistCustomInfo,
      mode: 'editFarmer',
    },
  });
};

watchEffect(() => {
  const farmerListNo = route.query.farmerListNo;
  if (farmerListNo) {
    $getOnClient('/api/farmer/getInsureCode', {
      farmerListNo,
    }).then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        set(planCodeOptions, 'value', res.data);
      } else {
        planCodeOptions.value = [];
      }
    });
    const { gateWay, service } = useRuntimeConfig().public || {};
    $getOnClient(`${gateWay}${service.farmer}/farmerList/queryMoreRiskList`, {
      farmerListNo,
    }).then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        const tempData = res.data;
        riskNameOptions.value = get(tempData, 'riskNameDropDown', []);
        // riskBreedOptions.value = get(tempData, 'riskBreedDropDown', []);
      } else {
        riskNameOptions.value = [];
        // riskBreedOptions.value = [];
      }
    });
  }
});

// 调整地块
const drawLand = (landNo: string) => {
  router.push({
    path: 'landEdit',
    query: {
      farmerListNo: route.query.farmerListNo,
      idFarmerlistCustomInfo: route.query.idFarmerlistCustomInfo,
      activeLandNo: landNo,
      mode: 'editFarmer',
    },
  });
};
</script>

<style lang="less">
.title-tag {
  border-radius: 4px 4px 0px 0px;
  width: fit-content;
  padding-left: 10px;
  padding-right: 9px;
  position: relative;
  line-height: 25px;
  height: 31px;
  background-image: linear-gradient(90deg, #029a4b 0%, #017036 100%);
  color: #ffffff;
  z-index: 1;
  top: 6px;
}

.ant-table-cell {
  > .ant-form-item {
    margin-bottom: 0;
  }
}

.required-tag {
  display: inline-block;
  margin-right: 0.02778rem;
  color: #fa5151;
  font-size: 0.09722rem;
  font-family: SimSun, sans-serif;
  line-height: 1;
}
.inform-radio {
  line-height: 78px !important;
}
</style>
