<template>
  <a-modal v-model:open="visible" title="土地流转证明" :width="pxToRem(800)" :confirm-loading="confirmLoading" centered @ok="handleOk">
    <div class="mb-[15px]">
      <div class="flex items-center justify-between mb-[9px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[3px]">证明材料</span>
        </div>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <CusUpload v-model:value="attDocSaveList" text="附件上传" />
        <div v-if="lookFileList?.length > 0">
          <div v-for="(item, index) in lookFileList" :key="index" @click="handlePreview(item)">
            <VueIcon class="text-[#2D8CF0]" :icon="IconAttachmentFont" />
            <span class="text-[#2D8CF0] pl-[4px] cursor-pointer">{{ item.originName }}</span>
            <span class="ml-[20px] text-[#2D8CF0] cursor-pointer" @click.stop="removeLookFile(item)">删除</span>
          </div>
        </div>
      </div>
    </div>
    <div class="text-[14px]">温馨提示：土地流转证明材料作为土地权属证明材料，核保时将校验其真实性，并影响核保结果。</div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconPingtaijiekouFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import CusUpload from './cusUpload.vue';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const { service } = useRuntimeConfig().public || {};
const attDocSaveList = ref([]);
const confirmLoading = ref(false);
const { landList } = defineProps<{
  landList: [];
}>();

const route = useRoute();
const emit = defineEmits(['refresh']);
const handleOk = async () => {
  try {
    if (attDocSaveList.value?.length === 0 && lookFileList.value?.length === 0) {
      message.warning('请先上传附件');
    } else {
      const params = {
        bizType: '1',
        batchLandFileList: landList.map((land) => {
          const newFileList = attDocSaveList.value?.map((item: Record<string, string>) => {
            return {
              buketName: item.iobsBucketName,
              uploadPath: item.fileKey,
              documentSize: item.fileSize,
              originName: item.fileName,
              documentType: 'QD',
              documentFormat: 'pdf',
              shapeId: land.landCode.value,
            };
          });
          const hasFileLIst = lookFileList.value?.map((item: Record<string, string>) => {
            return {
              buketName: item.bucketName,
              uploadPath: item.uploadPath,
              documentSize: item.documentSize,
              originName: item.originName,
              // documentType: 'QD',
              documentFormat: 'pdf',
              shapeId: land.landCode.value,
            };
          });
          return {
            insrCheckTaskNo: route.query.endorseApplyNo,
            idInsrCheckFarmer: land.authCertificateNo.value,
            bizId: land.idFarmerlistBaseInfo.value,
            bizType: '1',
            fileList: [...newFileList, ...hasFileLIst],
          };
        }),
      };
      confirmLoading.value = true;
      const res = await $postOnClient(`/gateway${service.check}/web/file/uploadFileWithStreamForFarmerList`, params);
      confirmLoading.value = false;
      const { code, msg = '' } = res || {};
      if (code === SUCCESS_CODE) {
        visible.value = false;
        message.success('土地证明上传成功');
        emit('refresh');
      } else {
        message.error(msg);
      }
    }
  } catch (error) {
    confirmLoading.value = false;
    console.log(error);
  }
};
const lookFileList = ref<FileUrlType[]>([]);
// 查询附件信息
const queryFarmerLandFileList = async () => {
  // 目前的情况是只有单个地块可以编辑附件，所以取第一个没问题
  const currentLand = landList[0];
  if (currentLand) {
    try {
      const res = await $postOnClient(`/gateway${service.check}/file/queryFarmerLandFileList`, {
        idInsrCheckFarmer: currentLand.authCertificateNo?.value,
        idInsrCheckFile: currentLand.idFarmerlistBaseInfo?.value,
        insrCheckTaskNo: route.query.endorseApplyNo,
        shapeId: currentLand.landCode?.value,
      });
      if (res?.code === SUCCESS_CODE) {
        lookFileList.value = res?.data as FileUrlType[];
      }
    } catch (error) {
      console.log(error);
    }
  }
};
// interface FileUrlType {
//   fileUrl?: string;
//   iobsKey?: string;
//   originName?: string;
//   bucketName?: string;
// }
// 下载文件
const handlePreview = async (file: FileUrlType) => {
  const params = {
    fileKey: file.iobsKey,
    fileName: file.originName,
    iobsBucketName: file.bucketName,
    storageTypeCode: '02',
  };
  const { data, msg, code } = (await $postOnClient('/api/iobs/getInIobsUrl', params)) || {};
  if (SUCCESS_CODE === code) {
    message.success(msg || '');
    const { fileUrl } = data as FileUrlType;
    window.open(fileUrl);
  } else {
    message.error(msg || '');
  }
};

const removeLookFile = (file: FileUrlType) => {
  lookFileList.value = lookFileList.value.filter((item) => item.uploadPath !== file.uploadPath);
};

onMounted(() => {
  queryFarmerLandFileList();
});
</script>
