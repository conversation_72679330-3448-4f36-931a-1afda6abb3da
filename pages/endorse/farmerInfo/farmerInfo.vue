<template>
  <a-spin :spinning="loading">
    <div class="relative mx-[14px] mt-[14px] bg-white form-content">
      <div class="h-[60px] w-ful bg-image-url">
        <span class="text-[18px] text-[#00190C] font-bold leading-[60px] ml-[16px]">{{ pageModeText[pageMode] }}-农户信息</span>
      </div>
      <div class="px-[16px] py-[20px] overflow-hidden">
        <a-form ref="formRef" :model="farmerData" :colon="false" :disabled="pageMode === 'view'">
          <InfoGroup title="农户/企业基础信息" class="mb-16px">
            <FarmerBasic v-model:farmer-basic="farmerData.farmerBasic" @handle-validate-field="handleValidateField" />
          </InfoGroup>
          <InfoGroup title="清单信息">
            <FarmerList ref="farmerList" v-model:confirm-loading="confirmLoading" v-model:former-list="farmerData.farmerList" v-model:selected-row="selectedRow" v-model:selected-row-key="selectedRowKey" :allow-add-risk-flag="allowAddRiskFlag" :allow-check-map-flag="allowCheckMapFlag" @handle-apply="handleApply">
              <!-- 选择流转情况 -->
              <a-modal v-model:open="visible" title="请选择" :width="pxToRem(520)" :confirm-loading="confirmLoading" :mask-closable="false" centered @ok="handleOk">
                <div>
                  <a-radio-group v-model:value="informSignType" class="w-full">
                    <div class="bg-gray-100 rounded px-14px box-border mb-12px">
                      <a-radio class="w-[100%] h-[78px] inform-radio" value="1">土地流转证明</a-radio>
                    </div>
                    <div class="bg-gray-100 rounded px-14px box-border mb-12px">
                      <a-radio class="w-[100%] h-[78px] inform-radio" value="2">爱农宝流转确认</a-radio>
                    </div>
                  </a-radio-group>
                </div>
              </a-modal>
              <!-- 土地流转证明附件上传 -->
              <ImportModal v-if="modalVisible" v-model:visible="modalVisible" :land-list="landList" @refresh="handleRefresh" />
            </FarmerList>
          </InfoGroup>
        </a-form>
      </div>
    </div>
    <div class="h-[10px]" />
    <div class="bg-white h-[50px] z-[100] sticky bottom-0 flex justify-center items-center space-x-8px">
      <a-button @click="back">返回</a-button>
      <a-button v-if="pageMode !== 'view'" :loading="saveLoading" @click="submit">保存</a-button>
    </div>
    <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleEditOk">
      <div>{{ confirmText }}</div>
    </a-modal>
    <a-modal v-model:open="bigFarmerTip" title="提醒" :mask-closable="false" :closable="false">
      <p>当前的农户的地块数量过于庞大，大数量地块线上编辑功能正在建设中，如需编辑农户信息，您可以回到分户清单首页下载清单进行线下编辑完再上传</p>
      <template #footer><a-button type="primary" @click="handleTipOk">关闭</a-button></template>
    </a-modal>
  </a-spin>
</template>

<script setup lang="ts">
import { get } from 'lodash-es';
// import { IconCheckCircleFilledFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { PageMode, TargetRecordType } from './farmer.d';
import FarmerBasic from './templates/farmerBasic.vue';
import FarmerList from './templates/farmerList.vue';
import ImportModal from './templates/ImportModal.vue';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { message } from '@/components/ui/Message';
import { pxToRem } from '@/utils/tools';

const { deletPageTabListItem } = inject('pageTab'); // 关闭页签

const router = useRouter();
const farmerData = reactive({
  farmerBasic: {},
  farmerList: [],
});

const allowAddRiskFlag = ref(false);
const resFarmerListNo = ref('');
const templateId = ref('');
const formRef = ref();
const { service } = useRuntimeConfig().public || {};
const visible = ref<boolean>(false);
const informSignType = ref<string>('');
const modalVisible = ref<boolean>(false);

const saveLoading = ref(false);
const confirmText = ref<string>('');
const allDeleteModal = ref<boolean>(false);
const farmerList = ref(null);
const submit = () => {
  formRef.value.validate().then(() => {
    // 校验表格
    farmerList.value
      .validate()
      .then(() => {
        handleEditOk();
      })
      .catch((err) => {
        message.error(err.message || '校验失败，请仔细检查');
      });
  });
};

const handleValidateField = (name: string) => {
  formRef?.value.validate([name]);
};

// 二次确认
const handleEditOk = async () => {
  saveLoading.value = true;
  const list = farmerList.value.getRecords();
  $postOnClient(`/gateway${service.farmer}/edrList/edrApplySave`, {
    idFarmerlistCustomInfo: idFarmerlistCustomInfo.value,
    templateId: templateId.value,
    farmerListNo: resFarmerListNo.value || route.query.farmerListNo, // 清单号
    farmerBasic: farmerData.farmerBasic,
    endorseApplyNo: route.query.endorseApplyNo, // 批改申请单号
    idInsrSpltCust: idInsrSpltCust.value, // 农户编号
    farmerList: list,
  })
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        message.success(res.msg);
        allDeleteModal.value = false;
        // 新增成功
        router.back();
        deletPageTabListItem('/endorseFarmerInfo'); // 关闭页签
      } else {
        message.error(res?.msg || '');
      }
    })
    .finally(() => {
      saveLoading.value = false;
    });
};
const route = useRoute();
// const idInsrCheckFarmer = ref<string>('');
// const shapeId = ref<string>('');
// const statusStr = ref<number>(0);
// 判断当前是否 批量点击爱农宝按钮
// const typeStatus = ref<string>('');
// const farmerLandList = ref<string[]>([]);
// const idFarmerlistBaseInfos = ref<string>('');
const idFarmerlistCustomInfo = ref<string>(String(route.query.idFarmerlistCustomInfo));
const idInsrSpltCust = ref<string>(String(route.query.idFarmerlistCustomInfo));

const landList = ref([]);
const openLandModal = () => {
  visible.value = true;
};
// 点击流转按钮
const handleApply = (_landList: [], openFile = false) => {
  if (openFile) {
    modalVisible.value = true;
  } else {
    openLandModal();
  }
  landList.value = _landList;
  // idInsrCheckFarmer.value = authCertificateNo;
  // shapeId.value = landCode;
  // statusStr.value = status;
  // idFarmerlistBaseInfos.value = idFarmerlistBaseInfo;
};
// 爱农宝
// 表格选中ID
type Key = string | number;
const selectedRow = ref<TargetRecordType[]>([]);
const selectedRowKey = ref<Key[]>([]);
// const handleAnApply = (list: string[]) => {
//   farmerLandList.value = list;
//   statusStr.value = status;
//   typeStatus.value = 'an';
//   handleAnbOk();
// };
// 土地流转成功更新初始化接口
const handleRefresh = () => {
  refresh({
    endorseApplyNo: route.query.endorseApplyNo, // 批改申请单号
    idInsrSpltCust: route.query.idFarmerlistCustomInfo, // 农户编号
    addFlag: false,
  });
  selectedRow.value = [];
  selectedRowKey.value = [];
};

const back = () => {
  router.back();
};
const allowCheckMapFlag = ref<boolean>(false);
// 当前页面模式，编辑，新增，查看
const pageMode = ref<PageMode>('edit');

provide('pageMode', pageMode);

const pageModeText = { edit: '编辑', view: '查看', add: '新增' };

const loading = ref(false);

const checkBigFarmer = (list) => {
  let isBig = false;
  for (const item of list) {
    if (item.tag && item.tag.length > 100000) {
      isBig = true;
      break;
    }
  }
  return isBig;
};

const bigFarmerTip = ref(false);
const handleTipOk = () => {
  bigFarmerTip.value = false;
  router.back();
  deletPageTabListItem('/endorseVerifyDetail'); // 关闭页签
};

// const switchToEdit = () => {
//   pageMode.value = 'edit';
// };

const refresh = async (bodyData: object) => {
  loading.value = true;
  const params = {
    ...bodyData,
    maskFlag: route.query.mode !== 'edit',
  };
  const res = await $postOnClient('/api/farmer/getEndorseFarmerListDetail', params);
  loading.value = false;
  if (res?.code === SUCCESS_CODE && res.data) {
    resFarmerListNo.value = get(res, 'data.farmerListNo', '');
    farmerData.farmerBasic = get(res, 'data.farmerBasic', {});
    allowAddRiskFlag.value = get(res, 'data.allowAddRiskFlag', false);
    templateId.value = get(res, 'data.templateId', '');
    allowCheckMapFlag.value = get(res, 'data.allowCheckMapFlag', false);
    idFarmerlistCustomInfo.value = res.data.idFarmerlistCustomInfo;
    idInsrSpltCust.value = res.data.idInsrSpltCust;
    // 对于标的很多的情况，暂时先停止用户进这里编辑，存在性能问题
    // 后续方案需要整改，当接入养殖险时，提示也需要调整
    const farmerList = get(res, 'data.farmerList', []);
    if (checkBigFarmer(farmerList)) {
      farmerData.farmerList = [];
      bigFarmerTip.value = true;
    } else {
      farmerData.farmerList = farmerList;
      // 列表选中需要唯一key值
      farmerData.farmerList[0]?.tag.forEach((item, index) => {
        item.id = index;
      });
    }
  } else if (res) {
    message.warning(res.msg);
  }
};
// 流转选择确认
const handleOk = () => {
  // 本地上传土地流转证明
  if (informSignType.value === '1') {
    visible.value = false;
    modalVisible.value = true;
  } else {
    handleAnbOk();
    // if (statusStr.value !== 3) {
    //   if (typeStatus.value !== 'an') {
    //     farmerLandList.value = [idFarmerlistBaseInfos.value];
    //   }
    //   handleAnbOk();
    // } else {
    //   message.warning('已流转，无需再次发起');
    // }
  }
};
const farmerListRef = ref();
const confirmLoading = ref<boolean>(false);
// 爱农宝确定
const handleAnbOk = async () => {
  try {
    confirmLoading.value = true;
    const res = await $postOnClient(`/gateway${service.farmer}/edrList/edrFarmerPushAnbLandTransfer`, { idList: landList.value.map((k) => k.idFarmerlistBaseInfo.value), endorseApplyNo: route.query.endorseApplyNo });
    if (res?.code === SUCCESS_CODE) {
      message.success(res?.msg);
      handleRefresh();
      visible.value = false;
    } else {
      message.error(res?.msg as string);
    }
    confirmLoading.value = false;
  } catch (error) {
    console.log(error);
    confirmLoading.value = false;
    if (farmerListRef.value) {
      farmerListRef.value.anDisabled = false;
    }
  }
};
// 获取清单是否已关联-3为已关联
const farmerlistStatus = ref<string>('');
const fetchTip = async () => {
  try {
    const url = '/gateway/icore-agr-an.farmer/farmerList/getFarmerListSummaryAndRuleInfo';
    const formData = new FormData();
    formData.append('farmerListNo', route.query.farmerListNo as string);
    const res = await $postOnClient<{ farmerlistStatus: string }>(url, formData);
    if (res && res.code === SUCCESS_CODE) {
      farmerlistStatus.value = res.data?.farmerlistStatus;
    }
  } catch (e) {
    console.log(e);
  }
};

watchEffect(() => {
  pageMode.value = route.query.mode as PageMode;
  if (pageMode.value === 'edit') {
    refresh({
      endorseApplyNo: route.query.endorseApplyNo, // 批改申请单号
      idInsrSpltCust: route.query.idFarmerlistCustomInfo, // 农户编号
      addFlag: false,
    });
  }
});
onMounted(() => {
  pageMode.value = route.query.mode as PageMode;
  if (pageMode.value !== 'edit') {
    refresh({
      endorseApplyNo: route.query.endorseApplyNo, // 批改申请单号
      idInsrSpltCust: route.query.idFarmerlistCustomInfo, // 农户编号
      addFlag: pageMode.value === 'add',
    });
  }
  fetchTip();
});
onDeactivated(() => {
  if (pageMode.value === 'edit') {
    // 切换页面，清空数据
    farmerData.farmerBasic = {};
    farmerData.farmerList = [];
  }
  fetchTip();
});
</script>

<style lang="less" scoped>
.form-content {
  min-height: calc(100vh - 34px - 50px - 30px);
  .bg-image-url {
    background-image: url(/assets/images/content-head.png);
    background-size: cover;
  }
}

.title-tag {
  border-radius: 4px 4px 0px 0px;
  width: fit-content;
  padding-left: 10px;
  padding-right: 16px;
  position: relative;
  line-height: 25px;
  height: 31px;
  background-color: #017036;
  color: #ffffff;
  z-index: 1;
  top: 6px;

  > img {
    position: absolute;
    height: 25px;
    left: 0;
  }
}
</style>
