<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[56px] w-ful bg-image-url">
          <span class="text-[24px] text-[#00190c] leading-[56px] ml-[16px]">保单详情</span>
        </div>
        <a-spin :spinning="loading">
          <div id="policy-details-descriptions" class="bg-white px-[24px] pt-[12px] pb-[4px]">
            <a-descriptions :colon="false">
              <a-descriptions-item class="font-number" label="产品">{{ contractModel?.baseInfo?.productName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="保单号">{{ contractModel?.baseInfo?.policyNo || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="录单人">{{ contractModel?.baseInfo?.inputBy || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="补贴类型">{{ contractModel?.baseInfo?.govSubsidyTypeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="投保单号">{{ contractModel?.baseInfo?.applyPolicyNo || '-' }}</a-descriptions-item>
              <a-descriptions-item v-if="contractModel?.baseInfo?.riskAsmtNo" class="font-number" label="风险评估编号">
                <span class="text-[#576B95] cursor-pointer font-number" @click="goRiskAssessDetail">{{ contractModel?.baseInfo?.riskAsmtNo }}</span>
              </a-descriptions-item>
            </a-descriptions>
          </div>
          <div class="relative mr-[14px] mt-[14px] form-content">
            <!-- 基础信息 -->
            <InfoGroupBox id="base-info" title="基础信息">
              <BaseInfo :contract-model="contractModel" />
            </InfoGroupBox>
            <!-- 清单信息 -->
            <InfoGroupBox id="farmer-info" title="清单信息">
              <FarmerInfo :contract-model="contractModel" />
            </InfoGroupBox>
            <!-- 保险方案 -->
            <InfoGroupBox id="insure-info" title="保险方案">
              <InsureInfo :contract-model="contractModel">
                <!-- 费用信息组件 -->
                <CostInfo ref="costInfoRef" :contract-model="contractModel" />
              </InsureInfo>
            </InfoGroupBox>
            <!-- 保费信息 -->
            <InfoGroupBox id="cost-info" title="保费信息">
              <InsureCostInfo :contract-model="contractModel" />
            </InfoGroupBox>
            <!-- 再次共保 -->
            <InfoGroupBox id="again-coinsurance-info" title="再/共保信息">
              <AgainCoinsurance :contract-model="contractModel" />
            </InfoGroupBox>
            <!-- 渠道信息 -->
            <InfoGroupBox id="channel-info" title="渠道信息">
              <ChannelInfo :contract-model="contractModel" />
            </InfoGroupBox>
          </div>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" ghost @click="handleGoLink">附件管理</a-button>
      <a-button type="primary" ghost :disabled="!contractModel?.baseInfo?.applyPolicyNo" @click="goInspectionInfo">验标信息</a-button>
      <a-button v-if="route?.query?.isOld === '1'" type="primary" ghost @click="showProcess">核保信息</a-button>
    </footer>
    <!-- 点击核保信息按钮 -->
    <InsureTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
  </div>
</template>

<script setup lang="ts">
import BaseInfo from './components/BaseInfo.vue';
import FarmerInfo from './components/FarmerInfo.vue';
import InsureInfo from './components/InsureInfo.vue';
import InsureCostInfo from './components/InsureCostInfo.vue';
import AgainCoinsurance from './components/AgainCoinsurance.vue';
import ChannelInfo from './components/ChannelInfo.vue';
import CostInfo from './components/CostInfo.vue';
import type { contractModelType } from './policyDetails';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import InsureTrack from '@/pages/review/insureProcess/components/InsureTrack.vue';

const loading = ref<boolean>(false);
// 定位锚点
const scrollWrapper = ref();
const getContainer = () => scrollWrapper.value || window;
const anchorItems = ref([
  { key: '1', href: '#base-info', title: '基础信息' },
  { key: '2', href: '#farmer-info', title: '清单信息' },
  { key: '3', href: '#insure-info', title: '保险方案' },
  { key: '4', href: '#cost-info', title: '保费信息' },
  { key: '5', href: '#again-coinsurance-info', title: '再/共保信息' },
  { key: '6', href: '#channel-info', title: '渠道信息' },
]);
const data: contractModelType = {
  baseInfo: {
    assisterInfoList: [],
    coinsuranceMark: '',
    applyPolicyType: '',
    farmerlistNo: '',
    farmersCount: '',
    disputedSettleModeChName: '',
    renewalTypeName: '',
    lastPolicyNo: '',
    insuranceBeginDate: '',
    shortTimeCoefficient: '',
    insuranceEndDate: '',
    timeRange: '',
    applyPolicyNo: '',
    govSubsidyTypeChName: '',
    inputBy: '',
    policyNo: '',
    productName: '',
    insuranceBeginEndDateType: '',
    deductionDesc: '',
  }, // 基础信息
  extendInfo: {
    tenderBusiness: '', // 项目来源
    tenderBusinessName: '', // 项目名称
    lossRate: '', // 风险程度
    customerType: '', // 客户类型
    customerAbbrName: '', // 客户简称
  },
  applicantInfoList: [], // 投保人
  insurantInfoList: [], // 被保人
  beneficaryInfoList: [], // 收益人
  riskAddressInfoList: [], // 标的
  riskGroupInfoList: [], // 标的
  sumRiskGroupAmount: {}, // 保费计划合计
  coinsuranceInfo: {
    innerCoinsuranceMark: '',
    innerCoinsuranceAgreement: '',
    totalPremium: '',
    totalInsuredAmount: '',
    coinsuranceDetailList: [],
  }, // 共保
  noclaimInfoList: [], // 免赔
  payInfoList: [], // 收费计划
  costInfo: {
    calamitySecurityRate: '', // 防灾防损费
    isPolicyBeforePayfee: '', // 财务标识
    assisterCharge: '', // 协办费
    commissionBrokerChargeProportion: '', // 手续费/经纪费
    managementFees: '', // 工作经费
    coinsuranceInsureFeeRatio: '', // 共保出单
    performanceValue1Default: '', // 农险补贴
    totalSumFeeLimit: '', //  string; // 总费用之和上限值
    assisterChargeLimit: '', // 协办费上限值
    commissionBrokerChargeProportionLimit: '', // 手续费/经纪费上限
    managementFeesLimit: '', // 工作经费上限-  // 管理费(业务员的工作经费比例
    calamitySecurityRateLimit: '', // 防灾防损费上限值
    performanceValue1DefaultLimit: '', // 农险补贴上限值
    coinsuranceInsureFeeRatioLimit: '', // 共保出单费上限值
  }, // 费用信息
  specialPromiseList: [], // 特约
  saleInfo: {
    departmentName: '',
    channelSourceDetailName: '',
    channelSourceName: '',
    saleagentIntroducerCode: '',
    employeeInfoList: [],
    developFlg: '',
    businessSourceCode: '',
    channelSourceCode: '',
    channelSourceDetailCode: '',
    primaryIntroducerInfo: {},
    brokerInfoList: [],
    agentInfoList: [],
    departmentCodeAndName: '',
    developFlgChName: '',
  },
  payerTypeSumOfPayInfo: {}, // 收费按类别汇总用于校验
  reinsuranceInfo: {},
  organizerInfo: {},
  extendGroupInfo: {},
};
const initContractModel = (data: contractModelType) => {
  return {
    baseInfo: data.baseInfo || {}, // 基础信息
    extendInfo: data.extendInfo || {},
    applicantInfoList: data.applicantInfoList || [], // 投保人
    insurantInfoList: data.insurantInfoList || [], // 被保人
    beneficaryInfoList: data.beneficaryInfoList || [], // 收益人
    riskAddressInfoList: data.riskAddressInfoList || [], // 标的
    riskGroupInfoList: data.riskGroupInfoList || [], // 标的
    sumRiskGroupAmount: data.sumRiskGroupAmount || {}, // 保费计划合计
    coinsuranceInfo: data.coinsuranceInfo || {}, // 共保
    noclaimInfoList: data.noclaimInfoList || [], // 免赔
    payInfoList: data.payInfoList || [], // 收费计划
    costInfo: data.costInfo || {}, // 费用信息
    specialPromiseList: data.specialPromiseList || [], // 特约
    saleInfo: data.saleInfo || {},
    payerTypeSumOfPayInfo: data.payerTypeSumOfPayInfo || {}, // 收费按类别汇总用于校验
    reinsuranceInfo: data.reinsuranceInfo || {},
    organizerInfo: data.organizerInfo || {},
    extendGroupInfo: data.extendGroupInfo || {},
  };
};
// 初始化-默认值
const contractModel = ref(initContractModel(data));
const route = useRoute();
// 初始化接口请求
const getInit = async () => {
  loading.value = true;
  try {
    const fetchUrl = `${gateWay}${service.policy}/web/policy/detail`;
    const res = await $postOnClient(fetchUrl, { policyNo: route.query?.policyNo || '' });
    const { code, msg = '', data = {} } = res || {};
    if (code === SUCCESS_CODE) {
      contractModel.value = initContractModel((data as contractModelType) || {});
    } else {
      message.error(msg);
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
const router = useRouter();
// 跳转附件管理页面
const handleGoLink = () => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: route.query?.policyNo || '',
      bizType: 'docViewTreePolicy',
    },
  });
};
const costInfoRef = ref();
// 跳转验标页面
const goInspectionInfo = () => {
  router.push({
    path: '/inspectionInfo',
    query: {
      applyPolicyNo: contractModel.value?.baseInfo?.applyPolicyNo,
      isHiddenProcess: 'Y',
    },
  });
};
// 跳转风险评估页面
const goRiskAssessDetail = () => {
  router.push({
    path: '/riskAssessDetail',
    query: { riskAsmtNo: contractModel.value?.baseInfo?.riskAsmtNo },
  });
};

// 核保信息
interface QueryApprovalTaskList {
  approveStatus: string;
  approveResultDesc: string;
  approveUm: string;
  approveTime: string;
  createdBy: string;
  createdDate: string;
}
const processOpen = ref(false);
const insureInfo = ref<QueryApprovalTaskList[]>([]);
const chainStr = ref<string>('');
const showProcess = async () => {
  processOpen.value = true;
  try {
    const fetchUrl = `${gateWay}${service.ums}/evntApprovalTaskRecord/queryApprovalTaskList`;
    const res = await $getOnClient<{ taskRecordVOList: QueryApprovalTaskList[]; chainList: Record<string, string>[] }>(fetchUrl, { voucherNo: route.query.applyPolicyNo });
    const { taskRecordVOList = [], chainList = [] } = res?.data || {};
    if (res?.code === SUCCESS_CODE) {
      insureInfo.value = taskRecordVOList || [];
      chainStr.value = chainList.join('->');
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
onActivated(() => {
  getInit();
});
const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
    #policy-details-descriptions {
      :deep(.ant-descriptions-item) {
        padding-bottom: 8px;
        .ant-descriptions-item-label {
          color: rgba(0, 0, 0, 0.6);
        }
        .ant-descriptions-item-content {
          color: #333;
        }
      }
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
