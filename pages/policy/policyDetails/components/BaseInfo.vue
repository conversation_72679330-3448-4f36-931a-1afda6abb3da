<!-- 基础信息 -->
<template>
  <div id="policy-basinfo-descriptions">
    <div class="text-[#333333] text-[16px] font-[600]">投保方式</div>
    <p>{{ contractModel?.baseInfo?.applyPolicyType === '2' ? '组织投保' : contractModel?.baseInfo?.applyPolicyType === '1' ? '非组织投保' : '-' }}</p>
    <a-divider />
    <div class="text-[#333333] text-[16px] font-[600]">客户信息</div>
    <customerInfo :contract-model="contractModel" />
    <!-- 项目信息 -->
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] text-[14px]">项目信息</div>
    <productInfo :contract-model="contractModel" />
  </div>
</template>

<script setup lang="ts">
import type { contractModelType } from '../policyDetails';
import customerInfo from '@/pages/detail/customerInfo/customerInfo.vue';
import productInfo from '@/pages/detail/productInfo/productInfo.vue';
// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
</script>

<style lang="less" scoped>
#policy-basinfo-descriptions {
  :deep(.ant-descriptions-item) {
    padding-bottom: 8px;
    .ant-descriptions-item-label {
      color: rgba(0, 0, 0, 0.6);
    }
    .ant-descriptions-item-content {
      color: #333;
    }
  }
}
</style>
