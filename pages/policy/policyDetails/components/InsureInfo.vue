<template>
  <!-- 保险方案 -->
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">续保信息</div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否属于续保</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.baseInfo?.renewalTypeName || '-' }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">被续保保单号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.baseInfo?.lastPolicyNo || '-' }}</div>
        </div>
      </div>
    </div>
  </div>
  <a-divider />
  <div>
    <plantRisk :contract-model="contractModel" />
  </div>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">产品信息</div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险起期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ contractModel?.baseInfo?.insuranceBeginDate || '-' }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险期限</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ contractModel?.baseInfo?.insuranceBeginEndDateType || '-' }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">年限系数</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ contractModel?.baseInfo?.shortTimeCoefficient || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险止期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ contractModel?.baseInfo?.insuranceEndDate || '-' }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">起止时间</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.baseInfo?.timeRange === '0' ? '0-24' : contractModel?.baseInfo?.timeRange === '1' ? '12-12' : '-' }}时</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">减免系数</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.reductionCoefficient || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">农户是否代缴</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y' ? '是' : '否' }}</div>
        </div>
      </div>
      <div v-if="contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo?.substitute === 'Y'" class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">代缴保费资金来源</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.riskGroupInfoList?.[0]?.riskAgrInfo.fundingSourceChName || '-' }}</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 产品信息-表格 -->
  <template v-for="(planItem, planIndex) in contractModel?.riskGroupInfoList" :key="planIndex">
    <div class="w-[73vw] mb-[16px]">
      <a-table :data-source="planItem?.planInfoList" :columns="columns" :pagination="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : '')">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <a-space>
              <a-button type="primary" size="small" @click="handleDuty(record as PlanInfoType)">责任</a-button>
              <a-button type="primary" size="small" @click="downTermOrRate('term', record.planCode)">条款</a-button>
              <a-button type="primary" size="small" @click="downTermOrRate('rate', record.planCode)">费率</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </template>
  <div class="text-[rgba(0,0,0,.4)]">总计：保单保险金额={{ contractModel?.sumRiskGroupAmount?.totalInsuredAmount || 0 }} ｜ 减免前保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalStandardPremium || 0 }}｜ 减免后保单保费金额={{ contractModel?.sumRiskGroupAmount?.totalAgreePremium || 0 }} ｜ 保单复核保费={{ contractModel?.sumRiskGroupAmount?.totalActualPremium || 0 }}</div>
  <div v-if="contractModel.riskGroupInfoList.length === 1" class="text-[rgba(0,0,0,.4)] pl-[40px] mt-[4px]">保险数量：{{ contractModel.baseInfo?.insuredNumber || 0 }}{{ contractModel.riskGroupInfoList?.[0]?.riskAgrInfo?.insuredUnitChName }} ｜ 参保户次：{{ contractModel.baseInfo?.farmersCount || 0 }}户</div>
  <div v-if="contractModel.riskGroupInfoList.length > 1">
    <template v-for="(item, index) in contractModel.riskGroupInfoList" :key="index">
      <div class="text-[rgba(0,0,0,.4)] pl-[40px] mt-[4px]">标的名称：{{ item.combinedProductName }} ｜ 保险数量：{{ item.riskAgrInfo.insuredNumber || 0 }}{{ item.riskAgrInfo.insuredUnitChName }} ｜ 参保户次：{{ item.riskAgrInfo.farmersCount || 0 }}户</div>
    </template>
  </div>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">特约信息</div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">特约类型</div>
        <div class="pl-[10px]">
          <div v-if="contractModel?.specialPromiseList.length > 0" class="text-[#333333]">{{ contractModel?.specialPromiseList?.[0]?.promiseTypeChName }}</div>
          <div v-else class="text-[#333333]">通用特约</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">特约内容</div>
        <div class="pl-[10px]">
          <div v-if="contractModel?.specialPromiseList.length > 0" class="text-[#333333]">{{ contractModel.specialPromiseList?.[0]?.promiseDesc || '-' }}</div>
          <div v-else class="text-[#333333]">无其他特别约定</div>
        </div>
      </div>
    </div>
  </div>
  <a-divider />
  <div v-if="contractModel?.noclaimInfoList?.length > 0" class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">免赔信息</div>
  <!-- 免赔-表格 -->
  <a-table v-if="contractModel?.noclaimInfoList?.length > 0" class="mb-[12px]" :data-source="contractModel?.noclaimInfoList" :columns="freeColumns" :pagination="false" />
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">免赔描述</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ contractModel?.baseInfo?.deductionDesc || '-' }}</div>
      </div>
    </div>
  </div>
  <a-divider />
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">争议处理方式</div>
  <p class="pl-[10px]">{{ contractModel?.baseInfo?.disputedSettleModeChName || '-' }}</p>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">费用信息</div>
    <!-- 费用信息组件 -->
    <slot />
  </div>
  <!-- 责任弹窗 -->
  <a-modal v-model:open="visible" title="责任" centered :width="pxToRem(600)">
    <div v-if="dutyInfoList?.length > 0">
      <div v-for="(item, index) in dutyInfoList" :key="index">{{ item.dutyCode }}{{ item.dutyName }}</div>
    </div>
    <a-empty v-else :image="simpleImage" />
    <template #footer>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { TableProps } from 'ant-design-vue';
import { Empty } from 'ant-design-vue';
import type { contractModelType, dutyInfoListType, PlanInfoType } from '../policyDetails';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import plantRisk from '@/pages/detail/plantRisk/plantRisk.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
// 点击责任按钮-显示弹窗
const dutyInfoList = ref<dutyInfoListType[]>([]);
const visible = ref<boolean>(false);
const handleDuty = (record: PlanInfoType) => {
  dutyInfoList.value = record.dutyInfoList || [];
  visible.value = true;
};
// 产品信息-表格
const columns: TableProps['columns'] = [
  {
    title: '险种码',
    dataIndex: 'planCode',
  },
  {
    title: '险种名称',
    dataIndex: 'planName',
  },
  {
    title: '每次赔偿金额',
    dataIndex: 'eachCompensationMaxAmount',
  },
  {
    title: '单位保险金额',
    dataIndex: 'unitInsuredAmount',
  },
  {
    title: '保险金额',
    dataIndex: 'totalInsuredAmount',
  },
  {
    title: '保险费率（%）',
    dataIndex: 'expectPremiumRate',
  },
  {
    title: '单位保费',
    dataIndex: 'unitPrimium',
  },
  {
    title: '基准保费金额',
    dataIndex: 'totalStandardPremium',
  },
  {
    title: '减免后保费金额',
    dataIndex: 'totalAgreePremium',
  },
  {
    title: '复核保费',
    dataIndex: 'totalActualPremium',
  },
  {
    title: '文件',
    dataIndex: 'operation',
    fixed: 'right',
  },
];
// 免赔-表格
const freeColumns: TableProps['columns'] = [
  {
    title: '免赔项目',
    dataIndex: 'noclaimItemZh',
  },
  {
    title: '免赔类型',
    dataIndex: 'noclaimTypeZh',
  },
  {
    title: '免赔率（%）',
    dataIndex: 'noclaimRate',
  },
  {
    title: '免赔额',
    dataIndex: 'noclaimAmount',
  },
];
// 下载文件-条款&费率
const downTermOrRate = async (downType: string, planCode: string) => {
  try {
    const fetchUrl = `${gateWay}${service.policy}/web/policy/policyDownloadTermOrRate`;
    const res = await $postOnClient<{ url: string }>(fetchUrl, { planCode, downType, acceptInsuranceTime: new Date().getTime().toString() });
    if (res?.code === SUCCESS_CODE) {
      window.open(res?.data?.url);
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
</script>

<style lang="less" scoped>
.ant-table-cell .ant-btn.ant-btn-sm:first-of-type,
.ant-table-cell .ant-btn:first-of-type {
  padding-left: 8px;
}
</style>
