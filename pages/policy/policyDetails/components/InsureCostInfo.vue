<!-- 保费信息 -->
<template>
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">保费来源</div>
    <a-table class="mb-[12px]" :columns="sourceColumns" :data-source="sourceList" :pagination="false" />
  </div>
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">财务标识</div>
  <p class="pl-[10px]">{{ contractModel?.costInfo?.isPolicyBeforePayfeeChName || '-' }}</p>
  <a-divider />
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">应收信息</div>
  <!-- 表格 -->
  <a-table :data-source="contractModel?.payInfoList" :columns="receivableColumns" :pagination="false">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'certificateNo'">
        <span class="break-all">{{ record.certificateNo }}</span>
      </template>
      <template v-if="column.dataIndex === 'bankAccountNo'">
        <span class="break-all">{{ record.bankAccountNo }}</span>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import type { contractModelType } from '../policyDetails';
import { pxToRem } from '@/utils/tools';

// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();

// 保费来源表格datasource
const sourceList = computed(() => {
  if (contractModel.riskGroupInfoList?.length === 1) {
    // 单标的
    return contractModel.riskGroupInfoList[0].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    contractModel.riskGroupInfoList.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return result;
  }
});

// 应收信息-表格
const receivableColumns: TableProps['columns'] = [
  {
    title: '期次',
    dataIndex: 'termNo',
    width: pxToRem(60),
  },
  {
    title: '付款人类别',
    dataIndex: 'payerTypeName',
    width: pxToRem(110),
  },
  {
    title: '付款人名称',
    dataIndex: 'paymentPersonName',
    width: pxToRem(100),
  },
  {
    title: '支付方式',
    dataIndex: 'paymentPathName',
    width: pxToRem(100),
  },
  {
    title: '应收保费',
    dataIndex: 'agreePremium',
    width: pxToRem(100),
  },
  {
    title: '缴费起期',
    dataIndex: 'paymentBeginDate',
    width: pxToRem(100),
  },
  {
    title: '缴费止期',
    dataIndex: 'paymentEndDate',
    width: pxToRem(100),
  },
  {
    title: '账户类型',
    dataIndex: 'bankAttributeName',
    width: pxToRem(100),
  },
  {
    title: '身份证',
    dataIndex: 'certificateNo',
    width: pxToRem(100),
  },
  {
    title: '银行总行',
    dataIndex: 'bankHeadquartersName',
    width: pxToRem(100),
  },
  {
    title: '银行分行',
    dataIndex: 'bankName',
    width: pxToRem(100),
  },
  {
    title: '开户行明细',
    dataIndex: 'bankDetail',
    width: pxToRem(100),
  },
  {
    title: '银行账号',
    dataIndex: 'bankAccountNo',
    width: pxToRem(100),
  },
  {
    title: '预计收回日期',
    dataIndex: 'receivableDate',
    width: pxToRem(120),
  },
  {
    title: '备注',
    dataIndex: 'otherPayerTypeDesc',
    width: pxToRem(80),
  },
];
// 保费来源columns
const sourceColumns: TableColumnsType = [
  { title: '险种代码', dataIndex: 'planCode', key: 'planCode', width: pxToRem(120) },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(120) },
  { title: '中央补贴(%)', dataIndex: 'centralFinance', key: 'centralFinance' },
  { title: '省级补贴(%)', dataIndex: 'provincialFinance', key: 'provincialFinance' },
  { title: '地方补贴(%)', dataIndex: 'cityFinance', key: 'cityFinance' },
  { title: '县级补贴(%)', dataIndex: 'countyFinance', key: 'countyFinance' },
  { title: '农户自缴(%)', dataIndex: 'farmersFinance', key: 'farmersFinance' },
  { title: '其他补贴(%)', dataIndex: 'otherFinance', key: 'otherFinance' },
];
</script>
