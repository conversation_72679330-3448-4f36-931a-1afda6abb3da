// 共保信息
export interface CoinsuranceType {
  innerCoinsuranceMark: string; // 共保属性:0-系统外共保,1-系统内共保
  innerCoinsuranceAgreement: string; // 共保协议码
  totalPremium: string; // 总保费
  totalInsuredAmount: string; // 总保额
  coinsuranceDetailList: CoinsuranceDetailType[];
}
// 共保列表
export interface CoinsuranceDetailType {
  reinsureCompanyCode: string; // 共保公司编码
  reinsureCompanyName: string; // 共保公司名称
  reinsureScaleBigDecimal: string; // 共保比例
  insuredAmountBigDecimal: string; // 保额
  premiumBigDecimal: string; // 保费
  onlyselfScaleBigDecimal: string; // 净自留比例
  callPremiumBigDecimal: string; // 复核保费
  acceptInsuranceFlag: string; // 是 // 主承保：0 // 否,1是
  employeeCode: string; // 业务员
  idchannelSourceCode: string; // 渠道
  channelSourceName: string; // 渠道名称
  coinsuranceInsureFeeRatioBigDecimal: string; // 共保出单费比例
  coinsureCompanyFinanceDeptCode: string; // 分出管理机构编码
  coinsureCompanyMgDeptCode: string; // 分出财务机构编码
  employeeName: string; // 业务员名称
  totalValueAddedTaxBigDecimal: string; // 增值税
  totalVATExcludedPremiumBigDecimal: string; // 不含增值税保费
}
// 费用信息
export interface FeeInfoType {
  [key: string]: string;
  calamitySecurityRate: string; // 防灾防损费
  isPolicyBeforePayfee: string; // 财务标识
  assisterCharge: string; // 协办费
  commissionBrokerChargeProportion: string; // 手续费/经纪费
  managementFees: string; // 工作经费
  coinsuranceInsureFeeRatio: string; // 共保出单
  performanceValue1Default: string; // 农险补贴
  totalSumFeeLimit: string; //  string; // 总费用之和上限值
  assisterChargeLimit: string; // 协办费上限值
  commissionBrokerChargeProportionLimit: string; // 手续费/经纪费上限
  managementFeesLimit: string; // 工作经费上限-  // 管理费(业务员的工作经费比例
  calamitySecurityRateLimit: string; // 防灾防损费上限值
  performanceValue1DefaultLimit: string; // 农险补贴上限值
  coinsuranceInsureFeeRatioLimit: string; // 共保出单费上限值
}
// 免赔信息
export interface NoclaimInfoType {
  noclaimItem: string; // 免赔项目
  noclaimDesc: string; // 免赔项目描述
  noclaimType: string; // 免陪类别
  lossNoclaimRate: string; // 损失免赔率
}
// 应收信息
export interface PayInfoType {
  termNo: string; // 期次
  paymentPersonName: string; // 付款人姓名
  payerType: string; // 付款人类别
  agreePremium: string; // 应交保费
  paymentBeginCircDate: string; // 缴费起始时间 (平台)
  paymentEndCircDate: string; // 缴费截止时间 (平台)
  paymentPath: string; // 收付途径/ 支付方式
}
// 产品信息-列表
export interface PlanInfoType {
  planCode?: string; // 险种代码
  planName?: string; // 险种名称
  eachCompensationMaxAmount?: string; // 每次赔偿限额
  unitInsuredAmount?: string; // 单位保额
  totalInsuredAmount?: string; // 保险金额
  expectPremiumRate?: string; // 费率
  unitPrimium?: string; // 单位保费
  totalStandardPremium?: string; // 基准保费
  totalAgreePremium?: string; // 减免后保费金额
  totalActualPremium?: string; // 复核保费
  dutyInfoList?: dutyInfoListType[]; // 责任列表
  centralFinance: string;
  provincialFinance: string;
  cityFinance: string;
  countyFinance: string;
  farmersFinance: string;
  otherFinance: string;
}
export interface dutyInfoListType {
  dutyCode: string; // 责任编码
  dutyName: string; // 责任名称
}
// 项目信息
export interface ProjectInfoType {
  [key: string]: string;
  tenderBusiness: string; // 项目来源
  tenderBusinessName: string; // 项目名称
  lossRate: string; // 风险程度
  customerType: string; // 客户类型
  customerAbbrName: string; // 客户简称
}
// 保费来源
export interface RiskAgrInfoType {
  [key: string | number]: string | number;
  centralFinance: string; // 保费来源(中央财政)%
  provincialFinance: string; // 保费来源(省级财政)%
  cityFinance: string; // 保费来源(地市财政)%
  countyFinance: string; // 保费来源(县财政)%
  farmersFinance: string; // 保费来源(农户)%
  otherFinance: string; // 保费来源(其他来源)%
  agrRiskAttribute: Record<string, string>[];
}
// 特约信息
export interface SpecialPromiseType {
  promiseType: string; // 特约类型
  promiseDesc: string; // 特约描述
}
export interface ResType {
  code: string;
  msg: string;
  data: QueryApprovalTaskList[];
  httpAppUrl?: string;
  sysId?: string;
  timeStamp?: string;
  code?: string;
  virtualUsername?: string;
  userId?: string;
  deptCode?: string;
  taskRecordVOList?: QueryApprovalTaskList[];
  chainList?: string[];
}
// 保险方案
export interface riskGroupInfo {
  riskAgrInfo: RiskAgrInfoType;
  planInfoList: PlanInfoType[];
  unitInsuredAmount: string;
  combinedProductName: strubg;
  insuredRate: string;
  unitPremium: string;
  insuredAmount: string;
  premium: string;
}
// 保险方案
export interface saleInfo {
  departmentCodeAndName: string;
  developFlgChName: string;
  departmentName: string;
  channelSourceDetailName: string;
  channelSourceName: string;
  saleagentIntroducerCode: string;
  employeeInfoList: Record<string, string>[];
  developFlg: string;
  businessSourceCode: string;
  channelSourceCode: string;
  channelSourceDetailCode: string;
  primaryIntroducerInfo: Record<string, string>;
  brokerInfoList: Record<string, string>[];
  agentInfoList: Record<string, string>[];
}
// 基本信息
export interface BaseInfo {
  assisterInfoList: Record<string, string>[];
  coinsuranceMark: string;
  applyPolicyType: string;
  farmerlistNo: string;
  farmersCount: string;
  disputedSettleModeChName: string;
  renewalTypeName: string;
  lastPolicyNo: string;
  insuranceBeginDate: string;
  shortTimeCoefficient: string;
  insuranceEndDate: string;
  timeRange: string;
  applyPolicyNo: string;
  govSubsidyTypeChName: string;
  inputBy: string;
  policyNo: string;
  productName: string;
  insuranceBeginEndDateType: string;
  deductionDesc: string;
  secDeptCode: string;
  riskAsmtNo: string;
}
// 投保人、被保险人、受益人
export interface ApplicantInfo {
  [key: string | number]: string | number;
  personnelTypeChName: string;
  name: string;
  certificateNo: string;
  certificateIssueDate: string;
  certificateValidDate: string;
  address: string;
  postcode: string;
  sexCodeChName: string;
  mobileTelephone: string;
  poorSymbolChName: string;
  professionName: string;
  belongOrganizationNoChName: string;
  subjectNumberName: string;
  nationalityZh: string;
  personnelType: string;
  superviseInfoList: SuperviseExtend[];
}
interface SuperviseExtend {
  superviseExtendList: Record<string, string>[];
  yearlySalaries: string;
  companyName: string;
  businessScope: string;
  enterpriseTypeChName: string;
  registeredFund: string;
}
// 总集合
export interface contractModelType {
  baseInfo: BaseInfo;
  extendInfo: ProjectInfoType;
  applicantInfoList: ApplicantInfo[];
  insurantInfoList: ApplicantInfo[];
  beneficaryInfoList: ApplicantInfo[];
  riskAddressInfoList: Record<string, string>[];
  riskGroupInfoList: riskGroupInfo[];
  sumRiskGroupAmount: Record<string, string>;
  coinsuranceInfo: CoinsuranceType;
  noclaimInfoList: NoclaimInfoType[];
  payInfoList: PayInfoType[];
  costInfo: FeeInfoType;
  specialPromiseList: SpecialPromiseType[];
  saleInfo: saleInfo;
  payerTypeSumOfPayInfo: Record<string, string>;
  reinsuranceInfo: Record<string, string>;
  organizerInfo: Record<string, string>;
  extendGroupInfo: Record<string, string>;
}

// 再保一般临分列表
interface ReinsuranceDetailListType {
  facultativeMode?: string; // 临分方式（1比例临分;2 超赔临分）
  franchise?: string; // 起赔点
  separationRatio?: string; // 分出比例（前端不用转换百分比，下同）
  compensateLimit?: string; // 赔偿限额
  separationPremium?: string; // 分出净保费
  otherExplain?: string; // 其他说明
  poundage?: string; // 手续费
  abatement?: string; // 免赔
}
