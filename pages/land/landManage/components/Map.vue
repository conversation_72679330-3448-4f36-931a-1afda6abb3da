<template>
  <div id="agrmap" class="m-16px rounded page-height">
    <!-- 地图左上角的重新勾画按钮 -->
    <div v-if="isDrawing" class="absolute top-[16px] left-[16px] z-[1001]">
      <a-button type="primary" class="mr-[12px]" @click="resetDraw">重新勾画</a-button>
      <a-button type="primary" class="mr-[12px]" @click="quitDraw">退出勾画</a-button>
      <a-button type="primary" @click="landClose">地块闭合</a-button>
    </div>
    <!-- 切换地块显隐 -->
    <div v-if="route.query.farmerListNo" class="absolute bottom-[16px] left-[16px] z-[1001] flex flex-col gap-[12px] bg-[#222d44] p-[15px] rounded-[4px]">
      <div class="flex justify-between">
        <span class="text-[14px] text-[#fff] font-bold mr-[10px]">风险地块</span>
        <a-switch v-model:checked="riskSwitch" size="large" @change="switchShowRiskLand" />
      </div>
      <div class="flex justify-between">
        <span class="text-[14px] text-[#fff] font-bold mr-[10px]">清单地块</span>
        <a-switch v-model:checked="farmerSwitch" size="large" @change="switchShowCurrentLand" />
      </div>
      <div class="flex justify-between">
        <span class="text-[14px] text-[#fff] font-bold mr-[10px]">整村地块</span>
        <a-switch v-model:checked="villageSwitch" size="large" @change="switchShowAllLand" />
      </div>
    </div>
    <div class="absolute top-[16px] right-[16px] z-[1000]">
      <div :class="['sider-container', 'relative', collapsed ? 'collapsed' : 'expanded']">
        <div class="sider-content bg-white rounded">
          <div v-if="isCreate || isEdit" :class="['pt-[10px]', 'text-[16px]', 'text-[#333333]', 'mx-[14px]', { 'mb-[10px]': route.query.mode === 'editFarmer' }]">{{ isCreate ? '按图制作清单' : '按图指认' }}</div>
          <div v-if="route.query.mode !== 'editFarmer'" class="py-[16px] mx-[14px]">
            <a-form ref="landFormRef" :model="landForm" :label-col="{ style: { width: '60px' } }" :label-align="'right'">
              <a-form-item label="地区" name="village" :rules="[{ required: true, message: '请选择行政村', trigger: 'change' }]">
                <div v-if="route.query.addressName">{{ route.query.addressName }}</div>
                <RegionCascader v-else ref="regionRef" change-on-select :init-val="cascaderInitVal" :auth-dept-code="cascaderAuthDeptCode" @change-selected="regionChange" />
              </a-form-item>
              <a-form-item label="标的" name="riskCode">
                <RiskCodeSelect ref="riskCodeSelectRef" v-model:value="riskCode" department-code="2" :show-search="false" @level-change="handleRiskCodeLevelChange" />
              </a-form-item>
              <div class="flex justify-between">
                <a-form-item label="确权人" class="w-[35%]">
                  <a-input v-model:value.trim="farmerName" placeholder="请输入" />
                </a-form-item>
                <a-form-item label="证件号码" class="ml-[8px] w-[60%]">
                  <a-input v-model:value.trim="identityCardNo" placeholder="请输入" />
                </a-form-item>
              </div>
            </a-form>
            <div class="flex justify-end space-x-[8px]">
              <a-button :disabled="disabledQuery" @click="resetQuery">重置</a-button>
              <a-button type="primary" :disabled="disabledQuery" ghost @click="queryFarmer">查询</a-button>
              <a-dropdown v-if="isExport">
                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item key="1">全量下载</a-menu-item>
                    <a-menu-item key="2">自定义下载</a-menu-item>
                  </a-menu>
                </template>
                <a-button type="primary" :loading="isFullDownload && openExport">地块图表下载</a-button>
              </a-dropdown>
            </div>
          </div>
          <div v-if="showLandSelect" class="pb-[14px] mx-[14px]">
            <div v-if="route.query.mode !== 'editFarmer'" class="divide-line" />
            <div v-if="route.query.mode !== 'editFarmer'" class="h-[40px] flex items-center justify-between gap-x-[12px] mb-[8px]">
              <div class="flex items-center space-x-[12px]">
                <span class="text-[14px] text-[rgba(0,0,0,0.6)]">地块选择</span>
                <a-switch :checked="startClick" :disabled="!landForm.village" size="small" @change="handleSwitch" />
              </div>
              <a-button v-show="startClick" type="primary" @click="finishSelectLand">确认选择</a-button>
            </div>
            <div class="flex justify-between items-center mb-14px">
              <div class="flex gap-x-[8px] text-[14px]">
                <div class="text-[14px] text-[#333333] font-semibold">查询结果</div>
                <span>|</span>
                <div class="text-[rgba(0,0,0,.6)] text-[12px]">农户数{{ dataSource.length }}, 其中{{ sumBy(dataSource, (o) => o.landAbsence) }}户缺失地块数据</div>
              </div>
              <div class="flex gap-x-8px">
                <a-button v-if="isExport" type="primary" :disabled="disabledExport" :loading="!isFullDownload && openExport" @click="autoExport">自定义下载</a-button>
                <a-button v-if="isCreate" type="primary" :disabled="disabledCreate" @click="clickCreateList">生成清单</a-button>
                <a-button v-if="isEdit" type="primary" :disabled="disabledEdit" :loading="editLoading" @click="clickEditList">完成指认</a-button>
              </div>
            </div>
            <a-table :columns="columns" :data-source="showPagination ? paginatedData : dataSource" :loading="tableLoading" :pagination="false" :scroll="{ y: '50vh' }" :custom-row="addCustomRowProperty">
              <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex === 'shapeNum'">
                  <a-popover @open-change="onPopoverHover">
                    <template #content>
                      <div class="max-h-[60vh] overflow-y-auto">
                        <table>
                          <tbody>
                            <tr>
                              <td class="px-[8px]">所有人</td>
                              <td class="px-[8px]">地块编码</td>
                              <td class="px-[8px]">地块面积</td>
                              <td class="px-[8px]">投保面积</td>
                            </tr>
                            <tr v-for="shape in paginatedPopoverData(record.shapeList)" :key="shape.landCode" style="cursor: pointer" @click="highlightSigleLand(shape.landNoView)">
                              <td>{{ shape.authName }}</td>
                              <td :class="{ 'text-[#E6AD1C]': shape.riskLandFlag }">{{ shape.landCode }}</td>
                              <td>{{ shape.landArea }}</td>
                              <td>{{ shape.insuranceNums }}</td>
                            </tr>
                          </tbody>
                        </table>
                        <a-pagination v-if="record.shapeList.length > 100" v-model:current="popoverCurrentPage" class="flex justify-end" simple :show-size-changer="false" responsive :total="record.shapeList.length" :page-size="popoverPageSize" @change="handlePopoverPageChange" />
                      </div>
                    </template>
                    <a-button type="link">{{ text }}</a-button>
                  </a-popover>
                </template>
                <template v-if="column.dataIndex === 'landArea'">
                  <span>{{ sumShape(record) || '-' }}</span>
                </template>
                <template v-if="column.dataIndex === 'insuranceNums'">
                  <span>{{ text || '-' }}</span>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <div class="flex gap-x-[8px]">
                    <a-button v-if="!record.linkState" type="link" :disabled="startClick || (linkFarmerId.length > 0 && linkFarmerId !== record.certificateNo) || drawFarmerId.length > 0" @click.stop="linkFarmerLand(record)">关联地块</a-button>
                    <a-button v-else type="link" @click.stop="confirmFarmerLand(record, index)">确认关联</a-button>
                    <template v-if="record.shapeList?.length > 0">
                      <a-button v-if="!record.drawState" type="link" :disabled="startClick || (drawFarmerId.length > 0 && drawFarmerId !== record.certificateNo) || linkFarmerId.length > 0" @click.stop="startDraw(record)">勾画地块</a-button>
                      <a-button v-else type="link" @click.stop="endDraw(record)">完成勾画</a-button>
                    </template>
                  </div>
                </template>
              </template>
              <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                <div style="padding: 8px">
                  <a-input ref="searchInput" placeholder="按姓名搜索" :value="selectedKeys[0]" style="width: 188px; margin-bottom: 8px; display: block" @change="(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])" @press-enter="handleSearch(selectedKeys, confirm, column.dataIndex)" />
                  <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="handleSearch(selectedKeys, confirm, column.dataIndex)">
                    <template #icon><SearchOutlined /></template>
                    查询
                  </a-button>
                  <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)"> 重置 </a-button>
                </div>
              </template>
              <template #customFilterIcon="{}">
                <VueIcon :icon="IconTongyongSearchFont" class="text-[16px] text-[#07c160]" />
                <!-- <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" /> -->
              </template>
            </a-table>
            <a-pagination v-if="showPagination" v-model:current="currentPage" class="flex justify-end" :show-size-changer="false" responsive :total="filteredData.length" :page-size="pageSize" @change="handlePageChange" />
          </div>
        </div>
        <div class="toggle-button">
          <div class="cursor-pointer h-[50px] w-[18px] pl-[4px] bg-white flex justify-center items-center rounded-l-[10px]" @click="switchCollapsed">
            <VueIcon :icon="collapsed ? IconChevronLeftFont : IconChevronRightFont" :class="['text-[16px]', 'text-[rgba(0,0,0,0.40)]']" />
          </div>
        </div>
      </div>
    </div>
    <!-- 清单生成成功弹窗 -->
    <a-modal v-model:open="successModal" title="" centered :footer="null">
      <div class="flex flex-col items-center">
        <div class="text-[35px] text-[#07c160]">
          <VueIcon :icon="IconCheckCircleFilledFont" />
        </div>
        <div class="flex mt-[14px] mb-[24px] text-[16px] text-[rgba(0,0,0,0.9)]">
          <div class="mr-[4px]">{{ isEdit ? '清单已完成指认' : '清单已生成' }} {{ newListNo }}</div>
          <div style="cursor: pointer" class="flex items-center" @click="copyText(newListNo)">
            <VueIcon :icon="IconFuzhiFont" />
            <span class="text-[12px]" style="font-weight: 400">复制</span>
          </div>
        </div>
        <div class="flex gap-x-[8px]">
          <a-button @click="backList">回到投保清单页</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                clearModal();
                successModal = false;
              }
            "
            >关闭</a-button
          >
        </div>
      </div>
    </a-modal>
    <!-- 清单生成失败弹窗 -->
    <a-modal v-model:open="failModal" title="" centered :footer="null">
      <div class="flex flex-col items-center">
        <div class="text-[35px] text-[#F03E3E]">
          <VueIcon :icon="IconCloseCircleFilledFont" />
        </div>
        <div class="mt-[14px] mb-[24px] text-[16px] text-[rgba(0,0,0,0.9)]">生成清单失败, 请重新操作</div>
        <div class="flex gap-x-[8px]">
          <a-button
            type="primary"
            @click="
              () => {
                clearModal();
                failModal = false;
              }
            "
            >关闭</a-button
          >
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import RiskCodeSelect from '@/components/selector/RiskCodeSearch.vue';
import { groupBy, sumBy } from 'lodash-es';
import { IconCheckCircleFilledFont, IconCloseCircleFilledFont, IconChevronRightFont, IconChevronLeftFont, IconTongyongSearchFont, IconFuzhiFont } from '@pafe/icons-icore-agr-an';
import type { BaseFarmerLand, ChooseLand, EcosmapInstance } from './landQuery';
import RegionCascader from '@/components/selector/RegionCascader.vue';
import { $post, $get } from '@/utils/request';
import { downloadBlob, pxToRem, copyText } from '@/utils/tools';
import { useUserStore } from '@/stores/useUserStore';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import EcosmapL from 'ecosmap-leaflet';
import 'ecosmap-leaflet/dist/main.css';
// import EcosmapL from '/Users/<USER>/Documents/home/<USER>/admin/ecos-map-leaflet-sdk/src/map.ts';
// import '/Users/<USER>/Desktop/code/admin/ecos-map-leaflet-sdk/src/styles/main.css';
const showDrawLayer = ref(true);
const { gateWay, service } = useRuntimeConfig().public || {};
const route = useRoute();
const router = useRouter();
const riskCode = ref();

const collapsed = ref(false);
const switchCollapsed = () => {
  collapsed.value = !collapsed.value;
};

const searchInput = ref();
const state = reactive({
  searchText: '',
  searchedColumn: '',
});
const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: string) => {
  currentPage.value = 1;
  confirm();
  state.searchText = selectedKeys[0];
  state.searchedColumn = dataIndex;
};

const handleReset = (clearFilters: (param?: { confirm: boolean }) => void) => {
  currentPage.value = 1;
  clearFilters({ confirm: true });
  state.searchText = '';
};

let map: EcosmapInstance = {};

const landFormRef = ref();
const landForm = reactive({
  village: '',
});

const successModal = ref(false);
const failModal = ref(false);

const isExport = computed(() => route.path === '/landQuery'); // 地块查询
const isCreate = computed(() => route.path === '/landToList'); // 按图制作
const isEdit = computed(() => route.path === '/landEdit'); // 按图指认
const showLandSelect = ref(isExport.value ? false : true);
const dataSource = ref<BaseFarmerLand[]>([]);
const columns = ref([
  {
    title: '被保人',
    dataIndex: 'farmerName',
    width: pxToRem(75),
    customFilterDropdown: true,
    onFilter: (value: string, record: BaseFarmerLand) => record.farmerName.toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },
  },
  {
    title: '证件号码',
    dataIndex: 'certificateNo',
    width: pxToRem(165),
  },
  {
    title: '地块数',
    align: 'center',
    width: pxToRem(60),
    dataIndex: 'shapeNum',
  },
  {
    title: '地块面积',
    align: 'center',
    width: pxToRem(70),
    dataIndex: 'landArea',
  },
  {
    title: '投保面积',
    align: 'center',
    width: pxToRem(70),
    dataIndex: 'insuranceNums',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
]);

watchEffect(() => {
  if (isExport.value) {
    columns.value = columns.value.filter((col) => col.dataIndex !== 'operation');
  }
});

const landList = computed(() => {
  const result: { authName: string; authCertificateNo: string; landCode: string; agrLandClassification: string; insuranceNums?: number }[] = [];
  dataSource.value.forEach(({ shapeList }) => {
    if (Array.isArray(shapeList)) {
      shapeList.forEach((land) => {
        result.push(land);
      });
    }
  });
  return result;
});

const landCodeList = computed(() => landList.value.map((item) => item.landNoView));

const { userInfo } = useUserStore();
const defaultDeptCode = ref(userInfo.deptList[0]);
const startClick = ref<boolean>(false); // 开始选择地块
const regionCodeList = ref<string[]>([]);
const regionRef = ref();
const regionChange = (selected: string[]) => {
  regionCodeList.value = selected || [];
  landForm.village = selected?.length > 4 ? selected[4] : '';
  dataSource.value = [];
  if (region2Dept.has(selected[0])) {
    defaultDeptCode.value = region2Dept.get(selected[0]);
  }
};

const isAutoDownload = ref(false);
const openExport = ref(false);
const isFullDownload = ref(false);
const handleMenuClick = async ({ key }: { key: string }) => {
  if (key === '1') {
    // 导出全村数据
    isFullDownload.value = true;
    clickExport();
    isAutoDownload.value = false;
  } else {
    isAutoDownload.value = true;
    isFullDownload.value = false;
    showLandSelect.value = true;
  }
};

const autoExport = () => {
  isAutoDownload.value = true;
  isFullDownload.value = false;
  clickExport();
};

watch(
  () => landForm.village,
  async (villageCode: string) => {
    // 清空标的
    riskCode.value = '';
    if (villageCode) {
      try {
        await map.clearHighlightedLands();
        await map.addDistrictLayer({ code: villageCode, level: 5 });
        if (!route.query.farmerListNo) {
          const length = await map.getVillageLandListLength({ code: landForm.village, level: 5 });
          await map.addLandLayerByCode({ code: villageCode, level: 5, zoom: setZoom(length) });
        }
      } catch (e) {
        message.error(e as string);
      }
    }
  },
);

watch(
  () => riskCode.value,
  async (newValue, oldValue) => {
    await map.clearHighlightedLands();

    // 清空一级标的
    if (oldValue && !newValue) {
      try {
        await map.addDistrictLayer({ code: landForm.village, level: 5 });
        const length = await map.getVillageLandListLength({ code: landForm.village, level: 5 });
        await map.addLandLayerByCode({ code: landForm.village, level: 5, zoom: setZoom(length) });
      } catch (e) {
        message.error(e as string);
      }
    } else {
      if (riskCodeLevel.value === 1) {
        await map.addLandLayerByCode({
          code: landForm.village,
          level: 5,
          landType: 1,
          cropCode: newValue,
        });
      }
      if (riskCodeLevel.value === 5) {
        await map.addLandLayerByCode({
          code: landForm.village,
          level: 5,
          landType: 4,
          cropCode: newValue,
        });
      }
    }
    dataSource.value = [];
    queryLandInfo.value = [];
    // 清空高亮
    // await map.addActiveLandLayer({ landNoList: [], code: landForm.village, needLocate: false, level: 5 });
    // await map.addQueryLandLayer({ code: landForm.village, level: 5, landNoList: [] });
  },
);

const sumShape = (record: BaseFarmerLand) => (record?.shapeList?.length > 0 ? sumBy(record?.shapeList, (o) => o.landArea)?.toFixed(2) : 0);

// 点击导出按钮
const clickExport = () => {
  landFormRef.value?.validate().then(() => {
    openExport.value = true;
    Promise.all([exportList(), exportPNG()])
      .then(() => {
        message.success('导出成功');
        exportLoading.value = false;
        openExport.value = false;
      })
      .catch(() => {
        exportLoading.value = false;
        openExport.value = false;
      });
  });
};

// 点击生成清单
const clickCreateList = async () => {
  const formData = {
    mapImportType: '2', // 清单导入类型 1农户编辑,2生成清单,3编辑清单
    templateId: route.query.templateId, // 模板ID号
    departmentCode: route.query.departmentCode, // 机构编码
    productCode: route.query.productCode, // 产品编码
    productName: route.query.productName, // 产品名称
    productVersion: route.query.productVersion, // 产品版本号
    riskAddressCodeProvince: route.query.provinceCode, // 省
    riskAddressCodeCity: route.query.cityCode, // 市
    riskAddressCodeCounty: route.query.countyCode, // 区
    riskAddressCodeTown: route.query.townCode, // 镇
    riskAddressCodeVillage: route.query.villageCode, // 村
    farmerMapCustResVOList: dataSource.value.map((k) => ({
      farmerName: k.farmerName,
      certificateNo: k.identityCardNoCipher,
      shapeList: k.shapeList,
    })),
    fromCommon: route.query.fromCommon, // 是否从共
  };
  $post<string>(gateWay + service.farmer + '/farmerList/saveOrUpdateMapFarmer', formData).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      successModal.value = true;
      newListNo.value = res.data;
    } else {
      failModal.value = true;
    }
  });
};

const editLoading = ref(false);
// 编辑清单（完成指认）
const clickEditList = async () => {
  const formData = {
    mapImportType: route.query.mode === 'editFarmer' ? '1' : '3',
    farmerListNo: route.query.farmerListNo,
    farmerMapCustResVOList: dataSource.value.map((k) => ({
      idFarmerlistCustomInfo: k.idFarmerlistCustomInfo,
      farmerName: k.farmerName,
      accountNo: k.accountNo,
      certificateNo: k.identityCardNoCipher || k.certificateNo,
      shapeList: k.shapeList,
    })),
  };
  editLoading.value = true;
  // 编辑清单或者编辑农户
  $post<string>(gateWay + service.farmer + '/farmerList/saveOrUpdateMapFarmer', formData)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        successModal.value = true;
        newListNo.value = res.data;
      } else {
        failModal.value = true;
      }
    })
    .finally(() => {
      editLoading.value = false;
    });
};

const formState = reactive({
  productCode: '',
  templateId: '',
});

const clickFarmer = async (record: BaseFarmerLand) => {
  const landList = record.shapeList?.map((k) => ({ landNoStr: k.landNoView })) ?? [];
  try {
    await map.addActiveLandLayer({
      landList,
      needLocate: true,
      code: landForm.village,
      level: 5,
    });
  } catch (e) {
    message.error(e as string);
  }
};

const addCustomRowProperty = (record: BaseFarmerLand) => {
  // 添加行点击事件
  return {
    onClick: () => {
      clickFarmer(record);
    },
  };
};

// 查询条件
const farmerName = ref('');
const identityCardNo = ref('');
const queryLandInfo = ref<ChooseLand[]>([]);

const queryFarmer = async () => {
  await landFormRef.value?.validate();
  try {
    const landInfo = await map.queryLandInfo({
      queryType: '05',
      code: landForm.village,
      level: 5,
      identityCardNo: identityCardNo.value,
      farmerName: farmerName.value,
      insuranceType: riskCodeSelectRef.value.valueList[0],
      cropCode: riskCodeSelectRef.value.valueList[4],
    });
    queryLandInfo.value = landInfo;
    await map.highlightLandByCode({ code: landForm.village, landNoList: landInfo.map((k) => k.landNoStr), needLocate: true });
  } catch (err) {
    message.error(err as string);
  }
};
const resetQuery = async () => {
  queryLandInfo.value = [];
  identityCardNo.value = '';
  farmerName.value = '';
  riskCode.value = undefined;
  await map.highlightLandByCode({ code: landForm.village, landNoList: [] });
};

const exportLoading = ref(false);

const riskCodeSelectRef = ref();

// 导出清单
const exportList = async () => {
  try {
    let cropCodeToUse = riskCode.value;

    // 当 riskCodeLevel > 1 且 < 5 时，使用第一级的 riskCode
    if (riskCodeLevel.value > 1 && riskCodeLevel.value < 5 && riskCodeSelectRef.value) {
      const firstLevelCode = riskCodeSelectRef.value.valueList[0];
      if (firstLevelCode) {
        cropCodeToUse = firstLevelCode;
      }
    }
    const tempList = landList.value.map((item) => {
      const _landCode = item.landCode;
      const _landNoView = item.landNoView;
      return {
        ...item,
        landCode: _landNoView,
        landNoView: _landCode,
      };
    });

    await $post(
      gateWay + service.farmer + '/file/farmerLandExport',
      {
        villageCode: landForm.village,
        cropCode: cropCodeToUse,
        landList: isAutoDownload.value ? tempList : [], // 全量下载无需传landList
      },
      {
        async onResponse({ response }) {
          let fileName = '';
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          if (fileData) {
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              if (match) {
                fileName = decodeURI(match[1]);
              }
            }
            downloadBlob(fileData, fileName, fileType);
          } else {
            message.error('获取清单数据失败');
            return Promise.reject('获取清单数据失败');
          }
        },
      },
    );
  } catch (e) {
    console.log(e);
  }
};
const getBlob = (url: string): Promise<Blob> => {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response);
      }
    };
    xhr.send();
  });
};
// 导出png
const exportPNG = async () => {
  try {
    let cropCodeToUse = riskCode.value;

    // 当 riskCodeLevel > 1 且 < 5 时，使用第一级的 riskCode
    if (riskCodeLevel.value > 1 && riskCodeLevel.value < 5 && riskCodeSelectRef.value) {
      const firstLevelCode = riskCodeSelectRef.value.valueList[0];
      if (firstLevelCode) {
        cropCodeToUse = firstLevelCode;
      }
    }

    const res = await $post<string>(gateWay + service.farmer + '/file/getVillageLandMap', {
      villageCode: landForm.village,
      landCodeList: isAutoDownload.value ? landCodeList.value : [],
      cropCode: cropCodeToUse,
    });
    if (res?.code === SUCCESS_CODE) {
      getBlob(res.data).then((blob) => {
        downloadBlob(blob, '农户地块导出', blob.type);
      });
    } else {
      message.error(res?.msg || '图片导出失败');
      return Promise.reject(res?.msg || '图片导出失败');
    }
  } catch (e) {
    console.log(e);
    return Promise.reject(e);
  }
};

const newListNo = ref('');
const clearModal = () => {
  formState.productCode = '';
  formState.templateId = '';
};
const backList = () => {
  successModal.value = false;
  clearModal();
  router.push({
    path: '/farmerList',
    query: {
      farmerlistNo: newListNo.value,
    },
  });
};

const handleSwitch = async (checked: boolean) => {
  if (checked) {
    startClick.value = checked;
    try {
      const _activeLandNoList = queryLandInfo.value.length > 0 ? [...landCodeList.value, ...queryLandInfo.value.map((k) => k.landNoStr)] : landCodeList.value;
      await map.addLandLayerByPointClick({
        code: landForm.village,
        level: 5,
        activeLandNoList: _activeLandNoList,
      });
    } catch (e) {
      message.error(String(e));
    }
  }
};

const insertLand = (list: ChooseLand[]) => {
  const newLandCodeList = list?.map((k) => k.landNoStr);
  // 先处理删除的地块
  dataSource.value.forEach((row) => {
    row.shapeList = row.shapeList.filter((shape) => newLandCodeList.includes(shape.landNoView));
    row.shapeNum = row.shapeList.length;
  });
  // 已存在用户
  const hasIdentityCardNoCipher = dataSource.value.map((k) => k.identityCardNoCipher);
  const rowLandNo: string[] = [];
  dataSource.value.forEach((row) => {
    row.shapeList.forEach((shape) => rowLandNo.push(shape.landNoView));
  });
  // 处理新增的地块
  const diffLandList = list.filter((v) => !rowLandNo.includes(v.landNoStr));
  const hasExist = diffLandList.filter((k) => hasIdentityCardNoCipher.includes(k.identityCardNoCipher));
  hasExist.forEach((land) => {
    const farmer = dataSource.value.find((v) => v.identityCardNoCipher === land.identityCardNoCipher);
    if (farmer) {
      farmer.shapeNum = farmer.shapeNum + 1;
      farmer.shapeList.push({
        authName: land.farmerName,
        authCertificateNo: land.identityCardNoCipher,
        landCode: land.landNoView,
        landNoView: land.landNoStr,
        agrLandClassification: land.landType,
        landArea: land.areaInMu,
        landName: land.landName,
        confirmationLandCode: land.confirmationLandCode,
        landClassificationSecondName: land.landClassificationSecondName,
      });
    }
  });
  const noExist = diffLandList.filter((k) => !hasIdentityCardNoCipher.includes(k.identityCardNoCipher));
  const grouped = groupBy(noExist, 'identityCardNoCipher');
  const addList = Object.keys(grouped).map((key) => ({
    farmerName: grouped[key][0].farmerName,
    identityCardNoCipher: grouped[key][0].identityCardNoCipher,
    certificateNo: grouped[key][0].identityCardNo, // 展示脱敏身份证，不需要传给后端
    shapeNum: grouped[key].length,
    linkState: false, // 关联地块状态,不需要传给后端
    drawState: false,
    shapeList: grouped[key].map((item) => ({
      authName: item.farmerName,
      authCertificateNo: item.identityCardNoCipher,
      landCode: item.landNoView,
      landNoView: item.landNoStr,
      agrLandClassification: item.landType,
      landArea: item.areaInMu,
      landName: item.landName,
      confirmationLandCode: item.confirmationLandCode,
      landClassificationSecondName: item.landClassificationSecondName,
    })),
  }));
  dataSource.value = [...addList, ...dataSource.value];
};

// 地块选择确认
const finishSelectLand = async () => {
  if (startClick.value) {
    try {
      const list: ChooseLand[] = await map.removeLandLayerByPointClick({
        queryType: '03',
        code: landForm.village,
        level: '5',
      });
      if (list.length === 0) {
        message.warning('未选择地块');
      } else {
        insertLand(list);
        await map.highlightLandByCode({ code: landForm.village, landNoList: landCodeList.value });
      }
    } catch (e) {
      message.error(e as string);
    }
    startClick.value = false;
  }
};

const disabledExport = computed(() => startClick.value || dataSource.value.some((data) => data.linkState) || !landForm.village);
const disabledCreate = computed(() => dataSource.value.length === 0 || startClick.value || dataSource.value.some((data) => data.linkState) || !landForm.village);
const disabledEdit = computed(() => startClick.value || dataSource.value.some((data) => data.linkState) || dataSource.value.some((data) => data.drawState) || !landForm.village);
const disabledQuery = computed(() => startClick.value || dataSource.value.some((data) => data.linkState));

const linkFarmerId = ref('');
// 开始关联某个农户的地块
const linkFarmerLand = async (record: BaseFarmerLand) => {
  const linkLandCodeList = record.shapeList?.map((shape) => shape.landNoView) || [];
  const otherLandCodeList = landCodeList.value.filter((code) => !linkLandCodeList.includes(code));
  // 需定位后出现图层才可关联地块
  await map.flyToByLandNo({ code: landForm.village, level: 5, landNoList: linkLandCodeList });
  try {
    const fn = async () => {
      await map.addLandLayerByPointClick({
        code: landForm.village,
        level: 5,
        needLocate: true,
        activeLandNoList: linkLandCodeList,
        disabledLandNoList: otherLandCodeList,
      });
      linkFarmerId.value = record.certificateNo;
      record.linkState = true;
    };
    // 关联地块依赖其他地块的展示
    if (!farmerSwitch.value) {
      farmerSwitch.value = true;
      await switchShowCurrentLand(true);
    }
    if (!villageSwitch.value) {
      villageSwitch.value = true;
      await switchShowAllLand(true);
    }
    nextTick(setTimeout(fn, 500));
  } catch (e) {
    message.error(String(e));
  }
};

// 确认关联某个农户地块
const confirmFarmerLand = async (record: BaseFarmerLand, index: number) => {
  try {
    const list: ChooseLand[] = await map.removeLandLayerByPointClick({
      queryType: '03',
      code: landForm.village,
      level: '5',
    });
    record.shapeNum = list.length;
    record.shapeList = list.map((item) => {
      // 检查当前item的landNoStr是否在原shapeList中存在对应的landNoView
      const existingShape = record.shapeList.find((shape) => shape.landNoView === item.landNoStr);

      if (existingShape) {
        // 如果存在，直接返回原有的信息
        return existingShape;
      } else {
        // 如果不存在，创建新的shape信息
        return {
          authName: item.farmerName,
          authCertificateNo: item.identityCardNoCipher,
          landCode: item.landNoView || item.landNoStr,
          landNoView: item.landNoStr,
          agrLandClassification: item.landType,
          landArea: item.areaInMu,
          landName: item.landName,
          confirmationLandCode: item.confirmationLandCode,
        };
      }
    });
    linkFarmerId.value = '';
    record.linkState = false;
    dataSource.value.splice(index, 1);
    dataSource.value.unshift(record);
    // 高亮已选中
    await map.highlightLandByCode({ code: landForm.village, level: 5, landNoList: landCodeList.value });
  } catch (e) {
    message.error(e as string);
  }
};
// 根据清单号查地址
const getAddressByListNo = async (listNo: string) => {
  const _url = gateWay + service.farmer + '/farmerList/getFarmerListSummaryDetail';
  const _formData = new FormData();
  _formData.append('farmerListNo', listNo);
  try {
    const res = await $post<{
      riskAddressCodeVillage: string;
      riskAddressCodeCity: string;
      riskAddressCodeCounty: string;
      riskAddressCodeProvince: string;
      riskAddressCodeTown: string;
    }>(_url, _formData, { headers: { 'Content-Type': '' } });
    if (res?.code === SUCCESS_CODE) {
      const { riskAddressCodeVillage = '', riskAddressCodeCity = '', riskAddressCodeCounty = '', riskAddressCodeProvince = '', riskAddressCodeTown = '' } = res.data || {};
      regionCodeList.value = [riskAddressCodeProvince, riskAddressCodeCity, riskAddressCodeCounty, riskAddressCodeTown, riskAddressCodeVillage];
      landForm.village = riskAddressCodeVillage;
    }
  } catch (e) {
    console.log(e);
  }
};

// 重新勾画
const resetDraw = async () => {
  try {
    await map.resetDrawSatus();
  } catch (e) {
    message.error(String(e));
  }
};

//退出勾画
const quitDraw = async () => {
  drawFarmerId.value = '';

  // 找到当前正在勾画的农户记录并重置其状态
  const currentDrawRecord = dataSource.value.find((record) => record.drawState === true);
  if (currentDrawRecord) {
    currentDrawRecord.drawState = false;
  }

  isDrawing.value = false; // 重置勾画状态

  map.exitDrawingMode();
  await map.highlightLandByCode({ landNoList: landCodeList.value, code: landForm.village, level: 5 });
};

// 闭合地块
const landClose = async () => {
  try {
    await map.closePolygon();
  } catch (e) {
    message.error(String(e));
  }
};

// const layerCodes = ref<string[]>([]);
// 查询图层配置
// const getLayerConfig = async (listNo: string) => {
//   const _url = gateWay + service.farmer + '/dictSelect/getLandLayerType';
//   try {
//     const res = await $get(_url, { farmerListNo: listNo });
//     if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
//       layerCodes.value = res.data.map(v => v.parameterCode);
//     }
//   } catch (e) {
//     console.log(e);
//   }
// };

interface EcosUserAuthParam {
  umAccount?: string;
  depId?: string;
  systemId?: string;
  randomNumber?: string;
  unixTimeStamp?: string;
  signature?: string;
}
const initMap = async () => {
  const url = gateWay + service.farmer + '/ecos/getEcosUserAuthParam';
  try {
    const res = await $get(url);
    if (res) {
      const { umAccount = '', depId = '', systemId = '', randomNumber = '', unixTimeStamp = '', signature = '' } = res.data as EcosUserAuthParam;
      const { ecosEnv } = useRuntimeConfig().public;
      map = await new EcosmapL({
        id: 'agrmap',
        env: ecosEnv,
        loginInfo: {
          umAccount,
          depId,
          systemId,
          randomNumber,
          unixTimeStamp,
          signature,
        },
        config: {
          fullscreenControl: false,
          zoomControl: false,
        },
      }).init();
    }
  } catch (e) {
    message.error(e as string);
  }
};

const tableLoading = ref(false);

const getInitList = async () => {
  const url = gateWay + service.farmer + '/farmerList/queryFarmerList';
  try {
    tableLoading.value = true;
    const res = await $post<BaseFarmerLand[]>(url, {
      idFarmerlistCustomInfo: route.query.idFarmerlistCustomInfo,
      farmerListNo: route.query.farmerListNo,
    });
    if (res?.code === SUCCESS_CODE) {
      dataSource.value = res.data;
      // 高亮已选中
      if (route.query.activeLandNo) {
        // await map.addActiveLandLayer({
        //   landNoList: [route.query.activeLandNo as string],
        //   needLocate: true,
        //   code: landForm.village,
        //   level: 5,
        // });
        await map.highlightLandByCode({ code: landForm.village, landNoList: currentLandNoList.value });
      } else {
        await map.highlightLandByCode({ code: landForm.village, landNoList: landCodeList.value, zoom: setZoom(landCodeList.value.length) });
      }
      // 若当前单个地块和风险地块重复，则自动开启风险地块开关
      if (route.query.activeLandNo && riskLandNoList.value?.includes(currentLandNoList.value?.[0])) {
        riskSwitch.value = true;
      }
      switchShowRiskLand(riskSwitch.value);
      switchShowAllLand(villageSwitch.value);
    }
  } catch (e) {
    message.error(String(e));
  } finally {
    tableLoading.value = false;
  }
};

const clearData = async () => {
  dataSource.value = [];
  regionCodeList.value = [];
  regionRef.value?.clear();
  landForm.village = '';
  startClick.value = false;
  riskSwitch.value = false;
  farmerSwitch.value = true;
  villageSwitch.value = false;
};

const createListByMap = async () => {
  // 按图制作,路由把地址带过来
  const { provinceCode, cityCode, countyCode, townCode, villageCode } = route.query;
  regionCodeList.value = [provinceCode as string, cityCode as string, countyCode as string, townCode as string, villageCode as string];
  landForm.village = villageCode as string;
  try {
    await map.addDistrictLayer({ code: route.query.villageCode as string, level: 5 });
    await map.addLandLayerByCode({ code: route.query.villageCode as string, level: 5 });
  } catch (e) {
    message.error(e as string);
  }
};

// 完成指认
watch(
  [() => route.query.farmerListNo, () => route.query.idFarmerlistCustomInfo, () => route.query.activeLandNo],
  async () => {
    if (route.query.farmerListNo) {
      clearData();
      await getAddressByListNo(route.query.farmerListNo as string);
      await getInitList();
    }
  },
  { immediate: false },
);

// 按图制作
watch(
  () => route.query.villageCode,
  async () => {
    clearData();
    await createListByMap();
  },
  { immediate: false },
);

const cascaderInitVal = ref<string[]>([]);
const cascaderAuthDeptCode = ref<string[][]>([]);

const region2Dept = new Map();
// getAddressByDepartmentCode

// const getProvicePromise = (deptCode: string) => $get(gateWay + service.administrate + '/public/queryProvinceByDepartmentCode', { deptCode: deptCode });
const getAddressPromise = (deptCode: string) => $get(gateWay + service.administrate + '/public/getAddressByDepartmentCode', { departmentCode: deptCode });
const positionByDeptCode = async () => {
  try {
    if (userInfo.deptList.includes('2')) {
      return;
    }
    const promiseAll: Promise[] = [];
    userInfo.deptList.forEach((dept) => {
      promiseAll.push(getAddressPromise(dept));
    });
    const allRes = await Promise.all(promiseAll);
    const provinceList: string[] = allRes.map((k) => k?.data);
    provinceList.forEach((data, index) => {
      region2Dept.set(data.province, userInfo.deptList[index]);
    });
    cascaderInitVal.value = [provinceList[0].province, provinceList[0].city, provinceList[0].county];
    cascaderAuthDeptCode.value = provinceList.map((province) => [province.province]);
    await map.addDistrictLayer({
      code: provinceList[0].province.slice(0, 2),
      level: 1,
      onlyShowBounds: true,
    });
  } catch (e) {
    console.log(e);
  }
};

const drawFarmerId = ref('');
// Add a new ref to track if drawing is in progress
const isDrawing = ref(false);

// Modify the startDraw function
const startDraw = async (record: BaseFarmerLand) => {
  clickFarmer(record);
  map.drawPolygon(record.shapeList.map((k) => k.landNoView));
  await map.highlightLandByCode({ code: landForm.village, landNoList: record.shapeList.map((k) => k.landNoView), needLocate: true });
  drawFarmerId.value = record.certificateNo;
  record.drawState = true;
  isDrawing.value = true; // Set drawing state to true
};

// Modify the endDraw function
const endDraw = async (record: BaseFarmerLand) => {
  drawFarmerId.value = '';
  try {
    const res = await map.handleSaveSketch({
      landNo: record.shapeList[0].landNoView,
      code: landForm.village,
    });
    record.drawState = false;
    isDrawing.value = false; // Set drawing state to false

    // 只能划分为两块
    // 把划分出来的地块给当前农户
    if (res && res.data) {
      const currentDraw = res.data?.list.find((k) => k.current === 1);
      if (currentDraw) {
        record.shapeList.forEach((shape) => {
          if (shape.landNoView === currentDraw.parentLandNoStr) {
            shape.landCode = currentDraw.landNoStr;
            shape.landNoView = currentDraw.landNoStr;
            shape.landArea = currentDraw.areaInMu;
          }
        });
      }
      // 把划分的剩余地块给这块地的其他农户
      const otherDraw = res.data?.list.find((k) => k.current !== 1);
      if (otherDraw) {
        dataSource.value.forEach((r) => {
          if (r.idFarmerlistCustomInfo !== record.idFarmerlistCustomInfo) {
            r.shapeList.forEach((shape) => {
              if (shape.landNoView === otherDraw.parentLandNoStr) {
                shape.landCode = otherDraw.landNoStr;
                shape.landNoView = otherDraw.landNoStr;
                shape.landArea = otherDraw.areaInMu;
              }
            });
          }
        });
      }
    }

    await map.highlightLandByCode({ landNoList: landCodeList.value, code: landForm.village, level: 5 });
  } catch (e) {
    message.error(String(e));
  }
};

// 切换图层
// watch(landType, async (newLandType: number) => {
//   if (map && landForm.village) {
//     try {
//       await map.addLandLayerByCode({ code: landForm.village, landType: newLandType, level: 5 });
//     } catch (e) {
//       message.error(e);
//     }
//   }
// });

// 是否展示勾画地块
watch(showDrawLayer, async (showValue: boolean) => {
  if (map && landForm.village) {
    try {
      await map.addDrawLayer({ code: landForm.village, close: !showValue, level: 5 });
    } catch (e) {
      message.error(String(e));
    }
  }
});

const riskCodeLevel = ref(0);

const handleRiskCodeLevelChange = (level: number) => {
  riskCodeLevel.value = level;
  console.log('当前标的级别:', level);
  // 这里可以根据级别执行不同的逻辑
};

// 风险地块开关
const riskSwitch = ref(false);
// 清单地块开关
const farmerSwitch = ref(true);
// 村地块开关
const villageSwitch = ref(false);

// 当前匹配的地块编码列表
const currentLandNoList = computed(() => landList.value.filter((v) => v.landCode === route.query.activeLandNo).map((v) => v.landNoView));

// 风险地块列表
const riskLandNoList = computed(() => landList.value.filter((v) => v.riskLandFlag).map((v) => v.landNoView));

// 高亮单个地块
const highlightSigleLand = (landNoView: string) => {
  map.highlightLandByCode({ code: landForm.village, landNoList: [landNoView], needLocate: true });
};

// 切换展示风险地块
const switchShowRiskLand = async (flag: boolean) => {
  await map.highlightRiskLandByCode({ code: landForm.village, landNoList: flag ? riskLandNoList.value : [] });
};

const setZoom = (value: number) => {
  const intervals = [
    { max: 500, result: 0 },
    { max: 4000, result: 16 },
    { max: 6000, result: 17 },
    { max: 8000, result: 18 },
    { max: Infinity, result: 18 },
  ];
  for (const interval of intervals) {
    if (value < interval.max) {
      return interval.result;
    }
  }
  return intervals[intervals.length - 1].result; // 默认返回最大区间值
};

// 切换展示清单地块
const switchShowCurrentLand = async (flag: boolean) => {
  const landNoList = flag ? (route.query.activeLandNo ? currentLandNoList.value : landCodeList.value) : [];
  await map.highlightLandByCode({ code: landForm.village, landNoList, zoom: flag ? setZoom(landNoList.length) : 0 });
};

// 切换展示全村地块
const switchShowAllLand = async (flag: boolean) => {
  if (flag) {
    // 多地块时，应设置zoom的值大一些。
    const length = await map.getVillageLandListLength({ code: landForm.village, level: 5 });
    await map.addLandLayerByCode({ code: landForm.village, level: 5, zoom: setZoom(length) });
  } else {
    const landNoList = route.query.activeLandNo ? currentLandNoList.value : landCodeList.value;
    await map.addLandLayerById({ code: landForm.village, level: 5, landNoList, style: { lineColor: 'tansparent' } });
  }
};

const currentPage = ref(1); // 当前页码
const pageSize = ref(5); // 页数

const showPagination = computed(() => dataSource.value.length > 100); // 是否展示分页

// 过滤后的数据
const filteredData = computed(() => {
  if (!state.searchText) return dataSource.value;
  return dataSource.value.filter((item: BaseFarmerLand) => item.farmerName.includes(state.searchText));
});

// 计算当前页要显示的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredData.value.slice(start, end);
});

// 页码改变时触发
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const popoverCurrentPage = ref(1); // 当前页码
const popoverPageSize = ref(10); // 页数

// 获取浮层显示的数据
const paginatedPopoverData = (list) => {
  if (list.length <= 100) return list;
  const start = (popoverCurrentPage.value - 1) * popoverPageSize.value;
  const end = start + popoverPageSize.value;
  return list.slice(start, end);
};

// 浮层页码改变时触发
const handlePopoverPageChange = (page: number) => {
  popoverCurrentPage.value = page;
};

const onPopoverHover = (flag: boolean) => {
  // 离开时重置页码
  if (!flag) {
    popoverCurrentPage.value = 1;
  }
};

onMounted(async () => {
  await initMap();
  await positionByDeptCode();
  if (route.query.villageCode) {
    await createListByMap();
  }
  if (route.query.farmerListNo) {
    await getAddressByListNo(route.query.farmerListNo as string);
    await getInitList();
  }
});

onActivated(() => {
  if (typeof map?.resize === 'function') {
    map.resize();
  }
});

onBeforeUnmount(() => {
  // 销毁地图;
  map.destroy();
});
</script>

<style lang="less" scoped>
.page-height {
  position: relative;
  height: calc(100vh - 66px);
}

.sider-container {
  transition: transform 0.3s ease-in-out;

  &.expanded {
    transform: translateX(0);
  }

  &.collapsed {
    transform: translateX(calc(100% + 16px));
  }
}

.sider-content {
  width: 610px;
  overflow: hidden;
}

.toggle-button {
  position: absolute;
  top: 16px;
  left: -18px;
}

.divide-line {
  width: 100%;
  height: 1px;
  margin-bottom: 8px;
  background: #eaeaea;
}

:deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-wrapper .ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #f2f2f2;
}

:deep(.ant-table-wrapper .ant-table .ant-table-tbody > tr > td) {
  padding-right: 0;
  .ant-btn-link {
    padding-left: 0;
    padding-right: 0;
  }
}

:deep(.ant-table-wrapper .ant-table-thead > tr > th, .ant-table-wrapper .ant-table-thead > tr > td) {
  padding-right: 0;
}

table,
th,
td {
  text-align: center;
  vertical-align: middle;
  border: 1px solid black;
}
table {
  border-collapse: collapse;
}
</style>
