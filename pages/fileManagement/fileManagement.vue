<template>
  <ClientOnly>
    <div class="p-14px space-y-14px">
      <a-spin :spinning="btnLoading">
        <div class="bg-white p-16px rounded-md">
          <div class="flex">
            <a-form :colon="false" class="flex-grow">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="机构" :label-col="{ style: { width: pxToRem(80) } }" name="departmentCode" v-bind="validateInfos.departmentCode">
                    <department-search v-model:contain-child-depart="formData.containChildDepart" :dept-code="formData.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="保单生成日期">
                    <a-range-picker v-model:value="formData.createdDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="14">
                  <a-form-item label="单号" name="voucherNo" :label-col="{ style: { width: pxToRem(80) } }">
                    <a-input-group>
                      <a-row :gutter="8">
                        <a-col :span="5">
                          <a-select v-model:value="formData.voucherType" :options="voucherTypeList" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                        </a-col>
                        <a-col :span="8">
                          <a-input v-model:value.trim="formData.voucherNo" placeholder="请输入" allow-clear @blur="handleBlurDisabled" />
                        </a-col>
                      </a-row>
                    </a-input-group>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="24">
                  <a-form-item label="标的" :label-col="{ style: { width: pxToRem(80) } }">
                    <RiskCodeSelect v-model:value="formData.riskType" :department-code="formData.departmentCode" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-show="expand">
                <a-col :span="24">
                  <a-form-item label="标的地址" :label-col="{ style: { width: pxToRem(80) } }">
                    <region-select v-model:province="formData.riskProvince" v-model:city="formData.riskCity" v-model:county="formData.riskCounty" v-model:town="formData.riskTown" v-model:village="formData.riskVillage" class="w-[70%]" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-show="expand" :gutter="16">
                <a-col :span="6">
                  <a-form-item label="被保险人" :label-col="{ style: { width: pxToRem(80) } }">
                    <a-input v-model:value.trim="formData.insurantNames" placeholder="请输入" :disabled="disabled" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="投保人">
                    <a-input v-model:value.trim="formData.applicantNames" placeholder="请输入" :disabled="disabled" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
            <div class="w-[50px]">
              <form-fold v-model="expand" />
            </div>
          </div>
          <div class="flex justify-center items-center space-x-8px">
            <a-button @click="resetForm">重置</a-button>
            <a-button type="primary" ghost @click="submit">查询</a-button>
          </div>
        </div>
      </a-spin>
      <div class="bg-white p-16px rounded-md">
        <div class="mb-16px flex justify-between">
          <div class="flex items-center space-x-[12px]">
            <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">查询结果</span>
          </div>
        </div>
        <a-table :columns="columns" :data-source="dataSource" :pagination="pagination" :scroll="{ x: 'max-content' }" :loading="loading" class="table-box">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'policyNo'">
              <CopyLink :text="record.policyNo" @click="openPolicyDetail(record.policyNo)" />
            </template>
            <template v-if="column.dataIndex === 'insuranceBeginDate'">
              {{ record.insuranceBeginDate + '至' + record.insuranceEndDate }}
            </template>
            <template v-if="column.dataIndex === 'publicityStatusCode'">
              {{ record.publicityStatusName || '' }}
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-space :size="1">
                <a-button type="link" @click="handleGoLink(record.policyNo)">电子档案</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
import Form from 'ant-design-vue/es/form/Form';
import type { TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { insuranceTrackingType, formDataType, dataType, ChildrenType, OptionsType } from './fileManagement.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { pxToRem } from '@/utils/tools';
import { $post, usePost, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import CopyLink from '@/components/ui/CopyLink.vue';

const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 单证类型option
const voucherTypeList = ref<OptionsType[]>([]);

// 表单数据
const formData = reactive<formDataType>({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  createdDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  insurantNames: '',
  applicantNames: '',
  riskType: '',
  voucherNo: '',
  voucherType: undefined,
  riskProvince: undefined,
  riskCity: undefined,
  riskCounty: undefined,
  riskTown: undefined,
  riskVillage: undefined,
});

const formRules = reactive({
  departmentCode: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
});

const { resetFields, validateInfos, validate } = Form.useForm(formData, formRules);
const btnLoading = ref<boolean>(false);
/** 业务号有值时其余搜索条件禁用
 * 测试-龙伟文说的需求
 * 1.改成根据业务号去适配单证类型
 * 2.录入投保申请号就变成类型就是投保单申请
 * 3.PG开头的就是投保申请单，5开头的是投保单，1开头的是保单
 * 4.然后不用置灰清空单证类 **/
// 单号输入后调接口获取单号类型和机构,禁用其他组件
const disabled = ref<boolean>(false);
const handleBlurDisabled = async () => {
  if (formData.voucherNo) {
    disabled.value = true;
    formData.riskProvince = '';
    formData.riskCity = '';
    formData.riskCounty = '';
    formData.riskTown = '';
    formData.riskVillage = '';
    formData.riskType = '';
    formData.insurantNames = '';
    formData.createdDate = [];
    btnLoading.value = true;
    // 动态获取单号类型，和机构code
    try {
      const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: formData.voucherNo });
      if (res && res?.code === SUCCESS_CODE && res?.data) {
        const isBizType = voucherTypeList.value.findIndex((item: OptionsType) => item.value === res.data?.bizType);
        if (isBizType !== -1) {
          formData.voucherType = res.data?.bizType;
        }
        formData.departmentCode = res.data?.insureDepartmentNo || '';
      }
    } catch (error) {
      console.error('获取单号类型失败:', error);
    }
    btnLoading.value = false;
  } else {
    disabled.value = false;
    formData.voucherType = undefined;
    formData.departmentCode = defaultDeptCode.value;
    formData.createdDate = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    btnLoading.value = false;
  }
};
const expand = ref(false);

const columns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentNoAndName', fixed: 'left' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '产品名称', dataIndex: 'marketProductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '标的信息', dataIndex: 'agriculturalRiskObjectDetailName' },
  { title: '保险起止期', dataIndex: 'insuranceBeginDate' },
  { title: '操作', dataIndex: 'action', fixed: 'right' },
];
// 列表数据
const dataSource = ref<insuranceTrackingType[]>([]);
// 选择机构
const changeDeptCode = (newVal: string) => {
  formData.departmentCode = newVal;
};
const { gateWay, service } = useRuntimeConfig().public || {};
const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  validate()
    .then(() => {
      getListData();
    })
    .catch((error) => {
      console.error('表单验证失败:', error);
      message.error('请检查表单输入');
    });
};

const initOption = async () => {
  try {
    const res = await $post(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, ['agrAnVourcherType']);
    if (res?.code === SUCCESS_CODE) {
      const agrAnVourcherType = (res.data as ChildrenType[]).find((item: OptionsType) => item.value === 'agrAnVourcherType');
      if (agrAnVourcherType?.children) {
        voucherTypeList.value = agrAnVourcherType.children.map((child: OptionsType) => ({
          value: child.value,
          label: child.label,
        }));
      }
    }
  } catch (error) {
    console.error('初始化单证类型失败:', error);
  }
};
initOption();

// 列表接口
const loading = ref<boolean>(false);
const url = gateWay + service.policy + '/web/policy/document/list';
const queryVoucherSummaryInfos = await usePost(url);
const getListData = async () => {
  loading.value = true;
  const params = {
    ...formData,
    containChildDepart: formData.containChildDepart ? '1' : '0',
    startCreatedDate: formData.createdDate?.[0] || '',
    endCreatedDate: formData.createdDate?.[1] || '',
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };
  try {
    const res = await queryVoucherSummaryInfos.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = (res?.data as dataType) || {};
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
      message.error(res?.msg || '');
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
const router = useRouter();
// 重置
const resetForm = () => {
  resetFields();
  getListData();
  disabled.value = false;
};

// 打开投保单详情页
const openPolicyDetail = (policyNo: string) => {
  const path = {
    path: '/policyDetails',
    query: {
      policyNo,
    },
  };
  router.push(path);
};

const { pagination } = usePagination(getListData);
// 跳转附件管理页面
const handleGoLink = (policyNo: string) => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: policyNo || '',
      bizType: 'docViewTreePolicy',
    },
  });
};
onMounted(() => {
  getListData();
});
</script>
