<template>
  <a-directory-tree v-model:expanded-keys="expandedKeys" v-model:selected-keys="selectedKeys" class="attachment-tree tree" multiple :tree-data="treeData" @select="handleChange">
    <template #switcherIcon="{ key }">
      <VueIcon :icon="IconCaretRightSmallFont" :class="['text-[16px]', selectedKeys.includes(key) ? 'text-[#07C160]' : 'text-[#9D9D9D]']" />
    </template>
    <template #icon="{ isLeaf, key }">
      <template v-if="!isLeaf">
        <VueIcon v-if="selectedKeys.includes(key)" :icon="IconFujianXuanzhongColor" class="text-[16px]" />
        <VueIcon v-else :icon="IconFujianColor" class="text-[16px]" />
      </template>
    </template>
    <template #title="{ dataRef }">
      <span>{{ dataRef.title }}</span>
      <a-tooltip v-if="dataRef.title.includes('分户清单') && dataRef?.children?.length && route.query.bizType === 'docViewTreeApply'" placement="right">
        <template #title>
          <span>请先完成保费计算，系统会自动生成电子分户清单，请稍后查询下载，路径：①投保单详情-附件管理-电子分户清单或②投保跟踪-打印-电子分户清单</span>
        </template>
        <VueIcon :icon="IconErrorCircleFilledFont" class="text-[#666] leading-[-1px]" />
      </a-tooltip>
    </template>
  </a-directory-tree>
</template>

<script lang="ts" setup>
import { IconCaretRightSmallFont, IconFujianColor, IconFujianXuanzhongColor, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { VueIcon } from '@pag/icon-vue3';
import type { FileInfo } from '../attachment.d.ts';

const { treeData = [] } = defineProps<{
  treeData: FileInfo[];
}>(); // 树数据

const route = useRoute();

const selectedKeys = ref<string[]>([]); // 选中树节点
const selectedKey = defineModel<string>('selectedKey', { default: '' }); // 选中树节点
const expandedKeys = ref<string[]>([]); // 展开指定的树节点

const findNodeAndParents = (tree: FileInfo[], targetId: string, path: string[] = []): string[] | null => {
  for (const node of tree) {
    // 将当前节点添加到路径中
    path.push(node.key);
    if (node.key === targetId) {
      // 找到了目标节点
      return path.slice();
    }

    if (node.children && node.children.length > 0) {
      // 当前节点有子节点，递归查找
      const result = findNodeAndParents(node.children, targetId, path);
      if (result) {
        return result;
      }
    }

    // 回溯：从路径中移除当前节点，因为它不是目标节点也不是目标节点的父节点
    path.pop();
  }
  // 如果没有找到目标节点，则返回null
  return null;
};
const collectParentNodes = (key: string) => {
  const keys = findNodeAndParents(treeData, key);

  if (keys && keys.length > 0) {
    // 存在需要打开的父节点
    // keys.pop();
    const mergeArray = [...new Set([...keys, ...expandedKeys.value])];
    expandedKeys.value = mergeArray;
  }
};

watch(
  selectedKeys,
  (val) => {
    if (val[0] && val[0] !== selectedKey.value) {
      selectedKey.value = val[0];
    }
  },
  {
    deep: true,
  },
);

watch(
  selectedKey,
  (val) => {
    if (val && val !== selectedKeys.value[0]) {
      selectedKeys.value = [val];
      // 遍历打开父亲节点
      collectParentNodes(val);
    }
  },
  {
    immediate: true,
  },
);
const emit = defineEmits(['handleClick']);
const handleChange = (selectedKeys: string[]) => {
  selectedKey.value = selectedKeys?.join();
  emit('handleClick');
};
</script>

<style lang="less">
.attachment-tree.tree {
  &.ant-tree {
    background-color: inherit;
    font-size: 14px;
    font-weight: 600;
    .ant-tree-treenode {
      &.ant-tree-treenode-selected {
        &::before {
          background: rgba(6, 193, 7, 0.09);
        }
        .ant-tree-node-selected {
          color: #07c160;
        }
      }
      .ant-tree-switcher {
        line-height: 36px;
        width: 16px;
        margin-left: 8px;
        .txdicon {
          transition: transform 0.3s;
          transform: rotate(0deg);
        }
        &.ant-tree-switcher_open {
          .txdicon {
            transform: rotate(90deg);
          }
        }
      }
      .ant-tree-node-content-wrapper {
        line-height: 36px;
        .ant-tree-iconEle {
          text-align: left;
          height: 36px;
          line-height: 36px;
        }
      }
    }
  }
}
</style>
