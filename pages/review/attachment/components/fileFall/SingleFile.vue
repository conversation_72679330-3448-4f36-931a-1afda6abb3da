<template>
  <!-- 大图预览 -->
  <div v-if="previewMode === '1'" class="group bg-[#fff] w-[450px] relative text-center cursor-pointer">
    <div class="pt-[18px] flex items-center justify-center">
      <template v-if="isWord(format)">
        <VueIcon :icon="IcWordColor" class="text-[120px]" />
      </template>
      <template v-else-if="isExcel(format)">
        <VueIcon :icon="IcDianziqingdanColor" class="text-[120px]" />
      </template>
      <template v-else-if="isPdf(format)">
        <VueIcon :icon="IcPdfColor" class="text-[120px]" />
      </template>
      <template v-else-if="isZip(format)">
        <VueIcon :icon="IcWenjianColor" class="text-[120px]" />
      </template>
      <template v-else-if="isMail(format)">
        <VueIcon :icon="IcMailColor" class="text-[120px]" />
      </template>
      <template v-else-if="isHtml(format)">
        <VueIcon :icon="IcHtmlColor" class="text-[120px]" />
      </template>
      <template v-else-if="isPpt(format)">
        <VueIcon :icon="IcPptColor" class="text-[120px]" />
      </template>
      <template v-else-if="isTxt(format)">
        <VueIcon :icon="IcTxtColor" class="text-[120px]" />
      </template>
      <template v-else-if="isMp4(format)">
        <VueIcon :icon="IcYingxiangColor" class="text-[120px]" />
      </template>
      <template v-else-if="isUnPreviewImage(format)">
        <VueIcon :icon="IcYingxiangColor" class="text-[120px]" />
      </template>
      <template v-else>
        <img class="mt-[10px] w-full" :src="bigUrl" alt="" />
      </template>
    </div>
    <div class="text-left pt-[4px] text-[12px] px-[5px] truncate leading-[14px]">文件名称：{{ documentName }}</div>
    <div class="text-left pt-[6px] text-[12px] px-[5px] truncate leading-[14px]">文件类型：{{ documentFormat }}</div>
    <slot />
    <div class="absolute left-0 right-0 top-0 bottom-0 bg-[rgba(0,0,0,0.45)] justify-center items-center z-10 group-hover:flex hidden" @click="preview">
      <VueIcon :icon="IconPreviewFont" class="text-[16px] text-[#fff]" />
    </div>
  </div>
  <!-- 小图预览 -->
  <div v-if="previewMode === '2'" class="group w-[160px] h-[160px] bg-[#fff] relative text-center cursor-pointer">
    <div class="pt-[10px] flex h-[80px] items-center justify-center">
      <template v-if="isWord(format)">
        <VueIcon :icon="IcWordColor" class="text-[64px]" />
      </template>
      <template v-else-if="isExcel(format)">
        <VueIcon :icon="IcDianziqingdanColor" class="text-[64px]" />
      </template>
      <template v-else-if="isPdf(format)">
        <VueIcon :icon="IcPdfColor" class="text-[64px]" />
      </template>
      <template v-else-if="isZip(format)">
        <VueIcon :icon="IcWenjianColor" class="text-[64px]" />
      </template>
      <template v-else-if="isMail(format)">
        <VueIcon :icon="IcMailColor" class="text-[64px]" />
      </template>
      <template v-else-if="isHtml(format)">
        <VueIcon :icon="IcHtmlColor" class="text-[64px]" />
      </template>
      <template v-else-if="isPpt(format)">
        <VueIcon :icon="IcPptColor" class="text-[64px]" />
      </template>
      <template v-else-if="isTxt(format)">
        <VueIcon :icon="IcTxtColor" class="text-[64px]" />
      </template>
      <template v-else-if="isMp4(format)">
        <VueIcon :icon="IcYingxiangColor" class="text-[64px]" />
      </template>
      <template v-else-if="isUnPreviewImage(format)">
        <VueIcon :icon="IcYingxiangColor" class="text-[64px]" />
      </template>
      <template v-else>
        <img class="w-[100px] h-[80px]" :src="url" alt="" />
      </template>
    </div>
    <div class="text-left pt-[15px] text-[12px] px-[5px] truncate leading-[14px]">文件名称：{{ documentName }}</div>
    <div class="text-left pt-[10px] text-[12px] px-[5px] truncate leading-[14px]">文件类型：{{ documentFormat }}</div>
    <slot />
    <div class="absolute left-0 right-0 top-0 bottom-0 bg-[rgba(0,0,0,0.45)] justify-center items-center z-10 group-hover:flex hidden" @click="preview">
      <VueIcon :icon="IconPreviewFont" class="text-[16px] text-[#fff]" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IcPdfColor, IcWordColor, IcDianziqingdanColor, IconPreviewFont, IcWenjianColor, IcMailColor, IcHtmlColor, IcPptColor, IcTxtColor, IcYingxiangColor } from '@pafe/icons-icore-agr-an';
import { VueIcon } from '@pag/icon-vue3';
import { isWord, isPdf, isExcel, isZip, isMail, isHtml, isPpt, isTxt, isMp4, isUnPreviewImage } from '@/utils/tools';

const {
  format = '',
  url = '',
  documentName = '',
  bigUrl = '',
} = defineProps<{
  format?: string;
  url?: string;
  documentName?: string;
  documentFormat?: string;
  ocrIdentifiyResultCode?: string;
  ocrIdentifiyResultName?: string;
  previewMode: string;
  bigUrl?: string;
}>();

const emit = defineEmits(['preview']);

const preview = () => {
  emit('preview');
};
</script>
