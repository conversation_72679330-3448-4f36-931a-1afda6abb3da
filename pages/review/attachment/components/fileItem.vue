<template>
  <div id="file_dom" class="pl-[12px]">
    <div class="flex items-center justify-between sticky top-0 z-[300] bg-white border-b-[1px] border-[rgba(241,244,245,1)] border-solid border-x-0 border-t-0 pb-[5px]">
      <a-checkbox v-model:checked="checkAllState.checkAll" :indeterminate="checkAllState.indeterminate" @change="onCheckAllChange">全选</a-checkbox>
      <a-button type="primary" :disabled="checkAllState.checkedList.length === 0" :loading="loading" @click="getDownloadFileUrl">下载</a-button>
    </div>
    <div class="pt-[8px] flex flex-wrap">
      <a-checkbox-group v-model:value="checkAllState.checkedList" @change="checkedValue">
        <template v-for="(item, index) in fileList" :key="index">
          <div>
            <div class="content bg-[#FAFAFC] border border-[rgba(241,241,241,1)] border-solid rounded-[4px] mr-[10px] mb-[6px]">
              <SingleFile :format="item.documentFormat" :url="item.thumbnail" :big-url="item.bigThumbnail" :document-name="item.documentName" :document-format="item.documentFormat" :ocr-identifiy-result-code="item?.ocrIdentifiyResultCode" :ocr-identifiy-result-name="item.ocrIdentifiyResultName" :preview-mode="previewMode" @preview="preview(index, item)">
                <div class="absolute top-[6px] right-[8px] z-20">
                  <a-checkbox :value="item.uploadPath" />
                </div>
              </SingleFile>
              <a-empty v-if="fileList?.length === 0 || fileList === null" :image="simpleImage" />
            </div>
            <div v-if="item.ocrIdentifiyResultName" class="bg-[#fff]" :class="previewMode === '1' ? 'w-[300px]' : 'w-[160px]'">
              <div class="flex items-center justify-center">
                <div class="whitespace-normal text-center text-[#FAAD14] w-full leading-[16px] px-[6px] mb-[6px]" :class="previewMode === '1' ? 'text-[12px]' : 'text-[10px]'">{{ item?.ocrIdentifiyResultName }}</div>
              </div>
            </div>
          </div>
        </template>
      </a-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Empty } from 'ant-design-vue';
import type { fileListType } from '../attachment.d.ts';
import SingleFile from '../components/fileFall/SingleFile.vue';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
const { gateWay, service } = useRuntimeConfig().public || {};
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const checkAllState = reactive<{
  // 页面全选状态
  checkAll: boolean;
  indeterminate: boolean;
  checkedList: string[];
}>({
  checkAll: false,
  indeterminate: false,
  checkedList: [],
});
const { fileList = [] } = defineProps<{
  fileList: fileListType[];
  previewMode: string;
}>();
const onCheckAllChange = () => {
  if (checkAllState.checkAll) {
    checkAllState.indeterminate = false;
    checkAllState.checkedList = [];
    fileList.forEach((item) => {
      checkAllState.checkedList.push(item.uploadPath);
    });
  } else {
    checkAllState.checkedList = [];
  }
};
const emit = defineEmits(['preview']);
// 查看
const preview = (index: number, item) => {
  emit('preview', index, item);
};
// 控制身份信息等同层级的选择checkbox的样式
const checkedValue = () => {
  if (checkAllState.checkedList?.length > 0 && checkAllState.checkedList?.length !== fileList.length) {
    checkAllState.indeterminate = true;
  } else {
    checkAllState.indeterminate = false;
  }
  checkAllState.checkAll = checkAllState.checkedList.length === fileList.length;
};
const loading = ref<boolean>(false);
const route = useRoute();
const urlDate = ref<string>('');
// 获取下载url
const getDownloadFileUrl = async () => {
  const itemIdList = ref<string[]>([]);
  fileList.forEach((item) => {
    if (checkAllState.checkedList.includes(item.uploadPath)) {
      itemIdList.value.push(item.documentGroupItemsId);
    }
  });
  try {
    const params = {
      businessNo: route.query.bizNo,
      itemIdList: itemIdList.value,
    };
    loading.value = true;
    const res = await $postOnClient<{ fileUrl: string }>(`${gateWay}${service.document}/doc/downloadZip`, params);
    if (res && res.code === SUCCESS_CODE && res.data && res.data.fileUrl) {
      urlDate.value = res.data.fileUrl;
      downloadFile();
    } else {
      message.error(res?.msg || '下载失败');
    }
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};
// 下载
const downloadFile = () => {
  const url = gateWay + service.document + `/print/downloadCertificatePrint?url=${urlDate.value}`;
  window.open(url);
};
</script>
