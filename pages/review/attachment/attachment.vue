<template>
  <div class="attachment box-border bg-white mt-[16px] mx-[16px] rounded-t-[4px] px-[12px] pt-[16px] flex flex-col">
    <div class="flex items-center justify-between">
      <h4 class="m-0 text-[16px] text-[rgba(0,0,0,0.90)] font-bold leading-[20px]">附件管理</h4>
      <div class="flex gap-x-[10px]">
        <span>预览模式</span>
        <a-tooltip placement="top">
          <template #title>
            <span>大图预览</span>
          </template>
          <VueIcon :icon="IcDatuchakanFont" class="text-[18px] cursor-pointer" :class="{ 'text-[#07C160]': previewMode === '1' }" @click="changePreviewMode('1')" />
        </a-tooltip>
        <a-tooltip placement="top">
          <template #title>
            <span>小图预览</span>
          </template>
          <VueIcon :icon="IcXiaotuyulanFont" class="text-[18px] cursor-pointer" :class="{ 'text-[#07C160]': previewMode === '2' }" @click="changePreviewMode('2')" />
        </a-tooltip>
      </div>
    </div>
    <Board class="my-[10px]" :file-total="fileTotal" :risk-tip-list="riskTipList" />
    <div class="flex grow overflow-hidden">
      <div>
        <OverlayScrollbarsComponent class="left w-[240px] bg-[#FAFAFC] h-full box-border" @os-scroll="onScroll">
          <div class="px-[8px] py-[12px] treeContainer">
            <FileCatalogue v-model:selected-key="treeSelectKey" v-model:spin-loading="spinLoading" :tree-data="treeData" @handle-click="handleParentClick" />
          </div>
        </OverlayScrollbarsComponent>
      </div>
      <OverlayScrollbarsComponent class="grow h-full right box-border">
        <a-spin :spinning="loading">
          <div v-if="fileLists?.length > 0" class="py-[12px]">
            <div v-if="['AN02', ...VALIDATE_TYPE].includes(treeSelectKey) && (isDuplicateIDAndBank || hasDuplicates(fileLists))" class="bg-[#fff7e6] text-[#d46b08] px-[4px] py-[2px] mb-[3px]"><ExclamationCircleFilled class="text-[#F7BA1E] mr-[6px]" />身份信息和银行信息中存在相同照片，请关注</div>
            <fileItem v-model:selected-key="treeSelectKey" :file-list="fileLists" :preview-mode="previewMode" @preview="preview" />
            <div class="text-right">
              <a-pagination v-model:current="pageNum" v-model:page-size="pageSize" size="small" :total="totals" :show-total="(total) => `总共 ${totals} 条`" @change="handleChangePage" />
            </div>
          </div>
          <div v-else class="w-[100%] h-auto mt-[20%]">
            <a-empty :image="simpleImage" />
          </div>
        </a-spin>
      </OverlayScrollbarsComponent>
      <ImageViewer ref="viewer" :list="previewFileLists" :current="previewCurrent" :loading-more="loadingMore" @load-more="loadMore" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import { throttle, intersectionWith, uniqBy } from 'lodash-es';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue';
import { IcDatuchakanFont, IcXiaotuyulanFont } from '@pafe/icons-icore-agr-an';
import Board from './components/Board.vue';
import FileCatalogue from './components/FileCatalogue.vue';
import fileItem from './components/fileItem.vue';
import 'overlayscrollbars/styles/overlayscrollbars.css';
import type { FileInfo, FileManagerRes, fileListType, ListByPageType } from './attachment.d.ts';
import { $postOnClient, usePost, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE, VALIDATE_TYPE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import type { ViewerImageAndThumbnailItem } from '@/components/ui/imageViewer';
import { extractFileNameWithoutExtension, isPdf } from '@/utils/tools';
import type { DocumentNameResponse, fileNameItem } from '@/pages/insure/imageUpload/imageUpload.d.ts';
import { $post } from '@/utils/request';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { gateWay, service } = useRuntimeConfig().public || {};
const treeData = ref<FileInfo[]>([]); // 树数据
const treeSelectKey = ref(''); // 选中树节点
const fileLists = ref<fileListType[]>([]);
const previewFileLists = ref<fileListType[]>([]);
const route = useRoute();
const fileTotal = reactive<{
  needUploadFiles: string[];
  notUploadedFiles: string[];
  uploadedFiles: string[];
}>({
  needUploadFiles: [],
  notUploadedFiles: [],
  uploadedFiles: [],
});
const loading = ref<boolean>(false);
const fileManageReq = await usePost<FileManagerRes, { bizNo: string; bizType: string }>(`${gateWay}${service.administrate}/attachment/document/list`, { server: false }); // 附件列表接口,无需进行服务端调用接口
const spinLoading = ref<boolean>(false);
const loadingMore = ref(false);
const nextPageNum = ref(1);
const prevPageNum = ref(1);
const customNumberOfFile = reactive<{ [key: string]: number | undefined }>({});
// 预览模式 1为大图预览 2为小图预览
const previewMode = ref<string>('2');

const treeScrollDom = ref<Element | null>(null);
const tempScrollTop = ref(0); // 缓存的滚动条位置
// 当前选择大类下子类的code集合
const fileTypeCodes = computed(() => {
  return treeData?.value?.find((treeItem: FileInfo) => treeItem.key === treeSelectKey.value)?.children?.map((childrenItem: FileInfo) => childrenItem.key);
});

// 切换预览模式
const changePreviewMode = (mode: string) => {
  previewMode.value = mode;
};

const getfileManageInfo = async () => {
  // 获取文件列表
  try {
    loading.value = true;
    const { data, code, msg } = (await fileManageReq.fetchData({ bizNo: (route.query.bizNo || '') as string, bizType: (route.query.bizType || '') as string })) || {};
    // const { data, code, msg } = await fileManageReq.fetchData({ bizNo: '72104006500000015105', bizType: 'docTypeEndorse' }) || {};

    if (code === SUCCESS_CODE) {
      // 处理数据
      const fileInfoList = data?.fileTypeList || [];
      fileTotal.needUploadFiles = data?.requiredFileTypeList || [];
      fileTotal.notUploadedFiles = data?.unUploadedFileTypeList || [];
      fileTotal.uploadedFiles = data?.uploadedFileTypeList || [];
      treeData.value = fileInfoList?.map((item) => {
        (item?.children || [])?.forEach((dom) => {
          dom.title = dom.title + `（${dom.numberOfFile || 0}）`;
          if (VALIDATE_TYPE.includes(dom.key)) {
            customNumberOfFile[`${dom.key}No`] = dom.numberOfFile;
          }
        });
        return {
          ...item,
          title: item.title + `（${item.numberOfFile || 0}）`,
        };
      }); // 树数据
      if (fileInfoList[0]) {
        treeSelectKey.value = fileInfoList[0].key;
      } else {
        treeSelectKey.value = '';
      }
      handleClick();
      getDocList();
    } else {
      treeData.value = [];
      fileLists.value = [];
      message.error(msg || '请求有误，请稍后重试');
    }
    loading.value = false;
  } catch (err) {
    console.log(err);
    loading.value = false;
  }
};

// 获取滚动区域dom
const getScrollDom = async () => {
  if (treeScrollDom.value) return;
  await nextTick();
  const treeContainer = document.querySelector('.treeContainer');
  if (treeContainer && !treeScrollDom.value) {
    treeScrollDom.value = treeContainer.parentNode as Element;
  }
};

const riskTipList = ref<Record<string, string[]>>({});

const queryUnderwritingRiskAlert = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryUnderwritingRiskAlert`;
    const params = {
      applyPolicyNo: route.query.bizNo,
    };
    const res = await $getOnClient(fetchUrl, params);
    if (res?.code === SUCCESS_CODE && res.data) {
      riskTipList.value = res.data;
    } else {
      message.error(res?.msg);
    }
  } catch (err) {
    console.log(err);
  }
};

// watcheffect会调2次接口
watch(
  () => route.query.bizNo,
  (val) => {
    if (val) {
      // 重置缓存的滚动条距离
      tempScrollTop.value = 0;
      getfileManageInfo();
      queryUnderwritingRiskAlert();
    }
  },
  {
    immediate: true,
  },
);

onActivated(() => {
  recoverScroll();
});

// 缓存滚动距离
const onScroll = throttle(() => {
  if (treeScrollDom.value) {
    tempScrollTop.value = treeScrollDom.value.scrollTop;
  }
}, 200);

// 点击其他节点，分页为1，调用接口
const handleParentClick = () => {
  pageNum.value = 1;
  handleClick();
};
const totals = ref<number>(0);
const pageSize = ref<number>(20);
const pageNum = ref<number>(1);
const classifyList = ref<fileNameItem[]>();
// 获取身份信息、银行信息详情
const getDocList = async () => {
  // A13和A14同时存在时，获取文件列表
  if (customNumberOfFile?.A13No && customNumberOfFile?.A14No) {
    const fetchUrl = gateWay + service.administrate + '/attachment/document/name';
    try {
      const { code, data } = await $post<DocumentNameResponse>(fetchUrl, { bizNo: route.query.bizNo, bizType: route.query.bizType, docTypes: ['A13', 'A14'] });
      if (code === SUCCESS_CODE && data) {
        classifyList.value = data.fileNameList;
      }
    } catch (e) {
      console.log(e);
    }
  }
};
const hasDuplicates = (arr: fileListType[]) => arr?.length && arr?.length !== uniqBy(arr, 'documentName')?.length;
const isDuplicateIDAndBank = computed(() => {
  const A13Array: string[] = [];
  const A14Array: string[] = [];
  classifyList.value?.forEach((classify: fileNameItem) => {
    if (classify.docType === 'A13') {
      A13Array.push(...classify.nameList);
    } else if (classify.docType === 'A14') {
      A14Array.push(...classify.nameList);
    }
  });
  const intersectionArray = intersectionWith(A13Array, A14Array, (a, b) => a === b);
  return intersectionArray.length;
});

// 右侧文件列表
const handleClick = async () => {
  fileLists.value = [];
  try {
    loading.value = true;
    const requestUrl = route.query.bizType === 'docViewTreeApply' ? `${gateWay}${service.administrate}/attachment/document/risk/listByPage` : `${gateWay}${service.administrate}/attachment/document/listByPage`;
    const res = await $postOnClient<ListByPageType>(requestUrl, {
      bizNo: route.query.bizNo || '',
      bizType: route.query.bizType,
      fileTypeCode: treeSelectKey.value,
      fileTypeCodes: fileTypeCodes.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    });
    if (res?.code === SUCCESS_CODE) {
      const { fileList, total = 0, current = 1, size } = res?.data || {};
      fileLists.value = fileList || []; // 右侧文件列表
      totals.value = total;
      pageNum.value = current || 1;
      pageSize.value = size;
      getScrollDom();
    } else {
      fileLists.value = [];
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
    fileLists.value = [];
  }
};
const handleChangePage = (page: number) => {
  pageNum.value = page;
  handleClick();
};
const viewer = ref();
const previewCurrent = ref(0); // 当前要预览的文件id

// 文件下载和预览，预览仅支持pdf/word/excel/ppt
const download = async (image: ViewerImageAndThumbnailItem) => {
  // 后端要补充文件后缀
  const fileName = extractFileNameWithoutExtension(image.documentName || '') + '.' + image.documentFormat;
  try {
    const { data, msg, code } = (await $postOnClient('/api/iobs/getInIobsUrl', { fileKey: image.uploadPath, storageTypeCode: '02', iobsBucketName: image.bucketName, downloadFlag: false, fileName, documentFormat: image.documentFormat })) || {};
    if (SUCCESS_CODE === code) {
      const { fileUrl } = data;
      window.open(fileUrl);
    } else {
      message.error(msg || '');
    }
  } catch (err) {
    message.error(err?.message || '请求有误，请稍后重试');
  }
};
// 查看
// 如果是pdf直接下载
const preview = (index: number, item) => {
  if (isPdf(item.documentFormat)) {
    download(item);
  } else {
    previewCurrent.value = index;
    // 预览的文件去除pdf类型
    const noPdfFileList = fileLists.value.filter((fileItem) => !isPdf(fileItem.documentFormat));
    previewFileLists.value = noPdfFileList;
    nextPageNum.value = pageNum.value;
    prevPageNum.value = pageNum.value;
    if (viewer.value) {
      viewer.value?.openPreview();
    }
  }
};

const loadMoreData = async (pageNum: number): Promise<fileListType[]> => {
  let list: fileListType[] = [];
  try {
    const res = await $postOnClient<ListByPageType>(`${gateWay}${service.administrate}/attachment/document/listByPage`, {
      bizNo: route.query.bizNo || '',
      bizType: route.query.bizType,
      fileTypeCode: treeSelectKey.value,
      pageNum,
      pageSize: pageSize.value,
    });
    if (res?.code === SUCCESS_CODE) {
      const { fileList } = res?.data || {};
      list = fileList;
    }
    return list;
  } catch (error) {
    console.log(error);
    return list;
  }
};

const loadMore = async (type: 'prev' | 'next') => {
  if ((type === 'prev' && prevPageNum.value <= 1) || (type === 'next' && nextPageNum.value * pageSize.value >= totals.value)) {
    // 已经是第一页和最后一页了
    loadingMore.value = false;
  } else {
    loadingMore.value = true;
    // 加载数据，当前页面不翻页，仅仅是更新预览里面的数据
    const list = await loadMoreData(type === 'next' ? nextPageNum.value + 1 : prevPageNum.value - 1);
    if (list.length) {
      if (type === 'next') {
        previewFileLists.value = [...previewFileLists.value, ...list];
        nextPageNum.value += 1;
      } else {
        previewFileLists.value = [...list, ...previewFileLists.value];
        prevPageNum.value -= 1;
      }
    }
    loadingMore.value = false;
  }
};

// 切换树节点重置图片旋转角度
watch(treeSelectKey, (val) => {
  if (val && viewer.value) {
    viewer.value?.resetRotate();
  }
});

// 恢复缓存的滚动高度
const recoverScroll = () => {
  if (treeScrollDom.value && tempScrollTop.value) {
    treeScrollDom.value.scrollTo({
      top: tempScrollTop.value,
    });
  }
};
</script>

<style lang="less" scoped>
.attachment {
  height: calc(100% - 34px - 16px);
}
.h-full :deep(.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless) {
  top: 40px;
}
</style>
