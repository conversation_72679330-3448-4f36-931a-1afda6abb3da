<template>
  <div>
    <div class="form-title">{{ title }}</div>
    <div v-for="(data, index) in dataList" :key="data.id" class="grid grid-cols-3 gap-y-[12px] text-[#333333]">
      <div>
        <span class="label">客户属性</span>
        <CompareRender :value-path="`${personType}[${index}].personnelTypeChName`" :value="data.personnelTypeChName" />
      </div>
      <div>
        <span class="label">名称</span>
        <CompareRender :value-path="`${personType}[${index}].name`" :value="data.name" />
      </div>
      <div>
        <span class="label">证件类型</span>
        <CompareRender :value-path="`${personType}.certificateTypeChName`" :value="data.certificateTypeChName" />
      </div>
      <div>
        <span class="label">身份证</span>
        <CompareRender :value-path="`${personType}[${index}].certificateNo`" :value="data.certificateNo" />
      </div>
      <div>
        <span class="label">证件有效期</span>
        <CompareRender :value-path="`${personType}[${index}].certificateIssueDate`" :value="`${data.certificateIssueDate || '-'}`" />
        <span>-</span>
        <CompareRender :value-path="`${personType}[${index}].certificateValidDate`" :value="`${data.certificateValidDate || '-'}`" />
      </div>
      <div>
        <span class="label">地址</span>
        <CompareRender :value-path="`${personType}[${index}].address`" :value="data.address" />
      </div>
      <div>
        <span class="label">邮政编码</span>
        <CompareRender :value-path="`${personType}[${index}].postcode`" :value="data.postcode" />
      </div>
      <!-- <div>
        <span class="label">性别</span>
        <CompareRender :value-path="`${personType}[${index}].sexCodeChName`" :value="data.sexCodeChName" />
      </div> -->
      <div>
        <span class="label">联系电话</span>
        <CompareRender :value-path="`${personType}[${index}].mobileTelephone`" :value="data.mobileTelephone" />
      </div>
      <div>
        <span class="label">电子邮箱</span>
        <CompareRender :value-path="`${personType}[${index}].email`" :value="data.email" />
      </div>
      <div>
        <span class="label">农业主体类型</span>
        <CompareRender :value-path="`${personType}[${index}].subjectNumberName`" :value="data.subjectNumberName" />
      </div>
      <div v-if="personType === 'insurantInfoList'">
        <span class="label">贫困户标识</span>
        <CompareRender :value-path="`${personType}[${index}].poorSymbolChName`" :value="data.poorSymbolChName" />
      </div>
      <!-- 全部贫困户 && 二级机构是黑龙江(219)展示贫困户明细 -->
      <div v-if="personType === 'insurantInfoList' && data?.poorSymbol === '1' && secDeptCode === '219'">
        <span class="label">贫困户细分</span>
        <CompareRender :value-path="`${personType}[${index}].poorDetailTypeCnName`" :value="data.poorDetailTypeCnName" />
      </div>
      <!-- <div>
        <span class="label">企业归属</span>
        <CompareRender :value-path="`${personType}[${index}].belongOrganizationNoChName`" :value="data.belongOrganizationNoChName" />
      </div>
      <div>
        <span class="label">职业</span>
        <CompareRender :value-path="`${personType}[${index}].professionName`" :value="data.professionName" />
      </div> -->
      <!-- <div>
        <span class="label">国籍</span>
        <CompareRender :value-path="`${personType}[${index}].nationalityZh`" :value="data.nationalityZh" />
      </div> -->
      <div v-if="data?.personnelType === '0'" class="col-span-3">
        <template v-for="(item, index) in data?.superviseInfoList?.[0]?.superviseExtendList" :key="index">
          <!-- 授权办理业务人员 -->
          <div v-if="item.companyRelationType === '3'">
            <div class="h-px bg-[#E6E8EB] mb-[18px]" />
            <div class="form-title">
              <span>授权办理业务人员</span>
            </div>
            <a-descriptions>
              <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <!-- 法定代办人/负责人 -->
          <div v-if="item.companyRelationType === '2'">
            <div class="h-px bg-[#E6E8EB] mb-[18px]" />
            <div class="form-title">
              <span>法定代办人/负责人</span>
            </div>
            <a-descriptions>
              <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <!-- 控股股东/实际控制人 -->
          <div v-if="item.companyRelationType === '1'">
            <div class="h-px bg-[#E6E8EB] mb-[18px]" />
            <div class="form-title">
              <span>控股股东/实际控制人</span>
            </div>
            <a-descriptions>
              <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
            </a-descriptions>
          </div>
          <!-- 受益所有人 -->
          <div v-if="item.companyRelationType === '4'">
            <div class="h-px bg-[#E6E8EB] mb-[18px]" />
            <div class="form-title">
              <span>受益所有人</span>
            </div>
            <a-descriptions>
              <a-descriptions-item class="font-number" label="名称">{{ item.name || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件类型">{{ item.certificateTypeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件号码">{{ item.certificateNo || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效起期">{{ item.certificateIssueDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="证件有效止期">{{ item.certificateValidDate || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="地址">{{ item.address || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="判定受益所有人方式">{{ item.benefitModeChName || '-' }}</a-descriptions-item>
              <a-descriptions-item class="font-number" label="受益所有人持股数量或表决权占比（%）">{{ item.equityRatio || '-' }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CompareRender from './CompareRender.vue';

interface iData {
  id: string;
  personnelTypeChName: string;
  name: string;
  certificateTypeChName: string;
  certificateNo: string;
  certificateIssueDate: string;
  certificateValidDate: string;
  address: string;
  postcode: string;
  mobileTelephone: string;
  email: string;
  subjectNumberName: string;
  poorSymbolChName: string;
  poorSymbol: string;
  poorDetailTypeCnName: string;
  personnelType: string;
  superviseInfoList: iSuperviseInfoList[];
}

interface iSuperviseInfoList {
  superviseExtendList: iSuperviseExtendList[];
}

interface iSuperviseExtendList {
  companyRelationType: string;
  name: string;
  certificateTypeChName: string;
  certificateNo: string;
  certificateIssueDate: string;
  certificateValidDate: string;
  address: string;
  benefitModeChName: string;
  equityRatio: string;
}

defineProps<{ title: string; personType: string; dataList: iData[]; secDeptCode: string }>();
</script>

<style lang="less" scoped>
.label {
  margin-right: 12px;
}
</style>
