<template>
  <div ref="scrollWrapper" class="page-container">
    <a-spin :spinning="loading">
      <main class="main-wrapper">
        <div class="flex-1">
          <div class="h-[48px] w-ful bg-image-url">
            <span class="text-[16px] font-semibold text-[#00190c] leading-[48px] ml-[16px]">批改单信息</span>
          </div>
          <div class="flex items-center text-[14px] h-[46px] space-x-[24px] px-[24px] bg-white rounded-b-[4px]">
            <div>批改申请单号：{{ contractModel?.edrApplyBaseInfo?.endorseApplyNo }}</div>
            <div>保单号：{{ contractModel?.edrApplyBaseInfo?.policyNo }}</div>
            <div>录单人：{{ contractModel?.edrApplyBaseInfo?.inputBy }}</div>
            <div>批改类型：{{ contractModel?.edrApplySceneInfo?.sceneName }}</div>
          </div>
          <div class="relative mt-[14px] form-content">
            <InfoGroupBox id="base-info" title="批改内容">
              <ModifyInfo ref="modifyContentRef" v-model:data="contractModel.edrApplyBaseInfo" v-model:reason="contractModel.edrApplySceneInfo" :readonly="true" />
            </InfoGroupBox>
            <!-- 退保场景 -->
            <InfoGroupBox v-if="contractModel?.edrApplyBaseInfo?.sceneList === '00006'" id="surrender-info" title="退保信息">
              <div>退保金额：{{ positiveActualPremiumChange }}元</div>
            </InfoGroupBox>
            <!-- 保费来源批改场景 -->
            <template v-if="contractModel?.edrApplyBaseInfo?.sceneList === '90003' && contractModel.contract">
              <InfoGroupBox id="surrender-info" title="保费来源">
                <InsuranceSource v-model:change-data="contractModel.premiumSourceChangeVO" v-model:data="contractModel.contract.riskGroupInfoList" :gov-subsidy-type="contractModel.contract.baseInfo.govSubsidyType" :disabled="true" />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel?.edrApplyPayInfoVOList?.length > 0 || contractModel.value?.edrApplyPayInfoVOList !== null" id="fee-plan" title="收费计划">
                <a-table :columns="feePlanColumns" :data-source="contractModel?.edrApplyPayInfoVOList" :pagination="false" :scroll="{ y: '30vh' }">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'paymentPeriod'">
                      {{ record.paymentBeginDate + ' 至 ' + record.paymentEndDate }}
                    </template>
                    <template v-if="column.dataIndex === 'certificateNo'">
                      <span class="break-all">{{ record.certificateNo }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'bankAccountNo'">
                      <span class="break-all">{{ record.bankAccountNo }}</span>
                    </template>
                  </template>
                </a-table>
              </InfoGroupBox>
            </template>
            <!-- 整单批改场景 -->
            <template v-if="contractModel?.edrApplyBaseInfo?.sceneList === '90001' && contractModel.contract">
              <InfoGroupBox id="basic-info" title="基本信息">
                <BaseInfo ref="baseInfoRef" v-model:data="contractModel.contract.baseInfo" :readonly="true" />
              </InfoGroupBox>
              <InfoGroupBox id="list-info" title="清单">
                <FarmerInfo :contract-model="{ farmersCount: contractModel.insrSpltPlyEdrSumyDTO?.farmersCount, farmerListNo: contractModel.contract.baseInfo.farmerlistNo, endorseApplyNo: contractModel?.edrApplyBaseInfo?.endorseApplyNo }" :error-data-info="{}" />
              </InfoGroupBox>
              <InfoGroupBox id="customer-info" title="客户信息">
                <OrganizerInfo v-if="contractModel.contract.baseInfo?.applyPolicyType === '2'" class="mb-[12px]" title="投保组织者" :data="contractModel.contract.organizerInfo" person-type="organizerInfo" />
                <a-divider v-if="contractModel.contract.baseInfo?.applyPolicyType === '2'" />
                <PersonView class="mb-[12px]" title="被保人信息" :data-list="contractModel.contract.insurantInfoList" person-type="insurantInfoList" :sec-dept-code="contractModel.contract.baseInfo.secDeptCode" />
                <a-divider />
                <PersonView class="mb-[12px]" title="投保人信息" :data-list="contractModel.contract.applicantInfoList" person-type="applicantInfoList" />
                <a-divider />
                <PersonView title="受益人信息" :data-list="contractModel.contract.beneficaryInfoList" person-type="beneficaryInfoList" />
              </InfoGroupBox>
              <InfoGroupBox id="target-info" title="标的信息">
                <PlantRisk ref="riskFormRef" v-model:data="contractModel.contract.riskGroupInfoList" v-model:address-list="contractModel.contract.riskAddressInfoList" :department-code="contractModel.contract?.baseInfo?.departmentCode" :product-code="contractModel.contract?.baseInfo?.productCode" :product-version="contractModel.contract?.baseInfo?.productVersion" :readonly="true" page-from="endorseVerifyDetail" />
              </InfoGroupBox>
              <InfoGroupBox id="insurance-plan" title="保险方案">
                <InsurancePlan ref="planFormRef" v-model:data="contractModel.contract.riskGroupInfoList" :product-code="contractModel.contract?.baseInfo?.productCode" :product-version="contractModel.contract?.baseInfo?.productVersion" :department-code="contractModel.contract?.baseInfo?.departmentCode" :short-time-coefficient="contractModel.contract?.baseInfo?.shortTimeCoefficient" :gov-subsidy-type="contractModel.contract?.baseInfo.govSubsidyType" :sum-risk-group-amount="contractModel.contract?.sumRiskGroupAmount" :insured-number="contractModel.contract?.baseInfo?.insuredNumber" :farmers-count="contractModel.contract?.baseInfo?.farmersCount" :readonly="true" page-from="endorseVerifyDetail" />
              </InfoGroupBox>
              <InfoGroupBox v-if="contractModel?.edrApplyPayInfoVOList?.length > 0 || contractModel.value?.edrApplyPayInfoVOList !== null" id="fee-plan" title="收费计划">
                <a-table :columns="feePlanColumns" :data-source="contractModel?.edrApplyPayInfoVOList" :pagination="false" :scroll="{ y: '30vh' }">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'paymentPeriod'">
                      {{ record.paymentBeginDate + ' 至 ' + record.paymentEndDate }}
                    </template>
                    <template v-if="column.dataIndex === 'certificateNo'">
                      <span class="break-all">{{ record.certificateNo }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'bankAccountNo'">
                      <span class="break-all">{{ record.bankAccountNo }}</span>
                    </template>
                  </template>
                </a-table>
              </InfoGroupBox>
            </template>
            <InfoGroupBox id="underwriting-treatment" title="核保处理" content-bg="#ffffff">
              <UnderwritingTreatment ref="underwritingTreatmentRef" v-model:udw-process="udwProcess" :hidden-reject="true" />
            </InfoGroupBox>
          </div>
        </div>
        <!-- 侧边锚点导航 -->
        <div v-if="contractModel?.edrApplyBaseInfo?.sceneList === '90001'" class="right-sider">
          <div class="sticky top-[25px]">
            <span class="text-[#404442] font-semibold">大纲</span>
          </div>
          <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" @click="handleClick" />
        </div>
      </main>
    </a-spin>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" ghost @click="handleGoLink">附件管理</a-button>
      <a-button type="primary" ghost @click="goInspectionInfo">验标信息</a-button>
      <a-button type="primary" ghost @click="queryUnderwriteAssistant">核保轨迹</a-button>
      <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
    </footer>
    <!-- 点击核保信息按钮 -->
    <InsureTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
  </div>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import OrganizerInfo from './components/OrganizerInfo.vue';
import PersonView from './components/PersonView.vue';
import InsureTrack from '@/pages/review/insureProcess/components/InsureTrack.vue';
import ModifyInfo from '@/pages/endorse/endorseModify/components/ModifyInfo.vue';
import BaseInfo from '@/pages/endorse/endorseModify/components/BaseInfo.vue';
import InsurancePlan from '@/pages/endorse/endorseModify/components/InsurancePlan.vue';
import UnderwritingTreatment from '@/pages/review/insureProcess/components/UnderwritingTreatment.vue';
import PlantRisk from '@/pages/endorse/endorseModify/components/PlantRisk.vue';
import FarmerInfo from './components/FarmerInfo.vue';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import InsuranceSource from '@/pages/endorse/premiumSource/components/InsuranceSource.vue';
import { $getOnClient, $postOnClient } from '~/composables/request';
import { SUCCESS_CODE } from '~/utils/constants';
import { message } from '@/components/ui/Message';
import { pxToRem } from '@/utils/tools';
interface BaseInfo {
  farmersCount: number;
  farmerListNo: string;
  departmentCode?: string;
  productCode?: string;
  productVersion?: string;
  shortTimeCoefficient?: number;
  govSubsidyType?: string;
  insuredNumber?: number;
}

interface Contract {
  baseInfo: BaseInfo;
  insurantInfoList: Record<string, unknown>[];
  applicantInfoList: Record<string, unknown>[];
  beneficaryInfoList: Record<string, unknown>[];
  riskGroupInfoList: Record<string, unknown>[];
  riskAddressInfoList: Record<string, unknown>[];
  sumRiskGroupAmount?: number;
}

interface edrApplyBaseInfo {
  endorseApplyNo?: string;
  policyNo?: string;
  inputBy?: string;
  actualPremiumChange?: number;
  sceneList: string;
}

interface ContractModel {
  sceneName: string;
  edrApplyBaseInfo: edrApplyBaseInfo;
  contract?: Contract;
}

interface UdwProcess {
  taskId: string;
  voucherNo: string;
  approveStatus: string;
  approveResult?: string;
  approveResultDesc: string;
}

const route = useRoute();
const loading: Ref<boolean> = ref(false);
const scrollWrapper = ref<HTMLElement | null>(null);
const getContainer = () => scrollWrapper.value || window;
const anchorItems = ref([
  { key: '1', href: '#base-info', title: '批改内容' },
  { key: '2', href: '#basic-info', title: '基本信息' },
  { key: '3', href: '#list-info', title: '清单' },
  { key: '4', href: '#customer-info', title: '客户信息' },
  { key: '5', href: '#target-info', title: '标的信息' },
  { key: '5', href: '#insurance-plan', title: '保险方案' },
  { key: '7', href: '#fee-plan', title: '收费计划' },
  { key: '6', href: '#underwriting-treatment', title: '核保处理' },
]);

// 收费计划表头
const feePlanColumns: TableColumnsType = [
  { title: '期次', dataIndex: 'termNo', key: 'termNo', width: '5%' },
  { title: '补贴类型', dataIndex: 'payerTypeName', key: 'payerTypeName' },
  { title: '付款人名称', dataIndex: 'paymentPersonName', key: 'paymentPersonName' },
  { title: '收付途径', dataIndex: 'paymentPathName', width: pxToRem(100) },
  { title: '应收保费', dataIndex: 'actualPremium', key: 'actualPremium' },
  { title: '缴费起止期', dataIndex: 'paymentPeriod', key: 'paymentPeriod' },
  { title: '账户类型', dataIndex: 'bankAttributeName', width: pxToRem(100) },
  { title: '身份证', dataIndex: 'certificateNo', width: pxToRem(100) },
  { title: '银行总行', dataIndex: 'bankHeadquartersName', width: pxToRem(100) },
  { title: '银行分行', dataIndex: 'bankName', width: pxToRem(100) },
  { title: '开户行明细', dataIndex: 'bankDetail', width: pxToRem(100) },
  { title: '银行帐号', dataIndex: 'bankAccountNo', width: pxToRem(100) },
  { title: '预计收回日期', dataIndex: 'receivableDate', key: 'receivableDate' },
  { title: '备注', dataIndex: 'otherPayerTypeDesc', key: 'otherPayerTypeDesc' },
];
const compareValueMap = new Map<string, unknown>();

provide('compareValueMap', compareValueMap);

const { gateWay, service } = useRuntimeConfig().public || {};

const initContractModel = (data: Partial<ContractModel>): ContractModel => {
  return {
    edrApplyBaseInfo: data.edrApplyBaseInfo,
    edrApplySceneInfo: data.edrApplySceneInfo,
    contract: data.contract,
    insrSpltPlyEdrSumyDTO: data.insrSpltPlyEdrSumyDTO,
    edrApplyPayInfoVOList: data.edrApplyPayInfoVOList,
    premiumSourceChangeVO: data.premiumSourceChangeVO,
  };
};

const contractModel: Ref<ContractModel> = ref(initContractModel({}));

const positiveActualPremiumChange = computed(() => {
  return Math.abs(contractModel.value.edrApplyBaseInfo.actualPremiumChange || 0) || '';
});

const udwProcess: UdwProcess = reactive({
  taskId: '',
  voucherNo: '',
  approveStatus: '',
  approveResult: undefined,
  approveResultDesc: '',
});
// 标的信息数据处理
const setRiskData = () => {
  if (contractModel.value?.contract?.riskGroupInfoList?.length > 0) {
    contractModel.value.contract.riskGroupInfoList.forEach((item) => {
      item.defaultRisk = item.combinedProductCode;
    });
  }
};

const getDetail = async () => {
  const baseUrl = gateWay + service.ums;
  loading.value = true;
  try {
    const res = await $getOnClient(baseUrl + '/endorsement/queryEndorseHandlerInfo', {
      endorseApplyNo: route.query.endorseApplyNo,
    });
    if (!res) return;
    if (res.code === SUCCESS_CODE && res.data) {
      contractModel.value = initContractModel(res.data || {});
      if (contractModel.value?.edrApplyPayInfoVOList?.length === 0 || !contractModel.value?.edrApplyPayInfoVOList) {
        anchorItems.value = anchorItems.value.filter((item) => item.href !== '#fee-plan');
      }
      udwProcess.taskId = res.data.taskId;
      if (Array.isArray(res.data.diffValueList)) {
        res.data.diffValueList.forEach((obj: { path: string; value: unknown }) => {
          compareValueMap.set(obj.path, obj);
        });
      }
      setRiskData();
    } else {
      message.error(res.msg || '接口返回错误');
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const router = useRouter();
// 跳转验标页面
const goInspectionInfo = () => {
  router.push({
    path: '/inspectionInfo',
    query: {
      applyPolicyNo: contractModel.value?.contract?.baseInfo?.applyPolicyNo,
      isHiddenProcess: 'Y',
    },
  });
};

const processOpen: Ref<boolean> = ref(false);
const insureInfo: Ref<unknown[]> = ref([]);
const chainStr: Ref<string> = ref('');

const queryUnderwriteAssistant = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/evntApprovalTaskRecord/queryApprovalTaskList`;
    const res = await $getOnClient(fetchUrl, { voucherNo: contractModel.value?.edrApplyBaseInfo?.endorseApplyNo });
    if (!res) return;
    if (res.code === SUCCESS_CODE && res.data) {
      insureInfo.value = res.data.taskRecordVOList || [];
      chainStr.value = res.data.chainList.join('->');
      processOpen.value = true;
    } else {
      message.error(res.msg);
    }
  } catch (error) {
    console.log(error);
  }
};

const underwritingTreatmentRef = ref();
const { deletPageTabListItem } = inject('pageTab'); // 关闭页签

const submitLoading = ref(false);
const submit = async () => {
  const result = await underwritingTreatmentRef.value?.validate();
  if (result.valid) {
    const fetchUrl = `${gateWay}${service.ums}/endorsement/endorseUnderwriteProcess`;
    submitLoading.value = true;
    try {
      const res = await $postOnClient(fetchUrl, {
        dataSource: 'ICORE-AGR-AN',
        udwProcess: {
          ...udwProcess,
          voucherNo: contractModel.value?.edrApplyBaseInfo?.endorseApplyNo,
        },
      });
      if (res) {
        if (res.code === SUCCESS_CODE) {
          message.success(res.msg);
          router.push('/endorseVerify');
          deletPageTabListItem('/endorseVerifyDetail'); // 关闭页签
        } else {
          message.error(res.msg);
        }
      }
    } catch (e) {
      console.log(e);
    }
    submitLoading.value = false;
  }
};
// 跳转附件管理页面
const handleGoLink = () => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: route.query?.endorseApplyNo,
      bizType: 'docViewTreeEndorse',
      t: new Date().getTime(),
    },
  });
};

getDetail();
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    min-height: calc(100vh - 40px - 50px - 28px);
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .right-sider {
      border-left: 1px solid #e6e8eb;
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    width: 100%;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
