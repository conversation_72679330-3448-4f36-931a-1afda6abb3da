<template>
  <div class="m-14px space-y-16px">
    <a-spin :spinning="btnLoading" wrapper-class-name="search-spin-wrapper-css">
      <div class="bg-white rounded p-16px">
        <div class="flex">
          <a-form ref="formStateRef" :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: pxToRem(90) } }">
            <a-row :gutter="24">
              <a-col :span="9">
                <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, message: '不能为空', trigger: 'change' }]">
                  <department-search v-model:contain-child-depart="formState.containChildDepart" v-model:loading="btnLoading" :dept-code="formState.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item label="申请日期" required name="applyDate">
                  <a-range-picker v-model:value="formState.applyDate" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单号">
                  <a-input v-model:value.trim="formState.voucherNo" placeholder="可输入批改申请单号/批单号/保单号，不支持模糊搜索" allow-clear @blur="handleChange" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="11">
                <a-form-item label="核保状态" name="status" :rules="[{ required: true, trigger: 'change', message: '请选择核保状态' }]">
                  <CheckBoxGroup v-model:checked-list="formState.status" :disabled="disabled" :options="reviewStatusOptions" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="被保险人">
                  <a-input v-model:value.trim="formState.insuredPersonName" placeholder="请输入" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand" :gutter="16">
              <a-col :span="24">
                <a-form-item label="标的">
                  <RiskCodeSelect v-model:value="formState.riskType" :department-code="formState.departmentCode" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <div class="w-[50px]">
            <form-fold v-model="expand" />
          </div>
        </div>
        <div class="flex justify-center items-center space-x-8px">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" ghost @click="submit">查询</a-button>
        </div>
      </div>
    </a-spin>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(49,40,40,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'statusName'">
            <a-tag :bordered="false" :color="tagMap[record?.statusCode]?.color">
              <template #icon>
                <VueIcon :icon="tagMap[record?.statusCode]?.icon" />
              </template>
              {{ text }}
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'checkStatus'">
            <a-tag v-if="record.checkStatus === '0'" :bordered="false" color="#E6AD1C">
              <template #icon>
                <VueIcon :icon="IconErrorCircleFilledFont" />
              </template>
              未验标
            </a-tag>
            <a-tag v-if="record.checkStatus === '0'" :bordered="false" color="#07C160">
              <template #icon>
                <VueIcon :icon="IconCheckCircleFilledFont" />
              </template>
              已验标
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'voucherNo'">
            <CopyLink :text="record.endorseNo || text" @click="openEndorseDetail(record.endorseNo || text, text)" />
          </template>
          <template v-if="column.dataIndex === 'policyNo'">
            <CopyLink :text="text" @click="openDetail(text, record.applyPolicyNo)" />
          </template>
          <template v-if="['productName', 'insuredCustomerName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <a-button type="link" :disabled="!record.ableProcess" @click="handleDetail(record)">处理</a-button>
              <a-button type="link" @click="handlerCorrectingHistory(record.policyNo)">历史批改</a-button>
              <a-button type="link" @click="handlerClaimsRecord(record.policyNo)">理赔记录</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs, { type Dayjs } from 'dayjs';
import type { ColumnsType } from 'ant-design-vue/es/table';
import { IconCheckCircleFilledFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import type { PolicyReviewType, dataType } from './endorseVerify.d';
import { ReviewStatus } from '@/enums/insure';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import CopyLink from '@/components/ui/CopyLink.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import FormFold from '@/components/ui/FormFold.vue';

const tagMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
  B7: { color: 'warning', text: '拒保', icon: IconErrorCircleFilledFont },
  B3: { color: 'processing', text: '待审核', icon: IconErrorCircleFilledFont },
  B4: { color: 'success', text: '已同意', icon: IconCheckCircleFilledFont },
  B21: { color: 'blue', text: '审核中', icon: IconErrorCircleFilledFont },
  B2: { color: 'processing', text: '已下发', icon: IconErrorCircleFilledFont },
};
const listColumns: ColumnsType<Record<string, string>> = [
  { title: '出单机构', dataIndex: 'departmentAbbrName' },
  { title: '批改申请号/批单号', dataIndex: 'voucherNo' },
  { title: '产品名称', dataIndex: 'productName' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '被保险人', dataIndex: 'insuredCustomerName' },
  { title: '最新保费', dataIndex: 'premium' },
  { title: '保费变化', dataIndex: 'endorsePremiumChange' },
  { title: '批改场景', dataIndex: 'endorseScenes' },
  { title: '录单人', dataIndex: 'inputBy' },
  { title: '核保状态', dataIndex: 'statusName' },
  { title: '核保日期', dataIndex: 'underwritingTime' },
  { title: '验标状态', dataIndex: 'checkName' },
  { title: '申请日期', dataIndex: 'applyDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

const reviewStatusOptions = [
  { label: '待核保', value: ReviewStatus.Pending },
  { label: '审核中', value: ReviewStatus.InReview },
  { label: '已下发', value: ReviewStatus.Issued },
  { label: '已同意', value: ReviewStatus.Approved },
];
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
interface FormState {
  departmentCode: string;
  voucherNo: string;
  containChildDepart: boolean; // 是否包含下级机构
  applyDate: [Dayjs, Dayjs]; // 创建时间
  productCode: undefined;
  riskType: string;
  status: string[];
  province: undefined;
  city: undefined;
  town: undefined;
  county: undefined;
  village: undefined;
  riskAddr: string;
  insuredPersonName: string;
}
const formState = reactive<FormState>({
  departmentCode: defaultDeptCode.value,
  voucherNo: '',
  containChildDepart: true,
  productCode: undefined,
  riskType: '',
  status: [ReviewStatus.Pending, ReviewStatus.InReview],
  province: undefined,
  city: undefined,
  town: undefined,
  county: undefined,
  village: undefined,
  riskAddr: '',
  applyDate: [dayjs().subtract(1, 'month'), dayjs()],
  insuredPersonName: '',
});
const route = useRoute();
if (route.query.startTime && route.query.endTime) {
  formState.applyDate = [dayjs(route.query.startTime), dayjs(route.query.endTime)];
}
const insuranceDate = ref<[Dayjs, Dayjs] | undefined>(); // 保险日期数组
const btnLoading = ref<boolean>(false);
// 单号失焦，核保状态全选
const disabled = ref<boolean>(false);
const handleChange = async () => {
  if (formState.voucherNo) {
    formState.status = [ReviewStatus.Pending, ReviewStatus.InReview, ReviewStatus.Issued, ReviewStatus.Approved];
    disabled.value = true;
    btnLoading.value = true;
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: formState.voucherNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      formState.departmentCode = res.data?.insureDepartmentNo || '';
    }
    btnLoading.value = false;
  } else {
    formState.status = [ReviewStatus.Pending];
    disabled.value = false;
    formState.departmentCode = defaultDeptCode.value;
    btnLoading.value = false;
  }
};
// 搜索项是否展开
const expand = ref(false);
const changeDeptCode = (val: string) => {
  formState.departmentCode = val;
};
const formStateRef = ref();
// 重置
const resetForm = () => {
  insuranceDate.value = undefined; // 保险日期数组
  formStateRef.value.resetFields();
  submit();
};
// 跳转批改信息详情页面 ENDORSE_APPLY_NO/ENDORSE_NO
const openEndorseDetail = (voucherNo: string, endorseApplyNo: string) => {
  router.push({
    path: '/approvalTraceCheck',
    query: {
      documentNo: voucherNo,
      documentType: voucherNo.slice(0, 1) === '3' ? 'ENDORSE_NO' : 'ENDORSE_APPLY_NO',
      t: new Date().getTime(),
      endorseApplyNo,
    },
  });
};
// 打开保单详情页
const openDetail = (policyNo: string, applyPolicyNo: string) => {
  const path = {
    path: '/policyDetails',
    query: {
      policyNo: policyNo,
      applyPolicyNo: applyPolicyNo,
    },
  };
  router.push(path);
};
// 查询
const submit = () => {
  formStateRef.value.validate().then(() => {
    pagination.pageSize = 10;
    pagination.current = 1;
    refresh();
  });
};
// 初始化列表接口
const dataSource = ref<PolicyReviewType[]>([]);
const loading = ref(false);
const refresh = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.ums}/endorsement/queryEndorsementPage`;
    // 日期数据组装
    const dateFormat = 'YYYY-MM-DD';
    const [insuranceBeginDate, insuranceEndDate] = insuranceDate.value || [];
    const [applyBeginDate, applyEndDate] = formState.applyDate || [];
    const { province, city, town, county, village } = formState;
    const params = {
      ...formState,
      containChildDepart: formState.containChildDepart ? '1' : '0',
      insuranceBeginDate: insuranceBeginDate?.format(dateFormat),
      insuranceEndDate: insuranceEndDate?.format(dateFormat),
      applyBeginDate: applyBeginDate?.format(dateFormat),
      applyEndDate: applyEndDate?.format(dateFormat),
      riskAddr: `${province || ''}${city || ''}${town || ''}${county || ''}${village || ''}`,
      pageSize: pagination.pageSize,
      pageNum: pagination.current,
    };
    const res = await $postOnClient(fetchUrl, params);
    if (res?.code === SUCCESS_CODE) {
      const { records, total = 0, current = 1, size = 10 } = (res?.data as dataType) || {};
      dataSource.value = records;
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
      loading.value = false;
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    loading.value = false;
    dataSource.value = [];
    pagination.total = 0;
    pagination.current = 1;
    pagination.pageSize = 10;
    console.log(error);
  }
};
const router = useRouter();
// 点击处理按钮，跳转批单处理页面
const handleDetail = (record: Record<string, string>) => {
  router.push({
    path: '/endorseVerifyDetail',
    query: {
      endorseApplyNo: record.voucherNo,
      t: new Date().getTime(),
    },
  });
};
// 批改历史
const handlerCorrectingHistory = (policyNo: string) => {
  router.push({
    name: 'endorseHistory',
    query: {
      policyNo,
    },
  });
};
// 理赔记录
const handlerClaimsRecord = (policyNo: string) => {
  router.push({
    name: 'claimRecord',
    query: { policyNo },
  });
};
// 分页处理
const { pagination } = usePagination(refresh);

onActivated(() => {
  refresh();
});
const { gateWay, service } = useRuntimeConfig().public || {};
</script>
