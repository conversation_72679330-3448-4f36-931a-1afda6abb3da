<template>
  <div>
    <a-form ref="formRef" :model="formState" :colon="false" :label-col="{ style: { width: pxToRem(110) } }">
      <div class="grid grid-cols-4 gap-x-16px">
        <a-form-item :required="!commissionFlag" label="协办费" name="assisterCharge" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="formState.assisterCharge" :disabled="commissionFlag" addon-after="%" @change="handleAssisterCharge" />
        </a-form-item>
        <a-form-item :required="!brokerageFlag" label="手续费/经纪费" name="commissionBrokerChargeProportion" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="formState.commissionBrokerChargeProportion" :disabled="brokerageFlag" addon-after="%" @change="handleCommission" />
        </a-form-item>
        <a-form-item required label="工作经费" name="managementFees" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="formState.managementFees" addon-after="%" />
        </a-form-item>
        <a-form-item required label="农险补贴" name="performanceValue1Default" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="formState.performanceValue1Default" addon-after="%" />
        </a-form-item>
      </div>
      <div class="grid grid-cols-4 gap-x-16px">
        <a-form-item :required="false" label="防灾防损费" name="calamitySecurityRate" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="formState.calamitySecurityRate" :disabled="true" addon-after="%" />
        </a-form-item>
        <a-form-item :required="!commonGuaranteeFlag" label="共保出单费" name="coinsuranceInsureFeeRatio" :rules="[{ validator: coinsuranceInfoRule }]">
          <a-input v-model:value="formState.coinsuranceInsureFeeRatio" :disabled="commonGuaranteeFlag" addon-after="%" />
        </a-form-item>
      </div>
    </a-form>
    <!-- 总费率提示 -->
    <div v-show="totalRateFlag" class="col-span-12 mt-10px ml-16px text-[#F00] text-[12px]">费用比例之和超过{{ formState.totalSumFeeLimit }}%，请留意</div>
    <!-- 风险评估提示 -->
    <div v-show="riskTotalTips" class="col-span-12 mt-10px ml-16px text-[12px]" :class="isHighRiskMode ? 'text-[#f00]' : 'text-[#FAAD14]'">{{ riskTotalTips }}</div>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { contractModelType, FeeInfoType } from '../insureProcess.d';
import { pxToRem } from '@/utils/tools';

const formState = defineModel<FeeInfoType>('costInfo', { default: {} });
// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
// 校验
const validateNumberFee = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!value) {
    return Promise.reject('');
  }
  if (!reg.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过2位');
  }
  if (Number(value) > 100) {
    return Promise.reject('请输入0~100范围内的数字');
  }
  const _name: string = field + 'Limit';
  if (Number(value) > Number(formState.value[_name])) {
    return Promise.reject('输入值超过当前上限值');
  }
  return Promise.resolve();
};
// 出单费校验
const coinsuranceInfoRule = (rule: Rule, value: string) => {
  if (!commonGuaranteeFlag.value) {
    return validateNumberFee(rule, value);
  } else {
    return Promise.resolve();
  }
};
// 当前输入的总费率计算,如过超过上限则提示
const totalRateFlag = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = formState.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);
  return Number(formState.value.totalSumFeeLimit) < count;
});
// 当前输入的总费率如果是风险评估，根据规则展示提示
const riskTotalTips = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = formState.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);

  // busiModeNo为01 商险业务模式为保险+期货，存在跟单费用，请注意
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '01') {
    return '商险业务模式为保险+期货，存在跟单费用，请注意';
  }

  // busiModeNo为02 商险业务模式为二次开发类，存在跟单费用，请注意
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '02') {
    return '商险业务模式为二次开发类，存在跟单费用，请注意';
  }

  // busiModeNo为03 15-20% 提示商险业务模式为银抵类，跟单费用超15%，请注意 超过20%提示商险业务模式为银抵类，跟单费用不可超20%，请调整
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '03') {
    if (count > 20) {
      return '商险业务模式为银抵类，跟单费用不可超20%，请调整';
    } else if (count > 15) {
      return '商险业务模式为银抵类，跟单费用超15%，请注意';
    }
  }

  // busiModeNo为04 10-20% 商险业务模式为创新储备政策类，跟单费用超10%，请注意 超过20%提示商险业务模式为创新储备政策类，跟单费用不可超20%，请调整
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '04') {
    if (count > 20) {
      return '商险业务模式为创新储备政策类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为创新储备政策类，跟单费用超10%，请注意';
    }
  }

  // busiModeNo为05 10-20% 商险业务模式为创新储备非政策类，跟单费用超10%，请注意 超过20%提示商险业务模式为创新储备非政策类，跟单费用不可超20%，请调整
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '05') {
    if (count > 20) {
      return '商险业务模式为创新储备非政策类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为创新储备非政策类，跟单费用超10%，请注意';
    }
  }

  // busiModeNo为06 10-20% 商险业务模式为其他类，跟单费用超10%，请注意 超过20%提示商险业务模式为其他类，跟单费用不可超20%，请调整
  if (contractModel?.insurRiskRptApiDTO?.busiModeNo === '06') {
    if (count > 20) {
      return '商险业务模式为其他类，跟单费用不可超20%，请调整';
    } else if (count > 10) {
      return '商险业务模式为其他类，跟单费用超10%，请注意';
    }
  }

  return '';
});

// 判断是否为03、04、05、06业务模式且费用大于20%
const isHighRiskMode = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = formState.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);

  const highRiskModes = ['03', '04', '05', '06'];
  return highRiskModes.includes(contractModel?.insurRiskRptApiDTO?.busiModeNo) && count > 20;
});
/** 协办费是否disabled
当非从共业务农保ID选择无时，协办费不可录入
1.系统外共保&是主承&农保id无 协办费禁用
2.系统内共保&农保id无 协办费禁用
3.系统外同时系统内共保&主承&农保id无 协办费禁用
渠道来源为代理禁用-2 */
const commissionFlag = computed(() => {
  const { coinsuranceMark, acceptInsuranceFlag, assisterInfoList } = contractModel?.baseInfo || {};
  const { innerCoinsuranceMark } = contractModel?.coinsuranceInfo || {};
  const len = assisterInfoList?.length === 0 || assisterInfoList?.[0]?.assisterId === '无';
  return (len && (acceptInsuranceFlag === '1' || innerCoinsuranceMark === '1')) || (len && coinsuranceMark === '0');
});
/** 共保出单是否disabled
系统外共保-0&主承-是则禁用；系统外同时系统内共保-2&主承则禁用；是否选择共保-否则禁用；上限值为0则禁用;选择共保类型-系统内共保禁用
innerCoinsuranceMark-1-系统内共保禁用 */
const commonGuaranteeFlag = computed(() => {
  const { coinsuranceMark, acceptInsuranceFlag } = contractModel?.baseInfo || {};
  const { innerCoinsuranceMark } = contractModel?.coinsuranceInfo || {};
  return innerCoinsuranceMark === '1' || acceptInsuranceFlag === '1' || coinsuranceMark === '0';
});
/** 手续经费是否disabled-补贴类型为政策性1,2
补贴类型为政策性：baseInfo.govSubsidyType='1' 或者 '2'
补贴类型为商业性：baseInfo.govSubsidyType='3'
渠道来源细分包含“直接”：saleInfo.businessSourceCode='1'
渠道来源细分包含“代理”&“经纪”：saleInfo.businessSourceCode='2'或'3'
当补贴类型=政策性，此项置灰不允许填写；
当补贴类型=商业性&渠道来源细分包含“直接”， 此项置灰不允许填写
当补贴类型=商业性&渠道来源细分包含“代理/经纪”，此项必录 */
const brokerageFlag = computed(() => ['1', '2'].includes(contractModel?.baseInfo?.govSubsidyType) || (contractModel?.baseInfo?.govSubsidyType === '3' && contractModel?.saleInfo?.businessSourceCode === '1'));
// 禁用设置值为0
watch(brokerageFlag, (val) => {
  if (val) {
    formState.value.commissionBrokerChargeProportion = '0';
  } else {
    formState.value.commissionBrokerChargeProportion = '';
  }
});
// 由于保费计算后，后端值未落库，返回空，且brokerageFlag值是没有变化的，所以需要监听当前值，如果为禁用状态值添加0，防止表单校验住
watch(
  () => formState.value,
  () => {
    if (brokerageFlag.value) formState.value.commissionBrokerChargeProportion = '0';
    if (commissionFlag.value) formState.value.assisterCharge = '0';
  },
);
// 协办费有值，经纪代理费为0
const handleAssisterCharge = () => {
  formState.value.commissionBrokerChargeProportion = '0';
};
// 手续费/经纪费有值，经纪代理费为0
const handleCommission = () => {
  formState.value.assisterCharge = '0';
};
// 出单保费禁用-清空值
watch(commonGuaranteeFlag, (val) => {
  if (val) {
    formState.value.coinsuranceInsureFeeRatio = '';
    formRef.value.clearValidate('coinsuranceInsureFeeRatio');
  }
});
const formRef = ref();
const validate = async () => {
  if (formRef.value) {
    if (isHighRiskMode.value) {
      return { valid: false };
    }
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
defineExpose({
  validate,
});
</script>
