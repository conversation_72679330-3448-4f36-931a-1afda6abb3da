<!-- 渠道信息 -->
<template>
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
    渠道信息
    <a-tooltip placement="bottom">
      <template #title>
        <div v-for="(v, i) of riskTipList.channelInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
      </template>
      <VueIcon v-if="riskTipList?.channelInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
    </a-tooltip>
  </div>
  <div class="grid grid-cols-3 gap-x-16px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">出单机构</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ channelInfoData?.departmentCodeAndName || '-' }}</div>
        <div v-for="(item, index) in errorDataInfo?.channelInfo?.departmentCode" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否共展</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ channelInfoData?.developFlgChName || '-' }}</div>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-3 gap-x-16px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">渠道来源</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ channelInfoData?.channelSourceName || '-' }}</div>
        <div v-for="(item, index) in errorDataInfo?.channelInfo?.channelSourceName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">渠道来源细分</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ channelInfoData?.channelSourceDetailName || '-' }}</div>
        <div v-for="(item, index) in errorDataInfo?.channelInfo?.channelSourceDetailName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </div>
    </div>
  </div>
  <div v-for="(item, i) in channelInfoData?.employeeInfoList" :key="i" class="grid grid-cols-3 gap-x-16px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">业务员信息</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number flex items-center break-all">{{ item.employeeName || '-' }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">业务员执业证号</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ item.employeeProfCertifNo || '-' }}</div>
      </div>
    </div>
    <div v-if="channelInfoData?.developFlg === 'Y'" class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">占比</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">
          {{ item.commisionScale || '-' }} %
          <span v-if="item.mainEmployeeFlag === '1'">（主业务员）</span>
        </div>
      </div>
    </div>
  </div>
  <a-descriptions :colon="false">
    <!-- 代理人 -->
    <template v-if="channelInfoData?.businessSourceCode === '2'">
      <a-descriptions-item class="font-number" label="代理人">{{ channelInfoData?.agentInfoList?.[0]?.agentName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="代理协议">{{ channelInfoData?.agentInfoList?.[0]?.agentAgreementNoChName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="中介执业证号">{{ channelInfoData?.agentInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="中介人员名称">{{ channelInfoData?.agentInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
    </template>
    <!-- 经纪人 -->
    <template v-if="channelInfoData?.businessSourceCode === '3'">
      <a-descriptions-item class="font-number" label="经纪人">{{ channelInfoData?.brokerInfoList?.[0]?.brokerName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="中介执业证号">{{ channelInfoData?.brokerInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="中介人员名称">{{ channelInfoData?.brokerInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
    </template>
    <!-- 主介绍人 -->
    <template v-if="showIntroducer">
      <a-descriptions-item class="font-number" label="主介绍人代码">{{ channelInfoData?.primaryIntroducerInfo?.primaryIntroducerCode || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="主介绍人名称">{{ channelInfoData?.primaryIntroducerInfo?.primaryIntroducerName || '-' }}</a-descriptions-item>
    </template>
  </a-descriptions>
  <div class="grid grid-cols-1 gap-x-16px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">农保ID</div>
      <div class="pl-[10px]">
        <div v-if="contractModel?.baseInfo?.assisterInfoList?.length > 0" class="text-[#333333] flex items-center break-all">
          <div v-for="(item, index) in contractModel?.baseInfo?.assisterInfoList" :key="index" class="shrink-0">{{ item.assisterId === '无' ? '-' : item.assisterId }}<span v-if="index < contractModel?.baseInfo?.assisterInfoList?.length - 1">;</span></div>
        </div>
        <div v-else class="text-[#333333] font-number">-</div>
        <div v-for="(item, index) in errorDataInfo?.channelInfo?.assisterId" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ContractModelType, ErrorModelType } from '../insureProcess.d';
import { FujianFxtsColor } from '@pafe/icons-icore-agr-an';

// 获取信息大对象
const { contractModel, errorDataInfo, riskTipList } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
  riskTipList: Record<string, string[]>;
}>();
// 渠道信息数据处理
const channelInfoData = computed(() => contractModel?.saleInfo);
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
// 主介绍人
const showIntroducer = ref(false);
watchEffect(() => {
  const channelSourceCode = contractModel?.saleInfo?.channelSourceCode;
  const channelSourceDetailCode = contractModel?.saleInfo?.channelSourceDetailCode?.split(',')?.[0] ?? '';
  if ((channelSourceCode === '7' && ['D', 'L'].includes(channelSourceDetailCode)) || (channelSourceCode === 'C' && channelSourceDetailCode === 'G')) {
    showIntroducer.value = true;
  } else {
    showIntroducer.value = false;
  }
});
</script>

<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
:deep(.ant-descriptions-item) {
  padding-bottom: 8px;
  .ant-descriptions-item-label {
    color: rgba(0, 0, 0, 0.6);
  }
  .ant-descriptions-item-content {
    color: #333;
  }
}
</style>
