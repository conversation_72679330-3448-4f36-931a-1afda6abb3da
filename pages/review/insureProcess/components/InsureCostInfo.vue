<!-- 保费信息 -->
<template>
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      财务标识
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.financialFlag" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.financialFlag?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <div class="pl-[10px]">{{ contractModel?.costInfo?.isPolicyBeforePayfeeChName || '-' }}</div>
    <div v-for="(item, index) in errorDataInfo?.isPolicyBeforePayfeeChName" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}<a-button v-if="item.extendDesc" :href="item.extendDesc" target="_blank" size="small" class="ml-[8px]" type="primary">查看签报</a-button></div>
    <a-divider />
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      保费来源
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.premiumSource" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.premiumSource?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <div v-for="(item, index) in errorDataInfo?.riskAgrInfoExtend" :key="index" class="text-[12px] pb-[8px] pl-[6px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
    <a-table class="mb-[12px] free-table" :columns="sourceColumns" :data-source="sourceList" :pagination="false">
      <template #bodyCell="{ column, text, record }">
        <template v-if="['planName'].includes(column.dataIndex as string)">
          <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
            <div class="max-w-[180px] truncate">{{ text }}</div>
          </a-tooltip>
        </template>
        <template v-if="['centralFinance', 'provincialFinance', 'cityFinance', 'countyFinance', 'farmersFinance', 'otherFinance'].includes(column.dataIndex as string)">
          <span>{{ text }}</span>
          <div v-for="(item, index) in errorDataInfo?.riskAgrInfo?.[column.dataIndex as string]" :key="index">
            <div v-if="item.extendDesc === record.planCode" :class="colorText(item.promptLevel)" class="text-[10px]">{{ item.promptInfo }}</div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] pt-[8px]">
    应收信息
    <a-tooltip placement="bottom">
      <template #title>
        <div v-for="(v, i) of riskTipList.receivableInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
      </template>
      <VueIcon v-if="riskTipList?.receivableInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
    </a-tooltip>
  </div>
  <div v-for="(item, index) in errorDataInfo?.payInfo" :key="index" class="text-[12px] pb-[12px] pl-[8px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
  <!-- 表格 -->
  <a-table class="free-table" :data-source="contractModel.payInfoList" :columns="receivableColumns" :pagination="false">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'certificateNo'">
        <span class="break-all">{{ record.certificateNo }}</span>
      </template>
      <template v-if="column.dataIndex === 'bankAccountNo'">
        <span class="break-all">{{ record.bankAccountNo }}</span>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import type { TableProps, TableColumnsType } from 'ant-design-vue';
import type { ContractModelType, ErrorModelType } from '../insureProcess.d';
import { pxToRem } from '@/utils/tools';
import { FujianFxtsColor } from '@pafe/icons-icore-agr-an';

// 获取信息大对象
const { contractModel, errorDataInfo, riskTipList } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
  riskTipList: Record<string, string[]>;
}>();

// 保费来源表格datasource
const sourceList = computed(() => {
  if (contractModel.riskGroupInfoList?.length === 1) {
    // 单标的
    return contractModel.riskGroupInfoList[0].planInfoList;
  } else {
    // 多标的 合并所有的主险和附加险
    const uniquePlanCodes = new Set();
    const result = [];

    contractModel.riskGroupInfoList.forEach((item) => {
      item.planInfoList.forEach((plan) => {
        if (!uniquePlanCodes.has(plan.planCode)) {
          uniquePlanCodes.add(plan.planCode);
          result.push(plan);
        }
      });
    });

    return result;
  }
});
// 应收信息-表格
const receivableColumns: TableProps['columns'] = [
  { title: '期次', dataIndex: 'termNo', width: pxToRem(50) },
  { title: '补贴类型', dataIndex: 'payerTypeName', width: pxToRem(100) },
  { title: '付款人名称', dataIndex: 'paymentPersonName', width: pxToRem(100) },
  { title: '收付途径', dataIndex: 'paymentPathName', width: pxToRem(100) },
  { title: '支付方式', dataIndex: 'paymentPathName', width: pxToRem(100) },
  { title: '应收保费', dataIndex: 'actualPremium', width: pxToRem(100) },
  { title: '缴费起期', dataIndex: 'paymentBeginDate', width: pxToRem(100) },
  { title: '缴费止期', dataIndex: 'paymentEndDate', width: pxToRem(100) },
  { title: '账户类型', dataIndex: 'bankAttributeName', width: pxToRem(100) },
  { title: '身份证', dataIndex: 'certificateNo', width: pxToRem(100) },
  { title: '银行总行', dataIndex: 'bankHeadquartersName', width: pxToRem(100) },
  { title: '银行分行', dataIndex: 'bankName', width: pxToRem(100) },
  { title: '开户行明细', dataIndex: 'bankDetail', width: pxToRem(100) },
  { title: '银行帐号', dataIndex: 'bankAccountNo', width: pxToRem(100) },
];
// 保费来源columns
const sourceColumns: TableColumnsType = [
  { title: '险种代码', dataIndex: 'planCode', key: 'planCode', width: pxToRem(120) },
  { title: '险种名称', dataIndex: 'planName', key: 'planName', width: pxToRem(120) },
  { title: '中央补贴(%)', dataIndex: 'centralFinance', key: 'centralFinance' },
  { title: '省级补贴(%)', dataIndex: 'provincialFinance', key: 'provincialFinance' },
  { title: '地方补贴(%)', dataIndex: 'cityFinance', key: 'cityFinance' },
  { title: '县级补贴(%)', dataIndex: 'countyFinance', key: 'countyFinance' },
  { title: '农户自缴(%)', dataIndex: 'farmersFinance', key: 'farmersFinance' },
  { title: '其他补贴(%)', dataIndex: 'otherFinance', key: 'otherFinance' },
];
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
</script>

<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
.free-table :deep(.ant-table-content) {
  max-height: 220px;
  overflow-y: auto !important;
}
.free-table :deep(.ant-table-thead) {
  position: sticky;
  top: 0;
  z-index: 999;
}
.free-table :deep(.ant-table-empty .ant-table-content) {
  overflow-y: hidden !important;
}
</style>
