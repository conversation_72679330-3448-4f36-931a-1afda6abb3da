<!-- 基础信息 -->
<template>
  <div class="text-[#333333] text-[16px] font-[600]">
    投保方式
    <a-tooltip placement="bottom">
      <template #title>
        <div v-for="(v, i) of riskTipList.applicationMethod" :key="i">{{ i + 1 }}、{{ v }}</div>
      </template>
      <VueIcon v-if="riskTipList?.applicationMethod?.length" :icon="FujianFxtsColor" />
    </a-tooltip>
  </div>
  <p>{{ contractModel?.baseInfo?.applyPolicyTypeChName || '-' }}</p>
  <a-divider />
  <div class="text-[#333333] text-[16px] font-[600]">
    客户信息
    <a-tooltip placement="bottom">
      <template #title>
        <div v-for="(v, i) of riskTipList.customerInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
      </template>
      <VueIcon v-if="riskTipList?.customerInfo?.length" :icon="FujianFxtsColor" />
    </a-tooltip>
  </div>
  <customerInfo :contract-model="contractModel" :error-data-info="errorDataInfo" :is-insure-process="true" />
  <!-- 项目信息 -->
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] text-[14px]">项目信息</div>
  <productInfo :contract-model="contractModel" :error-data-info="errorDataInfo" />
  <!-- 业务模式-暂时不展示 -->
  <div v-if="false && projectInfos?.govSubsidyType === COMMERCEMODE">
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)] text-[14px]">
      业务模式
      <span class="text-[12px] text-[#faad14] font-normal ml-[6px]">请重点核实商业险信息准确性，并确认承保信息是否与业务评估信息一致</span>
    </div>
    <a-form ref="formRef" :model="formState" label-align="left" :colon="false" :label-col="{ style: { width: pxToRem(110) } }">
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item label="商业险模式" name="agrCommercialInsuranceModeCategory" :rules="[{ required: true, message: '请选择商业险模式' }]">
          <a-select v-model:value="formState.agrCommercialInsuranceModeCategory" :options="commerceModeList" @change="handleSelChange" />
        </a-form-item>
        <a-form-item v-if="formState.agrCommercialInsuranceModeCategory && formState.agrCommercialInsuranceModeCategory !== SECONDARYDEV" label="预估风险" name="riskFactor" :rules="[{ required: true, message: '请选择预估风险' }]">
          <a-input-number v-model:value="formState.riskFactor" class="w-full" :min="0" addon-after="%" @blur="handleFactorChange" />
        </a-form-item>
        <a-form-item v-if="formState.agrCommercialInsuranceModeCategory === SECONDARYDEV" label="关联保单" name="agrCommercialRelatedPolicyNo" :rules="[{ required: true, validator: validatePolicyNo, trigger: 'blur' }]">
          <a-select v-model:value="formState.agrCommercialRelatedPolicyNo" class="w-full" mode="tags" max-tag-count="responsive" :max-tag-text-length="5" :options="insuranceNoList" />
        </a-form-item>
        <a-form-item v-if="formState.agrCommercialInsuranceModeCategory === SECONDARYDEV" label="客户整体预估风险" name="customerWholeRiskDegree" :label-col="{ style: { width: pxToRem(130) } }" :rules="[{ required: true, message: '请输入客户整体预估风险' }]">
          <a-input-number v-model:value="formState.customerWholeRiskDegree" class="w-full" :min="0" addon-after="%" @blur="handleFactorChange" />
        </a-form-item>
      </div>
      <a-form-item v-if="formState.agrCommercialInsuranceModeCategory" label="业务评估材料" name="attachmentNo" :rules="[{ required: true, message: '请上传文件' }]">
        <a-spin :spinning="loading">
          <a-upload :accept="accept" :show-upload-list="false" :before-upload="handleFileChange">
            <a-button>
              <template #icon>
                <VueIcon :icon="IconTongyongShangchuanFont" />
              </template>
              <span class="ml-[2px]">上传文件</span>
            </a-button>
            <span class="ml-[8px] text-[#333]">仅支持附件类型：.eml,.word,.pdf,.png,.jpg</span>
          </a-upload>
          <div v-for="fileInfo in fileList" :key="fileInfo?.documentGroupItemsId" class="text-12px text-[#4E6085]">
            <VueIcon :icon="IconAttachmentFont" />
            <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo)">{{ fileInfo.documentName }}</span>
            <a-button type="link" @click="deleteFile(fileInfo)">删除</a-button>
          </div>
        </a-spin>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { ContractModelType, ErrorModelType, agrCommercialModelType, fielType } from '../insureProcess.d';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { IconTongyongShangchuanFont, IconAttachmentFont, FujianFxtsColor } from '@pafe/icons-icore-agr-an';
import customerInfo from '@/pages/detail/customerInfo/customerInfo.vue';
import productInfo from '@/pages/detail/productInfo/productInfo.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
// 获取信息大对象
const { contractModel, errorDataInfo, riskTipList } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
  riskTipList: Record<string, string[]>;
}>();

// 被保人
const insurantInfo = computed(() => contractModel?.insurantInfoList[0]);
// 项目信息
const projectInfos = computed(() => contractModel?.extendInfo);
// 业务模式表单
const COMMERCEMODE = '3'; // 商业性码值
const SECONDARYDEV = '02'; // 商业险模式二次开发类value
const formState = defineModel<agrCommercialModelType>('agrCommercialModelInfo', { default: { agrCommercialRelatedPolicyNo: [], attachmentNo: [] } });
const loading = ref(false);
const fileList = ref<fielType[]>([]);
const accept = '.eml,.word,.pdf,.png,.jpg';
const formRef = ref();
const commerceModeList = ref([]);
const insuranceNoList = ref([]);

const validatePolicyNo = async (_rule: Rule & { field?: string }, value: string[]) => {
  if (value.length === 0) {
    return Promise.reject('请输入关联保单');
  }
  const params = {
    certificateNo: insurantInfo.value.certificateNo,
    name: insurantInfo.value.name,
    townCode: contractModel?.riskAddressInfoList[0]?.county || '',
    isGroup: projectInfos.value?.applyPolicyType === '2',
    policyNoList: formState.value.agrCommercialRelatedPolicyNo,
    applyPolicyNo: contractModel.baseInfo.applyPolicyNo,
  };
  const fetchUrl = `${gateWay}${service.policy}/icoreAgr/validatePolicyNosForCommercialInsurance`;
  const { data, code, msg } = await $postOnClient(fetchUrl, params);
  if (code === SUCCESS_CODE && data) {
    if (data.notPolicys.length > 0 || data.notPersons.length > 0) {
      if (data.notPolicys.length > 0) {
        const notPolicys = data.notPolicys.join(',');
        return Promise.reject(`保单号${notPolicys}的保险起期非近两年保单，请修改`);
      }
      if (data.notPersons.length > 0) {
        const notPersons = data.notPersons.join(',');
        return Promise.reject(`保单号${notPersons}县域和当前单不一致或保单号${notPersons}被保险人和当前单不一致`);
      }
    } else {
      return Promise.resolve();
    }
  } else {
    return Promise.reject(msg);
  }
};

const handleFactorChange = (e) => {
  const val = e?.target?.value;
  if (val && val > 90) {
    message.error('预估风险过高，请核实');
  }
};

const querySelData = async () => {
  const fetchUrl = `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`;
  const { data, code } = await $postOnClient(fetchUrl, ['commercialInsuranceMode']);
  if (code === SUCCESS_CODE && data) {
    commerceModeList.value = data?.[0]?.children || [];
  }
};
// 查询上传文件列表
const queryFileList = async () => {
  if (!formState.value.agrCommercialInsuranceModeCategory) {
    fileList.value = [];
    return;
  }
  const fetchUrl = `${gateWay}${service.administrate}/attachment/document/listByPage`;
  const params = {
    bizNo: contractModel.baseInfo.applyPolicyNo,
    bizType: 'docTypeUnderWrite',
    fileTypeCode: 'A33',
    pageNum: 1,
    pageSize: 100,
  };
  const { data, code } = await $postOnClient(fetchUrl, params);
  if (code === SUCCESS_CODE && data) {
    fileList.value = data.fileList || [];
    formState.value.attachmentNo = data.fileList?.map((item: fielType) => item.documentGroupItemsId) || [];
  }
};

const queryInsuranceNoList = async () => {
  const params = {
    certificateNo: insurantInfo.value.certificateNo,
    name: insurantInfo.value.name,
    townCode: contractModel?.riskAddressInfoList[0]?.county || '',
    applyPolicyNo: contractModel.baseInfo.applyPolicyNo,
  };
  const fetchUrl = `${gateWay}${service.policy}/icoreAgr/queryGovernmentPolicyNosByCustomerInfoInLastYear`;
  const { data, code } = await $postOnClient(fetchUrl, params);
  if (code === SUCCESS_CODE) {
    insuranceNoList.value = data.map((item: string) => ({ label: item, value: item }));
  }
};

const handleSelChange = async (val: string) => {
  if (val !== '02') {
    formState.value.agrCommercialRelatedPolicyNo = [];
    formState.value.customerWholeRiskDegree = null;
  } else {
    formState.value.riskFactor = null;
  }
};

const handleFileChange = async (file: File & { uid: string }) => {
  let canUpload = true;
  const fileExtension = file.name.split('.').pop()?.toUpperCase();
  if (!fileExtension || !['EML', 'WORD', 'PDF', 'PNG', 'JPG'].includes(fileExtension)) {
    message.warning('只能上传eml、word、pdf、png、jpg格式文件');
    canUpload = false;
  }
  if (canUpload) {
    try {
      loading.value = true;
      const fetchUrl = `${gateWay}${service.administrate}/attachment/document/upload`;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bizNo', contractModel.baseInfo.applyPolicyNo);
      formData.append('fileType', 'A33');
      formData.append('bizType', 'docTypeUnderWrite');

      const { data, code, msg } = await $postOnClient(fetchUrl, formData);
      if (code === SUCCESS_CODE) {
        fileList.value.push({ ...data });
        formState.value.attachmentNo.push(data.documentGroupItemsId);
      } else {
        message.error(msg || '文件上传失败，请重试');
      }
      loading.value = false;
    } catch (e) {
      console.error('Upload error:', e);
      message.error('文件上传失败，请重试');
    } finally {
      loading.value = false;
    }
  }
  return false;
};
// 下载文件
const downloadFile = async (file: fielType) => {
  loading.value = true;
  const { data } =
    (await $postOnClient('/api/iobs/getInIobsUrl', {
      iobsBucketName: file.bucketName,
      fileKey: file.uploadPath,
      storageTypeCode: file.storageType,
      fileName: file.documentName,
      downloadFlag: true,
    })) || {};
  loading.value = false;
  if (data?.fileUrl) {
    window.open(data?.fileUrl);
  }
};
// 删除文件
const deleteFile = (file: fielType) => {
  if (fileList.value.length === 0) return;
  const delReq = {
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
    bizNo: contractModel.baseInfo.applyPolicyNo,
    bizType: 'docTypeUnderWrite',
  };
  loading.value = true;
  $postOnClient(`${gateWay}${service.administrate}/attachment/document/delete`, delReq)
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        fileList.value = fileList.value.filter((fileInfo) => fileInfo.documentId !== file.documentId);
        if (fileList.value.length === 0) {
          formState.value.attachmentNo = [];
        }
      }
    })
    .finally(() => (loading.value = false));
};
const validate = async () => {
  if (projectInfos.value?.govSubsidyType !== COMMERCEMODE) {
    return { valid: true, errors: [] };
  }
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
watch(
  () => projectInfos.value,
  (val) => {
    if (val?.govSubsidyType === COMMERCEMODE) {
      queryFileList();
      querySelData();
      // 组织投保不自动带出
      if (val?.applyPolicyType !== '2') {
        queryInsuranceNoList();
      }
    }
  },
  { deep: true, immediate: true },
);
defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
</style>
