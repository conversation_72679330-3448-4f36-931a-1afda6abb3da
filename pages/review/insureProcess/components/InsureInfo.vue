<template>
  <!-- 保险方案 -->
  <div>
    <div v-for="(item, index) in errorDataInfo?.insurePlan" :key="index" class="text-[12px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      续保信息
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.renewalInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.renewalInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <a-form ref="formRef" :colon="false" :model="baseInfo">
      <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
        <a-form-item label="是否属于续保" name="renewalType" :rules="[{ required: true, message: '请选择' }]">
          <a-radio-group v-model:value="baseInfo.renewalType" @change="handleChange">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <div class="flex items-baseline">
          <div class="text-[rgba(0,0,0,0.60)] shrink-0">被续保保单号</div>
          <div class="pl-[10px]">
            <a-form-item name="lastPolicyNo" :rules="[{ required: baseInfo.renewalType === '1' ? true : false, message: '请输入' }]">
              <a-input v-model:value="baseInfo.lastPolicyNo" :disabled="contractModel?.baseInfo?.renewalType !== '1'" class="w-[200px]" placeholder="请输入" />
            </a-form-item>
            <div v-for="(item, index) in errorDataInfo?.renewalInsuranceInfo?.renewalInsuranceVoucherNo" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
          </div>
        </div>
      </div>
    </a-form>
  </div>
  <a-divider />
  <div>
    <!-- 标的信息 -->
    <plantRisk :contract-model="contractModel" :error-data-info="errorDataInfo" :risk-tip-list="riskTipList" />
  </div>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      产品信息
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.productInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.productInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险起期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ baseInfo?.insuranceBeginDate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.baseInfo?.insuranceBeginDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}<a-button v-if="item.extendDesc" :href="item.extendDesc" target="_blank" size="small" class="ml-[8px]" type="primary">查看签报</a-button></div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险期限</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ baseInfo?.insuranceBeginEndDateType || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.baseInfo?.insurancePeriod" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">年限系数</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ baseInfo?.shortTimeCoefficient || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.baseInfo?.shortTimeCoefficient" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">保险止期</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ baseInfo?.insuranceEndDate || '-' }}</div>
          <div v-for="(item, index) in errorDataInfo?.baseInfo?.insuranceEndDate" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">起止时间</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ baseInfo?.timeRange === '0' ? '0-24时' : '12-12时' }}</div>
          <div v-for="(item, index) in errorDataInfo?.baseInfo?.timeRange" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">减免系数</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] font-number">{{ reductionCoefficient }}</div>
          <div v-for="(item, index) in errorDataInfo?.productInfo?.reductionCoefficient" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">农户是否代缴</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ substitute }}</div>
          <div v-for="(item, index) in errorDataInfo?.productInfo?.substitute" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 产品信息-表格 -->
  <div v-for="(riskGroup, riskGroupIndex) in contractModel?.riskGroupInfoList" :key="riskGroupIndex" class="w-[73vw] mb-[16px]">
    <a-table :data-source="riskGroup?.planInfoList" :columns="columns" :pagination="false" class="free-table" :scroll="{ x: 'max-content' }" :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : '')">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'riskName'">
          <div class="max-w-[80px] truncate">{{ riskGroup.combinedProductName }}</div>
        </template>
        <template v-if="['planName'].includes(column.dataIndex as string)">
          <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
            <div class="max-w-[150px] truncate">{{ text }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'isMain'">
          {{ text === '1' ? '主险' : '附加险' }}
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <a-button size="small" type="primary" @click="handleDuty(record)">责任</a-button>
            <a-button size="small" type="primary" @click="downTermOrRate('term', record.planCode)">条款</a-button>
            <a-button size="small" type="primary" @click="downTermOrRate('rate', record.planCode)">费率</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <div class="mt-[16px]">
    <div class="text-[rgba(0,0,0,.4)]">总计：保单保险金额={{ baseInfo?.totalInsuredAmount }} ｜ 减免前保单保费金额={{ baseInfo?.totalStandardPremium }} ｜ 减免后保单保单保费金额={{ baseInfo?.totalAgreePremium }} ｜ 保单复核保费={{ baseInfo?.totalActualPremium }}</div>
    <div v-for="(riskGroup, riskGroupIndex) in contractModel?.riskGroupInfoList" :key="riskGroupIndex" class="text-[rgba(0,0,0,.4)] pl-[40px] mt-[4px]">标的名称 {{ riskGroup.combinedProductName }} ｜ 保险数量：{{ riskGroup.riskAgrInfo?.insuredNumber }}{{ riskGroup.riskAgrInfo?.insuredUnitChName || '-' }} ｜ 参保户次：{{ riskGroup.riskAgrInfo?.farmersCount }}户</div>
  </div>
  <div v-for="(item, index) in errorDataInfo?.baseInfo?.totalInsuredAmount" :key="index" class="text-[12px] pt-[4px] pl-[6px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      特约信息
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.specialAgreementInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.specialAgreementInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <div v-for="(item, index) in errorDataInfo?.specialPromise" :key="index" class="text-[12px] pb-[8px] pl-[6px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
    <a-table v-if="contractModel?.specialPromiseList?.length > 0" :columns="specialColumns" :data-source="contractModel.specialPromiseList" :pagination="false" />
    <a-table v-else :columns="specialColumns" :data-source="specialDataDefault" :pagination="false">
      <template #bodyCell="{ column }">
        <template v-if="column.dataIndex === 'promiseTypeChName'">通用特约</template>
      </template>
    </a-table>
  </div>
  <div v-if="contractModel?.noclaimInfoList?.length > 0" class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">免赔信息</div>
  <!-- 免赔-表格 -->
  <a-table v-if="contractModel?.noclaimInfoList?.length > 0" class="free-table" :data-source="contractModel?.noclaimInfoList" :columns="freeColumns" :pagination="false">
    <template #bodyCell="{ column, text, index }">
      <template v-if="['noclaimItemZh'].includes(column.dataIndex as string)">
        <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
          <div class="max-w-[300px] truncate">{{ text }}</div>
        </a-tooltip>
      </template>
      <template v-if="column.dataIndex === 'noclaimRate'">
        <div>{{ text }}</div>
        <div v-for="(item, inx) in errorDataInfo?.noclaimInfoList?.[index]?.noclaimRate" :key="inx" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </template>
    </template>
  </a-table>
  <div v-if="contractModel?.noclaimInfoList?.length > 0" class="pt-[12px] pl-[8px]"><span class="text-[rgba(0,0,0,0.60)]">免赔描述：</span>{{ contractModel?.baseInfo?.deductionDesc || '-' }}</div>
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
    争议信息
    <a-tooltip placement="bottom">
      <template #title>
        <div v-for="(v, i) of riskTipList.disputeInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
      </template>
      <VueIcon v-if="riskTipList?.disputeInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
    </a-tooltip>
  </div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">争议处理方式</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ contractModel?.baseInfo?.disputedSettleModeChName || '-' }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">仲裁机构</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ contractModel?.baseInfo?.arbitralDepartment || '-' }}</div>
        <div v-for="(item, index) in errorDataInfo?.productInfo?.substitute" :key="index" class="text-[12px] pt-[4px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}</div>
      </div>
    </div>
  </div>
  <a-divider />
  <div>
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">
      费用信息
      <a-tooltip placement="bottom">
        <template #title>
          <div v-for="(v, i) of riskTipList.feeInfo" :key="i">{{ i + 1 }}、{{ v }}</div>
        </template>
        <VueIcon v-if="riskTipList?.feeInfo?.length" :icon="FujianFxtsColor" class="ml-[4px]" />
      </a-tooltip>
    </div>
    <!-- 费用信息组件 -->
    <slot />
  </div>
  <!-- 责任弹窗 -->
  <a-modal v-model:open="visible" title="责任" centered :width="pxToRem(600)">
    <div v-if="dutyInfoList?.length > 0">
      <div v-for="(item, index) in dutyInfoList" :key="index">{{ item.dutyCode }}{{ item.dutyName }}</div>
    </div>
    <a-empty v-else :image="simpleImage" />
    <template #footer>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { TableProps, TableColumnsType } from 'ant-design-vue';
import { Empty } from 'ant-design-vue';
import type { ContractModelType, dutyInfoListType, PlanInfoType, BaseInfoType, ErrorModelType } from '../insureProcess.d';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import plantRisk from '@/pages/detail/plantRisk/plantRisk.vue';
import { FujianFxtsColor } from '@pafe/icons-icore-agr-an';

const { gateWay, service } = useRuntimeConfig().public || {};
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { contractModel, errorDataInfo, riskTipList } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
  riskTipList: Record<string, string[]>;
}>();

const reductionCoefficient = computed(() => {
  const value = contractModel?.riskGroupInfoList[0]?.riskAgrInfo?.reductionCoefficient;
  return typeof value === 'undefined' ? '-' : value;
});
const substitute = computed(() => {
  return contractModel?.riskGroupInfoList[0]?.riskAgrInfo?.substitute === 'Y' ? '是' : '否';
});
const baseInfo = defineModel<BaseInfoType>('baseInfo', { default: {} });
// 点击责任按钮-显示弹窗
const dutyInfoList = ref<dutyInfoListType[]>([]);
const visible = ref<boolean>(false);
const handleDuty = (record: PlanInfoType) => {
  dutyInfoList.value = record.dutyInfoList || [];
  visible.value = true;
};
// 产品信息-表格
const columns: TableProps['columns'] = [
  { title: '标的名称', dataIndex: 'riskName' },
  { title: '险种码', dataIndex: 'planCode' },
  { title: '险种名称', dataIndex: 'planName' },
  { title: '主险/附加险', dataIndex: 'isMain' },
  { title: '每次赔偿金额', dataIndex: 'eachCompensationMaxAmount' },
  { title: '单位保险金额', dataIndex: 'unitInsuredAmount' },
  { title: '保险金额', dataIndex: 'totalInsuredAmount' },
  { title: '保险费率（%）', dataIndex: 'expectPremiumRate' },
  { title: '单位保费', dataIndex: 'unitPrimium' },
  { title: '基准保费金额', dataIndex: 'totalStandardPremium' },
  { title: '减免后保费金额', dataIndex: 'totalAgreePremium' },
  { title: '复核保费', dataIndex: 'totalActualPremium' },
  { title: '文件', dataIndex: 'operation', fixed: 'right' },
];
// 免赔-表格
const freeColumns: TableProps['columns'] = [
  { title: '免赔项目', dataIndex: 'noclaimItemZh', width: '40%' },
  { title: '免赔类型', dataIndex: 'noclaimTypeZh', width: '10%' },
  { title: '免赔额', dataIndex: 'noclaimAmount', width: '10%' },
  { title: '免赔率（%）', dataIndex: 'noclaimRate', width: '20%' },
];
// 产品信息数据处理，为空用'-'代替
// const baseInfo = computed(() => contractModel?.baseInfo);
// 是否续保 true-是，false-否
// const isBelongRenewalInsuranceText = computed(() => (contractModel?.baseInfo?.renewalType === '1' ? '是' : '否'));
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
type UrlType = {
  url: string;
};
// 下载文件-条款&费率
const downTermOrRate = async (downType: string, planCode: string) => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/downTermOrRate`;
    const res = await $postOnClient<UrlType>(fetchUrl, { planCode, downType, acceptInsuranceTime: new Date().getTime().toString() });
    if (res?.code === SUCCESS_CODE) {
      window.open(res?.data?.url);
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
    message.error('请求失败');
  }
};
// 特约表头
const specialColumns: TableColumnsType = [
  { title: '特约类型', dataIndex: 'promiseTypeChName', width: pxToRem(100) },
  { title: '特约内容', dataIndex: 'promiseDesc' },
];
// 特约无数据时默认数据
const specialDataDefault = [
  {
    promiseCode: '', // 编码
    promiseDesc: '无其他特别约定', // 特约内容
    promiseType: '',
  },
];
const handleChange = () => {
  if (baseInfo.value.renewalType === '0') {
    baseInfo.value.lastPolicyNo = '';
  }
};
const formRef = ref();
const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
.ant-table-cell .ant-btn.ant-btn-sm:first-of-type,
.ant-table-cell .ant-btn:first-of-type {
  padding-left: 8px;
}
.red {
  color: #f03e3e;
}
.green {
  color: #07c160;
}
.free-table :deep(.ant-table-content) {
  max-height: 220px;
  overflow-y: auto !important;
}
.free-table :deep(.ant-table-thead) {
  position: sticky;
  top: 0;
  z-index: 999;
}
.free-table :deep(.ant-table-empty .ant-table-content) {
  overflow-y: hidden !important;
}
</style>
