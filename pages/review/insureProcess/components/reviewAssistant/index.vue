<template>
  <div ref="container" class="bg-white rounded mb-12px px-12px sticky top-[0px] z-[100] shadow-[0px_2px_12px_0px_rgba(212,214,217,1)]">
    <div class="flex justify-between items-center h-[52px]">
      <div class="text-xl font-semibold text-[#333333]">核保助手</div>
      <div class="flex items-center">
        <!-- <a-button type="primary" ghost>按图查看</a-button> -->
        <div class="text-[#576b95] cursor-pointer pl-[24px]" @click="toggleExpand">
          <VueIcon :icon="IconChevronRightDoubleFont" :style="{ transform: expand ? 'rotate(-90deg)' : 'rotate(90deg)' }" />
          <span class="ml-[5px]">{{ expand ? '收起更多' : '查看更多' }}</span>
        </div>
      </div>
    </div>
    <div class="relative mx-[-12px]">
      <div v-show="showNormalBoard" class="grid grid-cols-3 gap-12px pb-12px">
        <!-- 经营分析 -->
        <BusinessAnalysis :review-assistant-info="reviewAssistantInfo" @handle-risk-ok="queryUnderwriteAssistant" />
        <!-- 客户分析 -->
        <CustomerAnalysis :review-assistant-info="reviewAssistantInfo" :farmer-list-no="farmerListNo" />
        <!-- 验标分析 -->
        <CheckAnalysis :review-assistant-info="reviewAssistantInfo" />
        <!-- 地块分析 -->
        <LandBlockAnalysis :review-assistant-info="reviewAssistantInfo" />
        <!-- 标的分析 -->
        <RiskAnalysis :review-assistant-info="reviewAssistantInfo" />
        <!-- 权属分析 -->
        <OwnerAnalysis :review-assistant-info="reviewAssistantInfo" />
        <!-- 其他风险 -->
        <OtherRisk :review-assistant-info="reviewAssistantInfo" />
        <!-- 附件资料 -->
        <AttachmentInformation :review-assistant-info="reviewAssistantInfo" />
      </div>
      <div v-show="showAbsoluteBoard" class="absolute z-[1000] w-full bg-white shadow-[0px_2px_12px_0px_rgba(212,214,217,1)] h-[260px]">
        <OverlayScrollbarsComponent class="h-full w-full">
          <div class="grid grid-cols-3 gap-12px pb-12px">
            <!-- 经营分析 -->
            <BusinessAnalysis :review-assistant-info="reviewAssistantInfo" @handle-risk-ok="queryUnderwriteAssistant" />
            <!-- 客户分析 -->
            <CustomerAnalysis :review-assistant-info="reviewAssistantInfo" />
            <!-- 验标分析 -->
            <CheckAnalysis :review-assistant-info="reviewAssistantInfo" />
            <!-- 地块分析 -->
            <LandBlockAnalysis :review-assistant-info="reviewAssistantInfo" />
            <!-- 标的分析 -->
            <RiskAnalysis :review-assistant-info="reviewAssistantInfo" />
            <!-- 权属分析 -->
            <OwnerAnalysis :review-assistant-info="reviewAssistantInfo" />
            <!-- 其他风险 -->
            <OtherRisk :review-assistant-info="reviewAssistantInfo" />
            <!-- 附件资料 -->
            <AttachmentInformation :review-assistant-info="reviewAssistantInfo" />
          </div>
        </OverlayScrollbarsComponent>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IconChevronRightDoubleFont } from '@pafe/icons-icore-agr-an';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue';
import { throttle } from 'lodash-es';
import BusinessAnalysis from './BusinessAnalysis.vue';
import CustomerAnalysis from './CustomerAnalysis.vue';
import LandBlockAnalysis from './LandBlockAnalysis.vue';
import RiskAnalysis from './RiskAnalysis.vue';
import OwnerAnalysis from './OwnerAnalysis.vue';
import OtherRisk from './OtherRisk.vue';
import CheckAnalysis from './CheckAnalysis.vue';
import AttachmentInformation from './attachmentInformation.vue';
import type { InfoType } from './reviewAssistant.d';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { farmerListNo } = defineProps<{
  farmerListNo: string;
}>();

const expand = ref(true);
const scrollOverPageTab = ref(false); // 滚动条是否已经超过页签
const customerExpand = ref(false); // 用户手动点击了展开、收起
const container = ref(null);
const businessRating = defineModel<string>('businessRating');
const showNormalBoard = ref(true); // normal模式
const showAbsoluteBoard = ref(false); // 定位模式

const toggleExpand = () => {
  expand.value = !expand.value;
};

watch(scrollOverPageTab, async (val) => {
  if (val && !customerExpand.value) {
    // 用户没有在核保助手sticky状态下手动操作展开、收起，则由滚动条控制
    expand.value = false;
  } else if (!val) {
    customerExpand.value = false; // 滚动条滚到顶部了，重置customerExpand状态
    if (expand.value) {
      // 如果还是打开状态，切换为normal模式
      expand.value = false;
      await nextTick();
      expand.value = true;
    }
  }
});

watch(expand, (val) => {
  if (val) {
    // 需要展示核保助手
    if (container.value) {
      const rect = (container.value as HTMLDivElement).getBoundingClientRect();
      if (Math.round(rect.top) - 34 > 0) {
        // 核保助手不在滚动条顶部
        showNormalBoard.value = true;
        showAbsoluteBoard.value = false;
        customerExpand.value = false;
      } else {
        showNormalBoard.value = false;
        showAbsoluteBoard.value = true;
        customerExpand.value = true;
      }
    }
  } else {
    // 需要隐藏核保助手
    showNormalBoard.value = false;
    showAbsoluteBoard.value = false;
  }
});

const checkScroll = () => {
  if (container.value) {
    const rect = (container.value as HTMLDivElement).getBoundingClientRect();
    if (Math.round(rect.top) - 34 <= 0) {
      // 34是导航栏的高度
      scrollOverPageTab.value = true;
    } else {
      scrollOverPageTab.value = false;
    }
  }
};

const onScroll = throttle(() => {
  checkScroll();
}, 100);
const route = useRoute();
// 核保助手模块对象
const data = {
  businessAnalysis: {
    // 新产品经营分析
    newProductInfo: {
      businessRiskLevel: '', // 业务风险评级
      expectLossRatio: '', // 预期满期赔付率
      orderRate: '', // 跟单费用率
      secondDepartmentNo: '', // 二级机构编码
      marketProductNo: '', // 市场产品编码
      isShowPrompt: '', // 是否展示提
    },
    oldProductInfo: {
      businessLevel: '', // 业务十分类评级
      threeYearLossRatio: '', // 近3年平均满期赔付率
      threeYearMerchandiserRatio: '', // 近3年平均跟单费用率
      threeYearIpmLossRatio: '', // 近3年平均减值损失率
      ratioDetail: {
        businessRiskLevel: '', // 业务风险评级
        ratioList: [], // 费率列表
      },
    }, // 旧产品经营分析
    isNewProduct: '', // 是否是新产品
    mtrlcDisasterRisk: {
      disasterType: '', // 灾害类型
      disasterLevel: '', // 灾害级别
    }, // 气象灾害风险
  },
  customerAnalysis: {
    applyPolicyType: '', // 投保方式：2组织投保/1非组织投保
    historyUnderwriteList: [], // 历史承保列表
    highRiskFarmer: '', // 高风险农户占比
    verifyRate: '', // 客户验真率
    selfCheckRate: '', // 自助验标率
    repeatPolicyRate: '', // 重复投保率
    repeatPolicyList: [], // 重复投保列表
    anbVerifyRate: '', // 爱农宝验真率
    verifyRateEoaNo: '', // 签报号
  },
  plotAnalysis: {
    amount: '',
    epidemicAreaRate: '',
    repeatPlotDetail: [],
  },
  riskAnalysis: {
    allowLater: '',
    growthLevel: '',
    smartCount: '',
    riskType: '',
    repeatMaterial: '',
    repeatMaterialDetail: [],
    insuranceNums: '',
    smartCountPercent: '',
    cattleFaceAmount: '',
    cattleFaceAmountPercent: '',
  },
  ownershipAnalysis: {
    ownershipFileVOList: [],
  },
  otherRisk: {
    riskList: [],
  },
  udwAnalysisVO: {
    promptInfo: '',
  },
};
const initInfoData = (data: InfoType) => {
  return {
    businessAnalysis: data.businessAnalysis,
    customerAnalysis: data.customerAnalysis,
    plotAnalysis: data.plotAnalysis,
    riskAnalysis: data.riskAnalysis,
    ownershipAnalysis: data.ownershipAnalysis,
    otherRisk: data.otherRisk,
    udwAnalysisVO: data.udwAnalysisVO,
    taskReuseInfoRespVO: data.taskReuseInfoRespVO,
  };
};
// 初始化集合
const reviewAssistantInfo = ref(initInfoData(data));
// 核保助手模块-初始化接口
const queryUnderwriteAssistant = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryUnderwriteAssistant`;
    const { applyPolicyNo, productCode, departmentCode } = route.query || {};
    const res = await $postOnClient(fetchUrl, { applyPolicyNo, productCode, departmentCode });
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      reviewAssistantInfo.value = initInfoData(res?.data as InfoType) || {};
      const { businessAnalysis } = reviewAssistantInfo.value || {};
      const _businessRating = businessAnalysis?.oldProductInfo?.businessLevel || businessAnalysis?.newProductInfo?.businessRiskLevel;
      businessRating.value = _businessRating;
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  window.addEventListener('scroll', onScroll, true);
});

onActivated(() => {
  queryUnderwriteAssistant();
});

onBeforeUnmount(() => {
  window.removeEventListener('scroll', onScroll);
});
const { gateWay, service } = useRuntimeConfig().public || {};
</script>
