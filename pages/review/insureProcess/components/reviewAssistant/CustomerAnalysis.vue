<template>
  <div v-if="customerAnalysis !== null" class="rounded p-12px bg-image-url">
    <div class="mb-12px">
      <VueIcon :icon="IcKehufenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">客户分析</span>
    </div>
    <div class="grid grid-cols-2 gap-8px">
      <div class="rounded bg-white p-12px flex flex-col justify-between h-[69px]">
        <div class="flex justify-between">
          <div class="flex">
            <span class="mr-[5px]">
              <VueIcon :icon="IconTankuangTishiColor" />
            </span>
            <span class="text-xs text-font-primary">{{ title }}</span>
          </div>
          <div v-if="applyPolicyTypeFlag" class="text-xs text-[#576b95] cursor-pointer shrink-0" @click="hightVisible = true">查看<VueIcon :icon="IconChevronRightFont" /></div>
        </div>
        <div v-if="customerAnalysis?.applyPolicyType === '2'">
          <span class="font-number-medium text-[28px] font-bold text-[#f03e3e]">{{ customerAnalysis?.highRiskFarmer || '0' }}</span>
          <span class="text-font-primary">&nbsp;%</span>
        </div>
        <div v-if="customerAnalysis?.applyPolicyType === '1'" class="text-xs text-[#576b95] cursor-pointer" @click="hightVisible = true">查看</div>
      </div>
      <div class="rounded bg-white p-12px flex flex-col justify-between h-[69px]">
        <div class="flex justify-between">
          <div class="text-xs text-font-primary">客户验真率</div>
          <div v-if="Number(customerAnalysis?.verifyRate) < 100 && customerAnalysis?.verifyRateEoaNo" class="text-xs text-[#576b95] cursor-pointer" @click="lookEoa">查看签报<VueIcon :icon="IconChevronRightFont" /></div>
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#333333]">{{ customerAnalysis?.verifyRate || '0' }}</span>
          <span class="text-font-primary">&nbsp;%</span>
        </div>
      </div>
      <div class="rounded bg-white p-12px flex flex-col justify-between h-[69px]">
        <div class="text-xs text-font-primary">农户自助验标率</div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#f03e3e]">{{ customerAnalysis?.selfCheckRate || '0' }}</span>
          <span class="text-font-primary">&nbsp;%</span>
        </div>
      </div>
      <div class="rounded bg-white p-12px flex flex-col justify-between h-[69px]">
        <div class="flex justify-between">
          <div class="flex items-center">
            <span class="mr-[5px]">
              <VueIcon :icon="IconTankuangTishiColor" />
            </span>
            <span class="text-xs text-font-primary truncate">客户疑似重复率&nbsp;</span>
          </div>
          <div
            v-if="Number(customerAnalysis?.repeatPolicyRate) > 0"
            class="text-xs text-[#576b95] cursor-pointer text-nowrap shrink-0"
            @click="
              visible = true;
              getInit();
            "
          >
            查看<VueIcon :icon="IconChevronRightFont" />
          </div>
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#f03e3e]">{{ customerAnalysis?.repeatPolicyRate || '0' }}</span>
          <span class="text-font-primary">&nbsp;%</span>
        </div>
      </div>
      <div v-if="identifySignData?.signStatus === '2' && identifySignData?.eoaDetailUrl" class="rounded bg-white p-12px flex flex-col justify-between h-[69px]">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-xs text-font-primary truncate">爱农宝核身率</span>
            <a-tooltip placement="right">
              <template #title>
                <div>分子：清单中核身的农户数；</div>
                <div>分母：清单中的农户数</div>
              </template>
              <VueIcon class="text-[12px] ml-[5px]" :icon="IconErrorCircleFilledFont" />
            </a-tooltip>
          </div>
          <div class="text-xs text-[#576b95] cursor-pointer text-nowrap shrink-0" @click="handleVerifyJump()">豁免签报<VueIcon :icon="IconChevronRightFont" /></div>
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#f03e3e]">{{ farmerVerifyInfo?.customerVerifyRate || '0' }}</span>
          <span class="text-font-primary">&nbsp;%</span>
          <span>({{ farmerVerifyInfo.customerVerifyCount || '0' }}/{{ farmerVerifyInfo.allCustomerCount || '0' }})</span>
        </div>
      </div>
    </div>
  </div>
  <a-modal v-model:open="visible" title="客户疑似重复投保" centered :width="pxToRem(900)">
    <a-table :data-source="repeatPolicyList" :pagination="pagination" :columns="columns">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'approveStatus'">{{ record }}</template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" :loading="loading" @click="downloadInfo">下载重复信息</a-button>
    </template>
  </a-modal>
  <a-modal v-model:open="hightVisible" :title="title" centered :width="pxToRem(900)">
    <a-table :data-source="customerAnalysis?.historyUnderwriteList" :columns="hightColumns" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'approveStatus'">{{ record }}</template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" @click="hightVisible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconChevronRightFont, IconTankuangTishiColor, IcKehufenxiFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { $postOnClient, $getOnClient, $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';

const { gateWay, service } = useRuntimeConfig().public || {};
const { reviewAssistantInfo, farmerListNo } = defineProps<{
  reviewAssistantInfo: InfoType;
  farmerListNo?: string;
}>();
const customerAnalysis = computed(() => reviewAssistantInfo?.customerAnalysis);
const visible = ref<boolean>(false);
const hightVisible = ref<boolean>(false);
const columns = [
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '身份证号', dataIndex: 'certificateNo' },
  { title: '保险数量', dataIndex: 'insuredNumber' },
  { title: '保期重叠投保单号/保单号', dataIndex: 'voucherNo' },
  { title: '原保险起期', dataIndex: 'insuranceBeginDate' },
  { title: '原保险止期', dataIndex: 'insuranceEndDate' },
  { title: '原保险数量', dataIndex: 'oldInsuredNumber' },
];
const hightColumns = [
  { title: '被保险人', dataIndex: 'customerName' },
  { title: '身份证号', dataIndex: 'identityCardNo' },
  { title: '保单号', dataIndex: 'applyPolicyNo' },
  { title: '保险数量', dataIndex: 'insuredNumber' },
  { title: '保费', dataIndex: 'premium' },
  { title: '赔款金额', dataIndex: 'earPayAmount' },
  { title: '赔付率', dataIndex: 'lossRatio' },
];
const repeatPolicyList = ref<Record<string, string>[]>([]);
const route = useRoute();
// 客户疑似重复投保接口
const getInit = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryRepeatInsurePage`;
    const res = await $postOnClient(fetchUrl, { applyPolicyNo: route.query?.applyPolicyNo || '', pageSize: pagination.pageSize, pageNum: pagination.current });
    if (res?.code === SUCCESS_CODE) {
      repeatPolicyList.value = res?.data?.records as unknown as Record<string, string>[];
      pagination.total = res?.data?.total;
      pagination.current = res?.data?.current;
      pagination.pageSize = res?.data?.size;
    } else {
      message.error(res?.msg || '请求失败');
    }
  } catch (error) {
    console.log(error);
  }
};
// 分页处理
const { pagination } = usePagination(getInit);
// 为组织投保2且风险大于0展示，或者为非组织投保时展示
const applyPolicyTypeFlag = computed(() => customerAnalysis.value?.applyPolicyType === '2' && Number(customerAnalysis.value?.highRiskFarmer) > 0);
// 非组织投保-客户历史承保信息，组织投保-高风险农户
const title = computed(() => (customerAnalysis.value?.applyPolicyType === '2' ? '高风险农户' : '客户历史承保信息'));
// 下载重复信息
const loading = ref<boolean>(false);
const downloadInfo = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.accept}/apply/attachment/file/excelCommonExport`;
    await $postOnClient(
      fetchUrl,
      { exportType: 'DUP_INSURE_FARMER_LIST', applyPolicyNo: route.query.applyPolicyNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            loading.value = false;
            message.success('下载成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
            loading.value = false;
          }
        },
      },
    );
  } catch (error) {
    console.log(error);
  }
};
type EoaDetailUrl = {
  eoaDetailUrl: string;
};
const lookEoa = async () => {
  // 一般性临分查看签报
  try {
    const res = await $getOnClient<EoaDetailUrl>(gateWay + service.ums + '/udw/eoa/queryEoaInfo', { eoaNo: customerAnalysis.value?.verifyRateEoaNo });
    if (res?.code === SUCCESS_CODE) {
      const { eoaDetailUrl } = res?.data || {};
      window.open(eoaDetailUrl);
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};

interface SignData {
  signType: string; // 签报类型
  signStatus: string; // 签报状态
  eoaDetailUrl: string; // 签报详情url
}

// 爱农宝核身率豁免签报信息
const identifySignData = ref();

const handleVerifyJump = () => {
  window.open(identifySignData.value?.eoaDetailUrl);
};

// 获取eoa签报数据
const getEoaData = async () => {
  const res = await $get<SignData[]>(`${gateWay}${service.accept}/accept/eoa/queryApplyPolicySignDataList`, { applyPolicyNo: route.query.applyPolicyNo });
  if (res && res.code === SUCCESS_CODE) {
    identifySignData.value = res.data?.find((item) => item?.signType === '039') || {};
  }
};

watch(
  () => farmerListNo,
  () => {
    if (farmerListNo) {
      getEoaData();
      queryFarmerVerifyInfoByFarmerListNo();
    }
  },
);

const farmerVerifyInfo = ref<Record<string, string>>({});
const queryFarmerVerifyInfoByFarmerListNo = () => {
  $get<Record<string, string>>(`${gateWay}${service.farmer}/farmerList/getFarmerVerifyInfoByFarmerListNo`, { farmerListNo: farmerListNo }).then((res) => {
    if (res && res.code === SUCCESS_CODE && res.data) {
      farmerVerifyInfo.value = res.data || {};
    } else {
      farmerVerifyInfo.value = {};
    }
  });
};
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
