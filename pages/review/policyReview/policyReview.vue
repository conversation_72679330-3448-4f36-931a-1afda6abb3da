<template>
  <div class="m-14px space-y-16px">
    <a-spin :spinning="btnLoading" wrapper-class-name="search-spin-wrapper-css">
      <div class="bg-white rounded p-16px">
        <div class="flex">
          <a-form :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: pxToRem(90) } }">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="机构" name="departmentCode" v-bind="validateInfos.departmentCode">
                  <department-search v-model:contain-child-depart="formState.containChildDepart" v-model:loading="btnLoading" :dept-code="formState.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请日期" required name="applyDate">
                  <a-range-picker v-model:value="formState.applyDate" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="10">
                <a-form-item label="单号">
                  <a-input v-model:value.trim="formState.voucherNo" placeholder="请输入" allow-clear @blur="handleChange" />
                </a-form-item>
              </a-col>
              <a-col :span="14">
                <a-form-item label="核保状态" v-bind="validateInfos.status">
                  <CheckBoxGroup v-model:checked-list="formState.status" :disabled="formState.voucherNo ? true : false" :options="reviewStatusOptions" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand" :gutter="16">
              <a-col :span="24">
                <a-form-item label="标的">
                  <RiskCodeSelect v-model:value="formState.riskType" :department-code="formState.departmentCode" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand" :gutter="16">
              <a-col :span="12">
                <a-form-item label="产品">
                  <ProductSelect v-model:value="formState.productCode" :department-code="formState.departmentCode" :encode-key="formState.riskType" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="保险起期">
                  <a-range-picker v-model:value="insuranceDate" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row v-show="expand" :gutter="16">
              <a-col :span="24">
                <a-form-item label="标的地址">
                  <RegionSelect v-model:province="formState.province" v-model:city="formState.city" v-model:county="formState.county" v-model:town="formState.town" v-model:village="formState.village" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <div class="w-[50px]">
            <form-fold v-model="expand" />
          </div>
        </div>
        <div class="flex justify-center items-center space-x-8px">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" ghost @click="submit">查询</a-button>
        </div>
      </div>
    </a-spin>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(49,40,40,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" class="table-box">
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'policyNo'">
            <div>
              <span class="mr-[4px]">单证号</span>
              <a-tooltip>
                <template #title>
                  <div class="mb-[2px]">
                    <a-tag color="red">保</a-tag>
                    <span>保单号</span>
                  </div>
                  <div class="mb-[2px]">
                    <a-tag color="orange">投</a-tag>
                    <span>投保单号</span>
                  </div>
                  <div class="mb-[2px]">
                    <a-tag color="blue">自助</a-tag>
                    <span>自助投保单号</span>
                  </div>
                </template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </div>
          </template>
        </template>
        <template #bodyCell="{ column, record, text }">
          <!-- 单证号 -->
          <template v-if="column.dataIndex === 'policyNo' || column.dataIndex === 'applyPolicyNo'">
            <div class="flex items-center">
              <a-tag v-if="record.policyNo" color="red">保</a-tag>
              <a-tag v-else color="orange">投</a-tag>
              <CopyLink :text="record.policyNo || record.applyPolicyNo" @click="openDetail(record.policyNo, record.applyPolicyNo)" />
              <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
              <a-tag v-if="record.doublePrecisionFlag" color="green">双</a-tag>
            </div>
          </template>
          <template v-if="['productName', 'insuredCustomerName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <AuthButton code="policyDeal" type="link" :disabled="!record.ableProcess" @click="handleDetail(record)">处理</AuthButton>
            <AuthButton code="policyRevoke" type="link" :disabled="true" @click="handleWithdraw(record?.policyNo)">撤回</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IconInfoCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { Form } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import type { ColumnsType } from 'ant-design-vue/es/table';
import type { PolicyReviewType, dataType } from './policyReview.d';
import { ReviewStatus } from '@/enums/insure';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import AuthButton from '@/components/ui/AuthButton.vue';
import CopyLink from '@/components/ui/CopyLink.vue';
import { SUCCESS_CODE } from '@/utils/constants';

const listColumns: ColumnsType<Record<string, string>> = [
  { title: '出单机构', dataIndex: 'departmentAbbrName' },
  { title: '单证号', dataIndex: 'policyNo' },
  { title: '产品名称', dataIndex: 'productName' },
  { title: '被保险人名称', dataIndex: 'insuredCustomerName' },
  { title: '保险起期', dataIndex: 'insuranceBeginDateStr' },
  { title: '保费金额', dataIndex: 'premium' },
  { title: '核保状态', dataIndex: 'statusName' },
  { title: '核保日期', dataIndex: 'underwritingTime' },
  { title: '申请日期', dataIndex: 'applyDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

const reviewStatusOptions = [
  { label: '待核保', value: ReviewStatus.Pending },
  { label: '审核中', value: ReviewStatus.InReview },
  { label: '已下发', value: ReviewStatus.Issued },
  { label: '已同意', value: ReviewStatus.Approved },
  { label: '拒保', value: ReviewStatus.Rejected },
];
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
interface FormState {
  departmentCode: string;
  voucherNo: string;
  containChildDepart: boolean; // 是否包含下级机构
  applyDate: [Dayjs, Dayjs]; // 创建时间
  productCode: undefined;
  riskType: string;
  status: string[];
  province: undefined;
  city: undefined;
  town: undefined;
  county: undefined;
  village: undefined;
  riskAddr: string;
}

const formState = reactive<FormState>({
  departmentCode: defaultDeptCode.value,
  voucherNo: '',
  containChildDepart: true,
  productCode: undefined,
  riskType: '',
  status: [ReviewStatus.Pending, ReviewStatus.InReview],
  province: undefined,
  city: undefined,
  town: undefined,
  county: undefined,
  village: undefined,
  riskAddr: '',
  applyDate: [dayjs().subtract(1, 'month'), dayjs()],
});
const route = useRoute();
if (route.query.startTime && route.query.endTime) {
  formState.applyDate = [dayjs(route.query.startTime), dayjs(route.query.endTime)];
}

const insuranceDate = ref<[Dayjs, Dayjs] | undefined>(); // 保险日期数组

const formRules = reactive({
  departmentCode: [{ required: true, message: '请选择机构' }],
  status: [{ required: true, message: '请选择核保状态' }],
});
const btnLoading = ref<boolean>(false);
// 单号失焦，核保状态全选
const handleChange = async () => {
  if (formState.voucherNo) {
    formState.status = [ReviewStatus.Pending, ReviewStatus.InReview, ReviewStatus.Issued, ReviewStatus.Approved, ReviewStatus.Rejected];
    btnLoading.value = true;
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: formState.voucherNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      formState.departmentCode = res.data?.insureDepartmentNo || '';
    }
    btnLoading.value = false;
  } else {
    formState.status = [ReviewStatus.Pending, ReviewStatus.InReview];
    formState.departmentCode = defaultDeptCode.value;
    btnLoading.value = false;
  }
};

// 搜索项是否展开
const expand = ref(false);

const changeDeptCode = (val: string) => {
  formState.departmentCode = val;
};
// 重置
const resetForm = () => {
  insuranceDate.value = undefined; // 保险日期数组
  resetFields();
  submit();
};
// 查询
const submit = () => {
  validate().then(() => {
    pagination.pageSize = 10;
    pagination.current = 1;
    refresh();
  });
};
// 初始化列表接口
const dataSource = ref<PolicyReviewType[]>([]);
const loading = ref(false);
const refresh = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryUnderwritePage`;
    // 日期数据组装
    const dateFormat = 'YYYY-MM-DD';
    const [insuranceBeginDate, insuranceEndDate] = insuranceDate.value || [];
    const [applyBeginDate, applyEndDate] = formState.applyDate || [];
    const { province, city, town, county, village } = formState;
    const params = {
      ...formState,
      containChildDepart: formState.containChildDepart ? '1' : '0',
      insuranceBeginDate: insuranceBeginDate?.format(dateFormat),
      insuranceEndDate: insuranceEndDate?.format(dateFormat),
      applyBeginDate: applyBeginDate?.format(dateFormat),
      applyEndDate: applyEndDate?.format(dateFormat),
      riskAddr: `${province || ''}${city || ''}${town || ''}${county || ''}${village || ''}`,
      pageSize: pagination.pageSize,
      pageNum: pagination.current,
    };
    const res = await $postOnClient<dataType>(fetchUrl, params);
    if (res?.code === SUCCESS_CODE) {
      const { records, total = 0, current = 1, size = 10 } = res?.data || {};
      dataSource.value = records;
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.error(res?.msg);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    dataSource.value = [];
    pagination.total = 0;
    pagination.current = 1;
    pagination.pageSize = 10;
    console.log(error);
  }
};
const router = useRouter();
// 点击处理按钮，跳转核保处理页面
const handleDetail = (record: Record<string, string>) => {
  router.push({
    path: '/insureProcess',
    query: {
      applyPolicyNo: record.applyPolicyNo,
      productCode: record.productCode,
      departmentCode: record.departmentCode,
      t: new Date().getTime(),
    },
  });
};
// 点击撤回按钮
const handleWithdraw = (applyPolicyNo: string) => {
  console.log(applyPolicyNo);
};
// 跳转详情
const openDetail = (policyNo: string, applyPolicyNo: string) => {
  if (policyNo) {
    // 有保单号跳转保单详情
    router.push({
      path: '/policyDetails',
      query: { applyPolicyNo, policyNo, isOld: '1' },
    });
  } else {
    // 打开投保单详情页
    router.push({
      path: '/applyPolicyDetails',
      query: { applyPolicyNo },
    });
  }
};
// 分页处理
const { pagination } = usePagination(refresh);

onActivated(() => {
  refresh();
});
const { gateWay, service } = useRuntimeConfig().public || {};
const { resetFields, validate, validateInfos } = Form.useForm(formState, formRules);
</script>
