<template>
  <a-modal v-model:open="processOpen" title="审核轨迹" centered :width="pxToRem(900)">
    <div class="pb-[12px]">审批链： {{ chainStr || '-' }}</div>
    <a-table :data-source="insureInfo" :columns="columns" :pagination="false" :loading="tableLoading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'approveStatus'">{{ approveStatusText(record.approveStatus) }}</template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" @click="processOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { ApprovalTaskItem } from '@/apiTypes/speciaManage.d.ts';

import { pxToRem } from '@/utils/tools';

// 表格数据
const { insureInfo, chainStr } = defineProps<{
  insureInfo: ApprovalTaskItem[];
  chainStr: string;
}>();

// 表格loading
const tableLoading = ref<boolean>(false);
// 弹窗
const processOpen = defineModel<boolean>('processOpen', { default: false });
const columns = [
  { title: '发送人', dataIndex: 'createdBy' },
  { title: '操作时间', dataIndex: 'createdDate' },
  { title: '处理人', dataIndex: 'approvalUm' },
  { title: '审核时间', dataIndex: 'updatedDate' },
  { title: '审核意见', dataIndex: 'approvalOpinions' },
  { title: '审核状态', dataIndex: 'approvalStatusName' },
];
// B3-待审核、B4-已同意、B2-已下发、B7-拒保
const approveStatusText = (type: string) => {
  const approveTypeMap: Record<string, string> = {
    B3: '待审核',
    B4: '已同意',
    B2: '已下发',
    B7: '拒保',
    B21: '审核中',
  };
  return approveTypeMap[type] || '-';
};
</script>
