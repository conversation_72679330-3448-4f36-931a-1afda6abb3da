<template>
  <a-spin :spinning="pageLoading">
    <a-form ref="formRef" :colon="false" :label-col="{ style: { width: pxToRem(110) } }" :model="formData">
      <InfoGroup title="基础信息" class="mb-[16px]">
        <div class="text-[#404442] mb-[20px]">
          机构：
          <span class="font-semibold">{{ formData.departmentName }}</span>
        </div>
        <a-table v-if="formData.transProductList" :columns="productColumns" :data-source="formData.transProductList" :scroll="{ x: 'max-content' }" :pagination="false">
          <template #bodyCell="{ column, index }">
            <template v-if="column.dataIndex === 'orderNum'">
              <span>{{ index + 1 }}</span>
            </template>
            <template v-if="column.dataIndex === 'technicProductCode'">
              {{ formData.transProductList[index].technicProductName }}
            </template>
            <template v-if="column.dataIndex === 'marketProductCode'">
              <div :style="{ maxWidth: pxToRem(700), maxHeight: pxToRem(116), 'overflow-y': 'auto' }">
                <div v-for="item in formData.transProductList[index].marketProductList" :key="item.marketProductCode">{{ item.marketProductName }}</div>
              </div>
            </template>
          </template>
        </a-table>
      </InfoGroup>
      <InfoGroup title="特约信息" class="mb-[16px]">
        <div class="grid grid-cols-2 gap-x-16px">
          <div class="text-[#404442] mb-[20px]">
            特约类型：
            <span class="font-semibold">{{ formData.specialPromiseTypeName }}</span>
          </div>
          <div class="text-[#404442] mb-[20px]">
            特约名称：
            <span class="font-semibold">{{ formData.specialPromiseName }}</span>
          </div>
        </div>
        <div v-if="formData.specialPromiseType !== 'F'" class="text-[#404442]">
          特约内容：
          <span v-for="(item, index) in formData.specialPromiseControlList" :key="index" class="leading-[32px]">
            <template v-if="item.type === 'F'">{{ item.fixedMsg }}</template>
            <span v-if="item.type === 'T'" class="inline-block border border-solid border-[#D4D6D9] bg-[#F9FAFA] rounded-[4px] mx-[2px] px-[4px]">
              <span class="text-[#E6AD1C] bg-[#FFF9EB]">文本</span>
              {{ item.dataMap.needTextMax === '1' ? `${item.dataMap.textMaxLength}位数` : '自由输入' }}
            </span>
            <span v-if="item.type === 'N'" class="inline-block border border-solid border-[#D4D6D9] bg-[#F9FAFA] rounded-[4px] mx-[2px] px-[4px]">
              <span class="text-[#576B95] bg-[#EAF1FF]">数字</span>
              <template v-if="item.dataMap.needRange === '0' && item.dataMap.needDecimals === '0'">自由输入</template>
              <template v-else>
                {{ item.dataMap.needRange === '1' ? `数值范围是 ${item.dataMap.mixNum}-${item.dataMap.maxNum}` : '' }}
                {{ item.dataMap.needRange === '1' && item.dataMap.needDecimals === '1' ? ',' : '' }}
                {{ item.dataMap.needDecimals === '1' ? `保留${item.dataMap.decimalPlaces}位小数` : '' }}
              </template>
            </span>
          </span>
        </div>
      </InfoGroup>
      <InfoGroup title="机构信息" class="mb-[16px]">
        <div class="text-[#404442]">
          可适用范围：
          <span class="font-semibold">{{ deptListStr }}</span>
        </div>
      </InfoGroup>
    </a-form>
    <footer v-if="formData?.isShowAudit === 'Y' && mode === 'view'" class="footer-wrapper space-x-8px flex justify-center">
      <a-button
        type="primary"
        ghost
        @click="
          processOpen = true;
          queryUnderwriteAssistant();
        "
        >查看审核轨迹</a-button
      >
    </footer>
  </a-spin>
  <!-- 点击核保信息按钮 -->
  <AuditTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import type { LocationQueryValue } from 'vue-router';
import type { SpecialDetail, TransProductItem } from '@/apiTypes/insure';
import { $get } from '@/utils/request';
import { useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { message } from '@/components/ui/Message';
import AuditTrack from './AuditTrack.vue';
import type { QuerySpecialPromiseApprovalDetail, ApprovalTaskItem } from '@/apiTypes/speciaManage.d.ts';

const props = defineProps<{
  mode: string;
  id: LocationQueryValue | LocationQueryValue[];
}>();
const processOpen = ref<boolean>(false);
const querySpecialPromiseDetailReq = await useGet<SpecialDetail>('/api/accept/querySpecialPromiseDetail');

const formData = ref<SpecialDetail>({
  idSpecialPromiseDefine: '',
  lockVersion: '',
  specialPromiseDept: '',
  specialPromiseName: '',
  specialPromiseCode: '',
  specialPromiseType: '',
  specialPromiseTypeName: '',
  technicProduct: '',
  marketProduct: '',
  adaptDepartment: '',
  specialPromiseControlList: [],
  specialPromiseProRelList: [],
  specialPromiseDeptRelList: [],
  transProductList: [],
  isShowAudit: 'N',
});
const deptListStr = ref('');

const pageLoading = ref(false);
const insureInfo = ref<ApprovalTaskItem[]>([]);
const chainStr = ref<string>('');
const route = useRoute();
const { service } = useRuntimeConfig().public || {};
const queryUnderwriteAssistant = async () => {
  try {
    const { code, data } = await $get<QuerySpecialPromiseApprovalDetail>(`/gateway${service.accept}/specialPromiseApproval/querySpecialPromiseApprovalOpinion`, { idSpecialPromiseDefine: route.query.id || '' });
    if (code === SUCCESS_CODE) {
      chainStr.value = data?.agrSpApprovalChainName || '';
      if (data?.specialPromiseOpinionsList && data?.specialPromiseOpinionsList?.length) {
        insureInfo.value = data.specialPromiseOpinionsList.map((val) => {
          return {
            createdDate: val.createdDate || '',
            createdBy: val.createdBy || '',
            approvalName: val.approvalName || '',
            approvalUm: val.approvalUm || '',
            updatedDate: val.updatedDate || '',
            approvalOpinions: val.approvalOpinions || '',
            approvalStatus: val.approvalStatus || '',
            approvalStatusName: val.approvalStatusName || '',
          };
        });
      } else {
        insureInfo.value = [];
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const querySpecialDetail = () => {
  if (props.id) {
    pageLoading.value = true;
    querySpecialPromiseDetailReq
      .fetchData({ idSpecialPromiseDefine: props.id })
      .then((res) => {
        const { msg = '' } = res || {};
        if (res?.code === SUCCESS_CODE) {
          if (res?.data) {
            formData.value = res.data;
            // 产品表格数据回填
            const newList: Array<TransProductItem> = [];
            formData.value.specialPromiseProRelList.map((item) => {
              const tempIndex = newList.findIndex((transItem) => transItem.technicProductCode === item.technicProductCode);
              if (tempIndex > -1) {
                newList[tempIndex].marketProductList.push({ marketProductCode: item.marketProductCode, marketProductName: item.marketProductName });
                newList[tempIndex].marketProductCodeList.push(item.marketProductCode);
              } else {
                newList.push({
                  marketProductList: [{ marketProductCode: item.marketProductCode, marketProductName: item.marketProductName }],
                  marketProductCodeList: [item.marketProductCode],
                  technicProductCode: item.technicProductCode,
                  technicProductName: item.technicProductName,
                  technicProductOptions: [],
                  marketProductOptions: [],
                });
              }
            });
            formData.value.transProductList = JSON.parse(JSON.stringify(newList));
            deptListStr.value = formData.value.specialPromiseDeptRelList.map((item) => item.adaptDepartmentName).join('，');
          }
        } else {
          message.error(msg);
        }
      })
      .finally(() => {
        pageLoading.value = false;
      });
  }
};

// 每次进入页面就查询特约详情
// querySpecialDetail();
watch(
  () => props.id,
  () => {
    querySpecialDetail();
  },
  {
    immediate: true,
  },
);
// 产品配置表格
const productColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'orderNum', fixed: 'left', width: pxToRem(80) },
  { title: '技术产品', dataIndex: 'technicProductCode' },
  { title: '市场产品', dataIndex: 'marketProductCode', width: pxToRem(780) },
];
</script>
