export const rltPlyListItem = {
  rltPlyNo: '',
  rltPlyCrpdPrem: '',
  plyAcumPayAmt: '',
  rltPlyCrpdSmplLossRate: '',
};

export const initAttachment = {
  attachUrl: '',
  docoldName: '',
  fileSize: '',
  documentFormat: '',
  bucketName: '',
  storageType: '',
  documentId: '',
  documentGroupItemsId: '',
};

export const initInsuranceFuture = {
  rgtAmtPct: '',
  rgtAmtPayTm: '',
  futureCompanyName: '',
  regRate: '',
  spcnIlglVioFlag: '',
  rltVioIlglBhvr: '',
};

export const initSecPolicyDevelopment = {
  whlEstiRiskVal: '',
  rltPlyList: [rltPlyListItem],
};

export const initMortgageBanking = {
  bankAuthRptFutSuplFlag: 'N',
  suplTm: '',
};

export const initContractModel = {
  baseInfo: {
    riskAsmtNo: '',
    attachmentGroupNo: '',
    riskAsmtName: '',
    riskAsmtStsCode: '',
    aprvTm: '',
    busiBcgd: '',
    inclLowLvlDeptFlag: false,
    aplyDeptNo: '',
    aplyDeptName: '',
    saleList: [
      {
        salemanNo: '',
        salemanName: '',
      },
    ],
    saleListCode: [],
    opCtctInfo: '',
    coinsuranceFlag: '',
    mainInsrFlag: '',
    prodNo: '',
    prodName: '',
    alowTypeCode: '',
    alowTypeName: '',
    agriRiskObjDetlCode: '',
    agriculturalRiskObjectDetailCode: '',
    riskLevel: '',
    riskObjLocVlgCode: '',
    riskAgrList: [
      {
        agriculturalRiskObjectDetailCode: '',
      },
    ],
    riskObjCompAddr: '',
    riskAddressInfoList: [
      {
        idAplyRiskEvaluateRiskAddress: '',
        countryCode: '',
        riskObjAddrPrvnCode: '', // 省编码
        provinceName: '', // 省名称
        riskObjAddrCityCode: '', // 市编码
        cityName: '', // 市名称
        riskObjAddrCntyCode: '', // 区编码
        countyName: '', // 区名称
        riskObjAddrTownNo: '', // 镇编码
        townName: '', // 镇名称
        riskObjAddrVlgCode: '', // 村编码
        villageName: '', // 村名称
        address: '', // 完整地址
      },
    ],
    curRiskObjMktPrc: '',
    curRiskObjMktPrcUnit: '',
    insrTgtPrc: '',
    insrTgtPrcUnit: '',
    listAtchId: '',
    riskObjOput: '',
    riskObjNumUnit: '',
    aplyPrps: '',
    clmPrps: '',
    attachmentList: [], // 历史价格清单附件
    isHeadUnderwriting: 'N', // Y-总部核保人、总部核保经理 N-非总部核保人、非总部核保经理,Y时，编辑页只可改跟单费用率、预估风险字段
    productVersionNo: '', // 产品版本号
  },
  businessInfo: {
    feeRate: '',
    unitInsrAmt: '',
    farmInsrNum: '',
    plyPstnNo: '',
    insuredAmount: '',
    premium: '',
    docFeeRate: '',
    insrClntNum: '',
    insuredName: '',
    whlEstiRiskVal: '',
    atchPt: '',
    ddctRate: '',
  },
  businessModel: {
    busiModeNo: '',
    busiModeName: '',
    insuranceFuture: initInsuranceFuture, // 01-保险+期货
    secPolicyDevelopment: initSecPolicyDevelopment, // 02-二次开发
    mortgageBanking: initMortgageBanking, // 03-银抵类
    bankAttachmentList: [], // 银行授信报告
    riskAttachmentList: [], // 风险调研报告
    businessAttachmentList: [], // 业务证明材料
  },
};

export const initBusinessInfo = {
  feeRate: '',
  unitInsrAmt: '',
  farmInsrNum: '',
  plyPstnNo: '',
  insuredAmount: '',
  premium: '',
  docFeeRate: '',
  insrClntNum: '',
  insuredName: '',
  whlEstiRiskVal: '',
  atchPt: '',
  ddctRate: '',
};

export const anchorList = [
  { key: '1', href: '#basic-info', title: '基础信息' },
  { key: '2', href: '#product-info', title: '产品信息' },
  { key: '3', href: '#business-info', title: '业务信息' },
  { key: '4', href: '#business-model', title: '业务模式' },
  { key: '5', href: '#control-plan', title: '管控方案' },
];

export const acceptType = ['BMP', 'DIB', 'JPG', 'JPEG', 'JPE', 'JFIF', 'GIF', 'TIF', 'TIFF', 'PNG', 'PSD', 'PCX', 'EXIF', 'DOC', 'XLS', 'PDF', 'MSG', 'HTM', 'PPT', 'PPTX', 'RAR', 'ZIP', 'TXT', 'XLSX', 'DOCX'];

export const accept = '.bmp,.dib,.jpg,.jpeg,.jpe,.jfif,.gif,.tif,.tiff,.png,.psd,.pcx,.exif,.doc,.xls,.pdf,.msg,.htm,.ppt,.pptx,.rar,.zip,.txt,.xlsx,.docx';

export const MAX_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_FILES_NUM = 10;
