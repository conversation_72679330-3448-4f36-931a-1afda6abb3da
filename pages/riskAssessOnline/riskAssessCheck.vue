<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="formRef" :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: '80px' } }">
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="机构" name="aplyDeptNo" required>
                <department-search v-model:contain-child-depart="formState.inclLowLvlDeptFlag" :dept-code="formState.aplyDeptNo" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="创建人UM" name="createdBy">
                <a-input v-model:value="formState.createdBy"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="24">
            <a-col :span="16">
              <a-form-item label="标的地址" name="riskObjCompAddr">
                <region-select v-model:province="formState.riskObjAddrPrvnCode" v-model:city="formState.riskObjAddrCityCode" v-model:county="formState.riskObjAddrCntyCode" v-model:town="formState.riskObjAddrTownNo" v-model:village="formState.riskObjAddrVlgCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="产品" name="prodNo">
                <ProductSelect v-model:value="formState.prodNo" :department-code="formState.aplyDeptNo" :encode-key="formState.agriculturalRiskObjectDetailCode" @product-change="handleProduct" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="24">
            <a-col :span="16">
              <a-form-item label="标的">
                <RiskCodeSelect v-model:value="formState.agriculturalRiskObjectDetailCode" :department-code="formState.aplyDeptNo" :show-search="false" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="风险评估编号" name="riskAsmtNo" :label-col="{ style: { width: pxToRem(120) } }">
                <a-input v-model:value="formState.riskAsmtNo"></a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="24">
            <a-col :span="16">
              <a-form-item label="状态" name="riskAsmtStsList">
                <CheckBoxGroup v-model:checked-list="formState.riskAsmtStsList" :options="statusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
        <!-- <a-button type="primary" @click="createPlan"> 新建 </a-button> -->
        <div class="flex items-center">
          <AuthButton class="mr-[8px]" code="deleteRisk" type="primary" @click="handleDelete">删除</AuthButton>
          <AuthButton code="toInsuranceApply" type="primary" @click="handleToInsuranceApply">去出单</AuthButton>
        </div>
      </div>
      <a-table :columns="listColumns" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" :pagination="pagination" :row-selection="rowSelection" row-key="riskAsmtNo">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'operation'">
            <div>
              <AuthButton code="viewRisk" type="link" @click="handleShowDetail(record.riskAsmtNo)">查看</AuthButton>
              <AuthButton code="editRisk" type="link" :disabled="!['0', '3'].includes(record.riskAsmtStsCode)" @click="handleEditApply(record.riskAsmtNo)">编辑</AuthButton>
            </div>
          </template>
          <template v-if="column.dataIndex === 'riskAsmtNo'">
            <CopyLink v-if="text" :text="text" @click="handleShowDetail(text)" />
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ColumnsType } from 'ant-design-vue/es/table';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import FormFold from '@/components/ui/FormFold.vue';
import CopyLink from '@/components/ui/CopyLink.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { $post, $get } from '@/utils/request';
import AuthButton from '@/components/ui/AuthButton.vue';

const listColumns: ColumnsType<Record<string, string>> = [
  { title: '机构', dataIndex: 'aplyDeptNoAndName' },
  { title: '风险评估名称', dataIndex: 'riskAsmtName' },
  { title: '风险评估单号', dataIndex: 'riskAsmtNo' },
  { title: '产品名称', dataIndex: 'prodName', width: '280px' },
  { title: '标的地址', dataIndex: 'riskObjCompAddr', width: '280px' },
  { title: '标的', dataIndex: 'agriculturalRiskObjectDetailName' },
  { title: '业务保费', dataIndex: 'premium' },
  { title: '总保额', dataIndex: 'insuredAmount' },
  { title: '创建日期', dataIndex: 'createdDate' },
  { title: '创建人', dataIndex: 'createdBy' },
  { title: '状态', dataIndex: 'riskAsmtStsName' },
  { title: '客户名称', dataIndex: 'insuredName' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

const statusOptions = [
  {
    value: '0',
    label: '暂存',
  },
  {
    value: '1',
    label: '审核中',
  },
  {
    value: '2',
    label: '审核通过',
  },
  {
    value: '3',
    label: '已拒绝',
  },
];

const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => onSelectChange(selectedRowKeys, selectedRows),
};
// 表格选中ID
const selectedRow = ref([]);
const selectedRowKey = ref([]);
// 选择数据
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedRow.value = selectedRows;
  selectedRowKey.value = selectedRowKeys;
};

interface FormState {
  aplyDeptNo: string; // 机构编码
  inclLowLvlDeptFlag: boolean; // 是否包含下级机构
  createdBy?: string; // 创建人
  prodNo: string; // 产品编码
  agriculturalRiskObjectDetailCode: string; // 标的
  riskAsmtNo: string; // 风险评估编码
  riskAsmtStsList: Array<string>; // 风险评估状态
  riskObjAddrPrvnCode?: string; // 标的地址省
  riskObjAddrCityCode?: string; // 标的地址市
  riskObjAddrCntyCode?: string; // 标的地址县
  riskObjAddrTownNo?: string; // 标的地址乡镇
  riskObjAddrVlgCode?: string; // 标的地址村
}
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

const formState = reactive<FormState>({
  aplyDeptNo: defaultDeptCode.value,
  inclLowLvlDeptFlag: true,
  prodNo: '',
  agriculturalRiskObjectDetailCode: '',
  riskAsmtNo: '',
  riskAsmtStsList: ['0', '1', '2', '3'],
});

const productVersionNo = ref('');
const handleProduct = (value: string, option: { version: string }) => {
  if (!value) {
    productVersionNo.value = '';
    return;
  }
  productVersionNo.value = option.version;
};

const formRef = ref();

// 搜索项是否展开
const expand = ref(true);

const changeDeptCode = (val: string) => {
  formState.aplyDeptNo = val;
};

const resetForm = () => {
  formRef.value?.resetFields();
  // 标的地址、标的重置不到位
  formState.agriculturalRiskObjectDetailCode = '';
  formState.riskObjAddrPrvnCode = '';
  formState.riskObjAddrCityCode = '';
  formState.riskObjAddrCntyCode = '';
  formState.riskObjAddrTownNo = '';
  formState.riskObjAddrVlgCode = '';
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const router = useRouter();

// 跳转风险评估详情页
const handleShowDetail = (riskAsmtNo: string) => {
  router.push({
    path: '/riskAssessDetail',
    query: { riskAsmtNo },
  });
};

// 跳转风险评估申请页
const handleEditApply = (riskAsmtNo: string) => {
  router.push({
    path: '/riskAssessApply',
    query: { riskAsmtNo },
  });
};

const handleDelete = () => {
  if (!selectedRowKey.value.length) {
    message.warning('请至少选择1条数据');
    return;
  }
  const flag = selectedRow.value.every((record) => ['0', '3'].includes(record.riskAsmtStsCode));
  if (!flag) {
    message.warning('仅可删除暂存、已拒绝的单，请重新选择');
    return;
  }
  Modal.confirm({
    title: '温馨提示',
    content: '即将删除，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    onOk: async () => {
      const fetchurl = gateWay + service.accept + '/web/risk/rpt/deleteByRiskAsmtNos';
      try {
        const res = await $post(fetchurl, { riskAsmtNoList: selectedRowKey.value });
        if (res && res.code === SUCCESS_CODE) {
          message.success('删除成功');
          selectedRowKey.value = [];
          selectedRow.value = [];
          submit();
        } else {
          message.error(res.msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
      }
    },
  });
};

const handleToInsuranceApply = () => {
  if (!selectedRowKey.value.length) {
    message.warning('请至少选择1条数据');
    return;
  }
  if (selectedRowKey.value.length > 1) {
    message.warning('只能选择一条数据去出单');
    return;
  }
  const flag = selectedRow.value.every((record) => record.riskAsmtStsCode === '2');
  if (!flag) {
    message.warning('仅可操作审批通过的单，请重新选择');
    return;
  }
  const { riskAsmtNo } = selectedRow.value[0];
  const fetchUrl = gateWay + service.accept + '/web/risk/rpt/judgePremOverLimit';
  $get(fetchUrl, { riskAsmtNo }).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      router.push({
        path: '/insuranceApply',
        query: { riskAsmtNo: selectedRowKey.value[0] },
      });
    } else {
      message.error(res.msg || '判断风险评估单剩余额度是否已超请求失败');
    }
  });
};

const dataSource = ref([]);
const loading = ref(false);
const refresh = () => {
  const getData = async () => {
    loading.value = true;
    const fetchurl = gateWay + service.accept + '/web/risk/rpt/queryPageList';
    try {
      const res = await $post(fetchurl, {
        ...formState,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        inclLowLvlDeptFlag: formState.inclLowLvlDeptFlag ? 'Y' : 'N', // Y/N 是否包含下级
      });
      if (res && res.code === SUCCESS_CODE && res.data) {
        const { records = [], current = 1, size = 10, total = 0 } = res.data;
        dataSource.value = records;
        pagination.current = current;
        pagination.total = total;
        pagination.size = size;
      }
    } catch (e) {
      console.log(e);
    }
    loading.value = false;
  };
  formRef.value?.validate().then(() => {
    getData();
  });
};

const { pagination } = usePagination(refresh);

const { gateWay, service } = useRuntimeConfig().public || {};

onMounted(() => {
  refresh();
});

// 切换tab刷新
onActivated(() => {
  refresh();
});
</script>
