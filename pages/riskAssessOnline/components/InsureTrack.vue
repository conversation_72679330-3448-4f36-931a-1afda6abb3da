<template>
  <a-modal v-model:open="processOpen" title="审核信息" centered :width="pxToRem(900)">
    <div class="pb-[12px]">审批链： {{ chainStr || '-' }}</div>
    <div class="pb-[12px]">审批记录</div>
    <a-table :data-source="insureInfo" :columns="columns" :pagination="false" :loading="tableLoading" />
    <template #footer>
      <a-button type="primary" @click="processOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { QueryApprovalTaskList } from '../riskAssessOnline';
import { pxToRem } from '@/utils/tools';

// 表格数据
const { insureInfo, chainStr } = defineProps<{
  insureInfo: QueryApprovalTaskList[];
  chainStr: string;
}>();

// 表格loading
const tableLoading = ref<boolean>(false);
// 弹窗
const processOpen = defineModel<boolean>('processOpen', { default: false });
const columns = [
  { title: '操作人', dataIndex: 'operator' },
  { title: '操作时间', dataIndex: 'opTime' },
  { title: '审核意见', dataIndex: 'approveOpinion' },
  { title: '审核状态', dataIndex: 'approveStatusName' },
];
</script>
