<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item required name="busiModeNo" label="业务模式" :label-col="labelColStyle">
        <a-select v-model:value="data.busiModeNo" placeholder="请选择" :options="insrRiskRptBusinessModelList" :disabled="disabled" @change="handleChangeModel" />
      </a-form-item>
      <!-- 保险+期货 -->
      <template v-if="data.busiModeNo === '01'">
        <a-form-item required :name="['insuranceFuture', 'rgtAmtPct']" label="权利金占比(权利金/保费)" :label-col="labelColStyle" :rules="[{ validator: fourValid }]">
          <a-input-number v-model:value="data.insuranceFuture.rgtAmtPct" placeholder="请输入" :disabled="disabled" addon-after="%" :min="0" />
          <div v-if="Number(data.insuranceFuture.rgtAmtPct) > 90" class="text-[#FAAD14]">权利金占比超90%的业务仅在有政府明文要求的情况下开展，请知悉</div>
        </a-form-item>
        <a-form-item required :name="['insuranceFuture', 'rgtAmtPayTm']" label="权利金支付时间" :label-col="labelColStyle">
          <a-date-picker v-model:value="data.insuranceFuture.rgtAmtPayTm" format="YYYY-MM-DD" value-format="YYYY-MM-DD 23:59:59" :disabled="disabled" />
        </a-form-item>
        <a-form-item :name="['insuranceFuture', 'futureCompanyName']" label="期货公司名称" :label-col="labelColStyle">
          <a-input v-model:value="data.insuranceFuture.futureCompanyName" placeholder="请输入" :disabled="disabled" :maxlength="100" />
        </a-form-item>
        <a-form-item :name="['insuranceFuture', 'regRate']" label="监管评级" :label-col="labelColStyle">
          <a-input v-model:value="data.insuranceFuture.regRate" placeholder="请输入" :disabled="disabled" :maxlength="100" />
        </a-form-item>
        <a-form-item :name="['insuranceFuture', 'spcnIlglVioFlag']" label="是否有重大违规违法行为" :label-col="labelColStyle">
          <a-select
            v-model:value="data.insuranceFuture.spcnIlglVioFlag"
            placeholder="请选择"
            :options="[
              { label: '是', value: 'Y' },
              { label: '否', value: 'N' },
            ]"
            :disabled="disabled"
            @change="handleChangeSpcnIlglVioFlag"
          />
        </a-form-item>
        <a-form-item v-if="data.insuranceFuture.spcnIlglVioFlag === 'Y'" required :name="['insuranceFuture', 'rltVioIlglBhvr']" label="相关违规违法行为" :label-col="labelColStyle" class="col-span-3">
          <a-textarea v-model:value="data.insuranceFuture.rltVioIlglBhvr" placeholder="请输入" :disabled="disabled" :maxlength="2000" show-count />
        </a-form-item>
      </template>
      <!-- 二次开发 -->
      <template v-if="data.busiModeNo === '02'">
        <div class="col-span-3 grid grid-container gap-x-16px bg-[#f2f3f5] p-[12px] pb-0 mb-[12px]">
          <template v-for="(it, index) in data.secPolicyDevelopment.rltPlyList" :key="index">
            <a-form-item :name="['secPolicyDevelopment', 'rltPlyList', index, 'rltPlyNo']" label="关联保单号" :label-col="{ style: { width: pxToRem(100) } }" class="col-span-5" :rules="[{ validator: (rule, value) => rltPlyNoValid(rule, value, index) }]">
              <a-auto-complete :key="autoCompleteKey" v-model:value="it.rltPlyNo" filter-option :dropdown-match-select-width="200" :options="rltPlyNoOptions" placeholder="请输入" style="width: 100%" :disabled="disabled" @search="(val) => (val?.length >= 20 || !val?.length ? debounceRltPlyNoChange(val, insuredName, countyCode, index) : '')" @select="(_: SelectValue, option: DefaultOptionType) => handleSelectRltPlyNo(option, index)" />
            </a-form-item>
            <a-form-item :name="['secPolicyDevelopment', 'rltPlyList', index, 'rltPlyCrpdPrem']" label="对应保费" :label-col="{ style: { width: pxToRem(100) } }" class="col-span-5" :rules="[{ validator: sixRateValid }]">
              <a-input-number v-model:value="it.rltPlyCrpdPrem" placeholder="请输入" :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
            </a-form-item>
            <a-form-item :name="['secPolicyDevelopment', 'rltPlyList', index, 'plyAcumPayAmt']" label="对应赔付金额" :label-col="{ style: { width: pxToRem(100) } }" class="col-span-5" :rules="[{ validator: sixRateValid }]">
              <a-input-number v-model:value="it.plyAcumPayAmt" placeholder="请输入" :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
            </a-form-item>
            <a-form-item :name="['secPolicyDevelopment', 'rltPlyList', index, 'rltPlyCrpdSmplLossRate']" label="对应简单赔付率" :label-col="{ style: { width: pxToRem(120) } }" class="col-span-5" :rules="[{ validator: rltPlyCrpdSmplLossRate }]">
              <a-input-number v-model:value="it.rltPlyCrpdSmplLossRate" placeholder="请输入" :disabled="disabled" addon-after="%" style="width: 100%" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
            </a-form-item>
            <a-form-item v-if="!disabled" :label-col="labelColStyle" class="col-span-1">
              <VueIcon v-if="index === 0" class="text-[#576B95]" :icon="IconTongyongXinzengFont" @click="handlePolicy('0')" />
              <img v-else class="w-[16px] h-[16px]" :src="circleDelete" @click="handlePolicy('1', index)" />
            </a-form-item>
          </template>
        </div>
        <a-form-item required :name="['secPolicyDevelopment', 'whlEstiRiskVal']" label="整体评估风险" :label-col="labelColStyle" :rules="[{ validator: fourValid }]">
          <a-input-number v-model:value="data.secPolicyDevelopment.whlEstiRiskVal" placeholder="请输入" :disabled="disabled" addon-after="%" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
          <div v-if="Number(data.secPolicyDevelopment.whlEstiRiskVal) > 80" class="text-[#FAAD14]">预估风险过高，请核实</div>
        </a-form-item>
      </template>
      <!-- 银抵类 -->
      <template v-if="data.busiModeNo === '03'">
        <a-form-item :rules="[{ required: true, message: '请选择授信报告是否后补' }]" :name="['mortgageBanking', 'bankAuthRptFutSuplFlag']" label="授信报告是否后补" :label-col="labelColStyle">
          <a-radio-group v-model:value="data.mortgageBanking.bankAuthRptFutSuplFlag" :disabled="disabled" @change="handleChangeBankAuthRptFutSuplFlag">
            <a-radio value="Y">是</a-radio>
            <a-radio value="N">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="data.mortgageBanking.bankAuthRptFutSuplFlag === 'Y'" required :name="['mortgageBanking', 'suplTm']" label="传补时间" :label-col="labelColStyle">
          <a-date-picker v-model:value="data.mortgageBanking.suplTm" format="YYYY-MM-DD" value-format="YYYY-MM-DD 23:59:59" :disabled-date="disabledDate" :disabled="disabled" />
        </a-form-item>
        <a-form-item validate-first name="bankAttachmentList" :required="data.mortgageBanking.bankAuthRptFutSuplFlag === 'N'" :rules="[{ validator: validatorBankFile }]" label="银行授信报告" :label-col="labelColStyle" class="col-span-3">
          <a-spin :spinning="bankUploading">
            <a-upload :accept="accept" :multiple="true" :show-upload-list="false" :disabled="disabled" :before-upload="handleBankFileChange">
              <a-button v-if="!disabled && !hideBankUpload" type="link" :disabled="disabled">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in data.bankAttachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo, 'R02')">{{ fileInfo.docoldName }}</span>
              <a-button type="link" @click="deleteFile(fileInfo, 'R02')">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
        <a-form-item validate-first name="riskAttachmentList" required :rules="[{ validator: validatorRiskFile }]" label="风险调研报告" :label-col="labelColStyle" class="col-span-3">
          <a-spin :spinning="riskUploading">
            <a-upload :accept="accept" :multiple="true" :show-upload-list="false" :disabled="disabled" :before-upload="handleRiskFileChange">
              <a-button v-if="!disabled && !hideRiskUpload" type="link">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in data.riskAttachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo, 'R03')">{{ fileInfo.docoldName }}</span>
              <a-button v-if="!disabled" type="link" @click="deleteFile(fileInfo, 'R03')">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
      </template>
      <!-- 创新储备政策、创新储备非政策 -->
      <template v-if="data.busiModeNo === '04' || data.busiModeNo === '05'">
        <a-form-item validate-first name="businessAttachmentList" required :rules="[{ validator: validatorProofFile }]" label="业务证明资料" :label-col="labelColStyle" class="col-span-3">
          <a-spin :spinning="proofUploading">
            <a-upload :accept="accept" :multiple="true" :show-upload-list="false" :disabled="disabled" :before-upload="handleProofFileChange">
              <a-button v-if="!disabled && !hideProofUpload" type="link">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <span class="text-[14px] font-[400] text-[#00000066]">请上传政府发布的相关工作方案/实施方案/招标文件/地方产业政策等文件</span>
            <div v-for="fileInfo in data.businessAttachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo, 'R04')">{{ fileInfo.docoldName }}</span>
              <a-button v-if="!disabled" type="link" @click="deleteFile(fileInfo, 'R04')">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
        <a-form-item validate-first name="riskAttachmentList" required :rules="[{ validator: validatorRiskFile }]" label="风险调研报告" :label-col="labelColStyle" class="col-span-3">
          <a-spin :spinning="riskUploading">
            <a-upload :accept="accept" :multiple="true" :show-upload-list="false" :disabled="disabled" :before-upload="handleRiskFileChange">
              <a-button v-if="!disabled && !hideRiskUpload" type="link">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in data.riskAttachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo, 'R03')">{{ fileInfo.docoldName }}</span>
              <a-button v-if="!disabled" type="link" @click="deleteFile(fileInfo, 'R03')">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
      </template>
      <!-- 其他 -->
      <template v-if="data.busiModeNo === '06'">
        <a-form-item validate-first name="riskAttachmentList" required :rules="[{ validator: validatorRiskFile }]" label="风险调研报告" :label-col="labelColStyle" class="col-span-3">
          <a-spin :spinning="riskUploading">
            <a-upload :accept="accept" :multiple="true" :show-upload-list="false" :disabled="disabled" :before-upload="handleRiskFileChange">
              <a-button v-if="!disabled && !hideRiskUpload" type="link">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in data.riskAttachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo, 'R03')">{{ fileInfo.docoldName }}</span>
              <a-button v-if="!disabled" type="link" @click="deleteFile(fileInfo, 'R03')">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
      </template>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import { debounce, cloneDeep } from 'lodash-es';
import type { Rule } from 'ant-design-vue/es/form';
import dayjs, { type Dayjs } from 'dayjs';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { DefaultOptionType } from 'ant-design-vue/es/vc-tree-select/TreeSelect';
import type { RadioChangeEvent } from 'ant-design-vue/es/radio';
import { IconTongyongShangchuanFont, IconTongyongXinzengFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';
import { $postOnClient, $post, $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import circleDelete from '@/assets/images/circle-delete.svg';
import type { IBusinessModel, IAttachment } from '../riskAssessOnline';
import { useUpload } from './useUpload';
import { rltPlyListItem, accept, acceptType, MAX_SIZE, MAX_FILES_NUM, initInsuranceFuture, initSecPolicyDevelopment, initMortgageBanking } from '../const';

const labelColStyle = {
  style: {
    width: pxToRem(170),
  },
};

const { disabled, attachmentGroupNo, insuredName, countyCode } = defineProps<{
  disabled: boolean;
  attachmentGroupNo: string;
  insuredName: string;
  countyCode: string;
}>();

const { gateWay, service } = useRuntimeConfig().public || {};

const data = defineModel<IBusinessModel>('value', { default: {} });
const formRef = ref();

const fourValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 4) || value > *************) {
    return Promise.reject('最大12位整数,4位小数');
  }
  return Promise.resolve();
};

// 六位小数校验规则
const sixRateValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 6) || value > 1000000000) {
    return Promise.reject('最大9位整数,6位小数');
  }
  return Promise.resolve();
};

// 关联保单号校验规则
const rltPlyNoValid = (_rule: Rule & { field?: string }, value: string, index) => {
  if (!value) {
    return Promise.resolve();
  }

  if (data.value?.secPolicyDevelopment?.rltPlyList?.find((it, inx) => it.rltPlyNo === value && index !== inx)) {
    return Promise.reject('保单号不能一致');
  }

  return Promise.resolve();
};

// 对应简单赔付率校验规则
const rltPlyCrpdSmplLossRate = (_rule: Rule & { field?: string }, value: number) => {
  if (value < 0 || value > 100) {
    return Promise.reject('请输入 [0 到 100] 之间的数字');
  }

  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if (parts?.length > 1 && parts[1]?.length > 4) {
    return Promise.reject('小数位数不能超过 4 位');
  }
  return Promise.resolve();
};

// 风险调研报告文件校验规则
const validatorRiskFile = () => {
  if (!data.value.riskAttachmentList.length) {
    return Promise.reject('请上传风险调研报告');
  }
  return Promise.resolve();
};

// 业务证明材料文件校验规则
const validatorProofFile = () => {
  if (!data.value.businessAttachmentList.length) {
    return Promise.reject('请上传业务证明材料');
  }
  return Promise.resolve();
};

// 银行授信报告
const validatorBankFile = () => {
  if (!data.value.bankAttachmentList.length && data.value?.mortgageBanking?.bankAuthRptFutSuplFlag === 'N') {
    return Promise.reject('请上传银行授信报告');
  }
  return Promise.resolve();
};

const multiDeleteFile = () => {
  if (data.value.bankAttachmentList?.length === 0 && data.value.riskAttachmentList?.length === 0 && data.value.businessAttachmentList?.length === 0) return;
  const bankDocumentId = data.value.bankAttachmentList?.map((it) => it.documentId);
  const riskDocumentId = data.value.riskAttachmentList?.map((it) => it.documentId);
  const businessDocumentId = data.value.businessAttachmentList?.map((it) => it.documentId);
  const documentId = [...bankDocumentId, ...riskDocumentId, ...businessDocumentId]?.join(',');
  const delReq = {
    documentId,
    bizType: 'docTypeRisk',
  };
  $post(`${gateWay}${service.administrate}/attachment/document/delete`, delReq).then((res) => {
    if (res?.code === SUCCESS_CODE) {
      data.value.bankAttachmentList = [];
      data.value.riskAttachmentList = [];
      data.value.businessAttachmentList = [];
    }
  });
};

const handleChangeModel = (val: SelectValue) => {
  if (val === '01') {
    data.value.secPolicyDevelopment = cloneDeep(initSecPolicyDevelopment);
    data.value.mortgageBanking = cloneDeep(initMortgageBanking);
  } else if (val === '02') {
    data.value.insuranceFuture = cloneDeep(initInsuranceFuture);
    data.value.mortgageBanking = cloneDeep(initMortgageBanking);
  } else if (val === '03') {
    data.value.insuranceFuture = cloneDeep(initInsuranceFuture);
    data.value.secPolicyDevelopment = cloneDeep(initSecPolicyDevelopment);
  }
  if (['03', '04', '05', '06'].includes(val)) {
    multiDeleteFile();
  }
};

const handleChangeSpcnIlglVioFlag = (val: SelectValue) => {
  if (val === 'N') {
    data.value.insuranceFuture.rltVioIlglBhvr = '';
  }
};

const handlePolicy = (type: string, index?: number) => {
  if (type === '0') {
    data.value.secPolicyDevelopment.rltPlyList.push(cloneDeep(rltPlyListItem));
  } else {
    data.value.secPolicyDevelopment.rltPlyList?.splice(index, 1);
  }
};

/** 上传模块 */
// 文件验证
const validateFile = (file: File): boolean => {
  const fileExtension = file?.name?.split('.').pop()?.toUpperCase();

  if (!fileExtension || !acceptType.includes(fileExtension)) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }

  if (file.size > MAX_SIZE) {
    message.warning(`${file.name} 大小超过${MAX_SIZE}MB`);
    return false;
  }

  return true;
};

const uploadApi = computed(() => {
  return `${gateWay}${service.administrate}/attachment/document/upload`;
});

// 银行授信报告
const hideBankUpload = ref(false);
const bankUploadParams = computed(() => {
  return {
    bizNo: '',
    bizType: 'docTypeRisk',
    fileType: 'R02',
    policyNo: '',
    documentGroupId: attachmentGroupNo || '',
  };
});
const { uploading: bankUploading, currentFile: bankCurrentFile, handleFileChange: handleBankFileChange } = useUpload(uploadApi, bankUploadParams, validateFile);
watch(bankCurrentFile, (file) => {
  data.value.bankAttachmentList.push({
    attachUrl: file.uploadPath,
    docoldName: file.documentName,
    fileSize: file.documentSize,
    documentFormat: file.documentFormat,
    bucketName: file.bucketName,
    storageType: file.storageType,
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
  });
  formRef.value.validateFields([['bankAttachmentList']]);
  if (data.value.bankAttachmentList.length >= MAX_FILES_NUM) {
    hideBankUpload.value = true;
  } else {
    hideBankUpload.value = false;
  }
});

// 风险调研报告
const hideRiskUpload = ref(false);
const riskUploadParams = computed(() => {
  return {
    bizNo: '',
    bizType: 'docTypeRisk',
    fileType: 'R03',
    policyNo: '',
    documentGroupId: attachmentGroupNo || '',
  };
});
const { uploading: riskUploading, currentFile: riskCurrentFile, handleFileChange: handleRiskFileChange } = useUpload(uploadApi, riskUploadParams, validateFile);
watch(riskCurrentFile, (file) => {
  data.value.riskAttachmentList.push({
    attachUrl: file.uploadPath,
    docoldName: file.documentName,
    fileSize: file.documentSize,
    documentFormat: file.documentFormat,
    bucketName: file.bucketName,
    storageType: file.storageType,
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
  });
  formRef.value.validateFields([['riskAttachmentList']]);
  if (data.value.riskAttachmentList.length >= MAX_FILES_NUM) {
    hideRiskUpload.value = true;
  } else {
    hideRiskUpload.value = false;
  }
});

// 业务证明资料
const hideProofUpload = ref(false);
const proofUploadParams = computed(() => {
  return {
    bizNo: '',
    bizType: 'docTypeRisk',
    fileType: 'R04',
    policyNo: '',
    documentGroupId: attachmentGroupNo || '',
  };
});
const { uploading: proofUploading, currentFile: proofCurrentFile, handleFileChange: handleProofFileChange } = useUpload(uploadApi, proofUploadParams, validateFile);
watch(proofCurrentFile, (file) => {
  data.value.businessAttachmentList.push({
    attachUrl: file.uploadPath,
    docoldName: file.documentName,
    fileSize: file.documentSize,
    documentFormat: file.documentFormat,
    bucketName: file.bucketName,
    storageType: file.storageType,
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
  });
  formRef.value.validateFields([['businessAttachmentList']]);
  if (data.value.businessAttachmentList.length >= MAX_FILES_NUM) {
    hideProofUpload.value = true;
  } else {
    hideProofUpload.value = false;
  }
});

const fileTypeMap = {
  R02: {
    loading: bankUploading,
    fileList: 'bankAttachmentList',
    hideBtn: hideBankUpload,
  },
  R03: {
    loading: riskUploading,
    fileList: 'riskAttachmentList',
    hideBtn: hideRiskUpload,
  },
  R04: {
    loading: proofUploading,
    fileList: 'businessAttachmentList',
    hideBtn: hideProofUpload,
  },
};

// 下载文件
const downloadFile = async (file: IAttachment, type: 'R02' | 'R03' | 'R04') => {
  fileTypeMap[type].loading.value = true;
  const { data } =
    (await $postOnClient<{ fileUrl: string }>('/api/iobs/getInIobsUrl', {
      iobsBucketName: file.bucketName,
      storageTypeCode: file.storageType,
      fileKey: file.attachUrl,
      fileName: file.docoldName,
      downloadFlag: true,
    })) || {};
  fileTypeMap[type].loading.value = false;
  if (data?.fileUrl) {
    window.open(data?.fileUrl);
  }
};

// 删除文件
const deleteFile = (file: IAttachment, type: 'R02' | 'R03' | 'R04') => {
  if (data.value[fileTypeMap[type].fileList as 'bankAttachmentList'].length === 0) return;
  const delReq = {
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
    bizNo: '',
    bizType: 'docTypeRisk',
  };
  fileTypeMap[type].loading.value = true;
  $post(`${gateWay}${service.administrate}/attachment/document/delete`, delReq)
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        data.value[fileTypeMap[type].fileList as 'bankAttachmentList'] = data.value[fileTypeMap[type].fileList as 'bankAttachmentList'].filter((fileInfo) => fileInfo.documentId !== file.documentId);
        if (data.value[fileTypeMap[type].fileList as 'bankAttachmentList'].length < MAX_FILES_NUM) {
          fileTypeMap[type].hideBtn.value = false;
        }
      }
    })
    .finally(() => (fileTypeMap[type].loading.value = false));
};

const disabledDate = (current: Dayjs) => {
  // 获取当前日期
  const today = dayjs().startOf('day'); // 今天00:00:00
  const fifteenDaysLater = today.add(15, 'day');
  return current < today || current >= fifteenDaysLater;
};

const insrRiskRptBusinessModelList = ref<Record<string, string>[]>([]);
const queryInsrRiskRptBusinessModelList = () => {
  const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/getInsrRiskRptBusinessModelList`;
  $get<Record<string, string>[]>(fetchUrl).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      insrRiskRptBusinessModelList.value = res.data;
    }
  });
};

const autoCompleteKey = ref(0); // 用于强制刷新组件
const rltPlyNoOptions = ref<Record<string, string>[]>([]);
const rltPlyNoChange = (value: string, name?: string, code?: string, index: number) => {
  const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/queryRelatedPolicyNos`;
  const insured = name || insuredName;
  const county = code || countyCode;
  $post<{ policyNo: string }[]>(fetchUrl, { policyNo: value, insuredName: insured, countyCode: county }).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      rltPlyNoOptions.value = res?.data?.map((it: { policyNo: string }) => {
        return {
          ...it,
          value: it.policyNo,
        };
      });
      autoCompleteKey.value += 1; // 更新 key，强制刷新组件
      if (rltPlyNoOptions.value?.length === 1) {
        data.value.secPolicyDevelopment.rltPlyList[index].rltPlyCrpdPrem = rltPlyNoOptions.value[0]?.premium || '';
      } else {
        data.value.secPolicyDevelopment.rltPlyList[index].rltPlyCrpdPrem = '';
      }
    } else {
      data.value.secPolicyDevelopment.rltPlyList[index].rltPlyNo = '';
      data.value.secPolicyDevelopment.rltPlyList[index].rltPlyCrpdPrem = '';
      formRef.value.validateFields([['secPolicyDevelopment', index, 'rltPlyNo']]);
      message.error(res?.msg || '');
    }
  });
};
const debounceRltPlyNoChange = debounce(rltPlyNoChange, 1000);
const handleChangeBankAuthRptFutSuplFlag = (e: RadioChangeEvent) => {
  if (e.target.value === 'N') {
    data.value.mortgageBanking.suplTm = '';
  }
};

const handleSelectRltPlyNo = (option: DefaultOptionType, index: number) => {
  data.value.secPolicyDevelopment.rltPlyList[index].rltPlyCrpdPrem = option.premium || '';
};

watch([() => insuredName, () => countyCode], ([val1, val2]) => {
  if (val1 || val2) debounceRltPlyNoChange('', val1, val2, 0);
});

onMounted(() => {
  queryInsrRiskRptBusinessModelList();
});

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({ validate });
</script>

<style lang="less" scoped>
.grid-container {
  grid-template-columns: repeat(21, 1fr);
}
</style>
