import { $postOnClient } from '@/composables/request';
import type { UploadFile } from '../riskAssessOnline';
import { SUCCESS_CODE } from '@/utils/constants';

export type FileUploadParams = { [name: string]: unknown };

// 导出一个自定义 Hook 用于处理上传逻辑
export function useUpload(
  defaultAPI: Ref<string> | ComputedRef<string>, // 默认上传数据接口
  params: Ref<Record<string, string>> | ComputedRef<Record<string, string>>, // 上传接口参数
  validateFile?: (file: File) => boolean,
) {
  const uploading = ref(false);
  const currentFile = ref<UploadFile>({ uploadPath: '', documentName: '', documentSize: '0', documentFormat: '', bucketName: '', storageType: '', documentId: '', documentGroupItemsId: '' });
  const handleFileChange = (file: File) => {
    const { name, size } = file;
    if (name && size) {
      // 文件验证
      if (validateFile && !validateFile(file)) {
        return false;
      }
      const formData = new FormData();
      formData.append('file', file);
      for (const key in params.value) {
        if (Object.prototype.hasOwnProperty.call(params.value, key)) {
          formData.append(key, params.value[key]);
        }
      }
      $postOnClient(toRaw(defaultAPI.value), formData)
        .then((res) => {
          if (res && res.code === SUCCESS_CODE) {
            currentFile.value = res.data as ICurrentFile;
            message.success(res.msg);
          } else if (res) {
            message.error(res.msg);
          }
        })
        .catch((err: string) => {
          message.error(err);
        });
    }
    return Promise.reject();
  };

  // 返回包含分页器对象的对象
  return {
    handleFileChange,
    uploading,
    currentFile,
  };
}
