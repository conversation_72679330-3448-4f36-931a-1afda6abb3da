<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item required name="feeRate" label="费率" :label-col="labelColStyle" validate-first :rules="[{ validator: fourRateValid }]">
        <a-input-number v-model:value="data.feeRate" placeholder="请输入" :disabled="disabled" addon-after="%" style="width: 100%" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" @change="handleCalculatePrem(data.feeRate, data.unitInsrAmt, data.farmInsrNum)" />
      </a-form-item>
      <a-form-item required name="unitInsrAmt" label="单位保额" :label-col="labelColStyle" :rules="[{ validator: unitInsrAmtValid }]">
        <a-input-number v-model:value="data.unitInsrAmt" placeholder="请输入" :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" @change="handleCalculateAmt(data.unitInsrAmt, data.farmInsrNum)" />
      </a-form-item>
      <a-form-item v-if="!isMultiTarget" required label="承保面积/数量" name="farmInsrNum" :rules="[{ validator: farmInsrNumValid }]">
        <a-input-number v-model:value.trim="data.farmInsrNum" placeholder="请输入" :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" @change="handleCalculateAmt(data.unitInsrAmt, data.farmInsrNum)">
          <template #addonAfter>
            <a-select
              v-model:value="data.plyPstnNo"
              style="width: 80px"
              :options="insuranceNumUnitList"
              :disabled="disabled"
              @change="
                () => {
                  formRef?.validateFields(['farmInsrNum']);
                }
              "
            />
          </template>
        </a-input-number>
      </a-form-item>
      <a-form-item required name="insuredAmount" label="整体保额" :label-col="labelColStyle" :rules="[{ validator: zeroValid }]">
        <a-input-number v-model:value="data.insuredAmount" placeholder="请输入" disabled style="width: 100%" />
      </a-form-item>
      <a-form-item required name="premium" label="业务保费" :label-col="labelColStyle" :rules="[{ validator: zeroValid }]">
        <a-input-number v-model:value="data.premium" placeholder="请输入" disabled style="width: 100%" />
      </a-form-item>
      <a-form-item required name="docFeeRate" label="跟单费用率" :label-col="labelColStyle" validate-first :rules="[{ validator: fourRateValid }]">
        <a-input-number v-model:value="data.docFeeRate" placeholder="请输入" :disabled="disabled && (isHeadUnderwriting === 'N' || !isHeadUnderwriting)" addon-after="%" style="width: 100%" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
      </a-form-item>
      <a-form-item required name="insrClntNum" label="承保客户数" :label-col="labelColStyle" :rules="[{ validator: insrClntNumValid }]">
        <a-input-number v-model:value="data.insrClntNum" placeholder="请输入" :disabled="disabled" style="width: 100%" :min="0" :precision="0" @change="handleZeroInsuredName" />
      </a-form-item>
      <a-form-item v-if="Number(data.insrClntNum) === 1" required name="insuredName" label="客户名称" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]">
        <a-input v-model:value="data.insuredName" placeholder="请输入" :disabled="disabled" />
      </a-form-item>
      <a-form-item required name="whlEstiRiskVal" label="预估风险" :label-col="labelColStyle" :rules="[{ validator: whlEstiRiskValValid }]">
        <a-input-number v-model:value="data.whlEstiRiskVal" placeholder="请输入" :disabled="disabled && (isHeadUnderwriting === 'N' || !isHeadUnderwriting)" addon-after="%" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
        <span v-if="Number(data.whlEstiRiskVal) > 90" class="text-[#FAAD14]">预估风险过高，请核实</span>
      </a-form-item>
      <a-form-item required name="atchPt" label="起赔点" :label-col="labelColStyle" validate-first :rules="[{ validator: fourRateValid }]">
        <a-input-number v-model:value="data.atchPt" placeholder="请输入" :disabled="disabled" addon-after="%" style="width: 100%" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
      </a-form-item>
      <a-form-item required name="ddctRate" label="免赔率" :label-col="labelColStyle" validate-first :rules="[{ validator: fourRateValid }]">
        <a-input-number v-model:value="data.ddctRate" placeholder="请输入" :disabled="disabled" addon-after="%" style="width: 100%" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')" />
      </a-form-item>
      <!-- <div v-if="isMultiTarget" class="col-span-3">
        <template v-for="(item, index) in riskAgrList" :key="index">
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item :label="`标的${index + 1}`" validate-first :name="['riskAgrList', index, 'agriRiskObjDetlName']" :label-col="labelColStyle">
              <a-input v-model:value="item.agriRiskObjDetlName" disabled />
            </a-form-item>
            <a-form-item required label="承保面积/数量" validate-first :name="['riskAgrList', index, 'insuredNumber']" :rules="[{ validator: () => checkRiskInsuredNumber(index), trigger: 'change' }]" :label-col="labelColStyle">
              <a-input-number v-model:value.trim="item.farmInsrNum" placeholder="请输入" allow-clear :disabled="disabled" style="width: 100%">
                <template #addonAfter>
                  <a-select
                    v-model:value="item.plyPstnNo"
                    style="width: 80px"
                    :options="[
                      { value: '01', label: '亩' },
                      { value: '02', label: '公顷' },
                    ]"
                  />
                </template>
              </a-input-number>
            </a-form-item>
          </div>
        </template>
      </div> -->
    </div>
  </a-form>
</template>

<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select';
import NP from 'number-precision';
import { pxToRem } from '@/utils/tools';
import { checkClientName } from '@/utils/validators';
import type { Rule } from 'ant-design-vue/es/form';
import type { IBusinessInfo } from '../riskAssessOnline';

const labelColStyle = {
  style: {
    width: pxToRem(120),
  },
};

const { isMultiTarget, isHeadUnderwriting } = defineProps<{
  disabled: boolean;
  isMultiTarget: boolean;
  insuranceNumUnitList: Record<string, string>[];
  isHeadUnderwriting: string;
}>();

const formRef = ref();
const data = defineModel<IBusinessInfo>('value', { default: {} });

const zeroValid = (_rule: Rule & { field?: string }, value: number) => {
  if (String(value) === '0') {
    return Promise.reject('不能为0');
  }
  return Promise.resolve();
};
// 费率、跟单费用率、起赔点、免赔率校验规则
const fourRateValid = (_rule: Rule & { field?: string }, value: number) => {
  if (value < 0 || value > 100) {
    return Promise.reject('请输入 [0 到 100] 之间的数字');
  }

  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if (parts?.length > 1 && parts[1]?.length > 4) {
    return Promise.reject('小数位数不能超过 4 位');
  }
  return Promise.resolve();
};

// 单位保额校验规则
const unitInsrAmtValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 2) || value > 1000000000) {
    return Promise.reject('最大9位整数,2位小数');
  }
  return Promise.resolve();
};

// 约定产量校验规则
const farmInsrNumValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 2) || value > 1000000000) {
    return Promise.reject('最大9位整数,2位小数');
  }
  if (!data.value.plyPstnNo) {
    return Promise.reject('请选择承保面积/数量单位');
  }
  return Promise.resolve();
};

const insrClntNumValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  if (value > 10000000000) {
    return Promise.reject('最大10位整数');
  }
  return Promise.resolve();
};

// 风险预估校验规则
const whlEstiRiskValValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 4) || value > 1000000000000) {
    return Promise.reject('最大12位整数,4位小数');
  }

  return Promise.resolve();
};

// const checkRiskInsuredNumber = (index: number) => {
//   if (!riskAgrList?.[index]?.farmInsrNum) {
//     return Promise.reject('请录入承保总面积/总数量');
//   }
//   const reg = /^(?:0\.(?!0+$)\d{1,2}|[1-9]\d{0,8}(?:\.\d{1,2})?)$/;
//   if (!reg.test(riskAgrList?.[index]?.farmInsrNum)) {
//     return Promise.reject('请输入大于0的数字，整数位不超过9位，小数位不超过2位');
//   }
//   if (!riskAgrList?.[index]?.plyPstnNo) {
//     return Promise.reject('请选择"承保总面积/总数量单位');
//   }
//   return Promise.resolve();
// };

const handleCalculateAmt = (unitInsrAmt: number | string, farmInsrNum: number | string) => {
  data.value.insuredAmount = Number(NP.times(Number(unitInsrAmt || 0), Number(farmInsrNum || 0)).toFixed(2));
  handleCalculatePrem(data.value.feeRate, unitInsrAmt, farmInsrNum);
  formRef.value.validateFields([['insuredAmount']]);
};
const handleCalculatePrem = (feeRate: number | string, unitInsrAmt: number | string, farmInsrNum: number | string) => {
  data.value.premium = Number(NP.times(Number(NP.divide(feeRate, 100) || 0), Number(unitInsrAmt || 0), Number(farmInsrNum || 0)).toFixed(2));
  formRef.value.validateFields([['premium']]);
};

const handleZeroInsuredName = (val: SelectValue) => {
  if (val !== 1) {
    data.value.insuredName = '';
  }
};

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({ validate });
</script>

<style lang="less" scoped></style>
