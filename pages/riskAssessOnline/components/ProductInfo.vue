<template>
  <a-form ref="formRef" :model="formData" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item class="col-span-2" label="产品名称" name="prodNo" required>
        <ProductSelect ref="productRef" v-model:value="formData.prodNo" :department-code="formData.aplyDeptNo" :encode-key="formData.agriculturalRiskObjectDetailCode?.split(',')?.[0]" module="accept" :disabled="disabled" @product-change="changeProduct" />
      </a-form-item>
      <a-form-item label="补贴类型" name="alowTypeCode" required :rules="[{ validator: validateAlowTypeCode }]">
        <a-select v-model:value="formData.alowTypeCode" placeholder="请选择" :options="subsidyTypeList" disabled />
      </a-form-item>
      <a-form-item label="标的" name="agriculturalRiskObjectDetailCode" class="col-span-3" required>
        <a-form-item-rest>
          <RiskCodeSelect ref="riskCodeRef" :default-value="defaultRisk" :default-mul-risk="defaultMulRisk" :department-code="formData.aplyDeptNo" :disabled="disabled" :is-multi-target="isMultiTarget" :multi-option="multiOption" @change-value="changeRiskCode" />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="标的地址" required validate-first :name="['riskAddressInfoList', 0, 'address']" class="col-span-3" :rules="[{ validator: validateAddress }]">
        <div class="flex">
          <a-form-item-rest>
            <RegionSelect v-model:province="formData.riskAddressInfoList[0].riskObjAddrPrvnCode" v-model:city="formData.riskAddressInfoList[0].riskObjAddrCityCode" v-model:county="formData.riskAddressInfoList[0].riskObjAddrCntyCode" v-model:town="formData.riskAddressInfoList[0].riskObjAddrTownNo" v-model:village="formData.riskAddressInfoList[0].riskObjAddrVlgCode" class="grow mr-8px w-[70%]" :disabled="disabled" @change-selected="changeRiskAddress" />
          </a-form-item-rest>
          <div class="w-[30%]">
            <a-input v-model:value.trim="formData.riskAddressInfoList[0].address" placeholder="请输入" allow-clear :disabled="disabled" />
          </div>
        </div>
      </a-form-item>
      <!-- 产品主险类型是价格类型展示以下字段 -->
      <template v-if="showPriceRelated">
        <a-form-item label="当前标的市场价格" required name="curRiskObjMktPrc" :rules="[{ validator: curRiskObjMktPrcValid }]">
          <a-input-number v-model:value.trim="formData.curRiskObjMktPrc" placeholder="请输入" allow-clear :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')">
            <template #addonAfter>
              <a-select
                v-model:value="formData.curRiskObjMktPrcUnit"
                style="width: 80px"
                :options="priceAgreedOptions"
                :disabled="disabled"
                @change="
                  () => {
                    formRef?.validateFields(['curRiskObjMktPrc']);
                  }
                "
              />
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="拟承保目标价格" required name="insrTgtPrc" :rules="[{ validator: insrTgtPrcValid }]">
          <a-input-number v-model:value.trim="formData.insrTgtPrc" placeholder="请输入" allow-clear :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')">
            <template #addonAfter>
              <a-select
                v-model:value="formData.insrTgtPrcUnit"
                style="width: 80px"
                :options="priceAgreedOptions"
                :disabled="disabled"
                @change="
                  () => {
                    formRef?.validateFields(['insrTgtPrc']);
                  }
                "
              />
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="约定产量" required name="riskObjOput" :rules="[{ validator: riskObjOputValid }]">
          <a-input-number v-model:value.trim="formData.riskObjOput" placeholder="请输入" allow-clear :disabled="disabled" style="width: 100%" :min="0" :formatter="(value) => value?.toString().replace(/\.0+$/, '')" :parser="(value) => value?.replace(/\.0+$/, '')">
            <template #addonAfter>
              <a-select
                v-model:value="formData.riskObjNumUnit"
                style="width: 80px"
                :options="unitInsureCountOptions"
                :disabled="disabled"
                @change="
                  () => {
                    formRef?.validateFields(['riskObjOput']);
                  }
                "
              />
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="历史价格清单" validate-first required name="attachmentList" :rules="[{ validator: validatorFile }]" class="col-span-3">
          <a-spin :spinning="uploading">
            <a-upload :accept="accept" :show-upload-list="false" :disabled="disabled" :multiple="true" :before-upload="handleFileChange">
              <a-button v-if="!disabled && !hideUpload" type="link">
                <template #icon>
                  <VueIcon class="text-[14px] text-[#07C160]" :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[6px] text-[14px] text-[#07C160]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in formData.attachmentList" :key="fileInfo?.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo)">{{ fileInfo.docoldName }}</span>
              <a-button v-if="!disabled" type="link" @click="deleteFile(fileInfo)">删除</a-button>
            </div>
          </a-spin>
        </a-form-item>
      </template>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { IconTongyongShangchuanFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import { $postOnClient, $post, $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { DefaultOptionType } from 'ant-design-vue/es/select';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { useUpload } from './useUpload';
import type { IBaseInfo, RegionType, IBusinessInfo, IAttachment } from '../riskAssessOnline';
import { acceptType, accept, MAX_SIZE, MAX_FILES_NUM } from '../const';

const { disabled } = defineProps<{
  disabled: boolean;
  defaultMulRisk: string[]; // 默认多标的
}>();

const { gateWay, service } = useRuntimeConfig().public || {};
const route = useRoute();

const formData = defineModel<IBaseInfo>('value', { default: {} });
const businessInfo = defineModel<IBusinessInfo>('businessInfo', { default: {} });
const emit = defineEmits(['update:insuranceNumUnitList']);
// 是否为多标的
const isMultiTarget = defineModel('isMultiTarget', { default: false });

const formRef = ref();
const riskCodeRef = ref();
const subsidyTypeList = ref<Array<SelectOptions>>([]); // 补贴类型关联产品
const defaultRisk = ref('');
// 多标的option
const multiOption = ref<DefaultOptionType[]>([]);
// 价格类主险展示价格相关
const showPriceRelated = ref<boolean>(false);
// 超数量禁止上传
const hideUpload = ref(false);

// 标的地址校验
const validateAddress = (_rule: Rule, value: string) => {
  if (!formData.value.riskAddressInfoList[0].riskObjAddrPrvnCode || !formData.value.riskAddressInfoList[0].riskObjAddrCityCode || !formData.value.riskAddressInfoList[0].riskObjAddrCntyCode) {
    return Promise.reject('请选择前三级地址');
  } else if (!value) {
    return Promise.reject('请输入地址');
  }
  return Promise.resolve();
};

// 当前标的市场价格校验规则
const curRiskObjMktPrcValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 2) || value > 1000000000000) {
    return Promise.reject('最大12位整数,2位小数');
  }
  if (!formData.value.curRiskObjMktPrcUnit) {
    return Promise.reject('请选择当前标的市场价格单位');
  }
  return Promise.resolve();
};

// 拟承保目标价格校验规则
const insrTgtPrcValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 2) || value > 1000000000000) {
    return Promise.reject('最大12位整数,2位小数');
  }
  if (!formData.value.insrTgtPrcUnit) {
    return Promise.reject('请选择拟承保目标价格单位');
  }
  return Promise.resolve();
};

// 约定产量校验规则
const riskObjOputValid = (_rule: Rule & { field?: string }, value: number) => {
  if (!value) {
    return Promise.resolve();
  }
  const strValue = value?.toString();
  const parts = strValue?.split('.');
  if ((parts?.length > 1 && parts[1]?.length > 2) || value > 1000000000000) {
    return Promise.reject('最大12位整数,2位小数');
  }
  if (!formData.value.riskObjNumUnit) {
    return Promise.reject('请选择约定产量单位');
  }
  return Promise.resolve();
};

// 补贴类型校验规则
const validateAlowTypeCode = (_rule: Rule & { field?: string }, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  // 补贴类型为“商业性”
  if (value !== '3') {
    return Promise.reject('补贴类型要选商业性');
  }

  return Promise.resolve();
};

// 文件校验
const validatorFile = () => {
  if (!formData.value.attachmentList.length) {
    return Promise.reject('请上传历史价格清单');
  }
  return Promise.resolve();
};

const getBaseInfo = async () => {
  try {
    const res = await $post<{ subsidyTypeList: Array<SelectOptions> }>('/api/common/getParmBaseConstantConf', ['subsidyType']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      subsidyTypeList.value = res.data.subsidyTypeList || [];
    } else {
      subsidyTypeList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
getBaseInfo();

// 选择产品名称
const changeProduct = async (_: string, option: DefaultOptionType) => {
  try {
    formData.value.prodNo = option?.value as string;
    formData.value.prodName = option?.realLabel || '';
    formData.value.productVersionNo = option?.version || '';
    formData.value.alowTypeCode = option?.subsidyType || '';
    if (!option) {
      defaultRisk.value = '';
      formData.value.agriculturalRiskObjectDetailCode = '';
      formData.value.riskLevel = '1';
      riskCodeRef.value?.resetData();
    } else if (Number(formData.value.riskLevel) !== 5) {
      //  5级标的有值，则不回显标的
      formData.value.agriculturalRiskObjectDetailCode = option?.targetType || '';
      defaultRisk.value = formData.value.agriculturalRiskObjectDetailCode;
    }
    formRef.value?.validateFields(['alowTypeCode', 'agriculturalRiskObjectDetailCode']);
    const url = gateWay + service.accept + '/web/applicationForm/productInfos';
    const data = {
      productCode: option?.value,
      version: option?.version,
    };
    const res = await $post<{ subsidyType: string; planList: Record<string, string>[]; targetTypeList: Record<string, string>[]; productType: string }>(url, data);
    if (res && res.code === SUCCESS_CODE && res.data) {
      formData.value.alowTypeCode = res.data.subsidyType || '';
      formRef.value?.validateFields(['alowTypeCode']);
      // 是否为多标产品
      if (res?.data?.targetTypeList?.length > 1) {
        isMultiTarget.value = true;
        multiOption.value = res?.data?.targetTypeList.map((item) => ({
          encodeValue: `${item.fourLevTargetName}/${item.fiveLevTargetName}`,
          encodeKey: item.fiveLevTargetType,
        }));
        // 多标产品改变时需要重置4级标的的值
        defaultRisk.value = '';
      } else {
        isMultiTarget.value = false;
        multiOption.value = [];
      }
      // 产品名称主险类型是非价格类的，要清空四个字段
      const len = res.data.planList.findIndex((item) => {
        return item.isMain === '1' && (item.planType.startsWith('06') || item.planType.startsWith('07'));
      });
      showPriceRelated.value = len !== -1;
      if (!showPriceRelated.value) {
        formData.value.curRiskObjMktPrc = '';
        formData.value.curRiskObjMktPrcUnit = '';
        formData.value.insrTgtPrc = '';
        formData.value.insrTgtPrcUnit = '';
        formData.value.riskObjOput = '';
        formData.value.riskObjNumUnit = '';
        // 切换产品名称时，删除附件
        multiDeleteFile();
      }
      if (formData.value.aplyDeptNo) {
        getParmBaseConstantConfData(option?.value as string, option?.version);
        if (!route.query?.riskAsmtNo) {
          businessInfo.value.plyPstnNo = '';
        }
      }
    }
  } catch (error) {
    console.log('error: ', error);
  }
};

const multiDeleteFile = () => {
  if (formData.value.attachmentList?.length === 0) return;
  const documentId = formData.value.attachmentList?.map((it) => it.documentId)?.join(',');
  const delReq = {
    documentId,
    bizType: 'docTypeRisk',
  };
  $post(`${gateWay}${service.administrate}/attachment/document/delete`, delReq).then((res) => {
    if (res?.code === SUCCESS_CODE) {
      formData.value.attachmentList = [];
    }
  });
};

const insuranceNumUnitList = ref<Record<string, string>[]>([]);
const unitInsureCountOptions = ref<Record<string, string>[]>([]);
const priceAgreedOptions = ref<Record<string, string>[]>([]);
// 查询承保数量单位
const getParmBaseConstantConfData = async (productCode: string, productVersion: string) => {
  try {
    const fetchInsuranceNum = $get(`${gateWay}${service.administrate}/public/queryRiskNumberUnitList`, { productCode, productVersion });
    const fetchInsureCount = $get(`${gateWay}${service.administrate}/public/queryProductionUnitList`, { productCode, productVersion });
    const fetchPriceAgreed = $get(`${gateWay}${service.administrate}/public/queryPriceIndexUnitList`, { productCode, productVersion });

    const promiseAll = [fetchInsuranceNum, fetchInsureCount, fetchPriceAgreed];
    const allRes = await Promise.all(promiseAll);
    if (allRes[0]?.code === '000000' && Array.isArray(allRes[0]?.data)) {
      insuranceNumUnitList.value = allRes[0]?.data;
      emit('update:insuranceNumUnitList', insuranceNumUnitList.value);
      // 默认选择承保数量单位
      if (!route.query.riskAsmtNo) {
        businessInfo.value.plyPstnNo = insuranceNumUnitList.value.find((item) => item.defaultSelect)?.value || '';
      }
    }
    if (allRes[1]?.code === '000000' && Array.isArray(allRes[1]?.data)) {
      unitInsureCountOptions.value = allRes[1]?.data;
      // 默认选择约定产量单位
      if (!route.query.riskAsmtNo) {
        formData.value.riskObjNumUnit = unitInsureCountOptions.value.find((item) => item.defaultSelect)?.value || '';
      }
    }
    if (allRes[2]?.code === '000000' && Array.isArray(allRes[2]?.data)) {
      priceAgreedOptions.value = allRes[2]?.data;
      // 默认选择价格相关单位
      if (!route.query.riskAsmtNo) {
        formData.value.curRiskObjMktPrcUnit = priceAgreedOptions.value.find((item) => item.defaultSelect)?.value || '';
        formData.value.insrTgtPrcUnit = priceAgreedOptions.value.find((item) => item.defaultSelect)?.value || '';
      }
    }
  } catch (error) {
    console.log(error);
  }
};

// 选择标的
const changeRiskCode = (value: string, level: string, option: DefaultOptionType | DefaultOptionType[]) => {
  if (String(level) === '4' && isMultiTarget.value) {
    if (option?.length > 0) {
      formData.value.riskAgrList = option.map((item: { encodeKey: string; encodeValue: string }) => {
        return {
          agriculturalRiskObjectDetailCode: item.encodeKey, // 农险标的细分代码
        };
      });
    }
  } else {
    formData.value.agriculturalRiskObjectDetailCode = value;
    formData.value.riskLevel = level;
    formRef.value.validateFields(['agriculturalRiskObjectDetailCode']);
  }
};

// 选择标的地址
const changeRiskAddress = async (option: { label: string }, type: RegionType) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    formData.value.riskAddressInfoList[0][`${type}Name`] = option?.label || '';
  }
  // 拼接地址时过滤掉undefined/null值，避免出现NaN
  const addressParts = [formData.value.riskAddressInfoList[0]?.provinceName, formData.value.riskAddressInfoList[0]?.cityName, formData.value.riskAddressInfoList[0]?.countyName, formData.value.riskAddressInfoList[0]?.townName, formData.value.riskAddressInfoList[0]?.villageName].filter(Boolean);

  formData.value.riskAddressInfoList[0].address = addressParts.join('') || '';
  formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);
};

const bankUploadApi = computed(() => {
  return `${gateWay}${service.administrate}/attachment/document/upload`;
});
const bankUploadParams = computed(() => {
  return {
    bizNo: '',
    bizType: 'docTypeRisk',
    fileType: 'R01',
    policyNo: '',
    documentGroupId: formData.value.attachmentGroupNo || '',
  };
});

// 文件验证
const validateFile = (file: File): boolean => {
  const fileExtension = file?.name?.split('.').pop()?.toUpperCase();

  if (!fileExtension || !acceptType.includes(fileExtension)) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }

  if (file.size > MAX_SIZE) {
    message.warning(`${file.name} 大小超过${MAX_SIZE}MB`);
    return false;
  }

  return true;
};

const { uploading, currentFile, handleFileChange } = useUpload(bankUploadApi, bankUploadParams, validateFile);

watch(currentFile, (file) => {
  formData.value.attachmentList.push({
    attachUrl: file.uploadPath,
    docoldName: file.documentName,
    fileSize: file.documentSize,
    documentFormat: file.documentFormat,
    bucketName: file.bucketName,
    storageType: file.storageType,
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
  });
  formRef.value.validateFields([['attachmentList']]);
  if (formData.value.attachmentList.length >= MAX_FILES_NUM) {
    hideUpload.value = true;
  } else {
    hideUpload.value = false;
  }
});

// 下载文件
const downloadFile = async (file: IAttachment) => {
  uploading.value = true;
  const { data } =
    (await $postOnClient<{ fileUrl: string }>('/api/iobs/getInIobsUrl', {
      iobsBucketName: file.bucketName,
      storageTypeCode: file.storageType,
      fileKey: file.attachUrl,
      fileName: file.docoldName,
      downloadFlag: true,
    })) || {};
  uploading.value = false;
  if (data?.fileUrl) {
    window.open(data?.fileUrl);
  }
};

// 删除文件
const deleteFile = (file: IAttachment) => {
  if (formData.value.attachmentList.length === 0) return;
  const delReq = {
    documentId: file.documentId,
    documentGroupItemsId: file.documentGroupItemsId,
    bizNo: '',
    bizType: 'docTypeRisk',
  };
  uploading.value = true;
  $post(`${gateWay}${service.administrate}/attachment/document/delete`, delReq)
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        formData.value.attachmentList = formData.value.attachmentList.filter((fileInfo) => fileInfo.documentId !== file.documentId);
      }
    })
    .finally(() => (uploading.value = false));
};

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({ validate });
</script>

<style lang="less" scoped></style>
