<template>
  <a-form ref="formRef" class="risk-baseInfo-container" :model="data" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px">
      <a-form-item required name="riskAsmtName" label="风险评估名称" :label-col="labelColStyle" class="col-span-3">
        <a-input v-model:value="data.riskAsmtName" placeholder="请输入" :disabled="disabled" :maxlength="30" show-count />
      </a-form-item>
      <a-form-item required name="aplyDeptNo" label="申报机构" :label-col="labelColStyle">
        <department-search v-model:contain-child-depart="data.inclLowLvlDeptFlag" :dept-code="data.aplyDeptNo" :disabled="disabled || !!data.riskAsmtNo" :show-child-depart="true" @change-dept-code="changeDeptCode" />
      </a-form-item>
      <a-form-item name="saleListCode" label="指定业务员" :label-col="labelColStyle">
        <div class="flex items-center">
          <a-select v-model:value="data.saleListCode" placeholder="请选择" :options="employeeList" mode="multiple" allow-clear show-search :loading="employeeLoading" option-filter-prop="label" :disabled="disabled" @change="handleSaleList" />
          <a-tooltip placement="top">
            <template #title>如填写指定业务员，则只有对应业务员可以使用此风险评估编号，未填写则指定机构下业务员均可使用。</template>
            <ExclamationCircleFilled class="text-[20px]" />
          </a-tooltip>
        </div>
      </a-form-item>
      <a-form-item required name="opCtctInfo" label="发起联系人方式" :rules="[{ validator: phoneRule, trigger: 'change' }]" :label-col="{ style: { width: pxToRem(120) } }">
        <a-input v-model:value="data.opCtctInfo" placeholder="请输入" :disabled="disabled" />
      </a-form-item>
      <a-form-item required name="coinsuranceFlag" label="是否共保" :label-col="labelColStyle" class="col-span-3">
        <a-radio-group v-model:value="data.coinsuranceFlag" :disabled="disabled">
          <a-radio value="Y">是</a-radio>
          <a-radio value="N">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item required name="busiBcgd" label="业务背景" :label-col="labelColStyle" class="col-span-3">
        <a-textarea v-model:value="data.busiBcgd" placeholder="请输入" :disabled="disabled" :maxlength="1000" show-count />
      </a-form-item>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import { pxToRem } from '@/utils/tools';
import { $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { phoneRule } from '@/utils/validators';
import type { IBaseInfo, ISelectOptions, IAddress } from '../riskAssessOnline';

const labelColStyle = {
  style: {
    width: pxToRem(100),
  },
};

const { disabled } = defineProps<{
  disabled: boolean;
}>();

const formRef = ref();
const data = defineModel<IBaseInfo>('value', { default: {} });
const { gateWay, service } = useRuntimeConfig().public || {};

// 选择机构
const changeDeptCode = (newVal: string) => {
  data.value.aplyDeptNo = newVal;
  handleSearchEmployeeList(newVal);
  getRiskAddressInfoList(newVal);
  formRef.value.validateFields([['aplyDeptNo']]);
};

const getRiskAddressInfoList = (departmentCode: string) => {
  if (!data.value.riskAsmtNo) {
    const fetchUrl = `${gateWay}${service.administrate}/public/getAddressByDepartmentCode`;
    $get<IAddress>(fetchUrl, { departmentCode }).then((addressRes) => {
      if (addressRes && addressRes.code === SUCCESS_CODE) {
        if (addressRes?.data) {
          const { province: riskObjAddrPrvnCode, provinceName, city: riskObjAddrCityCode, cityName, town: riskObjAddrTownNo, townName, county: riskObjAddrCntyCode, countyName, village: riskObjAddrVlgCode, villageName, address } = addressRes.data;
          data.value.riskAddressInfoList = [
            {
              idAplyRiskEvaluateRiskAddress: '',
              countryCode: '',
              riskObjAddrPrvnCode, // 省编码
              provinceName, // 省名称
              riskObjAddrCityCode, // 市编码
              cityName, // 市名称
              riskObjAddrCntyCode, // 区编码
              countyName, // 区名称
              riskObjAddrTownNo, // 镇编码
              townName, // 镇名称
              riskObjAddrVlgCode, // 村编码
              villageName, // 村名称
              address, // 完整地址
            },
          ];
          formRef.value.validateFields([['riskAddressInfoList', 0, 'address']]);
        } else {
          data.value.riskAddressInfoList = [
            {
              idAplyRiskEvaluateRiskAddress: '',
              countryCode: '',
              riskObjAddrPrvnCode: '', // 省编码
              provinceName: '', // 省名称
              riskObjAddrCityCode: '', // 市编码
              cityName: '', // 市名称
              riskObjAddrCntyCode: '', // 区编码
              countyName: '', // 区名称
              riskObjAddrTownNo: '', // 镇编码
              townName: '', // 镇名称
              riskObjAddrVlgCode: '', // 村编码
              villageName: '', // 村名称
              address: '', // 完整地址
            },
          ];
        }
      }
    });
  }
};

/** 指定业务员模糊搜索 */
const employeeList = ref<ISelectOptions[]>([]);
const employeeLoading = ref(false);
const handleSearchEmployeeList = async (departmentCode: string) => {
  try {
    employeeLoading.value = true;
    const res = await $get<ISelectOptions[]>('/api/common/getSasEmployeeList', {
      departmentCode,
    });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      employeeList.value = res?.data || [];
      // const defaultEmployee = employeeList.value?.[0];
      // data.value.saleList = defaultEmployee.value || [{ salemanNo: '',salemanName: '' }];
    }
  } catch (error) {
    console.log(error, '获取业务员失败');
  } finally {
    employeeLoading.value = false;
  }
};

// 搜索时，按回车键默认选中第一条数据
// const inputKeyDownChange = (e: Event & { code: string }) => {
//   if (e.code === 'Enter') {
//     const val = employeeList.value?.[0]?.value;
//     if (val) {
//       data.value.address = val
//     }
//   }
// };

const handleSaleList = (_: SelectValue, option: DefaultOptionType | DefaultOptionType[]) => {
  data.value.saleList = option.map((it: { value: string; label: string }) => {
    return { salemanNo: it.value, salemanName: it.label };
  });
};

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({ validate });
</script>

<style lang="less" scoped>
.risk-baseInfo-container {
  span.anticon.anticon-exclamation-circle {
    margin-left: 8px;
    color: #cccccc;
  }
}
</style>
