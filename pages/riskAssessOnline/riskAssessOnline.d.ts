export interface IFormState {
  baseInfo: IBaseInfo;
  businessInfo: IBusinessInfo;
  businessModel: IBusinessModel;
}

interface ISaleList {
  salemanNo: string;
  salemanName: string;
}

export interface IBaseInfo {
  riskAsmtNo: string; // 风险评估单号
  attachmentGroupNo: string; // 附件上传documentGroupId
  riskAsmtName: string; // 风险评估名称
  riskAsmtStsCode: string; // 风险评估状态
  aprvTm: string; // 审批时间
  busiBcgd: string; // 业务背景
  inclLowLvlDeptFlag: boolean; // 是否机构包含下级
  aplyDeptNo: string; // 申报机构编码
  aplyDeptName: string; // 申报机构名称
  saleList: ISaleList[]; // 指定业务员列表
  saleListCode: string[];
  opCtctInfo: string;
  coinsuranceFlag: string;
  mainInsrFlag: string;
  prodNo: string;
  prodName: string;
  alowTypeCode: string;
  alowTypeName: string;
  agriculturalRiskObjectDetailCode: string;
  riskLevel: string;
  riskObjLocVlgCode: string;
  riskAgrList?: IRiskAgrList[];
  riskObjCompAddr: string;
  riskAddressInfoList: IAddressInfo[];
  curRiskObjMktPrc: string;
  curRiskObjMktPrcUnit: string;
  insrTgtPrc: string;
  insrTgtPrcUnit: string;
  listAtchId: string;
  riskObjOput: string;
  riskObjNumUnit: string;
  aplyPrps: string;
  clmPrps: string;
  attachmentList: IAttachment[];
  isHeadUnderwriting: string; // Y-总部核保人、总部核保经理 N-非总部核保人、非总部核保经理
  productVersionNo: string; // 产品版本号
}

export interface IRiskAgrList {
  agriculturalRiskObjectDetailCode: string;
}

export interface IAddressInfo {
  idAplyRiskEvaluateRiskAddress: string;
  countryCode: string;
  riskObjAddrPrvnCode: string; // 省编码
  provinceName: string; // 省名称
  riskObjAddrCityCode: string; // 市编码
  cityName: string; // 市名称
  riskObjAddrCntyCode: string; // 区编码
  countyName: string; // 区名称
  riskObjAddrTownNo: string; // 镇编码
  townName: string; // 镇名称
  riskObjAddrVlgCode: string; // 村编码
  villageName: string; // 村名称
  address: string; // 完整地址
}

export interface IBusinessInfo {
  feeRate: string;
  unitInsrAmt: string;
  farmInsrNum: string;
  plyPstnNo: string;
  insuredAmount: string | number;
  premium: string | number;
  docFeeRate: string;
  insrClntNum: string;
  insuredName: string;
  whlEstiRiskVal: string | number;
  atchPt: string;
  ddctRate: string;
}

interface IInsuranceFuture {
  rgtAmtPct: string | number;
  rgtAmtPayTm: string;
  futureCompanyName: string;
  regRate: string;
  spcnIlglVioFlag: string;
  rltVioIlglBhvr: string;
}

interface rltPly {
  rltPlyNo: string;
  rltPlyCrpdPrem: string;
  plyAcumPayAmt: string;
  rltPlyCrpdSmplLossRate: string;
}

interface ISecPolicyDevelopment {
  whlEstiRiskVal: string | number;
  rltPlyList: rltPly[];
}

interface IMortgageBanking {
  bankAuthRptFutSuplFlag: string;
  suplTm: string;
}

export interface IBusinessModel {
  busiModeNo: string;
  busiModeName: string;
  insuranceFuture: IInsuranceFuture;
  secPolicyDevelopment: ISecPolicyDevelopment;
  mortgageBanking: IMortgageBanking;
  bankAttachmentList: IAttachment[]; // 银行授信报告
  riskAttachmentList: IAttachment[]; // 风险调研报告
  businessAttachmentList: IAttachment[]; // 业务正明材料
}

interface IAddress {
  province: string;
  provinceName: string;
  city: string;
  cityName: string;
  town: string;
  townName: string;
  county: string;
  countyName: string;
  village: string;
  villageName: string;
  address: string;
}

export interface ISelectOptions {
  label: string;
  value: string;
}

export type RegionType = 'province' | 'city' | 'county' | 'town' | 'village';

export interface ICurrentFile {
  fileKey: string;
  fileName: string;
  fileSize: string;
  documentFormat: string;
  bucketName: string;
  storageType: string;
  documentId: string;
  documentGroupItemsId: string;
}

// 签报eoadata
export interface EoaData {
  documentGroupId: string;
  eoaTitle?: string; // 签报主题
  eoaContent?: string; // 签报内容
  fileInfos?: { fileKey: string; fileName: string; fileType: string }[]; // 签报附件
  approvalChain?: string; // 跳转eoa审批链
  templateChainKey?: string; // 跳转eoa审批链key
  approveChainList: EoaChainsType[]; // 审批链
}
interface EoaChainsType {
  flowOwnerNameList: string[];
  handType: string;
  flowOwnerTypeList: string[] | OptionType[];
  seq: number;
  flowOwnerOptions: OptionType[][];
  flowOwner: (OptionType | null)[];
}
// 审批人选项
interface OptionType {
  um: string;
  name: string;
  option?: OptionType;
}

// 审核信息
export interface QueryApprovalTaskList {
  operator: string;
  opTime: string;
  approveOpinion: string;
  approveStatus: string;
}

export interface ResType {
  code: string;
  msg: string;
  data: QueryApprovalTaskList[];
  httpAppUrl?: string;
  sysId?: string;
  timeStamp?: string;
  code?: string;
  virtualUsername?: string;
  userId?: string;
  deptCode?: string;
  approveRecords?: QueryApprovalTaskList[];
  approveChainDesc?: string;
}

export interface UploadFile {
  documentName: string;
  documentId: string;
  documentFormat: string;
  uploadPath: string;
  bucketName: string;
  storageType: string;
  documentSize: string;
  documentGroupItemsId: string;
  fileType?: string;
}

export interface IAttachment {
  attachUrl: string;
  docoldName: string;
  fileSize: string;
  documentFormat: string;
  bucketName: string;
  storageType: string;
  documentId: string;
  documentGroupItemsId: string;
}

export interface IUploadResParams {
  uploadPath: string;
  documentName: string;
  documentSize: string;
}

export interface resType {
  msg: string;
  code: string;
  data: UploadFile;
}
