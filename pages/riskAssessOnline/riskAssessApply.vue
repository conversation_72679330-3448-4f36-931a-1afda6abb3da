<template>
  <div ref="riskScrollWrapper" class="risk-assess-apply-container">
    <main ref="riskAssessApplyRef" class="flex p-[14px]">
      <div class="flex-1 border-right-line">
        <div class="h-[76px] w-ful bg-[url('@/assets/images/content-head.png')]">
          <span class="text-[24px] text-[#00190c] leading-[76px] ml-[16px]">风险评估申请</span>
        </div>
        <a-spin :spinning="spinLoading">
          <div class="bg-white">
            <a-form :colon="false">
              <div class="p-16px space-y-8px">
                <InfoGroup id="policy-info" title="风险评估信息">
                  <span>风险评估单号：{{ formState.baseInfo.riskAsmtNo }}</span>
                </InfoGroup>
                <InfoGroup id="basic-info" title="基础信息">
                  <BaseInfo ref="baseInfoRef" v-model:value="formState.baseInfo" :disabled="disabled" />
                </InfoGroup>
                <InfoGroup id="product-info" title="产品信息">
                  <ProductInfo ref="productInfoRef" v-model:value="formState.baseInfo" v-model:business-info="formState.businessInfo" v-model:is-multi-target="isMultiTarget" v-model:insurance-num-unit-list="insuranceNumUnitList" :disabled="disabled" :default-mul-risk="defaultMulRisk" />
                </InfoGroup>
                <InfoGroup id="business-info" title="业务信息">
                  <BusinessInfo ref="businessInfoRef" v-model:value="formState.businessInfo" :insurance-num-unit-list="insuranceNumUnitList" :is-multi-target="false" :disabled="disabled" :is-head-underwriting="formState.baseInfo.isHeadUnderwriting" />
                </InfoGroup>
                <InfoGroup id="business-model" title="业务模式">
                  <BusinessModel ref="businessModelRef" v-model:value="formState.businessModel" :disabled="disabled" :attachment-group-no="formState.baseInfo.attachmentGroupNo" :insured-name="formState?.businessInfo?.insuredName || ''" :county-code="formState?.baseInfo?.riskAddressInfoList?.[0]?.riskObjAddrCntyCode || ''" />
                </InfoGroup>
                <InfoGroup id="control-plan" title="管控方案">
                  <a-form :model="formState" :colon="false">
                    <a-form-item label="承保方案" :label-col="labelColStyle" class="col-span-2">
                      <a-textarea v-model:value="formState.baseInfo.aplyPrps" placeholder="请输入" :disabled="disabled" :maxlength="2000" show-count />
                    </a-form-item>
                    <a-form-item label="理赔方案" :label-col="labelColStyle" class="col-span-2">
                      <a-textarea v-model:value="formState.baseInfo.clmPrps" placeholder="请输入" :disabled="disabled" :maxlength="2000" show-count />
                    </a-form-item>
                  </a-form>
                </InfoGroup>
              </div>
            </a-form>
          </div>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" />
      </div>
    </main>
    <!-- 发起eoa审批 -->
    <CommonApprovalModal v-if="showApproval" v-model:open="showApproval" title="风险评估签报申请" eoa-type="T30" show-uploadbtn :department-code="formState.baseInfo.aplyDeptNo" :eoa-title="eoaTitle" :extern-params="externEoaParams" :total-actual-premium="formState.businessInfo.premium?.toString()" @ok="handleSubmit" />
    <!-- 点击查看审核信息 -->
    <InsureTrack v-if="processOpen" v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <template v-if="!detailFlag">
        <a-button :disabled="saveLoading" :loading="saveLoading" type="primary" @click="handleSaveRisk">暂存</a-button>
        <a-button :disabled="submitReviewLoading" :loading="submitReviewLoading" type="primary" @click="handleSubmitReview">发起审核</a-button>
      </template>
      <template v-else>
        <a-button type="primary" @click="handleBack">返回</a-button>
        <a-button type="primary" @click="queryUnderwriteAssistant">查看审核信息</a-button>
      </template>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { pxToRem, getImg, base64toFile } from '@/utils/tools';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { $post, $get, usePost, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { useUserStore } from '@/stores/useUserStore';
import BaseInfo from './components/BaseInfo.vue';
import ProductInfo from './components/ProductInfo.vue';
import BusinessInfo from './components/BusinessInfo.vue';
import BusinessModel from './components/BusinessModel.vue';
import InsureTrack from './components/InsureTrack.vue';
import { initContractModel, anchorList, initBusinessInfo, initInsuranceFuture, initSecPolicyDevelopment, initMortgageBanking } from './const';
import type { IFormState, EoaData, QueryApprovalTaskList, ResType, IAttachment, resType, UploadFile } from './riskAssessOnline';

const labelColStyle = {
  style: {
    width: pxToRem(100),
  },
};
const { gateWay, service } = useRuntimeConfig().public || {};
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const { deletPageTabListItem, pageTabList } = inject('pageTab') as {
  deletPageTabListItem: (id: string) => void;
  pageTabList: Ref<{ fullPath: string }[]>;
}; // 关闭页签

const riskScrollWrapper = ref();
const riskAssessApplyRef = ref();
const getContainer = () => riskScrollWrapper.value || window;
const baseInfoRef = ref();
const productInfoRef = ref();
const businessInfoRef = ref();
const businessModelRef = ref();
const processOpen = ref(false);
const route = useRoute();
const router = useRouter();
// 定位锚点
const anchorItems = ref(anchorList);

const spinLoading = ref(false);
const formState = ref<IFormState>(cloneDeep(initContractModel));
// 是否展示审批弹窗
const showApproval = ref<boolean>(false);
const isMultiTarget = ref<boolean>(false);
const insuranceNumUnitList = ref<Record<string, string>[]>([]);
const externEoaParams = ref<{ busiModeNo: string[] }>({ busiModeNo: [''] });
// 默认的多标的
const defaultMulRisk = ref<string[]>([]);

const disabled = computed(() => {
  return !!(getImgIng.value || detailFlag.value || formState.value?.baseInfo?.isHeadUnderwriting === 'Y');
});

// 是详情页进来标志
const detailFlag = computed(() => {
  return !!(route.name === 'riskAssessDetail' && route.query?.riskAsmtNo);
});

// eoa签报标题
const eoaTitle = computed(() => {
  return `关于${formState.value.baseInfo.aplyDeptNo}${formState.value.baseInfo.aplyDeptName || ''}${formState.value.baseInfo.prodNo || ''}${formState.value.baseInfo.prodName || ''}开办申请`;
});

const handleBack = () => {
  deletPageTabListItem('/riskAssessDetail');
  router.back();
};

const getValidateArr = () => {
  const validateItems = [baseInfoRef.value?.validate(), productInfoRef.value?.validate(), businessInfoRef.value?.validate(), businessModelRef.value?.validate()];
  return validateItems;
};

// 截图
const getImgIng = ref(false);
const handleGetImg = async () => {
  getImgIng.value = true;
  // 先获取图片 浏览器空闲时会回调的异步函数
  await requestIdleCallback(async () => {
    const result = await getImg(riskAssessApplyRef.value);
    if (result) {
      const file = base64toFile(result, '风险评估申请');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bizNo', '');
      formData.append('bizType', 'docTypeRisk');
      formData.append('documentGroupId', formState.value.baseInfo.attachmentGroupNo);
      formData.append('fileType', '');
      try {
        const fetchUrl = `${gateWay}${service.administrate}/attachment/document/upload`;
        if (formData) {
          const res = (await $postOnClient(fetchUrl, formData)) as resType;
          if (res && res.code === SUCCESS_CODE) {
            attachmentList.value.push({
              attachUrl: res?.data?.uploadPath,
              docoldName: res?.data?.documentName,
              fileSize: res?.data?.documentSize,
              documentFormat: res?.data?.documentFormat,
              bucketName: res?.data?.bucketName,
              storageType: res?.data?.storageType,
              documentId: res?.data?.documentId,
              documentGroupItemsId: res?.data?.documentGroupItemsId,
            });
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        getImgIng.value = false;
      }
    }
  });
};

// 将机构包含下级字段的boolean类型转成Y/N
// 非多标的去掉 riskAgrList 字段
const saveParams = computed(() => {
  const riskParams = {
    ...formState.value,
    baseInfo: {
      ...formState.value.baseInfo,
      inclLowLvlDeptFlag: formState.value.baseInfo.inclLowLvlDeptFlag ? 'Y' : 'N',
    },
  };
  if (!isMultiTarget.value) {
    delete riskParams.baseInfo.riskAgrList;
  }
  return riskParams;
});

const saveLoading = ref(false);
const handleSaveRisk = () => {
  saveLoading.value = true;
  spinLoading.value = true;
  const params = cloneDeep(saveParams.value);
  // 暂存接口
  $post<{ riskAsmtNo: string }>(`${gateWay}${service.accept}/web/risk/rpt/save`, params)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        formState.value.baseInfo.riskAsmtNo = res.data.riskAsmtNo;
        message.success(res?.msg || '');
      } else {
        message.error(res?.msg || '');
      }
    })
    .finally(() => {
      saveLoading.value = false;
      spinLoading.value = false;
    });
};

const submitReviewLoading = ref(false);
const handleSubmitReview = async () => {
  const results = await Promise.all(getValidateArr());
  const isValid = results.every((result) => result?.valid);
  if (isValid) {
    try {
      // 所有表单校验通过
      submitReviewLoading.value = true;
      spinLoading.value = true;
      const params = cloneDeep(saveParams.value);
      const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/submit`;
      const res = await $post<{ aplyDeptName: string; busiModeNo: string; riskAsmtNo: string; aplyDeptNo: string }>(fetchUrl, params);
      if (res && res.code === SUCCESS_CODE) {
        message.success(res?.msg || '');
        formState.value.baseInfo.riskAsmtNo = res.data.riskAsmtNo;
        formState.value.baseInfo.aplyDeptNo = res.data.aplyDeptNo;
        formState.value.baseInfo.aplyDeptName = res.data.aplyDeptName;
        externEoaParams.value.busiModeNo = [res.data.busiModeNo];
        submitReviewLoading.value = false;
        spinLoading.value = false;
        // 清空发起审核成功获取的附件
        attachmentList.value = [];
        getAttachmentList();
        // 先截图
        await handleGetImg();
        // 发起审核成功，发起eoa审批
        showApproval.value = true;
      } else {
        message.error(res?.msg || '');
      }
    } catch (error) {
      console.log(error);
    } finally {
      submitReviewLoading.value = false;
      spinLoading.value = false;
    }
  } else {
    message.error('校验失败');
  }
};

const attachmentList = ref<IAttachment[]>([]);
const getAttachmentList = () => {
  attachmentList.value.push(...formState.value.baseInfo.attachmentList);
  attachmentList.value.push(...formState.value.businessModel.bankAttachmentList);
  attachmentList.value.push(...formState.value.businessModel.riskAttachmentList);
  attachmentList.value.push(...formState.value.businessModel.businessAttachmentList);
};

const createEoa = await usePost(`${gateWay}${service.accept}/accept/eoa/createEoa`);
// 发起签报
const handleSubmit = async (eoaData: EoaData) => {
  try {
    const params = {
      relationBusinessNo: formState.value.baseInfo.riskAsmtNo,
      departmentCode: formState.value.baseInfo.aplyDeptNo,
      eoaType: '045',
      eoaSubject: eoaData.eoaTitle,
      documentGroupId: eoaData.documentGroupId,
      eoaBody: eoaData.eoaContent,
      approveChainList: eoaData.approveChainList,
      attachmentList: attachmentList.value,
    };
    const res = await createEoa.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      showApproval.value = false;
      deletPageTabListItem('/riskAssessApply');
      // 返回上一页或者首页
      if (pageTabList.value.length) {
        router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
      } else {
        router.push('/home');
      }
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};

const getAssessmentCode = () => {
  const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/generateDocumentGroupNo`;
  $get<string>(fetchUrl).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      formState.value.baseInfo.attachmentGroupNo = res.data || '';
    }
  });
};

const initRiskModel = (data: IFormState) => {
  const { baseInfo, businessInfo, businessModel } = data;
  formState.value = data;
  if (baseInfo) {
    formState.value.baseInfo.saleListCode = baseInfo?.saleList?.map((it) => it.salemanNo);
    formState.value.baseInfo.attachmentList = [];
    const { inclLowLvlDeptFlag } = baseInfo;
    formState.value.baseInfo.inclLowLvlDeptFlag = (inclLowLvlDeptFlag as stirng | boolean) === 'Y' ? true : false;
    const { riskAgrList } = baseInfo;
    if (riskAgrList && riskAgrList.length > 0) {
      defaultMulRisk.value = riskAgrList?.map((item) => item.agriculturalRiskObjectDetailCode) as string[];
    }
  }
  if (!businessInfo) {
    // 暂存不填业务信息，详情接口不返回businessInfo，要自行填充
    formState.value.businessInfo = cloneDeep(initBusinessInfo);
  }
  if (businessModel) {
    const { insuranceFuture, secPolicyDevelopment, mortgageBanking } = businessModel;
    if (!insuranceFuture) {
      businessModel.insuranceFuture = cloneDeep(initInsuranceFuture);
    }
    if (!secPolicyDevelopment) {
      businessModel.secPolicyDevelopment = cloneDeep(initSecPolicyDevelopment);
    }
    if (!mortgageBanking) {
      businessModel.mortgageBanking = cloneDeep(initMortgageBanking);
    }
    formState.value.businessModel.bankAttachmentList = [];
    formState.value.businessModel.riskAttachmentList = [];
    formState.value.businessModel.businessAttachmentList = [];
  }
};

const queryRiskDetail = async () => {
  const riskAsmtNo = route.query?.riskAsmtNo;
  if (riskAsmtNo) {
    spinLoading.value = true;
    const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/queryByRiskAsmtNo`;
    $get<IFormState>(fetchUrl, { riskAsmtNo, needMask: detailFlag.value })
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          initRiskModel(res.data);
          queryDocumentGroup(res?.data?.baseInfo?.attachmentGroupNo);
        } else {
          message.error(res?.msg || '获取风险评估信息失败，请稍后再试');
          formState.value.baseInfo.aplyDeptNo = defaultDeptCode.value; // 默认机构
          formState.value.businessModel.busiModeNo = '01'; // 默认业务模式：保险+期货
          formState.value.businessModel.insuranceFuture.spcnIlglVioFlag = 'N'; //默认是否有重大违规违法行为：否
        }
      })
      .finally(() => {
        spinLoading.value = false;
      });
  } else {
    formState.value = cloneDeep(initContractModel);
    formState.value.baseInfo.aplyDeptNo = defaultDeptCode.value; // 默认机构
    formState.value.businessModel.busiModeNo = '01'; // 默认业务模式：保险+期货
    formState.value.businessModel.insuranceFuture.spcnIlglVioFlag = 'N'; //默认是否有重大违规违法行为：否
  }
};

// 审核信息
const insureInfo = ref<QueryApprovalTaskList[]>([]);
const chainStr = ref<string>('');
const queryUnderwriteAssistant = async () => {
  const riskAsmtNo = route.query?.riskAsmtNo;
  if (riskAsmtNo && detailFlag.value) {
    try {
      const fetchUrl = `${gateWay}${service.accept}/web/risk/rpt/approveList`;
      const res = await $get<ResType>(fetchUrl, { riskAsmtNo });
      const { approveRecords = [], approveChainDesc } = res?.data || {};
      if (res?.code === SUCCESS_CODE) {
        insureInfo.value = approveRecords || [];
        chainStr.value = approveChainDesc || '';
        processOpen.value = true;
      } else {
        message.error(res?.msg || '');
      }
    } catch (error) {
      console.log(error);
    }
  }
};

const tranceFileParams = (data: UploadFile) => {
  return {
    attachUrl: data?.uploadPath,
    docoldName: data?.documentName,
    fileSize: data?.documentSize,
    documentFormat: data?.documentFormat,
    bucketName: data?.bucketName,
    storageType: data?.storageType,
    documentId: data?.documentId,
    documentGroupItemsId: data?.documentGroupItemsId,
  };
};

const queryDocumentGroup = (documentGroupId: string) => {
  if (!documentGroupId) return;
  const fetchUrl = `${gateWay}${service.administrate}/attachment/documentGroup/query`;
  const params = {
    documentGroupId,
    bizType: 'docViewTypeRisk',
  };
  $postOnClient<{ fileTypeList: { typeCode: string; fileDetailList: UploadFile[] }[] }>(fetchUrl, params).then((res) => {
    if (res?.code === SUCCESS_CODE) {
      res.data?.fileTypeList?.forEach((item: { typeCode: string; fileDetailList: UploadFile[] }) => {
        if (item.typeCode === 'R01') {
          formState.value.baseInfo.attachmentList = item.fileDetailList.map((it) => {
            return tranceFileParams(it);
          });
        } else if (item.typeCode === 'R02') {
          formState.value.businessModel.bankAttachmentList = item.fileDetailList.map((it) => {
            return tranceFileParams(it);
          });
        } else if (item.typeCode === 'R03') {
          formState.value.businessModel.riskAttachmentList = item.fileDetailList.map((it) => {
            return tranceFileParams(it);
          });
        } else if (item.typeCode === 'R04') {
          formState.value.businessModel.businessAttachmentList = item.fileDetailList.map((it) => {
            return tranceFileParams(it);
          });
        }
      });
    }
  });
};

onMounted(() => {
  if (!detailFlag.value) {
    getAssessmentCode();
  }
  queryRiskDetail();
});

// 切换tab刷新
onActivated(() => {
  externEoaParams.value.busiModeNo = [''];
  attachmentList.value = [];
  defaultMulRisk.value = [];
  chainStr.value = '';
  insureInfo.value = [];
  queryRiskDetail();
});
</script>

<style lang="less" scoped>
.risk-assess-apply-container {
  position: relative;
  overflow: auto;
  height: calc(100vh - 40px);
  .border-right-line {
    border-right: 1px solid #e6e8eb;
  }
  .right-sider {
    background: #fff;
    width: 100px;
    padding-left: 32px;
  }

  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
  :deep(.ant-input-number-group .ant-input-number-group-addon) {
    background-color: #fff;
    .ant-select-disabled {
      background-color: rgba(51, 51, 51, 0.02);
    }
  }
}
</style>
