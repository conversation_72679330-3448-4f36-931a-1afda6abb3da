import { useUserStore } from '@/stores/useUserStore';
import { SUCCESS_CODE } from '@/utils/constants';
// import { useGetSSR } from '@/composables/useRequest';
import { useGet } from '@/composables/request';
import type { MenuItem } from '@/apiTypes';
import { sensorsInit } from '@pag/sensors';

let firstRun = true;

const formatCookies = (cookiesStr: string) => {
  const cookiesArray = cookiesStr.split(',');
  const cookies = [];
  for (const item of cookiesArray) {
    const cookieItemArr = item.split(';');
    const obj: Record<string, string> = {};
    for (const key of cookieItemArr) {
      // 去掉前后空格
      const content = key.trim();
      const contentArr = content.split('=');
      if (contentArr.length > 0) {
        obj[contentArr[0]] = contentArr[1] || '';
      }
    }
    if (Object.keys(obj).length) {
      cookies.push(obj);
    }
  }
  return cookies;
};

const getCookieByName = (name: string, cookiesStr: string) => {
  const cookiesArr = formatCookies(cookiesStr);
  let cookieValue;
  for (const item of cookiesArr) {
    if (item[name]) {
      cookieValue = item;
      break;
    }
  }
  return cookieValue;
};

export default defineNuxtRouteMiddleware(async (to) => {
  // 暂时简化中间件以解决构建问题
  console.log('Route middleware:', to.path);
  return;
  if (isServer) {
    console.log(nuxtApp.ssrContext?.event, 'beforRouteEach', new Date().getTime());
  }
  // 查询用户信息
  const userInfoReq = await useGet('/api/auth/queryUmUserInfo', {
    onResponse({ response }) {
      // api server会在响应头设置iobs权限的cooKie,需要特殊处理下
      // 由于这里是服务端渲染，使用useCookie设置html的请求头
      // ssr关闭时，这段逻辑为无用代码，当ssr开启时，这段逻辑为有效代码。设置html请求头携带cookie
      const cookies = response.headers.get('set-cookie'); // 'cookie_name=123; expires=/; path=/, cookie_name=123; expires=/; path=/'
      if (cookies) {
        const cookieValue = getCookieByName('paobs', cookies);
        paobs.value = (cookieValue && cookieValue['paobs']) || '';
      }
    },
  });
  const menuReq = await useGet<MenuItem[], { userName: string }>('/api/auth/menu');
  if (!hasInit) {
    try {
      const res = await userInfoReq.fetchData();
      if (res?.code === SUCCESS_CODE) {
        setUserInfo(res.data);
        if (isServer) {
          console.log(nuxtApp.ssrContext?.event, 'beforRouteEach--获取用户信息成功', res?.data);
          console.log(nuxtApp.ssrContext?.event, 'beforRouteEach--获取菜单信息', res?.data?.umCode || '');
        }
        const listRes = await menuReq.fetchData({
          userName: res?.data?.umCode || '',
        });
        setMenuList(listRes?.data || []);
        setAuthPathList(listRes?.data || []);
        isLogin.value = true;
      } else {
        isLogin.value = false;
      }
      changeInit();
    } catch (err) {
      console.log(err);
      // 设置用户信息失败，直接设置默认路由权限，避免无限循环跳转noauth
      setAuthPathList([]);
      isLogin.value = false;
      changeInit();
    }
  }
  if (!authPathList.includes(to.path) && isLogin.value && workEnv !== 'development') {
    if (isServer) {
      // 打印日志
      console.log('跳转路由无权限');
      console.log('权限list', authPathList);
      console.log('目标路由', to.path);
    }
    // 再不登录的情况下，该navigateTo会覆盖掉userInfoReq的navigateTo，导致登录后redirect到无权限页面，需要加登录情况下才进行权限校验
    return navigateTo('/noAuth');
  }
  // 初始化埋点
  if (!isServer && firstRun && workEnv !== 'development') {
    firstRun = false;
    sensorsInit({
      sdk_url: trackerUrl,
      server_url: trackerSeverUrl,
      project_id: trackerProjectId,
      site_id: trackerSiteId,
    }).then((sensors) => {
      // 登录
      sensors.login(userInfo.umCode || '');
      sensors.registerPage({
        userid: userInfo.umCode || '',
      });
    });

    // const sensors = window['sensorsDataAnalytic201505'];
    // if (sensors) {
    //   sensors.init({
    //     server_url: `${trackerSeverUrl}?project=${trackerProjectId}`,
    //     // 开启采集 device_id
    //     is_track_device_id: true,
    //     // 开启 App 打通 H5
    //     use_app_track: false,
    //     // 全埋点url
    //     heatmap_url: '',
    //     // 全埋点设置
    //     heatmap: {
    //       clickmap: 'default',
    //       scroll_notice_map: 'default',
    //       collect_tags: {
    //         div: true,
    //       },
    //     },
    //     show_log: true,
    //   });
    //   // 登录
    //   sensors.login(userInfo.umCode || '');
    //   // 初始化后设置公共属性（重要！无此设置数据将无法入库）【根据配置邮件中的值填入】
    //   sensors.registerPage({
    //     sourceprojectid: trackerProjectId,
    //     sourcesiteid: trackerSiteId,
    //     siteid: trackerSiteId,
    //     userid: userInfo.umCode || '',
    //   });
    //   // 开启页面浏览事件pageview
    //   sensors.quick('autoTrack');
    //   // 开启页面停留时长
    //   sensors.use('PageLeave');
    // }
  }
});
