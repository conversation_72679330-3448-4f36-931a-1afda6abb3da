import { getRequestURL, type H3Event } from 'h3';
import { v4 as uuidv4 } from 'uuid';
import { message } from '@/components/ui/Message';

export interface RequestConfig {
  key?: string;
  method?: string;
  headers?: { [key: string]: unknown };
  baseURL?: string;
  server?: boolean;
  lazy?: boolean;
  immediate?: boolean;
  deep?: boolean;
  watch?: boolean;
  query?: unknown;
}

export interface RequestOptions<D> extends RequestConfig {
  query?: D;
  params?: D;
  body?: D;
}

const isAPI = (url: string) => {
  return /\/api/.test(url);
};

const getRedirectUrl = (event: H3Event) => {
  const runtimeConfig = useRuntimeConfig() || {};
  const { isServer } = runtimeConfig;
  const { workEnv, loginRedirect } = useRuntimeConfig().public || {};
  if (isServer) {
    if (event) {
      const urlObj = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
      let protocal = urlObj.protocol;
      const host = urlObj.host;
      if (workEnv !== 'development') {
        // 测试环境和生产环境统一换成https
        protocal = protocal.replace(/^http(s)*/, 'https');
      }

      // return encodeURIComponent(url || '')
      // nodejs服务到浏览器之间有多个链路，不知是哪个链路会自动帮忙encode一次链接参数，导致浏览器拿到的参数会被encode2次，紧急修复一下。
      // 也有可能是navigateTo方法自动encode
      // return `${protocal}//${host}${loginRedirect}?X-Portal-Token=${xPortalToken}`; // 后端塞灰度标识入口
      return `${protocal}//${host}${loginRedirect}`; // 塞灰度标识已经在登录页实现了
    } else {
      return '';
    }
  } else {
    // return encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}?X-Portal-Token=${xPortalToken}`); // 后端塞灰度标识入口
    return encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}`); // 塞灰度标识已经在登录页实现了
  }
};

// 发起get请求的hooks，支持服务端和客户端调用，同一请求服务端调用后会缓存数据，客户端则不会触发同样请求
// T表示返回数据类型，D表示请求参数类型
// 服务端没有发起请求时接口默认返回null
export async function useGet<T = unknown, D = unknown>(url: string, config?: RequestOptions<D>) {
  const query = ref<D>();
  const runtimeConfig = useRuntimeConfig() || {};
  const { xPortalToken, loginUrl } = runtimeConfig.public || {};
  const { isServer } = runtimeConfig;
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  const uid = uuidv4();
  const options: RequestOptions<D> = {
    immediate: false,
    method: 'get',
    deep: false,
    query: query as D,
    watch: false,
    key: uid, // usefetch默认使用url和option生成的key，非常容易造成相同的key,相同的key的请求第一次返回数据，第二次返回是null,
  };

  if (!isAPI(url)) {
    options.headers = {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    };
  }
  const { data, pending, refresh, error } = await useFetch(url, {
    ...options,
    ...config,
  });

  async function fetchData(params?: D): Promise<ApiResult<T> | null> {
    try {
      query.value = params ? (isReactive(params) ? toRaw(params) : unref(params)) : params;
      await refresh();
      if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '401' || String(error.value.data.statusCode) === '401')) {
        // 登录超时
        if (isServer) {
          await navigateTo(LOGIN_URL, {
            external: true,
          });
        } else {
          window.location.href = LOGIN_URL;
        }
        // throw Error('登录超时')
        return { code: '401', msg: '登录超时' } as ApiResult<T>;
      } else if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '403' || String(error.value.data.statusCode) === '403')) {
        // message.error(`接口无权限`);
        navigateTo({ path: '/noAuth' });
        throw new Error(error.value.data.message);
      } else if (error.value && error.value.data && error.value.data && String(error.value.data.statusCode) !== '200') {
        message.error(error.value.data.message);
        throw new Error(error.value.data.message);
      } else {
        // 访问异常，需退出重新登录
        if (['988700', 'S0010'].includes(data?.value?.code)) {
          if (isServer) {
            await navigateTo(LOGIN_URL, {
              external: true,
            });
          } else {
            window.location.href = LOGIN_URL;
          }
        }
        return toRaw(data.value) as ApiResult<T> | null;
      }
    } catch (err) {
      console.log(err);
      throw err;
    }
  }

  return {
    pending,
    fetchData,
  };
}

// 发起post请求的hooks，支持服务端和客户端调用，同一请求服务端调用后会缓存数据，客户端则不会触发同样请求
// T表示返回数据类型，D表示请求参数类型
// 服务端没有发起请求时接口默认返回null
export async function usePost<T = unknown, D = unknown>(url: string, config?: RequestOptions<D>) {
  const body = ref<D>();
  const runtimeConfig = useRuntimeConfig() || {};
  const { xPortalToken, loginUrl } = runtimeConfig.public || {};
  const { isServer } = runtimeConfig;
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  const uid = uuidv4();
  const options: RequestOptions<D> = {
    immediate: false,
    method: 'post',
    deep: false,
    body: body as D,
    watch: false,
    key: uid,
  };

  if (!isAPI(url)) {
    options.headers = {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    };
  }

  const {
    data: resData,
    pending,
    refresh,
    error,
  } = await useFetch(() => url, {
    ...options,
    ...config,
  });

  async function fetchData(data?: D): Promise<ApiResult<T> | null> {
    try {
      body.value = data ? (isReactive(data) ? toRaw(data) : unref(data)) : data;
      await refresh();
      if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '401' || String(error.value.data.statusCode) === '401')) {
        // 登录超时
        if (isServer) {
          await navigateTo(LOGIN_URL, {
            external: true,
          });
        } else {
          window.location.href = LOGIN_URL;
        }
        return { code: '401', msg: '登录超时' } as ApiResult<T>;
      } else if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '403' || String(error.value.data.statusCode) === '403')) {
        // message.error(`接口无权限，${error.value.data.message || ''}`);
        navigateTo({ path: '/noAuth' });
        throw new Error(error.value.data.message);
      } else if (error.value && error.value.data && error.value.data && error.value.data.statusCode !== '200') {
        message.error(error.value.data.message);
        throw new Error(error.value.data.message);
      } else {
        // 访问异常，需退出重新登录
        if (['988700', 'S0010'].includes(resData?.value?.code)) {
          if (isServer) {
            await navigateTo(LOGIN_URL, {
              external: true,
            });
          } else {
            window.location.href = LOGIN_URL;
          }
        }
        return toRaw(resData.value) as ApiResult<T> | null;
      }
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
  return {
    pending,
    fetchData,
  };
}

// 发起get请求的方法，支持服务端和客户端调用，同一请求服务端调用后会缓存数据，客户端则不会触发同样请求
// 在vue的一些生命周期（例如：onmounted）并不合适使用，推荐使用useGet
// T表示返回数据类型，D表示请求参数类型
// 服务端没有发起请求时接口默认返回null
export async function $get<T = unknown, D = unknown>(url: string, params?: D, config?: RequestOptions<D>): Promise<ApiResult<T> | null> {
  const query = params ? (isReactive(params) ? toRaw(params) : unref(params)) : params;
  const runtimeConfig = useRuntimeConfig() || {};
  const { xPortalToken, loginUrl } = runtimeConfig.public || {};
  const { isServer } = runtimeConfig;
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  const options: RequestOptions<D> = {
    method: 'get',
    query,
    deep: false,
  };

  if (!isAPI(url)) {
    options.headers = {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    };
  }

  try {
    const { data: res, error } = await useFetch(url, {
      ...options,
      ...config,
    });
    if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '401' || String(error.value.data.statusCode) === '401')) {
      // 登录超时
      if (isServer) {
        await navigateTo(LOGIN_URL, {
          external: true,
        });
      } else {
        window.location.href = LOGIN_URL;
      }
      // throw Error('登录超时')
      return { code: '401', msg: '登录超时' } as ApiResult<T>;
    } else if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '403' || String(error.value.data.statusCode) === '403')) {
      // message.error(`接口无权限，${error.value.data.message || ''}`);
      navigateTo({ path: '/noAuth' });
      throw new Error(error.value.data.message);
    } else if (error.value && error.value.data && error.value.data && error.value.data.statusCode !== '200') {
      message.error(error.value.data.message);
      throw new Error(error.value.data.message);
    } else {
      // 访问异常，需退出重新登录
      if (['988700', 'S0010'].includes(res?.value?.code)) {
        if (isServer) {
          await navigateTo(LOGIN_URL, {
            external: true,
          });
        } else {
          window.location.href = LOGIN_URL;
        }
      }
      return toRaw(res.value) as ApiResult<T> | null;
    }
  } catch (err) {
    console.log(err);
    throw err;
  }
}

// 发起post请求的方法，支持服务端和客户端调用，同一请求服务端调用后会缓存数据，客户端则不会触发同样请求
// 在vue的一些生命周期（例如：onmounted）并不合适使用，推荐使用usePost
// T表示返回数据类型，D表示请求参数类型
// 服务端没有发起请求时接口默认返回null
export async function $post<T = unknown, D = unknown>(url: string, data?: D, config?: RequestOptions<D>): Promise<ApiResult<T> | null> {
  const body = data ? (isReactive(data) ? toRaw(data) : unref(data)) : data;
  const runtimeConfig = useRuntimeConfig() || {};
  const { xPortalToken, loginUrl } = runtimeConfig.public || {};
  const { isServer } = runtimeConfig;
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  const options: RequestOptions<D> = {
    method: 'post',
    body,
    deep: false,
  };

  if (!isAPI(url)) {
    options.headers = {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    };
  }

  try {
    const { data: res, error } = await useFetch(url, {
      ...options,
      ...config,
    });
    if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '401' || String(error.value.data.statusCode) === '401')) {
      // 登录超时
      if (isServer) {
        await navigateTo(LOGIN_URL, {
          external: true,
        });
      } else {
        window.location.href = LOGIN_URL;
      }
      // throw Error('登录超时')
      return { code: '401', msg: '登录超时' } as ApiResult<T>;
    } else if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '403' || String(error.value.data.statusCode) === '403')) {
      // message.error(`接口无权限，${error.value.data.message || ''}`);
      navigateTo({ path: '/noAuth' });
      throw new Error(error.value.data.message);
    } else if (error.value && error.value.data && error.value.data && error.value.data.statusCode !== '200') {
      message.error(error.value.data.message);
      throw new Error(error.value.data.message);
    } else {
      // 访问异常，需退出重新登录
      if (['988700', 'S0010'].includes(res?.value?.code)) {
        if (isServer) {
          await navigateTo(LOGIN_URL, {
            external: true,
          });
        } else {
          window.location.href = LOGIN_URL;
        }
      }
      return toRaw(res.value) as ApiResult<T> | null;
    }
  } catch (err) {
    console.log(err);
    throw err;
  }
}

// 发起get请求的方法，仅支持客户端调用，使用前请确认改方法不会在服务端被调用，例如点击事件发起的请求
// T表示返回数据类型，D表示请求参数类型
export async function $getOnClient<T = unknown, D = unknown>(url: string, params?: D, config?: RequestOptions<D>): Promise<ApiResult<T> | null> {
  const query = params ? (isReactive(params) ? toRaw(params) : unref(params)) : params;
  const { xPortalToken, loginUrl } = useRuntimeConfig().public || {};
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  let responseStatus = '';
  const options = {
    method: 'get',
    query,
    deep: false,
  };

  if (!isAPI(url)) {
    options.headers = {
      'X-Portal-Token': xPortalToken,
      // 'Content-Type': 'application/json'
    };
  }
  if (config && config.headers && (config.headers['Content-Type'] === '' || config.headers['Content-Type'] === null)) {
    // 说明接口要用默认的Content-Type
    delete options.headers['Content-Type'];
    delete config.headers['Content-Type'];
  }
  try {
    const res: ApiResult<T> | null = await $fetch(url, {
      ...options,
      ...config,
      onResponseError({ response }) {
        responseStatus = response.status;
        if (config.onResponseError) {
          config.onResponseError();
        }
      },
    });
    return res;
  } catch (err) {
    if (String(responseStatus) === '401') {
      window.location.href = LOGIN_URL;
    } else if (String(responseStatus) === '403') {
      // message.error('接口无权限');
      navigateTo({ path: '/noAuth' });
    }
    throw err;
  }
}

// 发起get请求的方法，仅支持客户端调用，使用前请确认改方法不会在服务端被调用，例如点击事件发起的请求
// T表示返回数据类型，D表示请求参数类型
export async function $postOnClient<T = unknown, D = unknown>(url: string, data?: D, config?: RequestOptions<D>): Promise<ApiResult<T> | null> {
  const body = data ? (isReactive(data) ? toRaw(data) : unref(data)) : data;
  let responseStatus = '';
  const { xPortalToken, loginUrl } = useRuntimeConfig().public || {};
  const nuxtApp = useNuxtApp();
  const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  try {
    const options: RequestOptions<D> = {
      method: 'post',
      body: body,
      deep: false,
    };
    // 非/api开头的，走proxy逻辑
    if (!isAPI(url)) {
      options.headers = {
        'X-Portal-Token': xPortalToken,
        // 'Content-Type': 'application/json'
      };
    }
    if (config && config.headers && (config.headers['Content-Type'] === '' || config.headers['Content-Type'] === null)) {
      // 说明接口要用默认的Content-Type
      delete options.headers['Content-Type'];
      delete config.headers['Content-Type'];
    }
    const res: ApiResult<T> | null = await $fetch(url, {
      ...options,
      ...config,
      onResponseError({ response }) {
        responseStatus = response.status;
        if (config.onResponseError) {
          config.onResponseError();
        }
      },
    });
    return res;
  } catch (err) {
    if (String(responseStatus) === '401') {
      window.location.href = LOGIN_URL;
    } else if (String(responseStatus) === '403') {
      // message.error('接口无权限');
      navigateTo({ path: '/noAuth' });
    }
    throw err;
  }
}

export interface ApiResult<T> {
  code: string;
  data: T;
  msg: string;
}
