import { message } from '@/components/ui/Message';
import { compressImage } from '@/utils/compressImage';
import { type ApiResult, $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { uploadToIobs } from '@/utils/iobsUpload';

export type FileUploadParams = { [name: string]: unknown };
export interface useUploadFilesParams {
  max?: number;
  validate?: (file: File, fileOption: FileUploadParams) => boolean;
  uploadUrl?: string;
}

export interface UploadFile {
  documentName: string;
  documentId: string;
  thumbnail: string; // 缩略图，上传图片会返回
  documentFormat: string;
  uploadPath: string;
  selected: boolean;
  bucketName: string;
  storageType: string;
  documentLimitFlag?: boolean; // 是否禁用
  documentGroupId?: string;
}

export interface BatchUploadConfig {
  batchSize: number; // 每批上传数量
  currentBatch: number; // 当前批次
  totalFiles: number; // 总文件数
  successCount: number; // 成功数量
  failCount: number; // 失败数量
}

export function useUploadFiles({ max = 200, validate, uploadUrl }: useUploadFilesParams) {
  const { gateWay, service } = useRuntimeConfig().public || {};
  const canUpload = ref(true);
  const isUploading = ref(false);
  const uploadedFile = ref<UploadFile>();
  const isUseIobsUpload = ref('2');

  // 添加文件收集队列
  const uploadQueue = ref<{
    files: Array<File & { uid: string }>;
    fileUploadParams: FileUploadParams;
  }>({
    files: [],
    fileUploadParams: {},
  });

  // 添加批量上传状态
  const batchUploadStatus = ref<BatchUploadConfig>({
    batchSize: 10, // 默认每批10个
    currentBatch: 0,
    totalFiles: 0,
    successCount: 0,
    failCount: 0,
  });

  const showFileUploadLoading = ref(false);
  const showFileUploadLoadingTips = ref('');

  // 单文件上传处理
  const uploadSingleFile = async (file: File & { uid: string }): Promise<boolean> => {
    try {
      // 文件验证
      if (validate && !validate(file, uploadQueue.value.fileUploadParams)) {
        return false;
      }

      let uploadFile = file;

      // 图片压缩处理
      if (file.type.startsWith('image/')) {
        try {
          // const compressConfig = {
          //   quality: [
          //     { size: 1, ratio: 1 }, // 1MB以下不压缩
          //     { size: 2, ratio: 1 }, // 1-2MB压缩30%
          //     { size: Infinity, ratio: 0.5 }, // 2MB以上压缩50%
          //   ],
          //   // maxWidth: 1920,
          //   // maxHeight: 1080,
          //   mimeType: file.type,
          // };

          uploadFile = await compressImage(file);
          console.log('压缩前大小=', file.size / 1024 / 1024, 'MB', '压缩后大小=', uploadFile.size / 1024 / 1024, 'MB');
        } catch (error) {
          console.error('图片压缩失败:', error);
          uploadFile = file;
        }
      }
      let resCode;
      let resData: UploadFile;
      let resMsg;
      // 有2种上传方式
      if (isUseIobsUpload.value === '1') {
        // 文件先上传到iobs，然后再上传到后端
        const fetchUrl = uploadUrl || `${gateWay}${service.administrate}/attachment/document/upload/save`;
        // 先上传到iobs
        const { bucket, key, hash, fileName, fileSize } = await uploadToIobs(uploadFile);
        const formData = { fileKey: key, bucket, hash, fileSize, fileName };
        Object.assign(formData, uploadQueue.value.fileUploadParams || {});
        const { data, code, msg } = await $post<ApiResult<UploadFile>>(fetchUrl, formData);
        resCode = code;
        resData = data;
        resMsg = msg;
      } else {
        // 文件直接上传到后端
        const fetchUrl = uploadUrl || `${gateWay}${service.administrate}/attachment/document/upload`;
        const formData = new FormData();
        formData.append('file', uploadFile);
        const paramsKeys = Object.keys(uploadQueue.value.fileUploadParams || {});
        for (const item of paramsKeys) {
          formData.append(item, uploadQueue.value.fileUploadParams[item]);
        }
        const { data, code, msg } = await $post<ApiResult<UploadFile>>(fetchUrl, formData, {
          headers: {
            'Content-Type': null,
          },
        });
        resCode = code;
        resData = data;
        resMsg = msg;
      }
      if (resCode === SUCCESS_CODE) {
        uploadedFile.value = resData;
        // targetList.unshift({ ...data, selected: false }); // 存在分页，统一在数组前塞数据
        return true;
      } else {
        console.error('上传失败:', resMsg);
        return false;
      }
    } catch (error) {
      console.error('上传出错:', error);
      return false;
    }
  };

  // 新增批量上传方法
  const startUpload = async () => {
    canUpload.value = true;
    if (uploadQueue.value.files.length === 0 || isUploading.value) {
      return;
    }

    isUploading.value = true;

    // 重置上传状态
    batchUploadStatus.value = {
      batchSize: 10,
      currentBatch: 0,
      totalFiles: uploadQueue.value.files.length,
      successCount: 0,
      failCount: 0,
    };

    // 创建批次数组
    const batches: Array<Array<File & { uid: string }>> = [];
    for (let i = 0; i < uploadQueue.value.files.length; i += batchUploadStatus.value.batchSize) {
      batches.push(uploadQueue.value.files.slice(i, i + batchUploadStatus.value.batchSize));
    }

    showFileUploadLoading.value = true;
    showFileUploadLoadingTips.value = batches.length > 1 ? '正在上传...(0%)' : '正在上传...';

    if (isUseIobsUpload.value === '2') {
      // 需要调后端开关
      const res = await $post(`${gateWay}${service.administrate}/attachment/document/upload/switch`);
      if (res.code === SUCCESS_CODE) {
        isUseIobsUpload.value = res.data as string; // '1'=iobs，‘0’ ！=iobs
      } else {
        isUseIobsUpload.value = '0';
      }
    }

    try {
      // 按批次上传
      for (let i = 0; i < batches.length; i++) {
        batchUploadStatus.value.currentBatch = i + 1;
        const batch = batches[i];

        // 并行上传当前批次的文件
        const uploadPromises = batch.map((file) => uploadSingleFile(file));
        const results = await Promise.allSettled(uploadPromises);
        // 统计成功和失败数量
        results.forEach((result) => {
          if (result.status === 'fulfilled' && result.value) {
            batchUploadStatus.value.successCount++;
          } else {
            batchUploadStatus.value.failCount++;
          }
        });

        // 更新进度提示
        const progress = Math.round(((batchUploadStatus.value.successCount + batchUploadStatus.value.failCount) / batchUploadStatus.value.totalFiles) * 100);

        showFileUploadLoading.value = true;
        showFileUploadLoadingTips.value = `正在上传...(${progress}%),成功:${batchUploadStatus.value.successCount},失败:${batchUploadStatus.value.failCount}`;
      }
    } finally {
      // 关闭进度提示
      // uploadModal.destroy();
      showFileUploadLoading.value = false;

      // 显示最终结果
      if (batchUploadStatus.value.failCount > 0) {
        message.warning(`上传完成，成功${batchUploadStatus.value.successCount}个，失败${batchUploadStatus.value.failCount}个`);
      } else {
        message.success(`上传完成，共上传${batchUploadStatus.value.successCount}个文件`);
      }

      // 清空上传队列
      uploadQueue.value = {
        files: [],
        fileUploadParams: {},
      };

      // 重置上传状态
      isUploading.value = false;
    }
  };

  const upload = (file: File & { uid: string }, fileList: Array<File & { uid: string }>, fileUploadParams: FileUploadParams) => {
    if (!canUpload.value) return false;
    if (fileList.length > max) {
      message.error(`一次上传最多不能超过${max}张，请重新选择要上传的附件！`);
      canUpload.value = false;
      return false;
    }

    // 将新文件添加到上传队列
    uploadQueue.value = {
      files: [...uploadQueue.value.files, file],
      fileUploadParams,
    };

    if (uploadQueue.value.files.length === fileList.length) {
      // 开始上传
      startUpload();
    }
    return false;
  };

  return {
    canUpload,
    uploadQueue,
    upload,
    showFileUploadLoading,
    showFileUploadLoadingTips,
    uploadedFile,
  };
}
