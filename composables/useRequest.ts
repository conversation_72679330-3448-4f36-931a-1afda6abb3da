import { message } from '@/components/ui/Message';
import type { RequestOptions, ApiResult } from '@/utils/request';
import { setXPortalToken, resetContentType, handleAuthError, runtimeConfig } from '@/utils/request';

const useReqSSR = async <T = unknown, D = unknown>(url: string, config?: RequestOptions<D>): Promise<{ fetch: (data?: D) => Promise<ApiResult<T>> }> => {
  const body = ref<D>();
  const options: RequestOptions<D> = {
    immediate: false,
    method: config?.method === 'post' ? 'post' : 'get',
    deep: false,
    watch: false,
    headers: {
      'Content-Type': 'application/json',
    },
    // key: uid,
  };

  Object.assign(options, config);
  setXPortalToken(options, url);
  resetContentType(options);

  const { data: resData, refresh, error } = await useFetch(url, options);

  async function fetchData(data?: D): Promise<ApiResult<T>> {
    try {
      body.value = data ? (isReactive(data) ? toRaw(data) : unref(data)) : data;
      if (options.method === 'get') {
        options.query = body.value;
      } else {
        options.body = body.value;
      }
      await refresh();
      if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '401' || String(error.value.data.statusCode) === '401')) {
        // 登录超时
        await handleAuthError();
        return { code: '401', msg: '登录超时' } as ApiResult<T>;
      } else if (error.value && error.value.data && error.value.data && (String(error.value?.statusCode) === '403' || String(error.value.data.statusCode) === '403')) {
        navigateTo({ path: '/noAuth' });
        throw new Error(error.value.data.message);
      } else if (error.value && error.value.data && error.value.data && error.value.data.statusCode !== '200') {
        const { isServer } = runtimeConfig();
        if (!isServer) {
          message.error(error.value.data.message);
        }
        throw new Error(error.value.data.message);
      } else {
        // 访问异常，需退出重新登录
        if (['988700', 'S0010'].includes(resData?.value?.code)) {
          await handleAuthError();
          return { code: '401', msg: '登录超时' } as ApiResult<T>;
        }
        return toRaw(resData.value) as ApiResult<T>;
      }
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
  return {
    fetch: fetchData,
  };
};

export async function useGetSSR<T = unknown, D = unknown>(url: string, config?: RequestOptions<D>): Promise<{ fetch: (data?: D) => Promise<ApiResult<T>> }> {
  return useReqSSR(url, { ...config, ...{ method: 'get' } });
}

export async function usePostSSR<T = unknown, D = unknown>(url: string, config?: RequestOptions<D>): Promise<{ fetch: (data?: D) => Promise<ApiResult<T>> }> {
  return useReqSSR(url, { ...config, ...{ method: 'post' } });
}
