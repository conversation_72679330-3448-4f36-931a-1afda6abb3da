#!/usr/bin/env node

/**
 * 性能测试脚本
 * 用于测试和比较构建性能
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试开发服务器启动时间
function testDevStartup() {
  log('🚀 测试开发服务器启动时间...', 'yellow');
  
  const startTime = Date.now();
  
  try {
    // 清理缓存以获得准确的启动时间
    execSync('rm -rf .nuxt node_modules/.vite', { stdio: 'pipe' });
    
    // 启动开发服务器并在准备就绪后立即停止
    const child = execSync('timeout 60s pnpm dev 2>&1 | grep -m 1 "ready in"', { 
      encoding: 'utf8',
      timeout: 60000
    });
    
    const endTime = Date.now();
    const startupTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log(`✅ 开发服务器启动时间: ${startupTime}s`, 'green');
    
    // 解析 Vite 报告的启动时间
    const viteTimeMatch = child.match(/ready in (\d+)ms/);
    if (viteTimeMatch) {
      const viteTime = parseInt(viteTimeMatch[1]);
      log(`📊 Vite 报告的启动时间: ${viteTime}ms`, 'blue');
    }
    
    return parseFloat(startupTime);
    
  } catch (error) {
    log('❌ 开发服务器启动测试失败', 'red');
    console.error(error.message);
    return null;
  }
}

// 测试构建时间
function testBuildTime() {
  log('📦 测试构建时间...', 'yellow');
  
  const startTime = Date.now();
  
  try {
    // 清理构建缓存
    execSync('rm -rf .nuxt .output dist', { stdio: 'pipe' });
    
    // 执行构建
    execSync('pnpm prd', { stdio: 'pipe' });
    
    const endTime = Date.now();
    const buildTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log(`✅ 构建时间: ${buildTime}s`, 'green');
    
    return parseFloat(buildTime);
    
  } catch (error) {
    log('❌ 构建时间测试失败', 'red');
    console.error(error.message);
    return null;
  }
}

// 分析构建产物
function analyzeBuildOutput() {
  log('📊 分析构建产物...', 'yellow');
  
  try {
    const distPath = '.nuxt/dist/client/_nuxt';
    
    if (!fs.existsSync(distPath)) {
      log('❌ 构建产物不存在', 'red');
      return null;
    }
    
    const files = fs.readdirSync(distPath);
    const jsFiles = files.filter(f => f.endsWith('.js'));
    const cssFiles = files.filter(f => f.endsWith('.css'));
    
    // 计算文件大小
    const fileSizes = files.map(file => {
      const filePath = path.join(distPath, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        type: file.split('.').pop()
      };
    }).sort((a, b) => b.size - a.size);
    
    const totalSize = fileSizes.reduce((sum, file) => sum + file.size, 0);
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
    
    log('📈 构建产物统计:', 'cyan');
    log(`   JavaScript 文件: ${jsFiles.length}`, 'blue');
    log(`   CSS 文件: ${cssFiles.length}`, 'blue');
    log(`   总文件数: ${files.length}`, 'blue');
    log(`   总大小: ${totalSizeMB} MB`, 'blue');
    
    // 显示最大的文件
    const largeFiles = fileSizes.filter(file => file.size > 500 * 1024).slice(0, 5);
    if (largeFiles.length > 0) {
      log('\n🔍 大文件 (>500KB):', 'yellow');
      largeFiles.forEach(file => {
        const sizeMB = (file.size / 1024 / 1024).toFixed(2);
        log(`   ${file.name}: ${sizeMB} MB`, 'yellow');
      });
    }
    
    // 分析代码分割效果
    const chunkFiles = jsFiles.filter(f => !f.includes('entry'));
    log(`\n📦 代码分割统计:`, 'cyan');
    log(`   入口文件: ${jsFiles.length - chunkFiles.length}`, 'blue');
    log(`   分割块: ${chunkFiles.length}`, 'blue');
    
    return {
      totalFiles: files.length,
      totalSize: totalSize,
      jsFiles: jsFiles.length,
      cssFiles: cssFiles.length,
      largeFiles: largeFiles.length
    };
    
  } catch (error) {
    log('❌ 构建产物分析失败', 'red');
    console.error(error.message);
    return null;
  }
}

// 性能基准测试
function runBenchmark() {
  log('🏁 运行性能基准测试...', 'yellow');
  
  const results = {
    devStartup: null,
    buildTime: null,
    buildOutput: null,
    timestamp: new Date().toISOString()
  };
  
  // 测试开发启动时间
  // results.devStartup = testDevStartup();
  
  // 测试构建时间
  results.buildTime = testBuildTime();
  
  // 分析构建产物
  results.buildOutput = analyzeBuildOutput();
  
  // 保存结果
  const resultsPath = 'performance-results.json';
  let history = [];
  
  if (fs.existsSync(resultsPath)) {
    try {
      history = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
    } catch (error) {
      // 忽略解析错误
    }
  }
  
  history.push(results);
  
  // 只保留最近10次结果
  if (history.length > 10) {
    history = history.slice(-10);
  }
  
  fs.writeFileSync(resultsPath, JSON.stringify(history, null, 2));
  
  log(`📝 性能测试结果已保存到 ${resultsPath}`, 'green');
  
  return results;
}

// 比较性能结果
function compareResults() {
  log('📊 比较历史性能结果...', 'yellow');
  
  const resultsPath = 'performance-results.json';
  
  if (!fs.existsSync(resultsPath)) {
    log('❌ 没有找到历史性能数据', 'red');
    return;
  }
  
  try {
    const history = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
    
    if (history.length < 2) {
      log('📝 历史数据不足，无法比较', 'yellow');
      return;
    }
    
    const latest = history[history.length - 1];
    const previous = history[history.length - 2];
    
    log('📈 性能对比结果:', 'cyan');
    
    if (latest.buildTime && previous.buildTime) {
      const timeDiff = latest.buildTime - previous.buildTime;
      const timePercent = ((timeDiff / previous.buildTime) * 100).toFixed(1);
      
      if (timeDiff < 0) {
        log(`   构建时间: 提升 ${Math.abs(timeDiff).toFixed(2)}s (${Math.abs(parseFloat(timePercent))}%)`, 'green');
      } else {
        log(`   构建时间: 增加 ${timeDiff.toFixed(2)}s (${timePercent}%)`, 'red');
      }
    }
    
    if (latest.buildOutput && previous.buildOutput) {
      const sizeDiff = latest.buildOutput.totalSize - previous.buildOutput.totalSize;
      const sizeDiffMB = (sizeDiff / 1024 / 1024).toFixed(2);
      
      if (sizeDiff < 0) {
        log(`   构建大小: 减少 ${Math.abs(parseFloat(sizeDiffMB))} MB`, 'green');
      } else {
        log(`   构建大小: 增加 ${sizeDiffMB} MB`, 'red');
      }
    }
    
  } catch (error) {
    log('❌ 性能结果比较失败', 'red');
    console.error(error.message);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'benchmark';
  
  log('🏁 性能测试工具', 'bright');
  log('===============', 'bright');
  
  switch (command) {
    case 'dev':
      testDevStartup();
      break;
      
    case 'build':
      testBuildTime();
      break;
      
    case 'analyze':
      analyzeBuildOutput();
      break;
      
    case 'compare':
      compareResults();
      break;
      
    case 'benchmark':
    default:
      runBenchmark();
      compareResults();
      break;
  }
  
  log('✨ 性能测试完成!', 'green');
}

// 运行主函数
main();
