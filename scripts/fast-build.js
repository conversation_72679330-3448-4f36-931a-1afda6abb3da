#!/usr/bin/env node

/**
 * 超快速构建脚本
 * 专注于最大化构建速度
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 设置最优的 Node.js 环境变量
function setupNodeEnvironment() {
  log('⚡ 设置 Node.js 优化环境...', 'yellow');
  
  // 设置 Node.js 优化选项
  process.env.NODE_OPTIONS = [
    '--max-old-space-size=8192',     // 增加内存限制到 8GB
    '--max-semi-space-size=512',     // 优化垃圾回收
    '--experimental-worker',         // 启用 Worker 线程
    '--no-warnings',                 // 禁用警告以减少输出
  ].join(' ');
  
  // 设置构建优化环境变量
  process.env.NODE_ENV = 'production';
  process.env.NUXT_TELEMETRY_DISABLED = '1';
  process.env.CI = 'true'; // 启用 CI 模式优化
  process.env.VITE_CJS_IGNORE_WARNING = 'true';
  process.env.DISABLE_ESLINT_PLUGIN = 'true';
  process.env.GENERATE_SOURCEMAP = 'false';
  process.env.SKIP_TYPE_CHECK = 'true';
  
  log('✅ Node.js 环境优化完成', 'green');
}

// 清理和优化缓存
function optimizeCache() {
  log('🧹 优化构建缓存...', 'yellow');
  
  try {
    // 保留 node_modules/.cache 但清理其他缓存
    const cacheDirs = [
      '.nuxt',
      '.output',
      'dist',
      'node_modules/.vite'
    ];
    
    cacheDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        execSync(`rm -rf ${dir}`, { stdio: 'pipe' });
        log(`🗑️  清理: ${dir}`, 'blue');
      }
    });
    
    // 优化 pnpm 存储
    try {
      execSync('pnpm store prune', { stdio: 'pipe' });
      log('📦 pnpm 存储优化完成', 'blue');
    } catch (error) {
      // 忽略错误
    }
    
    log('✅ 缓存优化完成', 'green');
  } catch (error) {
    log('⚠️ 缓存优化部分失败，继续构建', 'yellow');
  }
}

// 临时禁用耗时的功能
function disableSlowFeatures() {
  log('🚫 临时禁用耗时功能...', 'yellow');
  
  // 备份原始配置
  const configPath = 'nuxt.config.ts';
  const backupPath = 'nuxt.config.ts.fast-build-backup';
  
  if (fs.existsSync(configPath)) {
    fs.copyFileSync(configPath, backupPath);
    
    let config = fs.readFileSync(configPath, 'utf8');
    
    // 临时禁用一些耗时的功能
    const optimizations = [
      // 禁用 ESLint
      { 
        search: /'@nuxt\/eslint'/g, 
        replace: '// \'@nuxt/eslint\' // 临时禁用' 
      },
      // 禁用类型检查
      {
        search: /typeCheck:\s*false/g,
        replace: 'typeCheck: false'
      },
      // 禁用 sourcemap
      {
        search: /sourcemap:\s*false/g,
        replace: 'sourcemap: false'
      }
    ];
    
    optimizations.forEach(opt => {
      config = config.replace(opt.search, opt.replace);
    });
    
    fs.writeFileSync(configPath, config);
    log('📝 临时配置优化完成', 'blue');
  }
  
  return backupPath;
}

// 恢复原始配置
function restoreConfig(backupPath) {
  log('🔄 恢复原始配置...', 'yellow');
  
  const configPath = 'nuxt.config.ts';
  
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, configPath);
    fs.unlinkSync(backupPath);
    log('✅ 配置恢复完成', 'green');
  }
}

// 并行构建
function parallelBuild() {
  log('🚀 启动并行构建...', 'yellow');
  
  const startTime = Date.now();
  
  try {
    // 使用优化的构建命令
    const buildCommand = [
      'pnpm',
      'nuxt',
      'build',
      '--dotenv', '.env.prd'
    ].join(' ');
    
    log(`执行命令: ${buildCommand}`, 'blue');
    
    execSync(buildCommand, { 
      stdio: 'inherit',
      env: {
        ...process.env,
        // 额外的构建优化
        NITRO_PRESET: 'node-server',
        NUXT_ANALYZE: 'false',
        BUILD_CACHE: 'true'
      }
    });
    
    const endTime = Date.now();
    const buildTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log(`✅ 构建完成，耗时: ${buildTime}s`, 'green');
    
    return parseFloat(buildTime);
    
  } catch (error) {
    log('❌ 构建失败', 'red');
    console.error(error.message);
    throw error;
  }
}

// 分析构建结果
function analyzeBuildResult() {
  log('📊 分析构建结果...', 'yellow');
  
  try {
    const distPath = '.nuxt/dist/client/_nuxt';
    
    if (fs.existsSync(distPath)) {
      const files = fs.readdirSync(distPath);
      const totalSize = files.reduce((sum, file) => {
        const filePath = path.join(distPath, file);
        const stats = fs.statSync(filePath);
        return sum + stats.size;
      }, 0);
      
      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
      
      log('📈 构建结果:', 'cyan');
      log(`   文件数量: ${files.length}`, 'blue');
      log(`   总大小: ${totalSizeMB} MB`, 'blue');
      
      // 检查是否有超大文件
      const largeFiles = files.filter(file => {
        const filePath = path.join(distPath, file);
        const stats = fs.statSync(filePath);
        return stats.size > 2 * 1024 * 1024; // > 2MB
      });
      
      if (largeFiles.length > 0) {
        log(`⚠️ 发现 ${largeFiles.length} 个大文件 (>2MB)`, 'yellow');
      }
    }
  } catch (error) {
    log('⚠️ 构建结果分析失败', 'yellow');
  }
}

// 主函数
function main() {
  log('🚀 超快速构建工具', 'bright');
  log('==================', 'bright');
  
  let backupPath = null;
  
  try {
    // 1. 设置优化环境
    setupNodeEnvironment();
    
    // 2. 优化缓存
    optimizeCache();
    
    // 3. 临时禁用耗时功能
    backupPath = disableSlowFeatures();
    
    // 4. 执行并行构建
    const buildTime = parallelBuild();
    
    // 5. 分析构建结果
    analyzeBuildResult();
    
    // 6. 输出性能报告
    log('\n🎉 快速构建完成!', 'green');
    log(`⏱️  构建时间: ${buildTime}s`, 'cyan');
    
    if (buildTime < 120) {
      log('🏆 构建速度优秀!', 'green');
    } else if (buildTime < 300) {
      log('👍 构建速度良好', 'yellow');
    } else {
      log('⚠️ 构建速度仍需优化', 'red');
    }
    
  } catch (error) {
    log('❌ 快速构建失败', 'red');
    console.error(error);
    process.exit(1);
  } finally {
    // 7. 恢复原始配置
    if (backupPath) {
      restoreConfig(backupPath);
    }
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  log('❌ 未捕获的异常:', 'red');
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log('❌ 未处理的 Promise 拒绝:', 'red');
  console.error('Promise:', promise, 'Reason:', reason);
  process.exit(1);
});

// 运行主函数
main();
