#!/usr/bin/env node

/**
 * 构建优化脚本
 * 用于优化构建性能和包大小
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查并清理缓存
function cleanCache() {
  log('🧹 清理构建缓存...', 'yellow');

  const cacheDirs = ['node_modules/.vite', 'node_modules/.cache', '.nuxt', '.output', 'dist'];

  cacheDirs.forEach((dir) => {
    if (fs.existsSync(dir)) {
      try {
        execSync(`rm -rf ${dir}`, { stdio: 'inherit' });
        log(`✅ 已清理: ${dir}`, 'green');
      } catch (error) {
        log(`❌ 清理失败: ${dir}`, 'red');
      }
    }
  });
}

// 优化 node_modules
function optimizeNodeModules() {
  log('📦 优化 node_modules...', 'yellow');

  try {
    // 重新安装依赖以确保最新状态
    log('重新安装依赖...', 'blue');
    execSync('pnpm install --frozen-lockfile', { stdio: 'inherit' });

    // 清理未使用的依赖
    log('检查未使用的依赖...', 'blue');
    try {
      execSync('pnpm dlx depcheck', { stdio: 'inherit' });
    } catch (error) {
      log('depcheck 检查完成（可能有警告）', 'yellow');
    }

    log('✅ node_modules 优化完成', 'green');
  } catch (error) {
    log('❌ node_modules 优化失败', 'red');
    console.error(error.message);
  }
}

// 分析包大小
function analyzeBundleSize() {
  log('📊 分析包大小...', 'yellow');

  try {
    // 运行构建分析
    execSync('pnpm analyze', { stdio: 'inherit' });
    log('✅ 包大小分析完成', 'green');
  } catch (error) {
    log('❌ 包大小分析失败', 'red');
    console.error(error.message);
  }
}

// 预构建优化
function prebuildOptimization() {
  log('⚡ 执行预构建优化...', 'yellow');

  try {
    // 预构建依赖
    log('预构建 Vite 依赖...', 'blue');
    execSync('pnpm dev --force', {
      stdio: 'inherit',
      timeout: 30000, // 30秒超时
    });
  } catch (error) {
    // 预构建可能会因为超时而失败，这是正常的
    log('预构建完成（可能因超时结束）', 'yellow');
  }
}

// 生产构建
function productionBuild() {
  log('🚀 执行生产构建...', 'yellow');

  const startTime = Date.now();

  try {
    execSync('pnpm prd', { stdio: 'inherit' });

    const endTime = Date.now();
    const buildTime = ((endTime - startTime) / 1000).toFixed(2);

    log(`✅ 生产构建完成，耗时: ${buildTime}s`, 'green');

    // 显示构建结果统计
    showBuildStats();
  } catch (error) {
    log('❌ 生产构建失败', 'red');
    console.error(error.message);
    process.exit(1);
  }
}

// 显示构建统计信息
function showBuildStats() {
  log('📈 构建统计信息:', 'cyan');

  try {
    const distPath = '.nuxt/dist/client/_nuxt';
    if (fs.existsSync(distPath)) {
      const files = fs.readdirSync(distPath);
      const jsFiles = files.filter((f) => f.endsWith('.js'));
      const cssFiles = files.filter((f) => f.endsWith('.css'));

      log(`JavaScript 文件数量: ${jsFiles.length}`, 'blue');
      log(`CSS 文件数量: ${cssFiles.length}`, 'blue');

      // 计算总大小
      let totalSize = 0;
      files.forEach((file) => {
        const filePath = path.join(distPath, file);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
      });

      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
      log(`总构建大小: ${totalSizeMB} MB`, 'blue');

      // 找出最大的文件
      const largeFiles = files
        .map((file) => {
          const filePath = path.join(distPath, file);
          const stats = fs.statSync(filePath);
          return { name: file, size: stats.size };
        })
        .filter((file) => file.size > 500 * 1024) // 大于 500KB
        .sort((a, b) => b.size - a.size)
        .slice(0, 5);

      if (largeFiles.length > 0) {
        log('🔍 大文件 (>500KB):', 'yellow');
        largeFiles.forEach((file) => {
          const sizeMB = (file.size / 1024 / 1024).toFixed(2);
          log(`  ${file.name}: ${sizeMB} MB`, 'yellow');
        });
      }
    }
  } catch (error) {
    log('无法获取构建统计信息', 'yellow');
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'full';

  log('🚀 构建优化工具', 'bright');
  log('================', 'bright');

  switch (command) {
    case 'clean':
      cleanCache();
      break;

    case 'deps':
      optimizeNodeModules();
      break;

    case 'analyze':
      analyzeBundleSize();
      break;

    case 'prebuild':
      prebuildOptimization();
      break;

    case 'build':
      productionBuild();
      break;

    case 'full':
    default:
      log('执行完整优化流程...', 'cyan');
      cleanCache();
      optimizeNodeModules();
      prebuildOptimization();
      productionBuild();
      break;
  }

  log('✨ 优化完成!', 'green');
}

// 错误处理
process.on('uncaughtException', (error) => {
  log('❌ 未捕获的异常:', 'red');
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log('❌ 未处理的 Promise 拒绝:', 'red');
  console.error('Promise:', promise, 'Reason:', reason);
  process.exit(1);
});

// 运行主函数
main();
