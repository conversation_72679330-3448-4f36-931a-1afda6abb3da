#!/usr/bin/env node

/**
 * 开发环境启动优化脚本
 * 用于优化开发服务器启动速度
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查并优化开发环境缓存
function optimizeDevCache() {
  log('🔧 优化开发环境缓存...', 'yellow');
  
  const cacheDirs = [
    'node_modules/.vite',
    'node_modules/.cache',
    '.nuxt'
  ];
  
  // 检查缓存目录大小
  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        const stats = execSync(`du -sh ${dir}`, { encoding: 'utf8' });
        log(`📁 ${dir}: ${stats.trim()}`, 'blue');
      } catch (error) {
        // 忽略错误
      }
    }
  });
  
  // 清理过期的 Vite 缓存
  const viteCacheDir = 'node_modules/.vite';
  if (fs.existsSync(viteCacheDir)) {
    try {
      const files = fs.readdirSync(viteCacheDir);
      const now = Date.now();
      const oneDay = 24 * 60 * 60 * 1000;
      
      files.forEach(file => {
        const filePath = path.join(viteCacheDir, file);
        const stats = fs.statSync(filePath);
        
        // 删除超过1天的缓存文件
        if (now - stats.mtime.getTime() > oneDay) {
          try {
            if (stats.isDirectory()) {
              execSync(`rm -rf "${filePath}"`);
            } else {
              fs.unlinkSync(filePath);
            }
            log(`🗑️  删除过期缓存: ${file}`, 'gray');
          } catch (error) {
            // 忽略删除错误
          }
        }
      });
    } catch (error) {
      // 忽略错误
    }
  }
  
  log('✅ 开发环境缓存优化完成', 'green');
}

// 预热依赖
function warmupDependencies() {
  log('🔥 预热常用依赖...', 'yellow');
  
  try {
    // 强制预构建关键依赖
    log('预构建 Vite 依赖...', 'blue');
    execSync('FORCE_OPTIMIZE=true pnpm dev --force', { 
      stdio: 'inherit',
      timeout: 20000 // 20秒超时
    });
  } catch (error) {
    // 预构建可能会因为超时而失败，这是正常的
    log('依赖预热完成', 'green');
  }
}

// 检查系统资源
function checkSystemResources() {
  log('💻 检查系统资源...', 'yellow');
  
  try {
    // 检查内存使用
    const memInfo = execSync('free -h 2>/dev/null || vm_stat', { encoding: 'utf8' });
    log('内存信息:', 'blue');
    console.log(memInfo);
    
    // 检查磁盘空间
    const diskInfo = execSync('df -h . 2>/dev/null || diskutil info /', { encoding: 'utf8' });
    log('磁盘空间:', 'blue');
    console.log(diskInfo.split('\n')[1] || diskInfo.split('\n')[0]);
    
  } catch (error) {
    log('系统资源检查完成', 'yellow');
  }
}

// 优化 Node.js 运行时
function optimizeNodeRuntime() {
  log('⚡ 优化 Node.js 运行时...', 'yellow');
  
  // 设置 Node.js 优化环境变量
  process.env.NODE_OPTIONS = [
    '--max-old-space-size=4096',  // 增加内存限制
    '--max-semi-space-size=256',   // 优化垃圾回收
    '--experimental-loader',       // 启用实验性加载器
  ].join(' ');
  
  log('✅ Node.js 运行时优化完成', 'green');
}

// 快速启动开发服务器
function fastDevStart() {
  log('🚀 快速启动开发服务器...', 'yellow');
  
  const startTime = Date.now();
  
  try {
    // 使用优化的环境变量启动
    const env = {
      ...process.env,
      NODE_ENV: 'development',
      NUXT_TELEMETRY_DISABLED: '1',  // 禁用遥测
      VITE_CJS_IGNORE_WARNING: 'true', // 忽略 CJS 警告
    };
    
    execSync('pnpm dev', { 
      stdio: 'inherit',
      env
    });
    
  } catch (error) {
    const endTime = Date.now();
    const startupTime = ((endTime - startTime) / 1000).toFixed(2);
    
    log(`开发服务器启动时间: ${startupTime}s`, 'cyan');
    
    if (error.code !== 130) { // 不是 Ctrl+C 中断
      log('❌ 开发服务器启动失败', 'red');
      console.error(error.message);
    }
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'start';
  
  log('⚡ 开发环境优化工具', 'bright');
  log('==================', 'bright');
  
  switch (command) {
    case 'cache':
      optimizeDevCache();
      break;
      
    case 'warmup':
      warmupDependencies();
      break;
      
    case 'check':
      checkSystemResources();
      break;
      
    case 'runtime':
      optimizeNodeRuntime();
      break;
      
    case 'start':
    default:
      log('执行完整开发环境优化...', 'cyan');
      checkSystemResources();
      optimizeDevCache();
      optimizeNodeRuntime();
      // warmupDependencies(); // 可选，会增加启动时间
      fastDevStart();
      break;
  }
  
  log('✨ 开发环境优化完成!', 'green');
}

// 错误处理
process.on('uncaughtException', (error) => {
  log('❌ 未捕获的异常:', 'red');
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log('❌ 未处理的 Promise 拒绝:', 'red');
  console.error('Promise:', promise, 'Reason:', reason);
  process.exit(1);
});

// 运行主函数
main();
