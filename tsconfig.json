{
  "compilerOptions": {
    "module": "esnext",
    // 编译性能优化
    "incremental": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    // 构建优化
    "isolatedModules": true,
    "preserveWatchOutput": true,
    // 类型检查优化
    "noUnusedLocals": false,
    "noUnusedParameters": false
  },
  // https://nuxt.com/docs/guide/concepts/typescript
  "extends": "./.nuxt/tsconfig.json",
  // 排除不必要的文件
  "exclude": [
    "node_modules",
    ".nuxt",
    ".output",
    "dist",
    "**/*.bak"
  ]
}
