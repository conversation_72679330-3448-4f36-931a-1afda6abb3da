<template>
  <div class="nuxt-agr flex">
    <BrowserTips />
    <div :class="['left-bar', 'bg-white', !collapsed ? 'w-[160px]' : 'w-[62px]', 'shrink-0', 'relative', 'h-[100vh]', 'z-[200]']">
      <!-- <ClientOnly> -->
      <NavMenu :collapsed="collapsed" />
      <!-- </ClientOnly> -->
      <UserInfo :collapsed="collapsed" />
      <a-tooltip placement="right">
        <template #title>{{ collapsed ? '展开导航栏' : '收起导航栏' }}</template>
        <!-- <div class="absolute bottom-0 right-[-12px] w-[12px] h-[54px] rounded-r-[4px] shadow-[2px_1px_4px_0px_rgba(0,0,0,0.05)] bg-[#19DB85]
        cursor-pointer text-white flex items-center justify-center z-[100] text-[14px]" @click="toggleCollapsed">
          <VueIcon :icon="collapsed ? IconChevronRightFont : IconChevronLeftFont"></VueIcon>
        </div> -->
        <div class="absolute bottom-[0px] right-[-18px] z-[100] h-[76px] w-[20px] left-bar-icon">
          <div class="absolute w-[12px] h-[68px] left-0 top-[4px] cursor-pointer flex items-center" @click="toggleCollapsed">
            <VueIcon :icon="IcArrowFont" :class="['text-[16px]', 'text-[rgba(0,0,0,0.40)]', !collapsed ? 'icon-right' : 'icon-left', 'small-icon', 'absolute', 'left-[-3px]']" />
          </div>
        </div>
      </a-tooltip>
    </div>
    <div class="content grow bg-[#F2F3F5] overflow-y-auto h-[100vh] relative">
      <PageTab v-model:page-tab-list="pageTabList" />
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { IcArrowFont } from '@pafe/icons-icore-agr-an';
import type { PageTab as PageTabItem } from './components/interface.d';
import NavMenu from './components/NavMenu.vue';
import PageTab from './components/PageTab.vue';
import UserInfo from './components/UserInfo.vue';
import BrowserTips from './components/BrowserTips.vue';

/**
 * 对外提供查询页签数据和关闭某页签的能力
 * 具体用法：
 * const { list, deletItem } = inject('pageTab');
 */
const pageTabList = ref<PageTabItem[]>([]);

const deletPageTabListItem = (path: string) => {
  let newPath = path;
  if (!/^\//.test(path)) {
    newPath = `/${path}`;
  }
  pageTabList.value = pageTabList.value.filter((val) => val.path !== newPath);
};

provide('pageTab', {
  pageTabList: readonly(pageTabList),
  deletPageTabListItem: deletPageTabListItem,
});

const collapsed = ref(false);
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};
</script>

<style lang="less" scoped>
.nuxt-agr {
  height: 100vh;
  .left-bar {
    transition: width 0.5s;
    box-shadow: 0px 2px 12px 0px rgba(212, 214, 217, 1);
    .left-bar-icon {
      background-image: url('@/assets/images/left-bar.png');
      background-size: cover;
    }
  }
}
.small-icon {
  transition: transform 0.5s;
  &.icon-left {
    transform: rotate(-180deg);
  }
  &.icon-right {
    transform: rotate(0deg);
  }
}
</style>
