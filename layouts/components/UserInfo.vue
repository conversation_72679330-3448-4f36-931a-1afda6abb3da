<template>
  <div class="user-info px-[8px] box-border overflow-hidden">
    <ul :class="collapsed ? 'user-icon-collapsed space-y-[2px]' : 'user-icon-list space-x-[8px]'">
      <a-tooltip v-for="item in tipsGroup" :key="item.id" :placement="collapsed ? 'right' : 'top'">
        <template #title>{{ item.title }}</template>
        <div class="group w-[32px] h-[32px] rounded-[6px] border border-[rgba(0,0,0,0.26)] border-solid flex justify-center items-center shrink-0 cursor-pointer hover:border-[#07C160] relative z-[200]">
          <VueIcon class="text-[rgba(0,0,0,0.55)] text-[16px] group-hover:text-[#07C160]" :icon="item.icon" @click="item.clickHandler" />
        </div>
      </a-tooltip>
    </ul>
    <a-divider class="line" />
    <div :class="['box-border mb-[16px]', !collapsed ? 'px-[8px]' : 'px-[8px]']">
      <a-tooltip :placement="collapsed ? 'right' : 'top'">
        <template #title>{{ userInfo.umName }}({{ userInfo.umCode }})</template>
        <div :class="['user-title-name', !collapsed ? 'truncate' : 'overflow-hidden', 'text-[#666666]']">{{ userInfo.umName }}</div>
        <div v-show="!collapsed" class="user-title-code truncate text-[#BDBDBD] mb-[4px]">{{ userInfo.umCode }}</div>
      </a-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IcNongxianTuichuFont, IcNongxianYonghufankuiFont, IcNongxianXitongxiaoxiFont } from '@pafe/icons-icore-agr-an';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores/useUserStore';
import { message } from '@/components/ui/Message';

defineProps<{
  collapsed: boolean;
}>();

const { userInfo } = storeToRefs(useUserStore());
const tipsGroup = ref([
  {
    id: 1,
    title: '退出',
    icon: IcNongxianTuichuFont,
    style: {
      cursor: 'pointer',
    },
    clickHandler: async () => {
      const { data } = await useFetch('/api/auth/logout', {
        method: 'post',
      });
      if (data.value.code === '000000') {
        const { loginUrl, xPortalToken, loginRedirect } = useRuntimeConfig().public || {};
        // const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}?X-Portal-Token=${xPortalToken}`)}`;
        const LOGIN_URL = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}`)}`;
        window.location.href = LOGIN_URL;
      } else {
        message.error(msg);
      }
    },
  },
  {
    id: 2,
    title: '用户反馈建设中',
    icon: IcNongxianYonghufankuiFont,
    clickHandler: () => {},
  },
  {
    id: 3,
    title: '系统消息建设中',
    icon: IcNongxianXitongxiaoxiFont,
    clickHandler: () => {},
  },
]);
</script>

<style lang="less">
@subColor: rgba(0, 0, 0, 0.2);
@primary: #06cc74;
@primaryBg: #f4fffb;
@primaryLine: #cbf5eb;
@arrowColor: #fff;
@borderColor: #e5fcf4;

.user-info {
  position: absolute;
  width: 100%;
  bottom: 0px;
  background-color: #fff;

  .user-icon-list {
    padding: 0 6px;
    margin: 0 0 10px 0;
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-icon-collapsed {
    margin: 10px 0;
    list-style: none;
    padding-inline-start: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .ant-divider-horizontal.line {
    border-color: #f7f8fa;
    width: auto;
    min-width: unset;
    margin: 0 0 10px 0;
  }

  .user-icon {
    font-size: 34px;
    margin-right: 4px;
  }

  .user-title-name {
    font-size: 14px;
  }
  .user-title-code {
    font-size: 12px;
    // color: rgba(7,193,96,0.5)
  }
}
</style>
