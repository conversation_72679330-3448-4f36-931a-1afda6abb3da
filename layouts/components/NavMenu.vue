<template>
  <div class="h-full">
    <div v-if="!collapsed" class="h-[58px] relative banner flex overflow-hidden w-full flex justify-center box-border items-center">
      <img class="inline-block w-[128px] h-[28px]" src="@/assets/images/logo-big.png" alt="" />
    </div>
    <div v-else class="h-[58px] banner text-center relative overflow-hidden flex justify-center box-border items-center">
      <img class="inline-block w-[26px] h-[28px]" src="@/assets/images/logo-small.png" alt="" />
    </div>
    <!-- <ClientOnly> -->
    <OverlayScrollbarsComponent v-if="!collapsed" class="layout-sider-menu-bar">
      <ClientOnly>
        <a-menu v-model:open-keys="openKeys" v-model:selected-keys="selectedKeys" mode="inline" :inline-indent="0" :items="menuItemList" @click="clickMenuItem">
          <template #expandIcon>
            <div class="expand-icon">
              <VueIcon :icon="IconChevronDownFont" />
            </div>
          </template>
        </a-menu>
      </ClientOnly>
    </OverlayScrollbarsComponent>
    <!-- 菜单收起态 -->
    <ClientOnly v-else>
      <OverlayScrollbarsComponent class="layout-sider-hidden-menu-bar">
        <div class="flex flex-col items-center space-y-[8px]">
          <template v-for="item in popMenuItemList" :key="item.name">
            <a-popover placement="rightTop" overlay-class-name="layout-navmenu-menu-popover" :overlay-style="{ 'padding-left': '0.1vw' }">
              <template #content>
                <div class="menu-pop-title">{{ item.label }}</div>
                <VerticalMenu v-model:open-keys="openKeys" v-model:selected-keys="selectedKeys" :list="item.children" :origin="item" @click-item="clickMenuItem" />
              </template>
              <div :class="['group-icon-box relative', { 'group-icon-box-selected': openKeys.includes(item.key) }]">
                <VueIcon :icon="MENUICON[item.popIcon]" />
              </div>
            </a-popover>
          </template>
        </div>
      </OverlayScrollbarsComponent>
    </ClientOnly>
    <!-- </ClientOnly> -->
  </div>
</template>

<script lang="ts" setup>
import { IconChevronDownFont } from '@pafe/icons-icore-agr-an';
import type { ItemType } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { VueIcon } from '@pag/icon-vue3';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue'; // 该插件仅限用于左侧菜单，其他地方不能用
import { MENUICON } from './MenuIconMap';
import TitleContent from './TitleContent.vue';
import VerticalMenu from './verticalMenu/VerticalMenu.vue';
import { useUserStore } from '@/stores/useUserStore';
import 'overlayscrollbars/styles/overlayscrollbars.css';

const { collapsed } = defineProps<{
  collapsed: boolean;
}>();

const { menuList } = storeToRefs(useUserStore());

interface MenuItem {
  id: number;
  authCode: string;
  menuName: string;
  menuType: string; // 01目录， 02菜单(可访问路由)
  menuUrl: string; // 路由path
  menuDesc: string; // 用来判断是否是详情页（hidden）
  subMenus?: MenuItem[];
}

interface MenuItems extends MenuItem {
  menuIcon?: string;
  popIcon?: string;
}

type MenuItemType = ItemType & {
  menuUrl: string;
  popIcon?: string;
  children?: MenuItemType[] | null;
};

const menuItemList = ref<MenuItemType[]>([]);
const popMenuItemList = ref<MenuItemType[]>([]);
const openKeys = ref<number[]>([]);
const selectedKeys = ref<number[]>([]);
const route = useRoute();
const router = useRouter();

const setLabel = (name: string) =>
  h(TitleContent, {
    title: name,
  });

// 整理非浮窗类型的菜单数据
const collectMenu = (list: MenuItems[]): MenuItemType[] => {
  const menuItem: MenuItemType[] = [];
  for (const item of list) {
    if (item.menuDesc !== 'hidden' && item.menuType !== '03') {
      // 03是按钮
      const obj: MenuItemType = {
        label: item.menuUrl ? setLabel(item.menuName) : item.menuName,
        key: item.id,
        icon: null,
        children: item?.subMenus?.length && item.subMenus[0].menuType !== '03' ? collectMenu(item.subMenus) : null, // 如果下一级是按钮权限的就不必继续了
        menuUrl: item.menuUrl || '',
        popupClassName: 'popup-bar-menu',
      };
      menuItem.push(obj);
    }
  }

  return menuItem;
};
// 菜单导航栏结构调整，适配a-menu组件结构
const collectMenuGroup = (list: MenuItems[]) => {
  const menuItem: MenuItemType[] = [];
  // 第一层进行分组
  // 过滤第一层需要隐藏的菜单
  const newList = list.filter((val) => val.menuDesc !== 'hidden');
  // UI规范规定只有第一层才有图标
  for (const item of newList) {
    const childArr: MenuItems[] = cloneDeep(item.subMenus);
    const obj: MenuItemType = {
      label: item.menuName,
      key: item.id,
      icon: item.menuIcon && MENUICON[item.menuIcon] ? h(VueIcon, { icon: MENUICON[item.menuIcon] }) : null, // VueIcon不支持服务端渲染
      // icon: null,
      children: childArr?.length ? collectMenu(childArr) : null,
      menuUrl: item.menuUrl || '',
    };
    menuItem.push(obj);
  }
  menuItemList.value = menuItem;
};

// 整理浮窗类型的菜单数据
const collectPopMenu = (list: MenuItems[]): MenuItemType[] => {
  const menuItem: MenuItemType[] = [];
  for (const item of list) {
    const obj: MenuItemType = {
      label: item.menuName,
      key: item.id,
      icon: null,
      children: item?.subMenus?.length && item.subMenus[0].menuType !== '03' ? collectPopMenu(item.subMenus) : null,
      menuUrl: item.menuUrl || '',
    };
    if (item.menuDesc !== 'hidden') {
      menuItem.push(obj);
    }
  }

  return menuItem;
};

// 整理浮窗类型的菜单数据
const collectPopMenuGroup = (list: MenuItems[]) => {
  const menuItem: MenuItemType[] = [];
  // 过滤第一层需要隐藏的菜单
  const newList = list.filter((val) => val.menuDesc !== 'hidden');
  for (const item of newList) {
    const childArr: MenuItems[] = cloneDeep(item.subMenus);
    const obj: MenuItemType = {
      label: item.menuName,
      key: item.id,
      icon: null,
      children: childArr?.length ? collectPopMenu(childArr) : null,
      type: 'group',
      menuUrl: item.menuUrl || '',
      popIcon: item.menuIcon,
      popupClassName: 'popup-main-bar-menu',
    };
    menuItem.push(obj);
  }
  popMenuItemList.value = menuItem;
};
// 设置默认选中项
const findSelectKeys = (list: MenuItemType[]): number => {
  let selectKeys: number = 0;
  const findChildrenSelectKeys = (list: MenuItemType[]) => {
    for (const item of list) {
      if (item.menuUrl === route.path) {
        // 命中当前路由
        selectKeys = item.key as number;
        break;
      } else {
        if (item.children && item.children.length) {
          findChildrenSelectKeys(item.children);
        }
      }
    }
  };
  for (const item of list) {
    if (item.menuUrl === route.path) {
      // 命中当前路由
      selectKeys = item.key as number;
      break;
    } else {
      if (item.children && item.children.length) {
        findChildrenSelectKeys(item.children);
      }
    }
  }
  return selectKeys;
};

const findOpenKeysByObj = (obj: MenuItemType, targetValue: number, parentChain: number[] = []): number[] => {
  // 检查当前对象是否包含匹配的children值
  if (Array.isArray(obj.children) && obj.children.find((val) => val.key === targetValue)) {
    return [obj.key, ...parentChain];
  }
  if (Array.isArray(obj.children)) {
    for (const item of obj.children) {
      if (Array.isArray(item.children)) {
        const result = findOpenKeysByObj(item, targetValue, [obj.key, ...parentChain]);
        if (result) {
          return result;
        }
      }
    }
  }
};

watchEffect(async () => {
  if (menuList.value.length) {
    // 处理菜单数据
    collectMenuGroup(menuList.value);
    collectPopMenuGroup(menuList.value);
    await nextTick();
    const curSelectedKeys = findSelectKeys(menuItemList.value);
    // const curOpenKeys: number[] = findOpenKeys(menuItemList.value, curSelectedKeys);
    const curOpenKeys: number[] = findOpenKeysByObj({ key: 1000000000, label: '', menuUrl: '', children: menuItemList.value }, curSelectedKeys);
    openKeys.value = curOpenKeys ? curOpenKeys.slice(0, -1) : [];
    selectedKeys.value = [curSelectedKeys];
  }
});

watch(
  () => route.path,
  async () => {
    // 路由发生变化，需要更新selectedKeys
    await nextTick();
    const curSelectedKeys = findSelectKeys(menuItemList.value);
    if (selectedKeys.value[0] !== curSelectedKeys) {
      selectedKeys.value = [curSelectedKeys];
    }
  },
);

watch(
  () => collapsed,
  (val) => {
    // 隐藏切换，部分菜单会隐藏，可能是nuxt缺陷，重新刷新数据
    if (!menuList.value || !menuList.value.length) return;
    if (!val) {
      collectMenuGroup(menuList.value);
    } else {
      collectPopMenuGroup(menuList.value);
    }
  },
);

interface RecordItem {
  originItemValue: { menuUrl: string };
}
// 点击菜单
const clickMenuItem = ({ item }: Record<string, RecordItem>): void => {
  const originItemValue = item.originItemValue || {};
  const { oldSystemUrl } = useRuntimeConfig().public || {};
  // 行政区划映射跳转至老系统-平台映射关系维护页
  if (originItemValue.menuUrl === '/districtInfoManage') {
    window.open(`${oldSystemUrl}/#/supervisionPlatformManagementRelation`);
  } else if (originItemValue.menuUrl === '/royalty') {
    // 权利金支付和赔款摊回跳转旧核心
    window.open(`${oldSystemUrl}/#/royalty`);
  } else {
    const url = '/' + originItemValue.menuUrl.replace(/^\//, '');
    if (route.path !== url) {
      router.push({ path: url });
    }
  }
};
</script>

<style lang="less" scoped>
.banner {
  background-image: linear-gradient(0deg, #ffffff 0%, #e9faf1 100%);
}
</style>

<style lang="less">
.layout-sider-menu-bar {
  height: calc(100% - 172px);
  overflow-y: auto;
  .ant-menu {
    .txdicon {
      transition: transform, 0.3s;
    }
    .sub-title-content {
      color: rgba(0, 0, 0, 0.6);
    }
    & {
      // 第一级
      li {
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        .ant-menu-item-icon {
          color: #07c160;
        }
        & > .ant-menu-title-content {
          font-weight: normal;
          // margin-left: 4px;
        }
        &.ant-menu-submenu-selected {
          .ant-menu-item-selected {
            background-color: rgba(7, 193, 96, 0.05);
          }
          .ant-menu-submenu-title {
            color: #333333;
            &:hover {
              .ant-menu-item-icon {
                color: #333333;
              }
            }
            .ant-menu-title-content {
              color: #333333;
            }
          }
          .ant-menu-submenu-arrow {
            color: #333333;
          }
        }
        .ant-menu-submenu-title {
          color: #333333;
          margin: 0px 0px;
          width: 100%;
          &:hover {
            &.ant-menu-submenu-title {
              .ant-menu-item-icon {
                color: #333333;
              }
              .ant-menu-title-content {
                color: #333333;
              }
            }
            .ant-menu-submenu-expand-icon.expand-icon {
              color: rgba(0, 0, 0, 0.22);
            }
          }
        }
        .ant-menu-submenu-arrow {
          color: rgba(0, 0, 0, 0.22);
        }
        & {
          .ant-menu {
            li {
              font-size: 14px;
              // font-weight: bold;
              color: #333333;
              &.ant-menu-submenu-selected {
                color: #333333;
                .ant-menu-submenu-title {
                  color: #333333;
                  &:hover {
                    .ant-menu-item-icon {
                      color: #333333;
                    }
                  }
                  .ant-menu-title-content {
                    color: #333333;
                  }
                }
                .ant-menu-title-content {
                  color: #333333;
                }
                .ant-menu-submenu-arrow {
                  color: #333333;
                }
              }
              .ant-menu-submenu-title {
                font-weight: normal;
                color: #333333;
                &:hover {
                  .ant-menu-title-content {
                    color: #333333;
                  }
                }
              }
              .ant-menu-title-content {
                color: #333333;
              }
              .ant-menu-submenu-arrow {
                color: rgba(0, 0, 0, 0.22);
              }
              & {
                .ant-menu {
                  li {
                    font-size: 14px;
                    font-weight: normal;
                    color: #333333;
                    &.ant-menu-item-selected {
                      color: #333333;
                      background-color: rgba(7, 193, 96, 0.05);
                      .ant-menu-submenu-title {
                        color: #333333;
                        .ant-menu-title-content {
                          color: #333333;
                        }
                      }
                      .ant-menu-title-content {
                        color: #07c160;
                      }
                      &:hover {
                        .ant-menu-title-content {
                          color: #07c160;
                        }
                      }
                    }
                    .ant-menu-submenu-title {
                      color: #333333;
                      &:hover {
                        .ant-menu-title-content {
                          color: #333333;
                        }
                      }
                    }
                    .ant-menu-title-content {
                      color: #333333;
                    }
                    .ant-menu-submenu-arrow {
                      color: rgba(0, 0, 0, 0.22);
                    }
                    &:hover {
                      .ant-menu-title-content {
                        color: #333333;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .ant-menu-submenu-title {
      padding-inline-end: 4px;
      display: flex;
      &:hover {
        .ant-menu-submenu-expand-icon.expand-icon {
          color: #07c160;
        }
      }
      .ant-menu-submenu-expand-icon.expand-icon {
        display: flex;
        width: 16px;
        justify-items: flex-end;
        position: static;
        transform: translateY(0px);
        color: rgba(0, 0, 0, 0.22);
      }
    }

    & .ant-menu-submenu-title,
    .ant-menu-item {
      margin: 0 0px 2px;
      width: 100%;
    }
    .ant-menu-submenu-arrow {
      color: rgba(0, 0, 0, 0.22);
      inset-inline-end: 0px;
    }
  }
}

.layout-sider-hidden-menu-bar {
  height: calc(100% - 240px);
  .group-icon-box {
    width: 32px;
    height: 32px;
    background: rgba(246, 246, 246, 0.53);
    border: 1px solid rgba(242, 243, 245, 1);
    border-radius: 4px;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .group-icon-box-selected {
    background: #07c160;
    color: #fff;
    border: 1px solid #e5fcf4;
  }

  .menu-popover {
    .ant-popover-content {
      .ant-popover-arrow {
        display: none;
      }
      .ant-popover-inner {
        padding: 3px 10px 10px;
        .ant-popover-inner-content {
          display: flex;
          flex-direction: column;
          .menu-pop-title {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.26);
            font-weight: 600;
          }
        }
      }
      #popoverMenu {
        .txdicon {
          display: none;
        }
        .ant-menu-submenu-vertical > .ant-menu-submenu-title {
          overflow: visible !important;
          > .ant-menu-submenu-arrow {
            padding-left: 10px;
          }
        }
        .ant-menu-item-group .ant-menu-item-group-title {
          margin-left: 12px;
          padding: 0;
        }
        .ant-menu-item-group .ant-menu-item-group-list .ant-menu-submenu-title {
          padding-inline-start: 1px;
        }
        .ant-menu-submenu {
          margin-top: 1px;
          margin-bottom: 1px;
        }
      }
    }
  }
}
.ant-menu-submenu.ant-menu-submenu-popup.ant-menu.ant-menu-light.popup-bar-menu {
  ul {
    padding-top: 7px;
    padding-bottom: 7px;
    li {
      margin-top: 1px;
      margin-bottom: 1px;
      color: rgba(0, 0, 0, 0.6);
      .sub-title-content {
        font-size: 14px;
      }
      &:hover {
        color: rgba(0, 0, 0, 0.6);
      }
      &.ant-menu-item-selected {
        color: #07c160;
        background-color: rgba(7, 193, 96, 0.05);
        &:hover {
          color: #07c160;
        }
      }
    }
  }
}
.ant-menu.ant-menu-root.ant-menu-vertical.ant-menu-light.menu-bar {
  .ant-menu-submenu-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    &:hover {
      color: rgba(0, 0, 0, 0.6);
    }
    &.ant-menu-submenu-selected {
      color: #07c160;
      &:hover {
        color: #07c160;
      }
    }
  }
  .ant-menu-submenu-selected {
    .ant-menu-submenu-title {
      color: #07c160;
      &:hover {
        color: #07c160;
      }
    }
  }
}
.layout-navmenu-menu-popover {
  .ant-popover-content {
    .ant-popover-arrow {
      display: none;
    }
    .ant-popover-inner {
      .ant-popover-inner-content {
        .menu-pop-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.26);
          font-weight: 600;
          margin-bottom: 4px;
        }
      }
    }
  }
}

// 覆盖menu默认自带的inlineIndent样式，inlineIndent使用的是px单位，不支持修改并且是js设置了style级别
.layout-sider-menu-bar {
  .ant-menu.ant-menu-root {
    padding-left: 8px;
    padding-right: 16px;
    & > .ant-menu-submenu {
      & > .ant-menu-submenu-title {
        padding-left: 8px !important;
        .ant-menu-title-content {
          margin-left: 4px;
        }
      }
      // 二级菜单
      & > .ant-menu-sub {
        & > li {
          .ant-menu-title-content {
            padding-left: 26px !important;
          }
          // 三级菜单
          & > .ant-menu-sub {
            & > li {
              .ant-menu-title-content {
                padding-left: 34px !important;
              }
              // 四级菜单
              & > .ant-menu-sub {
                & > li {
                  .ant-menu-title-content {
                    padding-left: 42px !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
