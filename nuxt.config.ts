// https://nuxt.com/docs/api/configuration/nuxt-config
// import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import';
// import { fileURLToPath } from 'node:url';
import { defineNuxtConfig } from 'nuxt/config';

export default defineNuxtConfig({
  // ssr: false,
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },
  app: {
    // pageTransition: { name: 'page', mode: 'out-in'}, // 不用轻易使用，导致某些页面莫名的展示不出来
    keepalive: {
      exclude: [],
    },
    head: {},
  },
  build: {
    analyze: process.env.ANALYZE === 'true',
    // 激进的构建性能优化
    transpile: [
      'ant-design-vue',
      '@visactor/vtable'
    ]
  },

  // 禁用 TypeScript 类型检查以加速构建
  typescript: {
    typeCheck: false
  },

  // Nuxt 3 构建优化
  experimental: {
    // 启用 payload 提取
    payloadExtraction: false,
    // 启用 tree shaking
    treeshakeClientOnly: true,
  },
  runtimeConfig: {
    // runtimeConfig的值如果是随环境变量的，则需要在.env.dev、ecosystem.config.cjs、ecosystem.config.test1.cjs中配置
    isServer: true,
    cyberark: {
      url: '',
      appId: '',
      appkey: '',
      safe: '',
      folder: '',
      object: '',
      encryptver: '',
    },
    redis: {
      host: '',
      port: '',
    },
    public: {
      host: '',
      gateWay: '/gateway',
      xPortalToken: '9e02354d53a01a6e8c652a5b622518e6',
      service: {
        administrate: '/icore-agr-an.administrate',
        supercoder: '/icore-supercoder.menuAuthService',
        compliance: '/icore-agr-an.compliance',
        farmer: '/icore-agr-an.farmer',
        accept: '/icore-agr-an.accept',
        ums: '/icore-agr-an.ums',
        endorse: '/icore-agr-an.endorse',
        check: '/icore-agr-an.check',
        document: '/icore-agr-an.document',
        policy: '/icore-agr-an.policy',
      },
      loginUrl: '',
      oldSystemUrl: '',
      workEnv: '',
      trackerProjectId: '',
      trackerSiteId: '',
      trackerSeverUrl: '',
      trackerUrl: '',
      umsHost: '',
      ecosHost: '',
      ecosEnv: '',
      // loginRedirect: '/gateway/icore-agr-an.administrate/web/gray/redirectIndex',
      loginRedirect: '/home',
      kanyunUrl: '',
    },
  },
  routeRules: {
    // '/gateway/icore-agr-an.farmer/file/**': {proxy: 'http://*************:8010/file/**'},
    // '/gateway/**': { proxy: `${process.env.NUXT_PUBLIC_HOST}/gateway/**` }, // gateway接口的走代理模式
    '/publicCache/**': {
      headers: {
        'cache-control': 'public, max-age=31536000, immutable',
      },
    },
    '/reinsuranceFacIframeResult.html': {
      headers: {
        'cache-control': 'public, max-age=0',
      },
    },
  },
  nitro: {
    // 激进的构建性能优化
    compressPublicAssets: false, // 禁用压缩以加速构建
    minify: false, // 禁用压缩以加速构建
    // 预渲染优化
    prerender: {
      crawlLinks: false,
      routes: [], // 禁用预渲染
    },
    // 实验性优化
    experimental: {
      wasm: false,
    },
    // 构建优化
    rollupConfig: {
      // 减少并行处理
      maxParallelFileOps: 2,
    },
    // 开发环境代理
    devProxy: {
      '/gateway': {
        target: `https://${process.env.DEV_API_HOST}/gateway`,
        changeOrigin: true,
        cookieDomainRewrite: process.env.DEV_API_HOST,
        prependPath: true,
      },
    },
  },
  devServer: {
    host: 'dev.paic.com.cn',
    port: 3000,
  },
  eslint: {
    // config: {
    //   stylistic: {
    //     semi: true,
    //   },
    // },
  },
  modules: ['@nuxtjs/tailwindcss', '@ant-design-vue/nuxt', '@pinia/nuxt', '@pag/nuxt-winston-log', '@pag/nuxt-amfe-flexible', '@pag/nuxt-cancelAutoImport', '@pag/nuxt-icon-vue3', '@pag/nuxt-server-middleware', /* '@pag/nuxt-legacy', */ '@pag/nuxt-noprefetch', /* '@nuxt/eslint', */ '@vueuse/nuxt', '~/modules/performance-optimizer'],
  serverMiddleware: {
    serverMiddle: ['~/server/serverMiddleware/setHeader.ts'],
  },

  css: [
    // '~/assets/css/global.css',
  ],
  // vite: {
  //   build: {
  //     target: ['es2015'], // 旧版本浏览器支持程度
  //   },
  // },
  legacy: {
    // targets: ['chrome >= 66', 'not IE < 11'], // 旧版本浏览器支持程度
    // additionalModernPolyfills: ['es.global-this', 'es.promise.all-settled'], 添加core-js里没有的polifill
    // modernPolyfills: ['es.global-this', 'es.promise.all-settled', 'es.array.flat-map'], // chrome 71才支持globalthis,chrome 74才支持promise.allSettled
  },

  postcss: {
    plugins: {
      'postcss-pxtorem': {
        rootValue: 144,
        propList: ['*', '!border-width', '!border', 'border-radius', '!border-left-width', '!border-right-width', '!border-bottom-width', '!border-top-width'],
      },
    },
  },

  tailwindcss: {
    config: {
      corePlugins: {
        preflight: false, // 去掉tailwindcss默认样式
      },
      theme: {
        extend: {
          fontFamily: {
            number: ['D-DIN-PRO-Regular'],
            'number-medium': ['D-DIN-PRO-Medium'],
          },
          colors: {
            font: {
              primary: '#333333',
              second: 'rgba(0, 0, 0, 0.40)',
              link: '#576B95',
            },
            gray: {
              100: '#f8f8f8',
            },
          },
          fontSize: {
            xs: '12px',
            base: '14px',
            xl: '16px',
          },
          spacing: {
            '8px': '8px',
            '10px': '10px',
            '12px': '12px',
            '14px': '14px',
            '16px': '16px',
          },
          borderRadius: {
            DEFAULT: '4px',
            md: '6px',
          },
          width: {
            footer: 'calc(100% - 160px)',
          },
        },
      },
      content: ['./components/**/*.{vue,tsx}', './layouts/**/*.{vue,tsx}', './pages/**/*.{vue,tsx}'],
    },
  },

  antd: {
    extractStyle: true,
  },

  nuxt3WinstonLog: {
    infoLogPath: process.env.LOG_PATH,
    errorLogPath: process.env.LOG_PATH,
    infoLogName: '%DATE%-infolog.log',
    errorLogName: '%DATE%-errorlog.log',
    stdout: false,
    dailyRotate: true,
    maxSize: '5120m',
  },
  vite: {
    define: {
      'process.env.APP_BUILD_ID': JSON.stringify(new Date().getTime()),
    },

    // 开发服务器优化
    server: {
      hmr: {
        overlay: false,
      },
      // 预热常用文件
      warmup: {
        clientFiles: [
          './pages/home/<USER>',
          './layouts/default.vue',
          './components/ui/**/*.vue'
        ]
      }
    },

    // 构建缓存优化
    cacheDir: 'node_modules/.vite',

    // 优化依赖预构建
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'ant-design-vue/es/button',
        'ant-design-vue/es/input',
        'ant-design-vue/es/form',
        'ant-design-vue/es/table',
        'ant-design-vue/es/modal',
        'ant-design-vue/es/message',
        'ant-design-vue/es/notification',
        'dayjs',
        'lodash-es',
        'uuid',
        'axios'
      ],
      exclude: [
        '@visactor/vtable',
        'leaflet',
        'html2canvas',
        '@turf/turf'
      ],
      // 强制预构建
      force: process.env.FORCE_OPTIMIZE === 'true'
    },

    build: {
      // 激进的构建性能优化
      target: 'esnext', // 使用最新的 ES 标准，减少转换
      minify: 'esbuild', // 使用最快的压缩器
      cssCodeSplit: false, // 禁用 CSS 代码分割以加速构建
      chunkSizeWarningLimit: 2000, // 提高警告阈值
      sourcemap: false, // 禁用 sourcemap 以加速构建
      // 优化依赖预构建
      commonjsOptions: {
        include: [/node_modules/],
        transformMixedEsModules: true,
      },
      // 启用实验性优化
      reportCompressedSize: false, // 禁用压缩大小报告以加速构建
      write: true,
      // 优化 Rollup 配置
      rollupOptions: {
        // 减少并行处理以避免内存问题
        maxParallelFileOps: 2,
        output: {
          chunkFileNames: '_nuxt/[name]-[hash].js',
          // 简化的代码分割策略 - 减少构建复杂度
          manualChunks: (id: string) => {
            // 第三方库基础分割
            if (id.includes('node_modules')) {
              // 大型 UI 库
              if (id.includes('ant-design-vue')) {
                return 'antd';
              }

              // Vue 生态系统
              if (id.includes('vue') || id.includes('pinia') || id.includes('@vue')) {
                return 'vue-ecosystem';
              }

              // 表格组件
              if (id.includes('@visactor/vtable')) {
                return 'vtable';
              }

              // 地图相关
              if (id.includes('leaflet') || id.includes('@turf') || id.includes('ecosmap')) {
                return 'map-libs';
              }

              // 工具库
              if (id.includes('lodash') || id.includes('dayjs') || id.includes('uuid') || id.includes('axios')) {
                return 'utils';
              }

              // 图标库
              if (id.includes('icons') || id.includes('@pafe/icons') || id.includes('@pag/icon')) {
                return 'icons';
              }

              // 其他第三方库
              return 'vendor';
            }

            // 业务模块基础分割
            if (id.includes('/pages/')) {
              if (id.includes('/pages/insure/')) {
                return 'insure-module';
              }
              if (id.includes('/pages/review/')) {
                return 'review-module';
              }
              if (id.includes('/pages/farmer/')) {
                return 'farmer-module';
              }
              if (id.includes('/pages/compliance/')) {
                return 'compliance-module';
              }
              if (id.includes('/pages/inspection/')) {
                return 'inspection-module';
              }
              if (id.includes('/pages/systemManage/')) {
                return 'system-manage-module';
              }
              if (id.includes('/pages/endorse/')) {
                return 'endorse-module';
              }
              return 'other-pages';
            }
          },
        },
      },
    },

    // 性能优化插件
    plugins: [
      // 动态加载性能插件
      ...(process.env.NODE_ENV === 'development' ? [
        // 开发环境插件将在运行时加载
      ] : [
        // 生产环境插件将在运行时加载
      ])
    ],

    vueJsx: {
      mergeProps: true,
    },

    resolve: {
      alias: {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
      },
    },
    // plugins: [ // 若要vxetable按需加载，则vxetable不能进行服务端渲染，会报错，需要使用clientonly标签包起来
    //   lazyImport({
    //     resolvers: [
    //       VxeResolver({
    //         libraryName: 'vxe-table'
    //       })
    //     ]
    //   })
    // ],
    // css: {
    //   preprocessorOptions: {
    //     less: {
    //       javascriptEnabled: true,
    //     }
    //   }
    // }
  },

  compatibilityDate: '2024-09-06',
});
