// https://nuxt.com/docs/api/configuration/nuxt-config
// import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import';
// import { fileURLToPath } from 'node:url';
export default defineNuxtConfig({
  // ssr: false,
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },
  app: {
    // pageTransition: { name: 'page', mode: 'out-in'}, // 不用轻易使用，导致某些页面莫名的展示不出来
    keepalive: {
      exclude: [],
    },
    head: {},
  },
  build: {
    analyze: process.env.ANALYZE === 'true',
  },
  runtimeConfig: {
    // runtimeConfig的值如果是随环境变量的，则需要在.env.dev、ecosystem.config.cjs、ecosystem.config.test1.cjs中配置
    isServer: true,
    cyberark: {
      url: '',
      appId: '',
      appkey: '',
      safe: '',
      folder: '',
      object: '',
      encryptver: '',
    },
    redis: {
      host: '',
      port: '',
    },
    public: {
      host: '',
      gateWay: '/gateway',
      xPortalToken: '9e02354d53a01a6e8c652a5b622518e6',
      service: {
        administrate: '/icore-agr-an.administrate',
        supercoder: '/icore-supercoder.menuAuthService',
        compliance: '/icore-agr-an.compliance',
        farmer: '/icore-agr-an.farmer',
        accept: '/icore-agr-an.accept',
        ums: '/icore-agr-an.ums',
        endorse: '/icore-agr-an.endorse',
        check: '/icore-agr-an.check',
        document: '/icore-agr-an.document',
        policy: '/icore-agr-an.policy',
      },
      loginUrl: '',
      oldSystemUrl: '',
      workEnv: '',
      trackerProjectId: '',
      trackerSiteId: '',
      trackerSeverUrl: '',
      trackerUrl: '',
      umsHost: '',
      ecosHost: '',
      ecosEnv: '',
      // loginRedirect: '/gateway/icore-agr-an.administrate/web/gray/redirectIndex',
      loginRedirect: '/home',
      kanyunUrl: '',
    },
  },
  routeRules: {
    // '/gateway/icore-agr-an.farmer/file/**': {proxy: 'http://*************:8010/file/**'},
    // '/gateway/**': { proxy: `${process.env.NUXT_PUBLIC_HOST}/gateway/**` }, // gateway接口的走代理模式
    '/publicCache/**': {
      headers: {
        'cache-control': 'public, max-age=31536000, immutable',
      },
    },
    '/reinsuranceFacIframeResult.html': {
      headers: {
        'cache-control': 'public, max-age=0',
      },
    },
  },
  nitro: {
    devProxy: {
      // 开发环境代理
      '/gateway': {
        target: `https://${process.env.DEV_API_HOST}/gateway`,
        changeOrigin: true,
        cookieDomainRewrite: process.env.DEV_API_HOST,
        prependPath: true,
      },
    },
  },
  devServer: {
    host: 'dev.paic.com.cn',
    port: 3000,
  },
  eslint: {
    // config: {
    //   stylistic: {
    //     semi: true,
    //   },
    // },
  },
  modules: ['@nuxtjs/tailwindcss', '@ant-design-vue/nuxt', '@pinia/nuxt', '@pag/nuxt-winston-log', '@pag/nuxt-amfe-flexible', '@pag/nuxt-cancelAutoImport', '@pag/nuxt-icon-vue3', '@pag/nuxt-server-middleware', '@pag/nuxt-legacy', '@pag/nuxt-noprefetch', '@nuxt/eslint', '@vueuse/nuxt'],
  serverMiddleware: {
    serverMiddle: ['~/server/serverMiddleware/setHeader.ts'],
  },

  css: [
    // '~/assets/css/global.css',
  ],
  // vite: {
  //   build: {
  //     target: ['es2015'], // 旧版本浏览器支持程度
  //   },
  // },
  legacy: {
    // targets: ['chrome >= 66', 'not IE < 11'], // 旧版本浏览器支持程度
    // additionalModernPolyfills: ['es.global-this', 'es.promise.all-settled'], 添加core-js里没有的polifill
    // modernPolyfills: ['es.global-this', 'es.promise.all-settled', 'es.array.flat-map'], // chrome 71才支持globalthis,chrome 74才支持promise.allSettled
  },

  postcss: {
    plugins: {
      'postcss-pxtorem': {
        rootValue: 144,
        propList: ['*', '!border-width', '!border', 'border-radius', '!border-left-width', '!border-right-width', '!border-bottom-width', '!border-top-width'],
      },
    },
  },

  tailwindcss: {
    config: {
      corePlugins: {
        preflight: false, // 去掉tailwindcss默认样式
      },
      theme: {
        extend: {
          fontFamily: {
            number: ['D-DIN-PRO-Regular'],
            'number-medium': ['D-DIN-PRO-Medium'],
          },
          colors: {
            font: {
              primary: '#333333',
              second: 'rgba(0, 0, 0, 0.40)',
              link: '#576B95',
            },
            gray: {
              100: '#f8f8f8',
            },
          },
          fontSize: {
            xs: '12px',
            base: '14px',
            xl: '16px',
          },
          spacing: {
            '8px': '8px',
            '10px': '10px',
            '12px': '12px',
            '14px': '14px',
            '16px': '16px',
          },
          borderRadius: {
            DEFAULT: '4px',
            md: '6px',
          },
          width: {
            footer: 'calc(100% - 160px)',
          },
        },
      },
      content: ['./components/**/*.{vue,tsx}', './layouts/**/*.{vue,tsx}', './pages/**/*.{vue,tsx}'],
    },
  },

  antd: {
    extractStyle: true,
  },

  nuxt3WinstonLog: {
    infoLogPath: process.env.LOG_PATH,
    errorLogPath: process.env.LOG_PATH,
    infoLogName: '%DATE%-infolog.log',
    errorLogName: '%DATE%-errorlog.log',
    stdout: false,
    dailyRotate: true,
    maxSize: '5120m',
  },
  vite: {
    define: {
      __APP_BUILD_ID__: new Date().getTime(),
    },
    build: {
      rollupOptions: {
        output: {
          chunkFileNames: '_nuxt/[name]-[hash].js',
          manualChunks: (id) => {
            // 第三方库分割
            if (id.includes('node_modules')) {
              // Ant Design Vue 单独分包
              if (id.includes('ant-design-vue')) {
                return 'ant-design-vue';
              }
              // Vue 生态系统
              if (id.includes('vue') || id.includes('pinia') || id.includes('@vue')) {
                return 'vue-ecosystem';
              }
              // 工具库
              if (id.includes('lodash') || id.includes('dayjs') || id.includes('uuid')) {
                return 'utils';
              }
              // 地图相关
              if (id.includes('ecosmap') || id.includes('leaflet') || id.includes('@turf')) {
                return 'map-libs';
              }
              // 表格组件
              if (id.includes('@visactor/vtable')) {
                return 'vtable';
              }
              // 图标库
              if (id.includes('@pafe/icons') || id.includes('@pag/icon')) {
                return 'icons';
              }
              // 其他第三方库
              return 'vendor';
            }

            // 业务模块分割
            if (id.includes('/pages/')) {
              // 合规模块
              if (id.includes('/pages/compliance/')) {
                return 'compliance-module';
              }
              // 农户模块
              if (id.includes('/pages/farmer/')) {
                return 'farmer-module';
              }
              // 投保模块
              if (id.includes('/pages/insure/')) {
                return 'insure-module';
              }
              // 验标模块
              if (id.includes('/pages/inspection/')) {
                return 'inspection-module';
              }
              // 审核模块
              if (id.includes('/pages/review/')) {
                return 'review-module';
              }
              // 系统管理模块
              if (id.includes('/pages/systemManage/')) {
                return 'system-manage-module';
              }
            }
          },
        },
      },
    },
    vueJsx: {
      mergeProps: true,
    },
    resolve: {
      alias: {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
      },
    },
    server: {
      // hmr: {
      //   clientPort: 2024,
      //   overlay: false,
      // },
    },
    // plugins: [ // 若要vxetable按需加载，则vxetable不能进行服务端渲染，会报错，需要使用clientonly标签包起来
    //   lazyImport({
    //     resolvers: [
    //       VxeResolver({
    //         libraryName: 'vxe-table'
    //       })
    //     ]
    //   })
    // ],
    // css: {
    //   preprocessorOptions: {
    //     less: {
    //       javascriptEnabled: true,
    //     }
    //   }
    // }
  },

  compatibilityDate: '2024-09-06',
});
