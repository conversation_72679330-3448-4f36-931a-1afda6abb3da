// https://nuxt.com/docs/api/configuration/nuxt-config
// import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import';
// import { fileURLToPath } from 'node:url';
export default defineNuxtConfig({
  // ssr: false,
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },
  app: {
    // pageTransition: { name: 'page', mode: 'out-in'}, // 不用轻易使用，导致某些页面莫名的展示不出来
    keepalive: {
      exclude: [],
    },
    head: {},
  },
  build: {
    analyze: process.env.ANALYZE === 'true',
    // 构建性能优化
    transpile: [
      'ant-design-vue',
      '@visactor/vtable'
    ]
  },

  // Nuxt 3 构建优化
  experimental: {
    // 启用 payload 提取
    payloadExtraction: false,
    // 启用 tree shaking
    treeshakeClientOnly: true,
  },
  runtimeConfig: {
    // runtimeConfig的值如果是随环境变量的，则需要在.env.dev、ecosystem.config.cjs、ecosystem.config.test1.cjs中配置
    isServer: true,
    cyberark: {
      url: '',
      appId: '',
      appkey: '',
      safe: '',
      folder: '',
      object: '',
      encryptver: '',
    },
    redis: {
      host: '',
      port: '',
    },
    public: {
      host: '',
      gateWay: '/gateway',
      xPortalToken: '9e02354d53a01a6e8c652a5b622518e6',
      service: {
        administrate: '/icore-agr-an.administrate',
        supercoder: '/icore-supercoder.menuAuthService',
        compliance: '/icore-agr-an.compliance',
        farmer: '/icore-agr-an.farmer',
        accept: '/icore-agr-an.accept',
        ums: '/icore-agr-an.ums',
        endorse: '/icore-agr-an.endorse',
        check: '/icore-agr-an.check',
        document: '/icore-agr-an.document',
        policy: '/icore-agr-an.policy',
      },
      loginUrl: '',
      oldSystemUrl: '',
      workEnv: '',
      trackerProjectId: '',
      trackerSiteId: '',
      trackerSeverUrl: '',
      trackerUrl: '',
      umsHost: '',
      ecosHost: '',
      ecosEnv: '',
      // loginRedirect: '/gateway/icore-agr-an.administrate/web/gray/redirectIndex',
      loginRedirect: '/home',
      kanyunUrl: '',
    },
  },
  routeRules: {
    // '/gateway/icore-agr-an.farmer/file/**': {proxy: 'http://*************:8010/file/**'},
    // '/gateway/**': { proxy: `${process.env.NUXT_PUBLIC_HOST}/gateway/**` }, // gateway接口的走代理模式
    '/publicCache/**': {
      headers: {
        'cache-control': 'public, max-age=31536000, immutable',
      },
    },
    '/reinsuranceFacIframeResult.html': {
      headers: {
        'cache-control': 'public, max-age=0',
      },
    },
  },
  nitro: {
    devProxy: {
      // 开发环境代理
      '/gateway': {
        target: `https://${process.env.DEV_API_HOST}/gateway`,
        changeOrigin: true,
        cookieDomainRewrite: process.env.DEV_API_HOST,
        prependPath: true,
      },
    },
  },
  devServer: {
    host: 'dev.paic.com.cn',
    port: 3000,
  },
  eslint: {
    // config: {
    //   stylistic: {
    //     semi: true,
    //   },
    // },
  },
  modules: ['@nuxtjs/tailwindcss', '@ant-design-vue/nuxt', '@pinia/nuxt', '@pag/nuxt-winston-log', '@pag/nuxt-amfe-flexible', '@pag/nuxt-cancelAutoImport', '@pag/nuxt-icon-vue3', '@pag/nuxt-server-middleware', '@pag/nuxt-legacy', '@pag/nuxt-noprefetch', '@nuxt/eslint', '@vueuse/nuxt'],
  serverMiddleware: {
    serverMiddle: ['~/server/serverMiddleware/setHeader.ts'],
  },

  css: [
    // '~/assets/css/global.css',
  ],
  // vite: {
  //   build: {
  //     target: ['es2015'], // 旧版本浏览器支持程度
  //   },
  // },
  legacy: {
    // targets: ['chrome >= 66', 'not IE < 11'], // 旧版本浏览器支持程度
    // additionalModernPolyfills: ['es.global-this', 'es.promise.all-settled'], 添加core-js里没有的polifill
    // modernPolyfills: ['es.global-this', 'es.promise.all-settled', 'es.array.flat-map'], // chrome 71才支持globalthis,chrome 74才支持promise.allSettled
  },

  postcss: {
    plugins: {
      'postcss-pxtorem': {
        rootValue: 144,
        propList: ['*', '!border-width', '!border', 'border-radius', '!border-left-width', '!border-right-width', '!border-bottom-width', '!border-top-width'],
      },
    },
  },

  tailwindcss: {
    config: {
      corePlugins: {
        preflight: false, // 去掉tailwindcss默认样式
      },
      theme: {
        extend: {
          fontFamily: {
            number: ['D-DIN-PRO-Regular'],
            'number-medium': ['D-DIN-PRO-Medium'],
          },
          colors: {
            font: {
              primary: '#333333',
              second: 'rgba(0, 0, 0, 0.40)',
              link: '#576B95',
            },
            gray: {
              100: '#f8f8f8',
            },
          },
          fontSize: {
            xs: '12px',
            base: '14px',
            xl: '16px',
          },
          spacing: {
            '8px': '8px',
            '10px': '10px',
            '12px': '12px',
            '14px': '14px',
            '16px': '16px',
          },
          borderRadius: {
            DEFAULT: '4px',
            md: '6px',
          },
          width: {
            footer: 'calc(100% - 160px)',
          },
        },
      },
      content: ['./components/**/*.{vue,tsx}', './layouts/**/*.{vue,tsx}', './pages/**/*.{vue,tsx}'],
    },
  },

  antd: {
    extractStyle: true,
  },

  nuxt3WinstonLog: {
    infoLogPath: process.env.LOG_PATH,
    errorLogPath: process.env.LOG_PATH,
    infoLogName: '%DATE%-infolog.log',
    errorLogName: '%DATE%-errorlog.log',
    stdout: false,
    dailyRotate: true,
    maxSize: '5120m',
  },
  vite: {
    define: {
      __APP_BUILD_ID__: new Date().getTime(),
    },
    build: {
      // 设置块大小警告限制
      chunkSizeWarningLimit: 1000,
      // 启用 CSS 代码分割
      cssCodeSplit: true,
      // 优化依赖预构建
      commonjsOptions: {
        include: [/node_modules/],
      },
      rollupOptions: {
        output: {
          chunkFileNames: '_nuxt/[name]-[hash].js',
          // 更精细的代码分割策略
          manualChunks: (id) => {
            // 第三方库分割 - 更细粒度
            if (id.includes('node_modules')) {
              // Ant Design Vue 进一步分割
              if (id.includes('ant-design-vue')) {
                if (id.includes('ant-design-vue/es/table') || id.includes('ant-design-vue/es/form')) {
                  return 'antd-heavy';
                }
                if (id.includes('ant-design-vue/es/date-picker') || id.includes('ant-design-vue/es/select')) {
                  return 'antd-pickers';
                }
                return 'antd-core';
              }

              // Vue 生态系统细分
              if (id.includes('@vue/runtime-core') || id.includes('@vue/reactivity')) {
                return 'vue-core';
              }
              if (id.includes('vue-router') || id.includes('pinia')) {
                return 'vue-router-store';
              }
              if (id.includes('@vue') || id.includes('vue/')) {
                return 'vue-utils';
              }

              // VTable 进一步分割
              if (id.includes('@visactor/vtable')) {
                if (id.includes('vtable/es/core')) {
                  return 'vtable-core';
                }
                if (id.includes('vtable/es/components')) {
                  return 'vtable-components';
                }
                return 'vtable-utils';
              }

              // 地图库细分
              if (id.includes('leaflet')) {
                return 'leaflet';
              }
              if (id.includes('@turf') || id.includes('d3-geo')) {
                return 'geo-utils';
              }
              if (id.includes('ecosmap')) {
                return 'ecosmap';
              }

              // 工具库细分
              if (id.includes('lodash')) {
                return 'lodash';
              }
              if (id.includes('dayjs') || id.includes('moment')) {
                return 'date-utils';
              }
              if (id.includes('uuid') || id.includes('crypto')) {
                return 'crypto-utils';
              }
              if (id.includes('axios') || id.includes('ky')) {
                return 'http-utils';
              }

              // 图标库
              if (id.includes('@pafe/icons') || id.includes('@pag/icon')) {
                return 'icons';
              }
              if (id.includes('@ant-design/icons')) {
                return 'antd-icons';
              }

              // UI 相关库
              if (id.includes('html2canvas') || id.includes('lottie')) {
                return 'ui-libs';
              }

              // 其他第三方库
              return 'vendor';
            }

            // 业务模块分割 - 按页面进一步细分
            if (id.includes('/pages/')) {
              // 投保模块细分（最大的模块）
              if (id.includes('/pages/insure/')) {
                if (id.includes('/pages/insure/insuranceFormFill/')) {
                  return 'insure-form';
                }
                if (id.includes('/pages/insure/insuranceTracking/')) {
                  return 'insure-tracking';
                }
                return 'insure-other';
              }

              // 审核模块细分
              if (id.includes('/pages/review/')) {
                if (id.includes('/pages/review/insureProcess/')) {
                  return 'review-process';
                }
                if (id.includes('/pages/review/endorseVerify/')) {
                  return 'review-endorse';
                }
                return 'review-other';
              }

              // 农户模块细分
              if (id.includes('/pages/farmer/')) {
                if (id.includes('/pages/farmer/farmerInfo/')) {
                  return 'farmer-info';
                }
                if (id.includes('/pages/farmer/farmerList/')) {
                  return 'farmer-list';
                }
                return 'farmer-other';
              }

              // 其他模块保持原有分割
              if (id.includes('/pages/compliance/')) {
                return 'compliance-module';
              }
              if (id.includes('/pages/inspection/')) {
                return 'inspection-module';
              }
              if (id.includes('/pages/systemManage/')) {
                return 'system-manage-module';
              }
              if (id.includes('/pages/endorse/')) {
                return 'endorse-module';
              }
              if (id.includes('/pages/policy/')) {
                return 'policy-module';
              }
              if (id.includes('/pages/paymentManage/')) {
                return 'payment-module';
              }
            }

            // 组件分割
            if (id.includes('/components/')) {
              if (id.includes('/components/ui/')) {
                return 'ui-components';
              }
              if (id.includes('/components/selector/')) {
                return 'selector-components';
              }
              return 'common-components';
            }

            // 工具函数分割
            if (id.includes('/utils/') || id.includes('/composables/')) {
              return 'app-utils';
            }
          },
        },
      },
    },
    // 构建性能优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'ant-design-vue/es',
        'dayjs',
        'lodash-es',
        'uuid',
        'axios'
      ],
      exclude: [
        '@visactor/vtable',
        'leaflet',
        'html2canvas'
      ]
    },

    // 开发服务器优化
    server: {
      hmr: {
        overlay: false,
      },
      // 预热常用文件
      warmup: {
        clientFiles: [
          './pages/home/<USER>',
          './layouts/default.vue',
          './components/ui/**/*.vue'
        ]
      }
    },

    // 构建缓存优化
    cacheDir: 'node_modules/.vite',

    // 自定义插件将在运行时动态加载

    vueJsx: {
      mergeProps: true,
    },

    resolve: {
      alias: {
        'ant-design-vue/dist': 'ant-design-vue/dist',
        'ant-design-vue/es': 'ant-design-vue/es',
        'ant-design-vue/lib': 'ant-design-vue/es',
        'ant-design-vue': 'ant-design-vue/es',
      },
    },
    // plugins: [ // 若要vxetable按需加载，则vxetable不能进行服务端渲染，会报错，需要使用clientonly标签包起来
    //   lazyImport({
    //     resolvers: [
    //       VxeResolver({
    //         libraryName: 'vxe-table'
    //       })
    //     ]
    //   })
    // ],
    // css: {
    //   preprocessorOptions: {
    //     less: {
    //       javascriptEnabled: true,
    //     }
    //   }
    // }
  },

  compatibilityDate: '2024-09-06',
});
