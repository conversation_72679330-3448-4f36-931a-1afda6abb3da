// 资源预加载工具

export interface PreloadOptions {
  priority?: 'high' | 'low';
  crossorigin?: 'anonymous' | 'use-credentials';
  as?: 'script' | 'style' | 'image' | 'font' | 'fetch';
}

/**
 * 预加载资源
 * @param href 资源URL
 * @param options 预加载选项
 */
export function preloadResource(href: string, options: PreloadOptions = {}) {
  if (typeof window === 'undefined') return;

  const { priority = 'low', crossorigin, as } = options;

  // 检查是否已经预加载过
  const existingLink = document.querySelector(`link[href="${href}"]`);
  if (existingLink) return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  
  if (as) link.as = as;
  if (crossorigin) link.crossOrigin = crossorigin;
  if (priority === 'high') link.setAttribute('importance', 'high');

  document.head.appendChild(link);
}

/**
 * 预加载图片
 * @param src 图片URL
 * @param priority 优先级
 */
export function preloadImage(src: string, priority: 'high' | 'low' = 'low') {
  preloadResource(src, { as: 'image', priority });
}

/**
 * 预加载字体
 * @param src 字体URL
 * @param priority 优先级
 */
export function preloadFont(src: string, priority: 'high' | 'low' = 'high') {
  preloadResource(src, { 
    as: 'font', 
    priority, 
    crossorigin: 'anonymous' 
  });
}

/**
 * 预加载CSS
 * @param href CSS文件URL
 * @param priority 优先级
 */
export function preloadCSS(href: string, priority: 'high' | 'low' = 'high') {
  preloadResource(href, { as: 'style', priority });
}

/**
 * 预加载JavaScript
 * @param src JS文件URL
 * @param priority 优先级
 */
export function preloadScript(src: string, priority: 'high' | 'low' = 'low') {
  preloadResource(src, { as: 'script', priority });
}

/**
 * 批量预加载图片
 * @param images 图片URL数组
 * @param delay 每张图片之间的延迟（毫秒）
 */
export function batchPreloadImages(images: string[], delay: number = 100) {
  if (typeof window === 'undefined') return;

  images.forEach((src, index) => {
    setTimeout(() => {
      preloadImage(src);
    }, index * delay);
  });
}

/**
 * 预加载关键资源
 */
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return;

  // 预加载关键字体
  const criticalFonts = [
    '/publicCache/fonts/D-DIN-PRO-Regular.woff2',
    '/publicCache/fonts/D-DIN-PRO-Medium.woff2',
  ];

  criticalFonts.forEach(font => {
    preloadFont(font, 'high');
  });

  // 预加载关键图片
  const criticalImages = [
    '/publicCache/images/logo.png',
    '/publicCache/images/home/<USER>',
  ];

  criticalImages.forEach(image => {
    preloadImage(image, 'high');
  });
}

/**
 * 预加载下一页面的资源
 * @param routeName 路由名称
 */
export function preloadPageResources(routeName: string) {
  if (typeof window === 'undefined') return;

  const pageResourceMap: Record<string, string[]> = {
    home: [
      '/publicCache/images/home/<USER>',
      '/publicCache/images/home/<USER>',
    ],
    farmer: [
      '/publicCache/images/farmer/farmer-icon.png',
    ],
    insure: [
      '/publicCache/images/insure/insure-icon.png',
    ],
  };

  const resources = pageResourceMap[routeName];
  if (resources) {
    batchPreloadImages(resources);
  }
}

/**
 * 智能预加载（基于用户行为）
 */
export class SmartPreloader {
  private static instance: SmartPreloader;
  private preloadedRoutes = new Set<string>();
  private hoverTimer: number | null = null;

  static getInstance() {
    if (!SmartPreloader.instance) {
      SmartPreloader.instance = new SmartPreloader();
    }
    return SmartPreloader.instance;
  }

  /**
   * 初始化智能预加载
   */
  init() {
    if (typeof window === 'undefined') return;

    this.setupLinkHoverPreload();
    this.setupIdlePreload();
  }

  /**
   * 设置链接悬停预加载
   */
  private setupLinkHoverPreload() {
    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && link.href) {
        this.hoverTimer = window.setTimeout(() => {
          this.preloadRoute(link.href);
        }, 100);
      }
    });

    document.addEventListener('mouseout', () => {
      if (this.hoverTimer) {
        clearTimeout(this.hoverTimer);
        this.hoverTimer = null;
      }
    });
  }

  /**
   * 设置空闲时间预加载
   */
  private setupIdlePreload() {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        this.preloadCommonRoutes();
      });
    } else {
      setTimeout(() => {
        this.preloadCommonRoutes();
      }, 2000);
    }
  }

  /**
   * 预加载路由
   * @param href 路由URL
   */
  private preloadRoute(href: string) {
    try {
      const url = new URL(href, window.location.origin);
      const pathname = url.pathname;
      
      if (this.preloadedRoutes.has(pathname)) return;
      
      this.preloadedRoutes.add(pathname);
      
      // 根据路由预加载对应资源
      const routeName = pathname.split('/')[1] || 'home';
      preloadPageResources(routeName);
      
    } catch (error) {
      console.warn('预加载路由失败:', error);
    }
  }

  /**
   * 预加载常用路由
   */
  private preloadCommonRoutes() {
    const commonRoutes = ['home', 'farmer', 'insure'];
    
    commonRoutes.forEach((route, index) => {
      setTimeout(() => {
        preloadPageResources(route);
      }, index * 500);
    });
  }
}

/**
 * 检测网络状态并调整预加载策略
 */
export function adaptivePreload() {
  if (typeof window === 'undefined') return;

  // 检测网络连接类型
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  if (connection) {
    const { effectiveType, saveData } = connection;
    
    // 如果是慢速网络或开启了数据节省模式，减少预加载
    if (effectiveType === 'slow-2g' || effectiveType === '2g' || saveData) {
      console.log('检测到慢速网络，减少预加载');
      return;
    }
  }

  // 正常网络情况下进行预加载
  preloadCriticalResources();
  SmartPreloader.getInstance().init();
}

// 在客户端初始化时调用
if (typeof window !== 'undefined') {
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', adaptivePreload);
  } else {
    adaptivePreload();
  }
}
