import type { Component } from 'vue';
import { defineAsyncComponent } from 'vue';

// 组件懒加载配置接口
interface LazyComponentOptions {
  loadingComponent?: Component;
  errorComponent?: Component;
  delay?: number;
  timeout?: number;
  suspensible?: boolean;
  onError?: (error: Error, retry: () => void, fail: () => void, attempts: number) => void;
}

// 默认加载组件
const DefaultComponentLoading = defineComponent({
  template: `
    <div class="flex items-center justify-center p-4">
      <a-spin size="small" />
    </div>
  `,
});

// 默认错误组件
const DefaultComponentError = defineComponent({
  props: {
    error: Object,
    retry: Function,
  },
  template: `
    <div class="flex flex-col items-center justify-center p-4 text-center">
      <div class="text-red-500 mb-2">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <p class="text-sm text-gray-600 mb-2">组件加载失败</p>
      <a-button size="small" @click="retry" v-if="retry">
        重试
      </a-button>
    </div>
  `,
});

/**
 * 创建懒加载组件
 * @param importFn 动态导入函数
 * @param options 懒加载选项
 * @returns 异步组件
 */
export function createLazyComponent(
  importFn: () => Promise<any>,
  options: LazyComponentOptions = {}
) {
  const {
    loadingComponent = DefaultComponentLoading,
    errorComponent = DefaultComponentError,
    delay = 200,
    timeout = 15000,
    suspensible = false,
    onError,
  } = options;

  return defineAsyncComponent({
    loader: importFn,
    loadingComponent,
    errorComponent,
    delay,
    timeout,
    suspensible,
    onError: onError || ((error, retry, fail, attempts) => {
      console.error(`组件加载失败 (尝试 ${attempts} 次):`, error);
      
      if (attempts <= 2) {
        retry();
      } else {
        fail();
      }
    }),
  });
}

/**
 * 创建大型组件懒加载（如表格、图表等）
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLazyHeavyComponent(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    delay: 100,
    timeout: 30000,
  });
}

/**
 * 创建第三方组件懒加载
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLazyThirdPartyComponent(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    delay: 300,
    timeout: 20000,
  });
}

/**
 * 创建轻量级组件懒加载
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLazyLightComponent(importFn: () => Promise<any>) {
  return createLazyComponent(importFn, {
    delay: 50,
    timeout: 10000,
  });
}

/**
 * 条件懒加载组件
 * @param condition 加载条件
 * @param importFn 动态导入函数
 * @param fallbackComponent 降级组件
 * @returns 组件或异步组件
 */
export function createConditionalLazyComponent(
  condition: boolean | (() => boolean),
  importFn: () => Promise<any>,
  fallbackComponent?: Component
) {
  const shouldLoad = typeof condition === 'function' ? condition() : condition;
  
  if (!shouldLoad) {
    return fallbackComponent || defineComponent({
      template: '<div></div>'
    });
  }
  
  return createLazyComponent(importFn);
}

/**
 * 创建可见时懒加载的组件
 * @param importFn 动态导入函数
 * @param options 懒加载选项
 * @returns 异步组件
 */
export function createLazyOnVisibleComponent(
  importFn: () => Promise<any>,
  options: LazyComponentOptions = {}
) {
  return defineComponent({
    setup() {
      const isVisible = ref(false);
      const elementRef = ref<HTMLElement>();
      
      onMounted(() => {
        if (!elementRef.value) return;
        
        const observer = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              isVisible.value = true;
              observer.disconnect();
            }
          },
          { threshold: 0.1 }
        );
        
        observer.observe(elementRef.value);
        
        onUnmounted(() => {
          observer.disconnect();
        });
      });
      
      const LazyComponent = createLazyComponent(importFn, options);
      
      return {
        isVisible,
        elementRef,
        LazyComponent,
      };
    },
    template: `
      <div ref="elementRef">
        <LazyComponent v-if="isVisible" />
        <div v-else class="flex items-center justify-center p-4">
          <a-spin size="small" tip="组件准备中..." />
        </div>
      </div>
    `,
  });
}

/**
 * 批量预加载组件
 * @param components 组件导入函数数组
 * @param delay 每个组件之间的延迟（毫秒）
 */
export function batchPreloadComponents(components: (() => Promise<any>)[], delay: number = 200) {
  if (typeof window === 'undefined') return;

  components.forEach((importFn, index) => {
    setTimeout(() => {
      importFn().catch(error => {
        console.warn('组件预加载失败:', error);
      });
    }, index * delay);
  });
}
