import type { Component } from 'vue';
import { defineAsyncComponent } from 'vue';

// 路由懒加载配置接口
interface LazyRouteOptions {
  loadingComponent?: Component;
  errorComponent?: Component;
  delay?: number;
  timeout?: number;
  suspensible?: boolean;
  onError?: (error: Error, retry: () => void, fail: () => void, attempts: number) => void;
}

// 默认加载组件
const DefaultLoadingComponent = defineComponent({
  template: `
    <div class="flex items-center justify-center min-h-[200px]">
      <a-spin size="large" tip="页面加载中..." />
    </div>
  `,
});

// 默认错误组件
const DefaultErrorComponent = defineComponent({
  props: {
    error: Object,
    retry: Function,
  },
  template: `
    <div class="flex flex-col items-center justify-center min-h-[200px] p-4">
      <div class="text-red-500 mb-4">
        <svg class="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-800 mb-2">页面加载失败</h3>
      <p class="text-gray-600 mb-4 text-center">抱歉，页面加载时出现了问题</p>
      <a-button type="primary" @click="retry" v-if="retry">
        重新加载
      </a-button>
    </div>
  `,
});

/**
 * 创建懒加载路由组件
 * @param importFn 动态导入函数
 * @param options 懒加载选项
 * @returns 异步组件
 */
export function createLazyRoute(
  importFn: () => Promise<any>,
  options: LazyRouteOptions = {}
) {
  const {
    loadingComponent = DefaultLoadingComponent,
    errorComponent = DefaultErrorComponent,
    delay = 200,
    timeout = 30000,
    suspensible = false,
    onError,
  } = options;

  return defineAsyncComponent({
    loader: importFn,
    loadingComponent,
    errorComponent,
    delay,
    timeout,
    suspensible,
    onError: onError || ((error, retry, fail, attempts) => {
      console.error(`路由组件加载失败 (尝试 ${attempts} 次):`, error);
      
      // 最多重试 3 次
      if (attempts <= 3) {
        console.log('正在重试加载组件...');
        retry();
      } else {
        console.error('组件加载失败，已达到最大重试次数');
        fail();
      }
    }),
  });
}

/**
 * 创建模块懒加载路由组件（用于大型模块）
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLazyModuleRoute(importFn: () => Promise<any>) {
  return createLazyRoute(importFn, {
    delay: 100, // 大型模块延迟更短
    timeout: 45000, // 超时时间更长
  });
}

/**
 * 创建轻量级懒加载路由组件（用于小型页面）
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLightLazyRoute(importFn: () => Promise<any>) {
  return createLazyRoute(importFn, {
    delay: 50, // 轻量级组件延迟很短
    timeout: 15000, // 超时时间较短
  });
}

/**
 * 预加载路由组件
 * @param importFn 动态导入函数
 * @param condition 预加载条件，默认为 true
 */
export function preloadRoute(importFn: () => Promise<any>, condition: boolean = true) {
  if (condition && typeof window !== 'undefined') {
    // 在空闲时间预加载
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        importFn().catch(error => {
          console.warn('路由预加载失败:', error);
        });
      });
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        importFn().catch(error => {
          console.warn('路由预加载失败:', error);
        });
      }, 100);
    }
  }
}

/**
 * 批量预加载路由
 * @param routes 路由导入函数数组
 * @param delay 每个路由之间的延迟（毫秒）
 */
export function batchPreloadRoutes(routes: (() => Promise<any>)[], delay: number = 500) {
  if (typeof window === 'undefined') return;

  routes.forEach((importFn, index) => {
    setTimeout(() => {
      preloadRoute(importFn);
    }, index * delay);
  });
}
