import type { Component } from 'vue';
import { defineAsyncComponent } from 'vue';

// 路由懒加载配置接口
interface LazyRouteOptions {
  loadingComponent?: Component;
  errorComponent?: Component;
  delay?: number;
  timeout?: number;
  suspensible?: boolean;
  onError?: (error: Error, retry: () => void, fail: () => void, attempts: number) => void;
}

// 默认配置
const defaultOptions: LazyRouteOptions = {
  delay: 200,
  timeout: 30000,
  suspensible: false,
};

/**
 * 创建懒加载路由组件
 * @param importFn 动态导入函数
 * @param options 配置选项
 * @returns 异步组件
 */
export function createLazyRoute(importFn: () => Promise<any>, options: LazyRouteOptions = {}) {
  const mergedOptions = { ...defaultOptions, ...options };

  return defineAsyncComponent({
    loader: importFn,
    delay: mergedOptions.delay,
    timeout: mergedOptions.timeout,
    suspensible: mergedOptions.suspensible,
    onError: mergedOptions.onError,
  });
}

/**
 * 创建轻量级懒加载路由组件（用于小型页面）
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createLightLazyRoute(importFn: () => Promise<any>) {
  return createLazyRoute(importFn, {
    delay: 50,
    timeout: 15000,
  });
}

/**
 * 创建重型懒加载路由组件（用于大型页面）
 * @param importFn 动态导入函数
 * @returns 异步组件
 */
export function createHeavyLazyRoute(importFn: () => Promise<any>) {
  return createLazyRoute(importFn, {
    delay: 300,
    timeout: 45000,
  });
}

/**
 * 预加载路由组件
 * @param importFn 动态导入函数
 */
export function preloadRoute(importFn: () => Promise<any>) {
  if (typeof window !== 'undefined') {
    // 在空闲时预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        importFn().catch(() => {
          // 预加载失败不影响正常使用
        });
      });
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        importFn().catch(() => {
          // 预加载失败不影响正常使用
        });
      }, 100);
    }
  }
}

/**
 * 批量预加载路由组件
 * @param importFns 动态导入函数数组
 * @param delay 每个预加载之间的延迟（毫秒）
 */
export function preloadRoutes(importFns: (() => Promise<any>)[], delay = 100) {
  importFns.forEach((importFn, index) => {
    setTimeout(() => {
      preloadRoute(importFn);
    }, index * delay);
  });
}