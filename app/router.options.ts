import type { RouterConfig } from '@nuxt/schema';
import { createLightLazyRoute, preloadRoute } from '@/utils/lazyRoute';
import complianceRoutes from '@/router/compliance';
import systemManageRoutes from '@/router/systemManage';
import farmer from '@/router/farmer';
import insure from '@/router/insure';
import inspection from '@/router/inspection';
import review from '@/router/review';
import paymentManage from '@/router/paymentManage';
import policy from '@/router/policy';
import endorse from '@/router/endorse';
import infoModify from '@/router/infoModify';
import fileManagement from '@/router/fileManagement';
import taskCenter from '@/router/taskCenter';
import riskAssess from '@/router/riskAssess';

// 预加载关键页面
if (typeof window !== 'undefined') {
  // 预加载首页（用户最可能访问的页面）
  preloadRoute(() => import('@/pages/home/<USER>'));
}

// 通过路由配置可以选择性覆盖或扩展你的路由，返回null或者undefined，将回退到默认路由（由pages目录自动生成）
export default {
  routes: (_routes: RouterConfig): RouterConfig =>
    _routes.concat([
      {
        path: '/',
        meta: {},
        redirect: '/home',
      },
      {
        path: '/home',
        name: 'home',
        meta: {
          title: '首页',
        },
        component: createLightLazyRoute(() => import('@/pages/home/<USER>')),
      },
      {
        path: '/noAuth',
        name: 'noAuth',
        meta: {
          title: '暂无权限',
        },
        component: createLightLazyRoute(() => import('@/pages/noAuth/index.vue')),
      },
      {
        path: '/clearCache',
        name: 'clearCache',
        meta: {
          title: '清除缓存',
        },
        component: createLightLazyRoute(() => import('@/pages/clearCache/index.vue')),
      },
      ...complianceRoutes,
      ...systemManageRoutes,
      ...farmer,
      ...insure,
      ...inspection,
      ...review,
      ...paymentManage,
      ...policy,
      ...endorse,
      ...infoModify,
      ...fileManagement,
      ...taskCenter,
      ...riskAssess,
    ]),
};
