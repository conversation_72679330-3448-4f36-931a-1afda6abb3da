import type { RouterConfig } from '@nuxt/schema';
import complianceRoutes from '@/router/compliance';
import systemManageRoutes from '@/router/systemManage';
import farmer from '@/router/farmer';
import insure from '@/router/insure';
import inspection from '@/router/inspection';
import review from '@/router/review';
import paymentManage from '@/router/paymentManage';
import policy from '@/router/policy';
import endorse from '@/router/endorse';
import infoModify from '@/router/infoModify';
import fileManagement from '@/router/fileManagement';
import taskCenter from '@/router/taskCenter';
import riskAssess from '@/router/riskAssess';

// 通过路由配置可以选择性覆盖或扩展你的路由，返回null或者undefined，将回退到默认路由（由pages目录自动生成）
export default {
  routes: (_routes: RouterConfig): RouterConfig =>
    _routes.concat([
      {
        path: '/',
        meta: {},
        redirect: '/home',
      },
      {
        path: '/home',
        name: 'home',
        meta: {
          title: '首页',
        },
        component: () => import('@/pages/home/<USER>'),
      },
      {
        path: '/noAuth',
        name: 'noAuth',
        meta: {
          title: '暂无权限',
        },
        component: () => import('@/pages/noAuth/index.vue'),
      },
      {
        path: '/clearCache',
        name: 'clearCache',
        meta: {
          title: '清除缓存',
        },
        component: () => import('@/pages/clearCache/index.vue'),
      },
      ...complianceRoutes,
      ...systemManageRoutes,
      ...farmer,
      ...insure,
      ...inspection,
      ...review,
      ...paymentManage,
      ...policy,
      ...endorse,
      ...infoModify,
      ...fileManagement,
      ...taskCenter,
      ...riskAssess,
    ]),
};
