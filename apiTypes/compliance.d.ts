import type { PageParams } from './interface';

export interface InterfaceCodeName {
  idParmThrdPtyPlatInterface: string;
  thirdPartyPlatformInterfaceName: string;
}

export interface PlatformCodeName {
  idParmThrdPtyPlatBase: string;
  thirdPartyPlatformNo: string;
  platformCodeName: string;
}

export interface ParameterType {
  parameterClassName: string;
  parameterClassNo: string;
}

export interface PlatformBaseParams {
  belongBusinessDepartmentNo: string | undefined; // 机构
  thirdPartyPlatformNo: string | undefined; // 平台名称
  parameterClassNo: string | undefined; // 类型名称
}

export interface CallSceneLis {
  callScenesCodeLabel: string;
  callScenesCode: string;
}

export interface GetPlatformPageType extends PageParams {
  idParmThrdPtyPlatInterface?: string;
  thirdPartyPlatformName?: string;
}

export interface PlatformBaseType {
  belongBusinessDepartmentNo: string | undefined;
  contactNumber: string;
  departmentNoAndName: string;
  effectiveTime: string;
  idParmThrdPtyPlatBase: string | undefined;
  invalidTime: string;
  platformServiceAddress: string;
  remark: string;
  serviceAuthorizationAccountNo: string;
  serviceAuthorizationPassword: string;
  serviceProviderName: string;
  supplierContactName: string;
  thirdPartyPlatformName: string;
  thirdPartyPlatformNo: string;
  attDocSaveList: Array<
    Partial<{
      businessType: string;
      fileKey: string;
      fileName: string;
      fileSize: string;
      iobsBucketName: string;
      storageTypeCode: string;
      attachmentClassNo?: string;
    }>
  >;
  attachmentClassNo?: string;
  platformDockMethod?: string;
  serviceDockAddress?: string;
}

// 查看接口列表入参
export interface GetInterfaceListParams extends PageParams {
  belongBusinessDepartmentNo?: string;
  idParmThrdPtyPlatBase?: string;
  idParmThrdPtyPlatInterface?: string;
}

// 接口列表出参
export interface InterfaceBaseType {
  belongBusinessDepartmentNo: string;
  connectionTimeoutTime: number | null;
  departmentNoAndName: string;
  effectiveTime: string;
  idParmThrdPtyPlatBase: string | undefined;
  idParmThrdPtyPlatInterface: string;
  interfaceAuthorizationAccountNo: string;
  interfaceAuthorizationPassword: string;
  pageUrl: string;
  interfaceServiceAddress: string;
  interfaceVersion: string;
  invalidTime: string;
  realtimeFlagCode: number | undefined;
  requestMessageFormatCode: string | undefined;
  requestTypeCode: string | undefined;
  scheduleExecutionBeginTime?: string;
  scheduleExecutionEndTime: string;
  scheduleFlagCode: string | number | null;
  thirdPartyPlatformInterfaceExecutionSequence: number | null;
  parmThrdPtyPlatItfcReqtMsgTmplDto?: {
    idParmThrdPtyPlatItfcReqtMsgTmpl?: string;
    requestMessageTemplateFileName: string;
    requestMessageTemplateFileAddress: string;
  };
  callScenesCode: string | undefined;
  thirdPartyPlatformInterfaceName: string;
  thirdPartyPlatformInterfaceNo: string;
  thirdPartyPlatformName: string;
  encryptionFlag: string | undefined;
  remark: string;
  revisionIssueFlag: number | string;
  dockItfcClntIdtf: string;
  dockItfcClntIdttIdtf: string;
  dockItfcSectKey: string;
  platAsnyHndlFlag: string;
  encryptionSymbol?: string;
  callScenesCodeLabel?: string;
  imageUploadLimitSize: string;
  dataReportSetting?: string;
}

export interface TemplateType {
  documentAddress: string;
  documentName: string;
}

// 平台基础数据维护-列表入参
export interface DataMaintenancePageParams extends PageParams {
  belongBusinessDepartmentNo: string;
  parameterClassNo?: string;
  thirdPartyPlatformNo: string;
}

// 平台基础数据维护-列表出参
export interface ParameterBaseType {
  parameterClassName: string;
  parameterClassNo: string;
  parameterCode: string;
  parameterName: string;
  thirdPartyPlatformName: string;
  thirdPartyPlatformNo: string;
  idParmThrdPtyPlatCommonParameter: string;
}

// 平台基础数据维护-新增入参
export interface AddParameterParams {
  thirdPartyPlatformNo: string;
  parameterClassNo: string;
  parameterCode: string;
  parameterName: string;
}

// 接口数据映射明细映射配置列表
export interface ParamCodeListType {
  belongBusinessDepartmentNo: string;
  pageNum: number;
  pageSize: number;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}

// 【参数数据类型】新增业务代码映射配置（保存接口）
export interface AddPlatMapingConfigType {
  effectiveTime: string;
  idParmComnParmPaAndThrdPtyPlatMapping: number;
  invalidTime: string;
  parameterClassNo: string;
  pinganDataValueNo: string;
  platformDataValueNo: string;
  thirdPartyPlatformNo: string;
}

// 数据映射明细新增页面平台下拉列表
export interface PlatCommonParameterListByParamsType {
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}

// 数据映射明细新增页面-明细查询接口
export interface QueryPlatMapingConfigType {
  parameterClassNo: string;
  pinganDataValueNo: string;
  thirdPartyPlatformNo: string;
}

// 处理方案配置-新增
export interface AddErrorHandleType {
  idParmThrdPtyPlatErrorHandleProposal?: string;
  thirdPartyPlatformNo: string;
  errorNoComment: string;
  errorCategoryNo: string;
  itAnalysisResult?: string;
  errorNo: string;
  handleProposal: string;
  invalidTime?: string;
  effectiveTime?: string;
  remark?: string;
  errorCategoryNoName?: string;
}

// 查询错误信息配置列表接口
export interface QueryErrorHandleListType {
  belongBusinessDepartmentNo: string;
  pageNum?: number;
  pageSize?: number;
  thirdPartyPlatformNo?: string;
  errorNo?: string;
  errorNoComment?: string;
  errorCategoryNo?: string;
  ruleMatchMethod?: string | undefined;
  failureType?: string | undefined;
  failureTypeSubdivision?: string | undefined;
  handleProposal: string | undefined;
}

// 编辑错误信息配置接口
export interface EditErrorHandleType {
  idParmThrdPtyPlatErrorHandleProposal: string | undefined;
  thirdPartyPlatformNo?: string;
  errorNoComment?: string;
  errorCategoryNo?: string;
  itAnalysisResult?: string;
  errorNo?: string;
  handleProposal?: string;
  invalidTime?: string;
  effectiveTime?: string;
  remark?: string;
  idParmErrorHandleProposal: string;
}

// 新增/编辑错误信息基础配置接口
export interface ErrorBaseType {
  belongBusinessDepartmentNo?: string;
  belongBusinessDepartmentName: string;
  thirdPartyPlatformNo: string;
  thirdPartyPlatformName: string;
  errorNoComment?: string;
  errorCategoryNo?: string;
  itAnalysisResult?: string;
  errorNo?: string;
  handleProposal?: string;
  invalidTime?: string;
  effectiveTime?: string;
  remark?: string;
  updatedDate?: string;
  idParmThrdPtyPlatErrorHandleProposal: string;
  ruleMatchMethod: string | undefined;
  ruleMatchContent: string | undefined;
  fuzzyMatchFlag: string | undefined;
  failureType: string | undefined;
  failureTypeSubdivision: string | undefined;
  idParmErrorHandleProposal: string;
}

// 错误代码清单列表
export interface ErrorColumn {
  belongBusinessDepartmentNo: string;
  belongBusinessDepartmentName: string;
  thirdPartyPlatformNo: string;
  thirdPartyPlatformName: string;
  errorNo: string;
  errorNoComment: string;
  errorCategoryNoName: string;
  itAnalysisResult: string;
  handleProposal: string;
  updatedDate: string;
  idParmThrdPtyPlatErrorHandleProposal: string;
  ruleMatchMethod: string | undefined;
  failureType: string | undefined;
  failureTypeSubdivision: string | undefined;
  ruleMatchContent: string | undefined;
  fuzzyMatchFlag: string | undefined;
  idParmErrorHandleProposal: string;
  failureTypeName: string;
  failureTypeSubName: string;
  handleProposalName: string;
  remark: string;
}

// 平台区划维护列表页面平台下拉列表
export interface RegionDivisionsPlatformCodeName {
  platformCodeName: string;
  thirdPartyPlatformNo: string;
}

// 查看日志入参
export interface GetReportRecordParams extends PageParams {
  insuranceBusinessNo: string;
  insuranceBusinessNoTypeCode: string;
  thirdPartyPlatformInterfaceNo: string;
  premiumFinalId?: string;
}

// 上报情况导出入参
export interface GetResultParams {
  belongBusinessDepartmentNo: string; // 归属机构
  dockStatusCode: string; // 状态代码
  errorCategoryNo: string; // 错误分类代码
  insuranceBusinessNo: string; // 单证号
  insuranceBusinessNoTypeCode: string; // 单证号类型
  thirdPartyPlatformInterfaceNo: string; // 接口编码
  thirdPartyPlatformNo: string; // 平台编码
  updatedDateEnd: string; // 响应时间结束
  updatedDateStart: string; // 响应时间开始
  failureType: string | undefined;
  failureTypeSubdivision: string | undefined;
  handleProposal: string | undefined;
}

// 上报情况查询清单入参
export interface GetReportParams extends PageParams {
  belongBusinessDepartmentNo: string; // 归属机构
  dockStatusCode: string; // 状态代码
  errorCategoryNo: string; // 错误分类代码
  insuranceBusinessNo: string; // 单证号
  insuranceBusinessNoTypeCode: string; // 单证号类型
  thirdPartyPlatformInterfaceNo: string; // 接口编码
  thirdPartyPlatformNo: string; // 平台编码
  updatedDateEnd: string; // 响应时间结束
  updatedDateStart: string; // 响应时间开始
  failureType: string | undefined;
  failureTypeSubdivision: string | undefined;
  handleProposal: string | undefined;
  failureTypeSubName: string;
  failureTypeName: string;
  handleProposalName: string;
}

// 上报情况导出入参
export interface GetResultParams {
  belongBusinessDepartmentNo: string; // 归属机构
  dockStatusCode: string; // 状态代码
  errorCategoryNo: string; // 错误分类代码
  insuranceBusinessNo: string; // 单证号
  insuranceBusinessNoTypeCode: string; // 单证号类型
  thirdPartyPlatformInterfaceNo: string; // 接口编码
  thirdPartyPlatformNo: string; // 平台编码
  updatedDateEnd: string; // 响应时间结束
  updatedDateStart: string; // 响应时间开始
  failureTypeSubdivision: string | undefined;
  handleProposal: string | undefined;
}

// 上报情况查询清单出参
export interface GetReportData {
  belongBusinessDepartmentName: string; // 归属机构名称
  deptCode: string; // 出单机构
  deptName: string; // 出单机构名称
  dockStatusName: string; // 状态名称
  handleProposal: string; // 平台编码
  idEvntAsynchronousTaskHandle: string;
  idEvntUpldThrdPtyPlatFinalResult: string; // 主键
  insuranceBusinessNo: string; // 单证号
  insuranceBusinessNoTypeCode: string; // 单证号类型编码
  insuranceBusinessNoTypeName: string; // 单证号类型名称
  platErrorReason: string; // 失败原因
  thirdPartyPlatformInterfaceNo: string; // 接口编码
  thirdPartyPlatformInterfaceName: string; // 接口名称
  thirdPartyPlatformName: string; // 平台名称
  updatedDate: string; // 响应时间
  needDocWindFlg: string;
  needCollPayFlag: boolean; // 支付接口是否需要展示详情入口
  needBlockOfLand: boolean;
  noNeedToDeal: boolean; // 无需处理按钮
  idEvntAsynchronousTaskHandle?: string;
  errorCategoryName: string;
}

export interface DepartmentNoType {
  belongBusinessDepartmentNo?: string;
  headDeptFlag?: boolean;
}

export interface DeptCodeName {
  belongBusinessDepartmentNo: string; // 机构号
  departmentNoAndName: string; // 机构名称
}

export interface LogReport {
  insuranceBusinessNo: string;
  insuranceBusinessNoTypeCode: string;
  requestTime: string;
  returnMessage: string;
  returnStatusName: string;
  updatedDate: string;
  uploadMessage: string;
  idEvntUpldThrdPtyPlatRec: string;
  ifDownUploadMessage: boolean;
  uploadLoading: boolean;
  updownLoading: boolean;
}

export interface ErrorCategory {
  errorCategoryNo: string;
  errorCategoryNoName: string;
}

export interface MappingConfigParams extends PageParams {
  belongBusinessDepartmentNo: string;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}
export interface ConfigHisType {
  belongBusinessDepartmentNo: string;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}

export interface MappingConfigColumn {
  belongBusinessDepartmentName: string;
  belongBusinessDepartmentNo: string;
  effectiveTime: string;
  invalidTime: string;
  parameterClassName: string;
  parameterClassNo: string;
  thirdPartyPlatformName: string;
  thirdPartyPlatformNo: string;
  updatedDate: string;
}

export interface MappingConfigHisType {
  belongBusinessDepartmentName: string;
  thirdPartyPlatformName: string;
  parameterClassNo: string;
  pinganDataCodeName: string;
  platformDataCodeName: string;
  effectiveTime: string;
}

export interface DetailMappingConfigColumn {
  pinganDataValueNo: string;
  pinganDataValueName: string;
  platformDataValueNo: string;
  platformDataValueName: string;
  updatedDate: string;
  effectiveTime: string;
  invalidTime: string;
  parameterClassName: string;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
  idParmComnParmPaAndThrdPtyPlatMapping: string;
}

export interface MappingConfigChangeHisType {
  active: boolean;
  updateRecord: string;
  updateTime: string;
}

export interface UploadedDocListParams {
  insuranceBusinessNo: string;
  thirdPartyPlatformInterfaceNo: string;
  pageSize: number;
  pageNum: number;
}

export interface UploadedDocListRes {
  policyNo: string;
  documentTypeName: string;
  uploadPath: string;
  status: string;
  errorMessage: string;
}

export interface UploadedCollPayListRes {
  policyNo?: string; // 保单号
  premiumTerm: string; // 期次
  dockStatusName: string; // 状态
  dockStatusCode?: string; // 状态
  idEvntUpldThrdPtyPlatFinalResult?: string;
  idEvntUpldThrdPtyPlatPremiumFinalResult?: string;
  insuranceBusinessNo: string;
  thirdPartyPlatformInterfaceNo: string;
  insuranceBusinessNoTypeCode: string;
  updatedDate: string;
  errorReason: string;
}

export interface UpdatePlatformInfoParams {
  policyNo: string;
  platformConfirmCode: string;
  thirdPartyPlatformNo: string;
  thirdPartyPlatformInterfaceNo: string;
  demandSequenceNo: string;
}

export interface DepartmentOptions {
  departmentNoAndName: string;
  belongBusinessDepartmentNo: string;
}

export interface QueryMapingConfigHisParams {
  belongBusinessDepartmentNo: string;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}

export interface SelectOptionsType {
  value: string;
  label: string;
}

export interface PlatformRes {
  parameterCode: string;
  thirdPartyPlatformNo: string;
  parameterName: string;
}

export interface PlatformEditForm {
  idParmThrdPtyPlatCommonParameter: string;
  thirdPartyPlatformNo: string;
  parameterClassNo: string;
  parameterCode: string;
  parameterName: string;
}
export interface ParameterCodeType {
  parameterCode: string;
  parameterName: string;
  idParmComnParm: string;
}
