export interface querySpecialPromiseApprovalDetailItem {
  createdDate: string;
  approvalName: string;
  approvalStatusName: string;
  approvalOpinions: string;
  createdBy?: string;
  status?: string;
  updatedDate?: string;
  approvalStatus?: string;
  approvalUm?: string;
}

export interface ApprovalDetailItem {
  key: number;
  name: string;
  dateTime: string;
  actionType: string;
  opinionDesc: string;
}
export interface QuerySpecialPromiseApprovalDetail {
  agrSpApprovalChainName: string;
  specialPromiseOpinionsList: querySpecialPromiseApprovalDetailItem[];
}

export interface ApprovalTaskItem {
  createdDate: string;
  createdBy: string;
  approvalName: string;
  updatedDate: string;
  approvalOpinions: string;
  approvalStatus: string;
}
