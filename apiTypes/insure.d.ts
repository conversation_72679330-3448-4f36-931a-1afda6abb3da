import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';
import type { SelectOptions } from './apiCommon';

export interface TenderProject {
  projectCode: string; // 项目编码
  projectName: string; // 项目名字
}

export interface CustomerInfo {
  idCustomerTypeMaintenance: string;
  customerAbbrName: string;
  lossRate: string;
}

export interface CustomerOptions {
  value: string;
  label: string;
  lossRate: string;
}

export interface RiskAgentInfo {
  agentCode: string;
  agentName: string;
}

export interface RiskBrokerInfo {
  brokerCode: string;
  chineseName: string;
}

export interface ConferInfo {
  conferNo: string;
  conferName: string;
}

export interface BusinessProject {
  list: Array<{
    projectCode: string;
    projectName: string;
  }>;
}

export interface IntroducerInfo {
  chineseName: string;
  agentName: string;
  business: string;
}

export interface BaseInfo {
  renewalType: string; // 续保类型 0-新保、1-续保
  saveStatus?: string; // 投保申请单状态
  preRiskNo?: string; // 投保申请单号
  departmentCode: string; // 出单机构
  productCode: string; // 产品名称
  productName: string; // 产品编码
  productVersion: string; // 产品版本
  govSubsidyType: string | undefined; // 补贴类型(1-中央补贴、2-地方补贴、3-商业性、4-其他补贴)
  applyPolicyType: string; // 投保方式：2-组织投保、1-非组织投保
  coinsuranceMark: string; // 共保标志 0：非共保 1：共保
  acceptInsuranceFlag: string; // 是否主承保：Y/N
  customerType: string | undefined; // 客户类型 01-重要客户 02-钩子客户 03-集团客户 04-非三类客户
  customerAbbrName?: string; // 客户简称
  lossRate: string; // 风险程度
  riskAllCode?: string; // 完整标的编码
  riskCode: string; // 标的编码
  riskLevel: string; // 标的层级
  insuredNumber: string; // 承保数量
  farmersCount: string; // 承保户数
  insuredRate: string; // 费率
  totalInsuredAmount: string; // 总保额
  yearOrderRateAll: string; // 总跟单费用率
  diseaseObservation?: string; // 疾病观察期
  insuranceUnit: string | undefined;
  insuredUnitChName: string | undefined;
  showProductTextFlag: string;
  riskAsmtNo?: string;
}

export interface Employee {
  employeeCode: string | undefined; // 业务员编码
  employeeName: string | undefined; // 业务员名称
  employeeProfCertifNo: string | undefined; // 业务员执业证号
  commisionScale?: string | undefined; // 占比
  mainEmployeeFlag?: string | undefined; // 主业务员标志：Y/N
}

export interface SaleInfo {
  developFlg: string; // 是否共展：Y/N
  channelSourceCode: string | undefined; // 渠道来源编码
  channelSourceDetailCode: string | undefined; // 渠道来源细分编码
  isTenderBusiness: string; // 项目来源 是否投标业务：1-是/遴选内  0-否/遴选外
  tenderBusinessName: string; // 项目名称
  tenderBusinessNo: string | undefined; // 项目编号
  isReportTuanjin: string; // 是否上报团金业绩  Y/N
  businessOpportunitySource?: string; // 商机来源
  tuanjinProjectNo?: string; // 团金E立项号
  presenterCode?: string; // 主介绍人代码
  presenterName?: string; // 主介绍人名称
  agentInfoList: AgentInfo[]; // 代理人
  brokerInfoList: BrokerInfo[]; // 经纪人
  employeeInfoList: Employee[]; // 业务员
}

export interface AgentInfo {
  agentCode: string; // 代理人代码
  agentName: string; // 代理人名称
  agentAgreementNo: string; // 代理协议号
  agencySaleName: string; // 中介销售人员姓名
  agencySaleProfCertifNo: string; // 中介销售人员职业资格证号码
}

export interface BrokerInfo {
  brokerCode: string; // 经纪人代码
  brokerName: string; // 代理人名称
  agencySaleName: string; // 中介销售人员姓名
  agencySaleProfCertifNo: string; // 中介销售人员职业资格证号码
}

export interface AddressInfo {
  province: string; // 省编码
  provinceName: string; // 省名称
  city: string; // 市编码
  cityName: string; // 市名称
  town: string; // 区编码
  townName: string; // 区名称
  county: string; // 镇编码
  countyName: string; // 镇名称
  village: string; // 村编码
  villageName: string; // 村名称
  address: string; // 完整地址
}

export interface ExtendInfo {
  preRiskNo: string; // 投保申请单号
  fixNo: string; // 定价编号
  farmerListNo: string; // 关联清单编号
  futureCompanyNo: string; // 期货公司编码
  futureCompanyName: string; // 期货公司名称
}

export interface RiskAgrList {
  agriculturalRiskObjectDetailCode: string; //  农险标的细分代码
  agriculturalRiskObjectDetailName: string; //  农险标的细分名称
  insuredNumber: string; // 投保数量
  insureUnit: string; //  投保数量单位
}

export interface FormState {
  baseInfo: BaseInfo;
  saleInfo: SaleInfo;
  riskAddressInfoList: AddressInfo[];
  extendInfo: ExtendInfo;
  riskAgrList?: RiskAgrList[];
}

// 投保申请
export interface RiskInfoResponse {
  code: string;
  msg: string;
  data: FormState;
}

// 特约
export interface SpecialListItem {
  idSpecialPromiseDefine: string;
  departmentCode: string;
  departmentName: string;
  marketProductName: string;
  specialPromiseCode: string;
  specialPromiseName: string;
  specialPromiseType: string;
  specialPromiseTypeName: string;
  specialPromiseDesc: string;
  updateTime: string;
  createdBy: string;
  specialPromiseStatus: string;
  currentApprovalName: string;
  createdRole: string;
  disabled: boolean;
  display: boolean;
  xSpecialPromiseList?: SpecialContentInfo[];
  currentApprovalStatus: string;
}

export interface SpecialListResult {
  list: SpecialListItem[];
  records?: SpecialListItem[];
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
  size?: number;
  current?: number;
}

export interface SpecialListRes {
  code: string;
  msg: string;
  data: SpecialListResult;
}

export interface SpecialContentInfo {
  type: string;
  fixedMsg?: string;
  dataMap: {
    needTextMax?: string;
    needRange?: string;
    needDecimals?: string;
    mixNum?: string;
    maxNum?: string;
    decimalPlaces?: string;
    textMaxLength?: string;
  };
  tips?: string;
  value?: string;
  placeholder?: string;
}

export interface SpecialDeptInfo {
  adaptDepartmentCode: string;
  adaptDepartmentName: string;
}

export interface SpecialProductInfo {
  technicProductCode: string;
  technicProductName: string;
  marketProductCode: string;
  marketProductName: string;
}

export interface TransProductItem {
  technicProductCode: string;
  technicProductName: string;
  marketProductList: Array<{
    marketProductCode: string;
    marketProductName: string;
  }>;
  marketProductCodeList: Array<string>;
  technicProductOptions?: Array<SelectOptions>;
  marketProductOptions: Array<SelectOptions>;
}

interface DeptOptionsType {
  label: string;
  value: string;
}
export interface SpecialDetail {
  idSpecialPromiseDefine: string;
  lockVersion: string;
  specialPromiseDept: string;
  departmentName?: string;
  specialPromiseCode: string;
  specialPromiseName: string;
  specialPromiseType: string;
  specialPromiseTypeName: string;
  specialPromiseProRelList: Array<SpecialProductInfo>;
  specialPromiseDeptRelList: Array<SpecialDeptInfo> | DeptOptionsType[] | CheckboxValueType[];
  specialPromiseControlList: Array<SpecialContentInfo>;
  technicProduct?: string;
  marketProduct?: string;
  adaptDepartment?: string;
  transProductList: Array<TransProductItem>;
  isShowAudit?: string;
}

export interface SpecialDetailRes {
  code: string;
  msg: string;
  data: SpecialDetail;
}

export interface PreRiskRes {
  preRiskNo: string;
  saveStatus: string;
  resultData: {
    message: string;
  };
}
