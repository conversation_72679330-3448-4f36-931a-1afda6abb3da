import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/historicalDataManage',
    component: () => import('@/pages/systemManage/historicalDataManage/historicalDataManage.vue'),
    name: 'historicalDataManage',
    meta: {
      title: '历史数据补传',
    },
  },
  {
    path: '/fieldDataManage',
    component: () => import('@/pages/systemManage/fieldDataManage/fieldDataManage.vue'),
    name: 'fieldDataManage',
    meta: {
      title: '监管字段回补',
    },
  },
  {
    path: '/sftpOperation',
    component: () => import('@/pages/systemManage/sftpOperation/sftpOperation.vue'),
    name: 'sftpOperation',
    meta: {
      title: 'SFTP文件操作',
    },
  },
  {
    path: '/specialSetting',
    component: () => import('@/pages/systemManage/specialManage/specialSetting.vue'),
    name: 'specialSetting',
    meta: {
      title: '特约设置',
    },
  },
  {
    path: '/specialEdit',
    component: () => import('@/pages/systemManage/specialManage/specialEdit.vue'),
    name: 'specialEdit',
    meta: {
      title: '特约编辑',
    },
  },
  {
    path: '/specialDetail',
    component: () => import('@/pages/systemManage/specialManage/specialDetail.vue'),
    name: 'specialDetail',
    meta: {
      title: '特约详情',
    },
  },
  {
    path: '/specialApproval',
    component: () => import('@/pages/systemManage/specialManage/specialApproval.vue'),
    name: 'specialApproval',
    meta: {
      title: '特约审批',
    },
  },
  {
    path: '/specialAudit',
    component: () => import('@/pages/systemManage/specialManage/specialAudit.vue'),
    name: 'specialAudit',
    meta: {
      title: '特约审核',
    },
  },
  {
    path: '/insurePlanManage',
    component: () => import('@/pages/systemManage/insurePlanManage/insurePlanManage.vue'),
    name: 'insurePlanManage',
    meta: {
      title: '保险方案配置',
    },
  },
  {
    path: '/switchManage',
    component: () => import('@/pages/systemManage/switchManage/switchManage.vue'),
    name: 'switchManage',
    meta: {
      title: '开关配置',
    },
  },
  {
    path: '/eoaRoleUmConfig',
    component: () => import('@/pages/systemManage/eoaRoleUmConfig/eoaRoleUmConfig.vue'),
    name: 'eoaRoleUmConfig',
    meta: {
      title: 'EOA审批角色UM配置',
    },
  },
  {
    path: '/insurePlanEdit',
    component: () => import('@/pages/systemManage/insurePlanManage/insurePlanEdit.vue'),
    name: 'insurePlanEdit',
    meta: {
      title: '保险方案编辑',
    },
  },
  {
    path: '/costAllocation',
    component: () => import('@/pages/systemManage/costAllocation/costAllocation.vue'),
    name: 'costAllocation',
    meta: {
      title: '费用配置',
    },
  },
  {
    path: '/costDetail',
    component: () => import('@/pages/systemManage/costAllocation/costDetail.vue'),
    name: 'costDetail',
    meta: {
      title: '费用配置详情',
    },
  },
  {
    path: '/costSetting',
    component: () => import('@/pages/systemManage/costAllocation/costSetting.vue'),
    name: 'costSetting',
    meta: {
      title: '费用配置配置',
    },
  },
  {
    path: '/publicityAllocation',
    component: () => import('@/pages/systemManage/publicityAllocation/publicityAllocation.vue'),
    name: 'publicityAllocation',
    meta: {
      title: '公示配置',
    },
  },
  {
    path: '/eoaApplication',
    component: () => import('@/pages/systemManage/eoaApplication/eoaApplication.vue'),
    name: 'eoaApplication',
    meta: {
      title: '签报申请',
    },
  },
  {
    path: '/eoaChainSetting',
    component: () => import('@/pages/systemManage/eoaChainSetting/eoaChainSetting.vue'),
    name: 'eoaChainSetting',
    meta: {
      title: 'EOA审批链设置',
    },
  },
  {
    path: '/umsInspectionApplication',
    component: () => import('@/pages/systemManage/umsIframe/umsInspectionApplication.vue'),
    name: 'umsInspectionApplication',
    meta: {
      title: '拍照农户数比例规则',
    },
  },
  {
    path: '/umsUnderwriteApplication',
    component: () => import('@/pages/systemManage/umsIframe/umsUnderwriteApplication.vue'),
    name: 'umsUnderwriteApplication',
    meta: {
      title: '资料管理规则',
    },
  },
  {
    path: '/umsApproveApplication',
    component: () => import('@/pages/systemManage/umsIframe/umsApproveApplication.vue'),
    name: 'umsApproveApplication',
    meta: {
      title: '分级核保配置',
    },
  },
  {
    path: '/umsFeeControl',
    component: () => import('@/pages/systemManage/umsIframe/umsFeeControl.vue'),
    name: 'umsFeeControl',
    meta: {
      title: '费用管控规则',
    },
  },
  {
    path: '/umsPhoneRule',
    component: () => import('@/pages/systemManage/umsIframe/umsPhoneRule.vue'),
    name: 'umsPhoneRule',
    meta: {
      title: '联系电话重复校验规则',
    },
  },
  {
    path: '/umsInsureRule',
    component: () => import('@/pages/systemManage/umsIframe/umsInsureRule.vue'),
    name: 'umsInsureRule',
    meta: {
      title: '大户单独投保规则',
    },
  },
  {
    path: '/umsInsureNumRule',
    component: () => import('@/pages/systemManage/umsIframe/umsInsureNumRule.vue'),
    name: 'umsInsureNumRule',
    meta: {
      title: '投保数量雷同规则',
    },
  },
  {
    path: '/umsPaymentRule',
    component: () => import('@/pages/systemManage/umsIframe/umsPaymentRule.vue'),
    name: 'umsPaymentRule',
    meta: {
      title: '缴费止期管理规则',
    },
  },
  {
    path: '/typeCustomerMaintenance',
    component: () => import('@/pages/systemManage/typeCustomerMaintenance/typeCustomerMaintenance.vue'),
    name: 'typeCustomerMaintenance',
    meta: {
      title: '类型客户维护',
    },
  },
  {
    path: '/umsPlotCollectRule',
    component: () => import('@/pages/systemManage/umsIframe/umsPlotCollectRule.vue'),
    name: 'umsPlotCollectRule',
    meta: {
      title: '地块采集农户数比例规则',
    },
  },
  {
    path: '/umsInspectionPhotoRule',
    component: () => import('@/pages/systemManage/umsIframe/umsInspectionPhotoRule.vue'),
    name: 'umsInspectionPhotoRule',
    meta: {
      title: '验标照片必拍及数量管控规则',
    },
  },
  {
    path: '/umsPlotRule',
    component: () => import('@/pages/systemManage/umsIframe/umsPlotRule.vue'),
    name: 'umsPlotRule',
    meta: {
      title: '地块距离校验规则',
    },
  },
  {
    path: '/umsLoadRule',
    component: () => import('@/pages/systemManage/umsIframe/umsLoadRule.vue'),
    name: 'umsLoadRule',
    meta: {
      title: '地块面积偏差率规则',
    },
  },
  {
    path: '/umsAnbRule',
    component: () => import('@/pages/systemManage/umsIframe/umsAnbRule.vue'),
    name: 'umsAnbRule',
    meta: {
      title: '爱农宝自核规则',
    },
  },
  {
    path: '/umsAnimalRule',
    component: () => import('@/pages/systemManage/umsIframe/umsAnimalRule.vue'),
    name: 'umsAnimalRule',
    meta: {
      title: '畜龄风险管控规则',
    },
  },
  {
    path: '/umsAnimalRecognizeRule',
    component: () => import('@/pages/systemManage/umsIframe/umsAnimalRecognizeRule.vue'),
    name: 'umsAnimalRecognizeRule',
    meta: {
      title: '牛脸识别数量占比规则',
    },
  },
  {
    path: '/umsSmartRecognizeRule',
    component: () => import('@/pages/systemManage/umsIframe/umsSmartRecognizeRule.vue'),
    name: 'umsSmartRecognizeRule',
    meta: {
      title: '智能点数数量占比规则',
    },
  },
  {
    path: '/umsPhotoRule',
    component: () => import('@/pages/systemManage/umsIframe/umsPhotoRule.vue'),
    name: 'umsPhotoRule',
    meta: {
      title: '重复照片数量校验规则',
    },
  },
  {
    path: '/umsAnbRateRule',
    component: () => import('@/pages/systemManage/umsIframe/umsAnbRateRule.vue'),
    name: 'umsAnbRateRule',
    meta: {
      title: '爱农宝核身率管控规则',
    },
  },
  {
    path: '/umsMarkingApprovalChain',
    component: () => import('@/pages/systemManage/umsIframe/umsMarkingApprovalChain.vue'),
    name: 'umsMarkingApprovalChain',
    meta: {
      title: '批改核保审批链配置',
    },
  },
  {
    path: '/umsBankFourRule',
    component: () => import('@/pages/systemManage/umsIframe/umsBankFourRule.vue'),
    name: 'umsBankFourRule',
    meta: {
      title: '银行卡四要素认证规则',
    },
  },
  {
    path: '/umsWatermarkedPhotoRule',
    component: () => import('@/pages/systemManage/umsIframe/umsWatermarkedPhotoRule.vue'),
    name: 'umsWatermarkedPhotoRule',
    meta: {
      title: '水印照片日期校验规则',
    },
  },
  {
    path: '/umsCheckAfterSupplement',
    component: () => import('@/pages/systemManage/umsIframe/umsCheckAfterSupplement.vue'),
    name: 'umsCheckAfterSupplement',
    meta: {
      title: '验标后补规则',
    },
  },
  {
    path: '/umsCheckCopy',
    component: () => import('@/pages/systemManage/umsIframe/umsCheckCopy.vue'),
    name: 'umsCheckCopy',
    meta: {
      title: '验标任务复用规则',
    },
  },
  {
    path: '/umsLandlineRatio',
    component: () => import('@/pages/systemManage/umsIframe/umsLandlineRatio.vue'),
    name: 'umsLandlineRatio',
    meta: {
      title: '支持座机号比例规则',
    },
  },
  {
    path: '/umsCheckReback',
    component: () => import('@/pages/systemManage/umsIframe/umsCheckReback.vue'),
    name: 'umsCheckReback',
    meta: {
      title: '保单验标修改规则',
    },
  },
  {
    path: '/umsInsureRiskTip',
    component: () => import('@/pages/systemManage/umsIframe/umsInsureRiskTip.vue'),
    name: 'umsInsureRiskTip',
    meta: {
      title: '核保风险提示配置',
    },
  },
  {
    path: '/umsCheckOperDispatchArea',
    component: () => import('@/pages/systemManage/umsIframe/umsCheckOperDispatchArea.vue'),
    name: 'umsCheckOperDispatchArea',
    meta: {
      title: '验标任务可派发的协保员范围',
    },
  },
];

export default routes;
