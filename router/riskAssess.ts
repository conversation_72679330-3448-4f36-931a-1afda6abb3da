import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/riskAssessApply',
    component: () => import('@/pages/riskAssessOnline/riskAssessApply.vue'),
    name: 'riskAssessApply',
    meta: {
      title: '风险评估申请',
    },
  },
  {
    path: '/riskAssessDetail',
    component: () => import('@/pages/riskAssessOnline/riskAssessApply.vue'),
    name: 'riskAssessDetail',
    meta: {
      title: '风险评估详情',
    },
  },
  {
    path: '/riskAssessCheck',
    component: () => import('@/pages/riskAssessOnline/riskAssessCheck.vue'),
    name: 'riskAssessCheck',
    meta: {
      title: '风险评估查询',
    },
  },
  {
    path: '/riskAssessAudit',
    component: () => import('@/pages/riskAssessOnline/riskAssessAudit.vue'),
    name: 'riskAssessAudit',
    meta: {
      title: '风险评估审核',
    },
  },
];

export default routes;
