import { defineNuxtModule } from '@nuxt/kit';
import { vitePerformancePlugin, devPerformancePlugin, buildOptimizationPlugin } from '../plugins/vite-performance';

export default defineNuxtModule({
  meta: {
    name: 'performance-optimizer',
    configKey: 'performanceOptimizer',
    compatibility: {
      nuxt: '^3.0.0'
    }
  },
  defaults: {
    enabled: true,
    development: true,
    production: true,
    monitoring: true
  },
  setup(options, nuxt) {
    if (!options.enabled) return;
    
    // 开发环境优化
    if (nuxt.options.dev && options.development) {
      nuxt.hook('vite:extendConfig', (config) => {
        config.plugins = config.plugins || [];
        config.plugins.push(
          vitePerformancePlugin(),
          devPerformancePlugin()
        );
        
        // 开发环境特定优化
        config.server = {
          ...config.server,
          hmr: {
            ...config.server?.hmr,
            overlay: false,
          },
          // 预热文件
          warmup: {
            clientFiles: [
              './pages/home/<USER>',
              './layouts/default.vue',
              './components/ui/**/*.vue',
              './composables/**/*.ts'
            ]
          }
        };
        
        // 优化依赖预构建
        config.optimizeDeps = {
          ...config.optimizeDeps,
          include: [
            ...(config.optimizeDeps?.include || []),
            'vue',
            'vue-router',
            'pinia',
            'ant-design-vue/es/button',
            'ant-design-vue/es/input',
            'ant-design-vue/es/form',
            'ant-design-vue/es/table',
            'ant-design-vue/es/modal',
            'dayjs',
            'lodash-es'
          ],
          exclude: [
            ...(config.optimizeDeps?.exclude || []),
            '@visactor/vtable',
            'leaflet',
            'html2canvas'
          ]
        };
      });
      
      // 开发环境钩子
      nuxt.hook('dev:ssr-logs', (logs) => {
        if (options.monitoring) {
          const errors = logs.filter(log => log.level === 'error');
          if (errors.length > 0) {
            console.warn(`⚠️ 发现 ${errors.length} 个 SSR 错误`);
          }
        }
      });
    }
    
    // 生产环境优化
    if (!nuxt.options.dev && options.production) {
      nuxt.hook('vite:extendConfig', (config) => {
        config.plugins = config.plugins || [];
        config.plugins.push(
          vitePerformancePlugin(),
          buildOptimizationPlugin()
        );
        
        // 生产环境构建优化
        config.build = {
          ...config.build,
          minify: 'esbuild',
          cssCodeSplit: true,
          chunkSizeWarningLimit: 1000,
          rollupOptions: {
            ...config.build?.rollupOptions,
            output: {
              ...config.build?.rollupOptions?.output,
              // 优化输出文件名
              chunkFileNames: '_nuxt/[name]-[hash].js',
              assetFileNames: '_nuxt/[name]-[hash].[ext]',
            }
          }
        };
      });
    }
    
    // 通用钩子
    nuxt.hook('build:before', () => {
      if (options.monitoring) {
        console.log('🚀 性能优化模块已启用');
        console.log(`   开发环境优化: ${options.development ? '✅' : '❌'}`);
        console.log(`   生产环境优化: ${options.production ? '✅' : '❌'}`);
        console.log(`   性能监控: ${options.monitoring ? '✅' : '❌'}`);
      }
    });
    
    nuxt.hook('build:done', () => {
      if (options.monitoring) {
        console.log('✅ 性能优化构建完成');
      }
    });
    
    // 监控构建性能
    if (options.monitoring) {
      let buildStartTime: number;
      
      nuxt.hook('build:before', () => {
        buildStartTime = Date.now();
      });
      
      nuxt.hook('build:done', () => {
        const buildTime = Date.now() - buildStartTime;
        const buildTimeSeconds = (buildTime / 1000).toFixed(2);
        
        console.log(`📊 构建性能统计:`);
        console.log(`   总构建时间: ${buildTimeSeconds}s`);
        
        if (buildTime > 120000) { // > 2分钟
          console.log(`💡 性能建议:`);
          console.log(`   - 构建时间较长，考虑优化代码分割策略`);
          console.log(`   - 检查是否有大型依赖可以异步加载`);
        }
      });
    }
  }
});
