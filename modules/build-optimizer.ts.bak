import { defineNuxtModule } from '@nuxt/kit';
import { buildOptimizerPlugin, depOptimizationPlugin, devPerformancePlugin } from '../plugins/vite-build-optimizer';

export default defineNuxtModule({
  meta: {
    name: 'build-optimizer',
    configKey: 'buildOptimizer',
    compatibility: {
      nuxt: '^3.0.0'
    }
  },
  defaults: {
    enabled: true,
    development: true,
    production: true
  },
  setup(options, nuxt) {
    if (!options.enabled) return;
    
    // 开发环境优化
    if (nuxt.options.dev && options.development) {
      nuxt.hook('vite:extendConfig', (config) => {
        config.plugins = config.plugins || [];
        config.plugins.push(
          depOptimizationPlugin(),
          devPerformancePlugin()
        );
      });
    }
    
    // 生产环境优化
    if (!nuxt.options.dev && options.production) {
      nuxt.hook('vite:extendConfig', (config) => {
        config.plugins = config.plugins || [];
        config.plugins.push(buildOptimizerPlugin());
      });
    }
    
    // 添加构建钩子
    nuxt.hook('build:before', () => {
      console.log('🚀 构建优化模块已启用');
    });
    
    nuxt.hook('build:done', () => {
      console.log('✅ 构建优化完成');
    });
  }
});
