// 打印日志
export default defineNitroPlugin((nitro) => {
  nitro.hooks.hook('render:html', (html, { event }) => {
    console.log(event, '构建html之前', new Date().getTime());
  });

  nitro.hooks.hook('render:response', (response, { event }) => {
    console.log(event, '发送响应之前', new Date().getTime());
  });

  nitro.hooks.hook('beforeResponse', (event) => {
    console.log(event, '==请求==beforeResponse', new Date().getTime());
  });

  nitro.hooks.hook('afterResponse', (event) => {
    console.log(event, '==请求==afterResponse', new Date().getTime());
  });

  nitro.hooks.hook('request', (event) => {
    console.log(event, '==请求==request', new Date().getTime());
  });
});
