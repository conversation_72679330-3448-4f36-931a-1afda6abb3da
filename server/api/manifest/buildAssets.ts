import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

export default defineEventHandler(async (event) => {
  // 使用 import.meta.url 获取当前文件路径
  const currentFileUrl = import.meta.url;
  const currentFilePath = fileURLToPath(currentFileUrl);
  const currentDir = path.dirname(currentFilePath);
  const rootDir = path.resolve(currentDir, '../');
  const publicDir = path.join(rootDir, 'public/_nuxt');
  console.log(event, '当前路径---', currentDir, rootDir, publicDir);
  const cssFiles: string[] = [];
  const jsFiles: string[] = [];

  try {
    // 递归遍历目录获取所有文件
    async function walkDir(dir: string) {
      const files = await fs.promises.readdir(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.promises.stat(filePath);

        if (stat.isDirectory()) {
          await walkDir(filePath);
        } else {
          // 获取相对于 public/_nuxt 目录的路径
          const relativePath = path.relative(publicDir, filePath);
          // 根据文件扩展名分类，并排除包含 -legacy 的 JS 文件
          if (file.endsWith('.css')) {
            cssFiles.push(relativePath);
          } else if (file.endsWith('.js') && !file.includes('-legacy')) {
            jsFiles.push(relativePath);
          }
        }
      }
    }
    await walkDir(publicDir);
    return {
      cssFiles,
      jsFiles,
    };
  } catch (error) {
    console.error('Error reading build assets:', error);
    return {
      cssFiles: [],
      jsFiles: [],
    };
  }
});
