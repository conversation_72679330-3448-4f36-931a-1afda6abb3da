import { defineNuxtPlugin } from '#app';

export default defineNuxtPlugin({
  name: 'build-optimizer',
  setup() {
    // 这个插件主要用于运行时的性能监控
    if (process.client) {
      // 监控页面性能
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const nav = entry as PerformanceNavigationTiming;
            console.log('📊 页面加载性能:', {
              domContentLoaded: nav.domContentLoadedEventEnd - nav.domContentLoadedEventStart,
              loadComplete: nav.loadEventEnd - nav.loadEventStart,
              totalTime: nav.loadEventEnd - nav.fetchStart
            });
          }
        });
      });
      
      try {
        observer.observe({ entryTypes: ['navigation'] });
      } catch (e) {
        // 某些浏览器可能不支持
      }
      
      // 监控资源加载
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const slowResources = entries.filter(entry => entry.duration > 1000);
        
        if (slowResources.length > 0) {
          console.warn('⚠️ 慢资源检测:', slowResources.map(r => ({
            name: r.name,
            duration: r.duration
          })));
        }
      });
      
      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (e) {
        // 某些浏览器可能不支持
      }
    }
  }
});
