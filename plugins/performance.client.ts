// 性能监控插件（仅在客户端运行）

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (typeof window === 'undefined') return;

  // 性能监控类
  class PerformanceMonitor {
    private metrics: Record<string, number> = {};
    private observer: PerformanceObserver | null = null;

    constructor() {
      this.init();
    }

    private init() {
      // 监控页面加载性能
      this.measurePageLoad();
      
      // 监控资源加载
      this.measureResourceLoad();
      
      // 监控长任务
      this.measureLongTasks();
      
      // 监控布局偏移
      this.measureLayoutShift();
      
      // 监控首次内容绘制
      this.measurePaint();
    }

    private measurePageLoad() {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          
          if (navigation) {
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
            this.metrics.loadComplete = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.totalLoadTime = navigation.loadEventEnd - navigation.fetchStart;
            this.metrics.dnsLookup = navigation.domainLookupEnd - navigation.domainLookupStart;
            this.metrics.tcpConnect = navigation.connectEnd - navigation.connectStart;
            this.metrics.serverResponse = navigation.responseEnd - navigation.requestStart;
            this.metrics.domParsing = navigation.domComplete - navigation.domLoading;
            
            this.reportMetrics('page-load', this.metrics);
          }
        }, 0);
      });
    }

    private measureResourceLoad() {
      // 监控资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resource = entry as PerformanceResourceTiming;
            
            // 分类统计不同类型资源的加载时间
            const resourceType = this.getResourceType(resource.name);
            const loadTime = resource.responseEnd - resource.startTime;
            
            if (!this.metrics[`${resourceType}_count`]) {
              this.metrics[`${resourceType}_count`] = 0;
              this.metrics[`${resourceType}_total_time`] = 0;
            }
            
            this.metrics[`${resourceType}_count`]++;
            this.metrics[`${resourceType}_total_time`] += loadTime;
            
            // 记录慢资源
            if (loadTime > 1000) {
              console.warn(`慢资源加载: ${resource.name} - ${loadTime.toFixed(2)}ms`);
            }
          }
        });
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
    }

    private measureLongTasks() {
      if ('PerformanceObserver' in window) {
        try {
          const longTaskObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach((entry) => {
              if (entry.duration > 50) {
                console.warn(`长任务检测: ${entry.duration.toFixed(2)}ms`);
                
                if (!this.metrics.longTasks) {
                  this.metrics.longTasks = 0;
                  this.metrics.longTasksTotalTime = 0;
                }
                
                this.metrics.longTasks++;
                this.metrics.longTasksTotalTime += entry.duration;
              }
            });
          });
          
          longTaskObserver.observe({ entryTypes: ['longtask'] });
        } catch (e) {
          // 某些浏览器可能不支持 longtask
          console.log('Long task monitoring not supported');
        }
      }
    }

    private measureLayoutShift() {
      if ('PerformanceObserver' in window) {
        try {
          let clsValue = 0;
          
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            
            this.metrics.cumulativeLayoutShift = clsValue;
          });
          
          clsObserver.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.log('Layout shift monitoring not supported');
        }
      }
    }

    private measurePaint() {
      if ('PerformanceObserver' in window) {
        const paintObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime;
            } else if (entry.name === 'first-paint') {
              this.metrics.firstPaint = entry.startTime;
            }
          });
        });
        
        paintObserver.observe({ entryTypes: ['paint'] });
      }
    }

    private getResourceType(url: string): string {
      if (url.includes('.js')) return 'script';
      if (url.includes('.css')) return 'style';
      if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
      if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
      return 'other';
    }

    private reportMetrics(type: string, metrics: Record<string, number>) {
      // 在开发环境下输出性能指标
      if (process.dev) {
        console.group(`🚀 性能指标 - ${type}`);
        Object.entries(metrics).forEach(([key, value]) => {
          if (typeof value === 'number') {
            console.log(`${key}: ${value.toFixed(2)}ms`);
          }
        });
        console.groupEnd();
      }
      
      // 在生产环境下可以发送到监控服务
      // this.sendToAnalytics(type, metrics);
    }

    // 获取当前性能指标
    getMetrics() {
      return { ...this.metrics };
    }

    // 手动记录自定义指标
    mark(name: string) {
      performance.mark(name);
    }

    measure(name: string, startMark: string, endMark?: string) {
      try {
        if (endMark) {
          performance.measure(name, startMark, endMark);
        } else {
          performance.measure(name, startMark);
        }
        
        const measure = performance.getEntriesByName(name, 'measure')[0];
        if (measure) {
          this.metrics[name] = measure.duration;
          
          if (process.dev) {
            console.log(`📊 自定义指标 ${name}: ${measure.duration.toFixed(2)}ms`);
          }
        }
      } catch (e) {
        console.warn(`无法测量 ${name}:`, e);
      }
    }
  }

  // 创建性能监控实例
  const performanceMonitor = new PerformanceMonitor();

  // 提供全局访问
  return {
    provide: {
      performanceMonitor,
    },
  };
});

// 声明全局类型
declare module '#app' {
  interface NuxtApp {
    $performanceMonitor: InstanceType<typeof PerformanceMonitor>;
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $performanceMonitor: InstanceType<typeof PerformanceMonitor>;
  }
}
