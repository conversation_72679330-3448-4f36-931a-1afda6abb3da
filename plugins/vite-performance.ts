import type { Plugin } from 'vite';

/**
 * Vite 性能优化插件
 * 专注于提升开发和构建性能
 */
export function vitePerformancePlugin(): Plugin {
  let startTime: number;
  let isProduction: boolean;
  
  return {
    name: 'vite-performance',
    
    configResolved(config) {
      isProduction = config.command === 'build';
      startTime = Date.now();
      
      if (!isProduction) {
        console.log('🚀 开发服务器启动中...');
      } else {
        console.log('📦 生产构建开始...');
      }
    },
    
    buildStart() {
      if (isProduction) {
        console.log('⚡ 构建优化已启用');
      }
    },
    
    configureServer(server) {
      if (!isProduction) {
        // 开发服务器优化
        server.middlewares.use('/api', (req, res, next) => {
          // API 请求性能监控
          const start = Date.now();
          
          res.on('finish', () => {
            const duration = Date.now() - start;
            if (duration > 1000) {
              console.warn(`⚠️ 慢 API 请求: ${req.url} (${duration}ms)`);
            }
          });
          
          next();
        });
        
        // HMR 性能监控
        server.ws.on('connection', () => {
          const elapsed = Date.now() - startTime;
          console.log(`🔥 HMR 连接建立 (启动耗时: ${elapsed}ms)`);
        });
      }
    },
    
    handleHotUpdate(ctx) {
      const { file } = ctx;
      const start = Date.now();
      
      // 监控 HMR 更新性能
      return new Promise((resolve) => {
        setTimeout(() => {
          const duration = Date.now() - start;
          if (duration > 500) {
            console.warn(`⚠️ 慢 HMR 更新: ${file} (${duration}ms)`);
          }
          resolve(undefined);
        }, 0);
      });
    },
    
    transform(code, id) {
      // 开发环境下的快速转换优化
      if (!isProduction && id.includes('node_modules')) {
        // 跳过对 node_modules 的不必要转换
        return null;
      }
      
      // 生产环境下的代码优化
      if (isProduction) {
        // 移除开发环境的调试代码
        if (id.includes('.vue') || id.includes('.ts') || id.includes('.js')) {
          code = code.replace(/console\.(log|debug|info)\([^)]*\);?/g, '');
          code = code.replace(/\/\*\s*@__DEV__\s*\*\/[\s\S]*?\/\*\s*@__DEV__\s*\*\//g, '');
        }
      }
      
      return null;
    },
    
    generateBundle(options, bundle) {
      if (isProduction) {
        // 构建统计
        const chunks = Object.values(bundle).filter(chunk => chunk.type === 'chunk');
        const assets = Object.values(bundle).filter(asset => asset.type === 'asset');
        
        console.log('\n📊 构建统计:');
        console.log(`   Chunks: ${chunks.length}`);
        console.log(`   Assets: ${assets.length}`);
        
        // 分析大型文件
        const largeFiles = Object.entries(bundle)
          .map(([name, item]) => ({
            name,
            size: item.type === 'chunk' 
              ? Buffer.byteLength(item.code, 'utf8') 
              : typeof item.source === 'string' 
                ? Buffer.byteLength(item.source, 'utf8')
                : 0
          }))
          .filter(file => file.size > 500 * 1024) // > 500KB
          .sort((a, b) => b.size - a.size);
        
        if (largeFiles.length > 0) {
          console.log('\n🔍 大型文件 (>500KB):');
          largeFiles.slice(0, 5).forEach(file => {
            const sizeMB = (file.size / 1024 / 1024).toFixed(2);
            console.log(`   ${file.name}: ${sizeMB} MB`);
          });
        }
      }
    },
    
    writeBundle() {
      if (isProduction) {
        const elapsed = Date.now() - startTime;
        console.log(`\n✅ 构建完成，总耗时: ${(elapsed / 1000).toFixed(2)}s`);
        
        // 性能建议
        if (elapsed > 120000) { // > 2分钟
          console.log('\n💡 性能建议:');
          console.log('   - 考虑增加更多的代码分割');
          console.log('   - 检查是否有不必要的大型依赖');
          console.log('   - 考虑使用 SWC 替代 Babel');
        }
      }
    }
  };
}

/**
 * 开发环境专用性能插件
 */
export function devPerformancePlugin(): Plugin {
  return {
    name: 'dev-performance',
    apply: 'serve',
    
    configureServer(server) {
      let requestCount = 0;
      
      server.middlewares.use((req, res, next) => {
        requestCount++;
        
        // 每100个请求报告一次
        if (requestCount % 100 === 0) {
          console.log(`📈 已处理 ${requestCount} 个请求`);
        }
        
        next();
      });
      
      // 监控内存使用
      setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsedMB = (memUsage.heapUsed / 1024 / 1024).toFixed(2);
        
        if (memUsage.heapUsed > 500 * 1024 * 1024) { // > 500MB
          console.warn(`⚠️ 内存使用较高: ${heapUsedMB} MB`);
        }
      }, 30000); // 每30秒检查一次
    }
  };
}

/**
 * 构建优化插件
 */
export function buildOptimizationPlugin(): Plugin {
  return {
    name: 'build-optimization',
    apply: 'build',
    
    config(config) {
      // 构建优化配置
      config.build = {
        ...config.build,
        // 使用 esbuild 进行压缩
        minify: 'esbuild',
        // 启用 CSS 代码分割
        cssCodeSplit: true,
        // 优化 chunk 大小
        chunkSizeWarningLimit: 1000,
        // 启用 sourcemap（可选）
        sourcemap: process.env.BUILD_SOURCEMAP === 'true',
        // 优化 rollup 选项
        rollupOptions: {
          ...config.build?.rollupOptions,
          output: {
            ...config.build?.rollupOptions?.output,
            // 优化文件名
            chunkFileNames: '_nuxt/[name]-[hash].js',
            assetFileNames: '_nuxt/[name]-[hash].[ext]',
          }
        }
      };
      
      return config;
    }
  };
}
