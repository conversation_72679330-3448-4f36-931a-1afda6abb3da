import type { Plugin } from 'vite';
import { createHash } from 'crypto';

/**
 * Vite 构建优化插件
 * 用于优化构建性能和包大小
 */
export function buildOptimizerPlugin(): Plugin {
  const chunkCache = new Map<string, string>();
  
  return {
    name: 'build-optimizer',
    apply: 'build',
    
    // 优化模块解析
    resolveId(id, importer) {
      // 优化 Ant Design Vue 的导入
      if (id.startsWith('ant-design-vue/es/') && !id.includes('/style')) {
        return id;
      }
      
      // 优化 lodash 导入
      if (id === 'lodash') {
        return 'lodash-es';
      }
      
      return null;
    },
    
    // 优化代码转换
    transform(code, id) {
      // 移除开发环境的调试代码
      if (id.includes('node_modules')) {
        return null;
      }
      
      // 优化 console 语句
      if (process.env.NODE_ENV === 'production') {
        code = code.replace(/console\.(log|debug|info)\([^)]*\);?/g, '');
        code = code.replace(/console\.(warn|error)\([^)]*\);?/g, '');
      }
      
      return {
        code,
        map: null
      };
    },
    
    // 优化构建输出
    generateBundle(options, bundle) {
      // 分析和优化 chunk
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        
        if (chunk.type === 'chunk') {
          // 缓存相同内容的 chunk
          const contentHash = createHash('md5').update(chunk.code).digest('hex');
          
          if (chunkCache.has(contentHash)) {
            const cachedFileName = chunkCache.get(contentHash)!;
            console.log(`🔄 发现重复 chunk: ${fileName} -> ${cachedFileName}`);
          } else {
            chunkCache.set(contentHash, fileName);
          }
          
          // 分析大型 chunk
          const sizeKB = Buffer.byteLength(chunk.code, 'utf8') / 1024;
          if (sizeKB > 500) {
            console.log(`⚠️  大型 chunk 检测: ${fileName} (${sizeKB.toFixed(2)} KB)`);
            
            // 分析 chunk 内容
            const imports = chunk.imports || [];
            const modules = Object.keys(chunk.modules || {});
            
            console.log(`   - 导入数量: ${imports.length}`);
            console.log(`   - 模块数量: ${modules.length}`);
            
            // 建议分割策略
            if (modules.length > 50) {
              console.log(`   💡 建议: 考虑进一步分割此 chunk`);
            }
          }
        }
      });
    },
    
    // 构建完成后的优化
    writeBundle(options, bundle) {
      const chunks = Object.values(bundle).filter(chunk => chunk.type === 'chunk');
      const assets = Object.values(bundle).filter(asset => asset.type === 'asset');
      
      console.log('\n📊 构建统计:');
      console.log(`   Chunks: ${chunks.length}`);
      console.log(`   Assets: ${assets.length}`);
      
      // 计算总大小
      let totalSize = 0;
      Object.values(bundle).forEach(item => {
        if (item.type === 'chunk') {
          totalSize += Buffer.byteLength(item.code, 'utf8');
        } else if (item.type === 'asset' && typeof item.source === 'string') {
          totalSize += Buffer.byteLength(item.source, 'utf8');
        }
      });
      
      console.log(`   总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
      
      // 分析最大的 chunks
      const largeChunks = chunks
        .map(chunk => ({
          name: chunk.fileName,
          size: Buffer.byteLength(chunk.code, 'utf8') / 1024
        }))
        .filter(chunk => chunk.size > 100)
        .sort((a, b) => b.size - a.size)
        .slice(0, 10);
      
      if (largeChunks.length > 0) {
        console.log('\n🔍 大型 Chunks (>100KB):');
        largeChunks.forEach(chunk => {
          console.log(`   ${chunk.name}: ${chunk.size.toFixed(2)} KB`);
        });
      }
      
      // 性能建议
      console.log('\n💡 性能建议:');
      
      const veryLargeChunks = largeChunks.filter(chunk => chunk.size > 500);
      if (veryLargeChunks.length > 0) {
        console.log('   - 考虑进一步分割超过 500KB 的 chunks');
      }
      
      const totalChunks = chunks.length;
      if (totalChunks > 100) {
        console.log('   - Chunk 数量较多，考虑合并一些小的 chunks');
      } else if (totalChunks < 10) {
        console.log('   - Chunk 数量较少，考虑增加代码分割');
      }
      
      console.log('   - 使用 gzip 压缩可以进一步减小文件大小');
      console.log('   - 考虑使用 CDN 加载大型第三方库\n');
    }
  };
}

/**
 * 依赖预构建优化插件
 */
export function depOptimizationPlugin(): Plugin {
  return {
    name: 'dep-optimization',
    apply: 'serve',
    
    config(config) {
      // 优化依赖预构建
      config.optimizeDeps = {
        ...config.optimizeDeps,
        include: [
          'vue',
          'vue-router',
          'pinia',
          'ant-design-vue/es/button',
          'ant-design-vue/es/input',
          'ant-design-vue/es/form',
          'ant-design-vue/es/table',
          'ant-design-vue/es/modal',
          'ant-design-vue/es/message',
          'ant-design-vue/es/notification',
          'dayjs',
          'lodash-es',
          'uuid',
          'axios'
        ],
        exclude: [
          '@visactor/vtable',
          'leaflet',
          'html2canvas',
          '@turf/turf'
        ],
        // 强制预构建
        force: process.env.FORCE_OPTIMIZE === 'true'
      };
      
      return config;
    }
  };
}

/**
 * 开发环境性能优化插件
 */
export function devPerformancePlugin(): Plugin {
  return {
    name: 'dev-performance',
    apply: 'serve',
    
    configureServer(server) {
      // 优化 HMR
      server.ws.on('connection', () => {
        console.log('🔥 HMR 连接已建立');
      });
      
      // 监听文件变化
      let changeCount = 0;
      server.watcher.on('change', (path) => {
        changeCount++;
        if (changeCount % 10 === 0) {
          console.log(`📝 已处理 ${changeCount} 次文件变化`);
        }
      });
    },
    
    handleHotUpdate(ctx) {
      const { file, server } = ctx;
      
      // 优化特定文件类型的 HMR
      if (file.endsWith('.vue')) {
        // Vue 文件的特殊处理
        console.log(`🔄 Vue 文件更新: ${file}`);
      } else if (file.endsWith('.ts') || file.endsWith('.js')) {
        // TypeScript/JavaScript 文件的特殊处理
        console.log(`📜 脚本文件更新: ${file}`);
      }
      
      return undefined;
    }
  };
}
