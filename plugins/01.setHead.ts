// 设置请求头，依赖环境变量返回不同的js
export default defineNuxtPlugin(() => {
  const { kanyunUrl } = useRuntimeConfig().public;
  useHead({
    link: [{ rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' }],
    script: [
      kanyunUrl ? { src: kanyunUrl + '?v=1', defer: true, tagPosition: 'bodyClose' } : {},
      // trackerUrl ? { src: trackerUrl + '?v=1', defer: true, tagPosition: 'bodyClose' } : {} 埋点包已经可以自动加载sdk，无需自行加载
    ],
  });
});
