// 懒加载组件导出文件
import { 
  createLazyHeavyComponent, 
  createLazyThirdPartyComponent,
  createLazyLightComponent,
  createLazyOnVisibleComponent 
} from '@/utils/lazyComponent';

// 表格组件懒加载
export const LazyVTable = createLazyHeavyComponent(
  () => import('@/components/ui/Vtable/vtable.vue')
);

// 图片查看器懒加载
export const LazyImageViewer = createLazyLightComponent(
  () => import('@/components/ui/ImageViewer.vue')
);

// 地图组件懒加载（大型第三方组件）
export const LazyMapComponent = createLazyThirdPartyComponent(
  () => import('@/pages/inspection/inspectionInfo/mapRoot.vue')
);

// 文件上传组件懒加载
export const LazyScrollLoadAttachments = createLazyLightComponent(
  () => import('@/components/ui/ScrollLoadAttachments.vue')
);

// 审批模态框懒加载
export const LazyCommonApprovalModal = createLazyLightComponent(
  () => import('@/components/ui/CommonApprovalModal.vue')
);

// 表单折叠组件懒加载
export const LazyFormFold = createLazyLightComponent(
  () => import('@/components/ui/FormFold.vue')
);

// 信息组懒加载
export const LazyInfoGroup = createLazyLightComponent(
  () => import('@/components/ui/InfoGroup.vue')
);

export const LazyInfoGroupBox = createLazyLightComponent(
  () => import('@/components/ui/InfoGroupBox.vue')
);

// 复选框组懒加载
export const LazyCheckBoxGroup = createLazyLightComponent(
  () => import('@/components/ui/CheckBoxGroup.vue')
);

// 权限按钮懒加载
export const LazyAuthButton = createLazyLightComponent(
  () => import('@/components/ui/AuthButton.vue')
);

// 复制链接组件懒加载
export const LazyCopyLink = createLazyLightComponent(
  () => import('@/components/ui/CopyLink.vue')
);

// 可见时懒加载的重型组件
export const LazyVTableOnVisible = createLazyOnVisibleComponent(
  () => import('@/components/ui/Vtable/vtable.vue')
);

export const LazyMapOnVisible = createLazyOnVisibleComponent(
  () => import('@/pages/inspection/inspectionInfo/mapRoot.vue')
);

// 选择器组件懒加载
export const LazyDepartmentSelector = createLazyLightComponent(
  () => import('@/components/selector/DepartmentSelector.vue')
);

export const LazyUserSelector = createLazyLightComponent(
  () => import('@/components/selector/UserSelector.vue')
);

// 导出所有懒加载组件的类型
export type LazyComponents = {
  LazyVTable: typeof LazyVTable;
  LazyImageViewer: typeof LazyImageViewer;
  LazyMapComponent: typeof LazyMapComponent;
  LazyScrollLoadAttachments: typeof LazyScrollLoadAttachments;
  LazyCommonApprovalModal: typeof LazyCommonApprovalModal;
  LazyFormFold: typeof LazyFormFold;
  LazyInfoGroup: typeof LazyInfoGroup;
  LazyInfoGroupBox: typeof LazyInfoGroupBox;
  LazyCheckBoxGroup: typeof LazyCheckBoxGroup;
  LazyAuthButton: typeof LazyAuthButton;
  LazyCopyLink: typeof LazyCopyLink;
  LazyVTableOnVisible: typeof LazyVTableOnVisible;
  LazyMapOnVisible: typeof LazyMapOnVisible;
  LazyDepartmentSelector: typeof LazyDepartmentSelector;
  LazyUserSelector: typeof LazyUserSelector;
};

// 预加载关键组件的函数
export function preloadCriticalComponents() {
  if (typeof window === 'undefined') return;
  
  // 在空闲时间预加载常用组件
  const criticalComponents = [
    () => import('@/components/ui/ImageViewer.vue'),
    () => import('@/components/ui/FormFold.vue'),
    () => import('@/components/ui/InfoGroup.vue'),
    () => import('@/components/ui/AuthButton.vue'),
  ];
  
  if ('requestIdleCallback' in window) {
    criticalComponents.forEach((importFn, index) => {
      window.requestIdleCallback(() => {
        setTimeout(() => {
          importFn().catch(error => {
            console.warn('关键组件预加载失败:', error);
          });
        }, index * 100);
      });
    });
  }
}
