<template>
  <div v-show="showDialog" class="doctor-nong-modal">
    <div class="doctor-nong-mask" @click="open = false"></div>
    <div id="draggablecontent" class="doctor-nong-content bg-white flex-row">
      <div id="draggablecontentDiv" class="h-[20px] w-full cursor-move top-0 left-0 fixed"></div>
      <iframe v-if="url" :src="url" style="border: none"></iframe>
      <div id="drageTop" class="handle top"></div>
      <div id="drageRight" class="handle right"></div>
      <div id="drageButtom" class="handle bottom"></div>
      <div id="drageLeft" class="handle left"></div>
    </div>
  </div>
  <img v-show="isShow" id="draggableIcon" class="doctor-nong-icon w-[64px] h-[64px] fixed bottom-[20px] right-[20px] cursor-pointer z-[9997]" draggable="true" src="@/assets/images/doctornong.png" alt="" loading="lazy" @click="openDialog" />
</template>

<script setup lang="ts">
import { $post } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { useMouseMove } from '@/composables/useMouseMove';
import { useMouseDrage } from '@/composables/useMouseDrage';
// import VueDraggableResizable from 'vue-draggable-resizable';
// import { useDraggable } from '@vueuse/core' // useDraggable没法阻止移动结束后click实际

const url = ref('');
const open = defineModel<boolean>('open', { default: false });
const { gateWay, service, ecosHost } = useRuntimeConfig().public || {};

const { isDragging: isIconDragging } = useMouseMove({ moveId: 'draggableIcon' });

const { stop: stopMove } = useMouseMove({ moveId: 'draggablecontentDiv', targetId: 'draggablecontent' });
const { stop: stopDrageLeft } = useMouseDrage({ moveId: 'drageLeft', targetId: 'draggablecontent', direction: 'left' });
const { stop: stopDrageRight } = useMouseDrage({ moveId: 'drageRight', targetId: 'draggablecontent', direction: 'right' });
const { stop: stopDrageButtom } = useMouseDrage({ moveId: 'drageButtom', targetId: 'draggablecontent', direction: 'bottom' });
const { stop: stopDrageTop } = useMouseDrage({ moveId: 'drageTop', targetId: 'draggablecontent', direction: 'top' });

const route = useRoute();
const isShow = computed(() => {
  // 首页不展示浮窗入口，首页有自己的入口
  return route.path !== '/home';
});

const getUrl = async () => {
  try {
    const fetchUrl = gateWay + service.administrate + '/sso/getSsoAuth';
    const { data, code } = await $post(fetchUrl, {
      ssoType: 'askBob',
    });
    if (code === SUCCESS_CODE) {
      // const originUrl = `http://dev.paic.com.cn:8080/#/retransmission?target=askbob&systemId=${data?.ecosAuthParamVO?.systemId}&randomNumber=${data?.ecosAuthParamVO?.randomNumber}&unixTimeStamp=${data?.ecosAuthParamVO?.unixTimeStamp}&signature=${data?.ecosAuthParamVO?.signature}&umAccount=${data?.ecosAuthParamVO?.umAccount}&depId=${data?.ecosAuthParamVO?.depId}&noNavbar=1&noSidebar=1&token=${encodeURIComponent(data?.redisKey)}&origin=${encodeURIComponent(location.protocol + '//' + location.hostname)}`;
      const originUrl = `${ecosHost}/#/askBob/index?noNavbar=1&noSidebar=1&token=${encodeURIComponent(data?.ssoToken)}&origin=${encodeURIComponent(location.protocol + '//' + location.host)}`;
      if (url.value) {
        url.value = '';
        await nextTick();
        url.value = originUrl;
      } else {
        url.value = originUrl;
      }
    }
  } catch (e) {
    console.log(e);
  }
};

const showDialog = computed(() => {
  return open.value;
});

watch(open, (isOpen) => {
  if (isOpen) {
    // 打开弹窗，请求接口获取登录态
    if (!url.value) {
      getUrl();
    }
  }
});

const openDialog = async () => {
  if (!isIconDragging.value) {
    // open.value = true;
    try {
      const fetchUrl = gateWay + service.administrate + '/sso/getSsoAuth';
      const { data, code, msg } = await $post(fetchUrl, { ssoType: 'askBob' });
      if (code === SUCCESS_CODE) {
        const width = window.screen.width * 0.7;
        const height = window.screen.height * 0.7;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;
        const features = `width=${width},height=${height},left=${left},top=${top},menubar=no,toolbar=no,location=no,status=no,scrollbars=yes,resizable=yes`;
        window.open(`${ecosHost}/admin-web/askbob?token=${encodeURIComponent(data?.ssoToken)}`, 'doctorWindow', features);
      } else {
        message.error(msg);
      }
    } catch (error) {
      console.log(error);
    }
  }
};
const listenMessage = (event) => {
  // 安全检查：确保消息来源可信（origin）
  // 这里假设子窗口的域名是 "http://example.com"
  const originHost = event.origin.replace(/^(http(s?):)?\/\//, '');
  const currentHost = ecosHost.replace(/^(http(s?):)?\/\//, '');
  if (originHost !== currentHost) {
    console.error('非法来源的消息，拒绝接收！');
    return;
  }
  // 获取子窗口发送的数据
  const data = event.data;
  if (data.event === 'close') {
    open.value = false;
  } else if (data.event === 'preview') {
    window.open(data.data.url);
  } else if (data.event === 'download') {
    let url = data.data.url;
    url = url.replace(/^http(s*):/, 'https:');
    window.open(url);
  } else if (data.event === 'login') {
    // 登录态已失效
    getUrl();
  } else if (data.event === 'mouseup') {
    stopMove();
    stopDrageLeft();
    stopDrageRight();
    stopDrageButtom();
    stopDrageTop();
  }
  console.log('父窗口收到消息:', data);
};

onMounted(() => {
  // // 监听message事件
  window.addEventListener('message', listenMessage);
});

onUnmounted(() => {
  window.removeEventListener('message', listenMessage);
});
</script>

<!-- <style>
@import 'vue-draggable-resizable/style.css';
</style> -->

<style lang="less" scoped>
.doctor-nong-modal {
  .doctor-nong-mask {
    position: fixed;
    width: 100vw;
    height: 100vh;
    z-index: 9998;
    background-color: rgba(0, 0, 0, 0.45);
    left: 0;
    top: 0;
  }
  .doctor-nong-content {
    width: 646px;
    height: 672px;
    z-index: 9999;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    iframe {
      flex: 1;
    }
    .handle {
      height: 15px;
      position: absolute;
      &.top {
        top: -15px;
        width: 60%;
        cursor: n-resize;
        left: 50%;
        transform: translateX(-50%);
      }
      &.right {
        height: 60%;
        width: 15px;
        right: -15px;
        cursor: e-resize;
        top: 50%;
        transform: translateY(-50%);
      }
      &.bottom {
        bottom: -15px;
        width: 60%;
        cursor: s-resize;
        left: 50%;
        transform: translateX(-50%);
      }
      &.left {
        height: 60%;
        width: 15px;
        left: -15px;
        cursor: w-resize;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
