<template>
  <div v-show="isActive" class="image-previewer">
    <div class="overlay" @click="closePreview" />
    <div class="preview-container">
      <div class="pre-button flex items-center justify-center">
        <img v-show="!prevLoadMore || !loadingMore" :src="preIconSrc" class="w-[47px] h-[47px]" @click="preAction" />
        <a-spin v-show="prevLoadMore && loadingMore" />
      </div>
      <template v-if="currentImage">
        <template v-if="isPdf(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcPdfColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <div class="flex justify-between w-full">
              <a-button @click="download(currentImage)">下载文件</a-button>
              <a-button @click="download(currentImage, true)">在线预览</a-button>
            </div>
          </div>
        </template>
        <template v-else-if="isWord(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcWordColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <div class="flex justify-between w-full">
              <a-button @click="download(currentImage)">下载文件</a-button>
              <!-- <a-button @click="download(currentImage, true)">在线预览</a-button> -->
            </div>
          </div>
        </template>
        <template v-else-if="isExcel(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcDianziqingdanColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <div class="flex justify-between w-full">
              <a-button @click="download(currentImage)">下载文件</a-button>
              <!-- <a-button @click="download(currentImage, true)">在线预览</a-button> -->
            </div>
          </div>
        </template>
        <template v-else-if="isMail(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcMailColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else-if="isHtml(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcHtmlColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else-if="isPpt(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcPptColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <div class="flex justify-between w-full">
              <a-button @click="download(currentImage)">下载文件</a-button>
              <!-- <a-button @click="download(currentImage, true)">在线预览</a-button> -->
            </div>
          </div>
        </template>
        <template v-else-if="isZip(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcWenjianColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else-if="isTxt(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcTxtColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else-if="isUnPreviewImage(currentImage.documentFormat || '')">
          <div class="w-[200px] flex items-center justify-center flex-col">
            <VueIcon :icon="IcYingxiangColor" class="text-[78px]" />
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else-if="isMp4(currentImage.documentFormat || '')">
          <div class="max-w-[600px] flex items-center justify-center flex-col">
            <video v-if="currentImageUrl" controls autoplay class="w-full h-auto">
              <source :src="currentImageUrl" type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <p class="text-white my-[16px]">{{ currentImage.documentName }}</p>
            <a-button @click="download(currentImage)">下载文件</a-button>
          </div>
        </template>
        <template v-else>
          <img v-if="currentImageUrl" id="previewImg" class="preview-img cursor-move" :src="currentImageUrl" :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px) rotate(${rotate}deg)` }" alt="Preview" />
          <!-- <img v-if="currentImageUrl" id="previewImg" class="preview-img cursor-move" src="@/assets/images/home/<USER>" :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px) rotate(${rotation}deg)` }" alt="Preview" /> -->
          <a-spin v-else size="large" />
        </template>
      </template>
      <div class="next-button flex items-center justify-center">
        <img v-show="!nextLoadMore || !loadingMore" :src="nextIconSrc" class="w-[47px] h-[47px]" @click="nextAction" />
        <a-spin v-show="nextLoadMore && loadingMore" />
      </div>

      <div class="preview-title">{{ imageTitle }}</div>
      <span class="close-button" @click="closePreview">
        <VueIcon :icon="IconCloseFont" />
      </span>
      <div v-show="isImage(currentImage?.documentFormat || '')" class="control-btn">
        <span class="control-btn-item" title="放大">
          <VueIcon :icon="FujianFdFont" @click="zoomIn" />
        </span>
        <span class="control-btn-item" title="缩小">
          <VueIcon :icon="FujianSxFont" @click="zoomOut" />
        </span>
        <span class="control-btn-line" />
        <span class="control-btn-item" title="向左旋转">
          <VueIcon :icon="FujianXzuoFont" @click="rotateImage('left')" />
        </span>
        <span class="control-btn-item" title="向右旋转">
          <VueIcon :icon="FujianXyFont" @click="rotateImage('right')" />
        </span>
        <span class="control-btn-line" />
        <span class="control-btn-item" title="下载">
          <VueIcon :icon="IconDownloadFont" @click="download(currentImage)" />
        </span>
      </div>
    </div>
    <div v-if="viewerType !== 'images'" class="thumbnails-container">
      <OverlayScrollbarsComponent ref="scrollDom" class="w-full h-full flex" :options="{ overflow: { x: 'scroll', y: 'hidden' } }">
        <div class="img-container">
          <div v-for="(image, index) in thumbnailImages" :key="image.documentGroupItemsId" :class="['relative', 'img', { active: index === currentIndex }]">
            <template v-if="isPdf(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcPdfColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isWord(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcWordColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isExcel(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcDianziqingdanColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isMail(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcMailColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isHtml(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcHtmlColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isPpt(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcPptColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isZip(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcWenjianColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isTxt(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcTxtColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isMp4(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcShipingColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else-if="isUnPreviewImage(image.documentFormat || '')">
              <div class="w-full h-full flex items-center justify-center bg-[rgba(0,0,0,0.55)]" @click="selectImage(index)">
                <VueIcon :icon="IcYingxiangColor" class="text-[40px]" />
              </div>
            </template>
            <template v-else>
              <img :src="image.thumbnail" :alt="`Thumbnail ${index}`" @click="selectImage(index)" />
            </template>
            <div class="w-full truncate bottom-0 absolute text-white text-[12px] img-name">
              {{ image.documentName }}
            </div>
          </div>
        </div>
      </OverlayScrollbarsComponent>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconCloseFont, IcPdfColor, IcWordColor, IcDianziqingdanColor, IcHtmlColor, IcPptColor, IcWenjianColor, IcTxtColor, IcMailColor, FujianFdFont, FujianSxFont, FujianXyFont, IconDownloadFont, IcShipingColor, IcYingxiangColor, FujianXzuoFont } from '@pafe/icons-icore-agr-an';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue';
import 'overlayscrollbars/styles/overlayscrollbars.css';
import type { ViewerImageAndThumbnailItem, ViewerImageItem, thumbnailImage } from './imageViewer.d.ts';
import preIconSrc from '@/assets/images/pre-icon.svg';
import nextIconSrc from '@/assets/images/next-icon.svg';
import { isImage, isWord, isExcel, isPdf, isMail, isHtml, isPpt, isZip, isTxt, isMp4, extractFileNameWithoutExtension, isUnPreviewImage } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { useMouseMove } from '@/composables/useMouseMove';

const { list, current = 0, loadingMore, openLoadMore } = defineProps<{ list: ViewerImageAndThumbnailItem[] | ViewerImageItem[]; current: number | string; loadingMore?: 'loading' | 'end' | 'stop'; openLoadMore?: boolean }>();

const copyList = ref<ViewerImageAndThumbnailItem[]>([]); // list的备份
const thumbnailImages = ref<thumbnailImage[]>([]); // 缩略图列表
const currentIndex = ref(0); // 当前预览文件的下标
const previousIndex = ref(0); // 上一次的预览文件的下标，识别用户预览是往前翻还是往后翻
const currentImage = ref<thumbnailImage>(); // 当前预览文件的原图
const isActive = ref(false); // 是否展示预览窗口
const scrollDom = ref(null); // 缩略图滚动区域的dom
const viewerType = ref<'file' | 'thumbnails' | 'images'>('file'); // 当前预览的方式（嵌套/带缩略图图片/不带缩略图图片）

const imageTitle = computed(() => `${currentIndex.value + 1}/${thumbnailImages.value.length}`); // 展示预览标题，如果是嵌套文件则展示第一级目录名称

const currentImageUrl = ref<string>('');
const currentImageUrlList = ref<{ [name: string]: { path?: string; time?: number } }>({});
const prevLoadMore = ref(false);
const nextLoadMore = ref(false);
const emits = defineEmits(['loadMore']);

const initViewList = () => {
  if (list && list.length) {
    if (typeof list[0] !== 'string' && list[0].uploadPath && list[0].thumbnail !== undefined) {
      // 需要展示缩略图的数据
      viewerType.value = 'thumbnails';
      copyList.value = (list as ViewerImageAndThumbnailItem[]).map((val) => {
        return {
          documentName: val?.documentName || '',
          documentGroupItemsId: val?.documentGroupItemsId || val.thumbnail,
          documentFormat: val.documentFormat,
          uploadPath: val.uploadPath,
          bucketName: val.bucketName,
          thumbnail: val.thumbnail,
        };
      });
    } else {
      viewerType.value = 'images';
      // 单纯预览图片，仅支持图片预览
      copyList.value = (list as ViewerImageItem[]).map((val) => {
        return {
          documentName: '',
          documentGroupItemsId: val,
          documentFormat: 'png',
          uploadPath: val,
          bucketName: '',
          thumbnail: '',
        };
      });
    }
  }
};

// 生成缩略图
const createThumbnailsList = () => {
  let cur = current ? current : 0; // 设置默认值
  if (typeof cur !== 'number') {
    // 是字符串，则按文件id查找需要预览的文件
    cur = copyList.value.findIndex((val) => val.documentGroupItemsId === cur);
  }
  if (currentIndex.value !== 0) {
    // list发生了变化，导致重新生成了缩略图list
    cur = copyList.value.findIndex((val) => val.documentGroupItemsId === currentImage.value?.documentGroupItemsId);
  }

  thumbnailImages.value = copyList.value;
  currentIndex.value = cur;
  previousIndex.value = currentIndex.value;
  currentImage.value = thumbnailImages.value[currentIndex.value];

  // 第一次打开如果是pdf/word/excel/ppt附件，直接打开预览
  // if (isWord(currentImage.value.documentFormat || '') || isExcel(currentImage.value.documentFormat || '') || isPdf(currentImage.value.documentFormat || '') || isPpt(currentImage.value.documentFormat || '')) {
  //   download(currentImage.value, true);
  // }
};

watch([isActive, () => list], async ([active]) => {
  await nextTick();
  if (active && list.length) {
    // current发生变化或list发生变化，都需要更新缩略图
    initViewList();
    createThumbnailsList();
  }

  if (!active) {
    currentIndex.value = 0;
  }
});

// 点击缩略图
const selectImage = (index: number) => {
  currentIndex.value = index;
  currentImage.value = thumbnailImages.value[currentIndex.value];
};

const checkCache = (uploadPath: string) => {
  const item = currentImageUrlList.value[uploadPath];
  const stamp = new Date().getTime();
  // 缓存小于15分钟，iobs链接没有过期
  return !!(item && item.path && item.time && stamp - item.time < 900000);
};

// 预览获取真正的图片
const loadTotalImg = async () => {
  const uploadPath = currentImage.value?.uploadPath || '';
  const bucketName = currentImage.value?.bucketName || '';
  if (checkCache(uploadPath)) {
    // 已经存在链接，直接使用
    currentImageUrl.value = currentImageUrlList.value[uploadPath].path || '';
  } else if (!bucketName) {
    // 没有原图，直接展示缩略图
    currentImageUrl.value = uploadPath;
  } else {
    // 通过接口获取
    currentImageUrl.value = '';
    try {
      const { data, msg, code } = (await $postOnClient('/api/iobs/getInIobsUrl', { fileKey: uploadPath, storageTypeCode: '02', iobsBucketName: bucketName })) || {};
      if (SUCCESS_CODE === code) {
        currentImageUrl.value = data.fileUrl || '';
        currentImageUrlList.value[uploadPath] = { path: data.fileUrl, time: new Date().getTime() };
      } else {
        message.error(msg || '');
      }
    } catch (err) {
      message.error(err?.message || '请求有误，请稍后重试');
    }
  }
};

watch(currentImage, (val) => {
  if (isImage(val.documentFormat) || isMp4(val.documentFormat)) {
    loadTotalImg();
  } else {
    // 这里要清空，不然切换时图片链接可能还是上一次的链接，导致currentImageUrl没有发生变化，触发不了图片拖拽事件监听
    currentImageUrl.value = '';
  }
});

watch(currentIndex, async (val) => {
  // 是否需要移动滚动条
  await nextTick();
  if (scrollDom.value) {
    const dom = scrollDom.value.getElement();
    const imgContainer = dom.querySelector('.img-container');
    const scrollContainer = imgContainer ? imgContainer.parentNode : null;
    if (scrollContainer) {
      // 计算需要移动的距离
      const scrollContainergClientRect = scrollContainer.getBoundingClientRect();
      const currentImageDom = imgContainer.querySelectorAll('.img')[val];
      if (currentImageDom) {
        const currentImageDomClientRect = currentImageDom.getBoundingClientRect();
        if (currentImageDomClientRect.left < scrollContainergClientRect.left) {
          // 当前图片已经在缩略图可视区左侧
          const moveWidth = currentImageDomClientRect.left - scrollContainergClientRect.left + scrollContainergClientRect.width / 2;
          scrollContainer.scrollTo({
            left: scrollContainer.scrollLeft - moveWidth,
            behavior: 'smooth',
          });
        } else if (currentImageDomClientRect.right > scrollContainergClientRect.right) {
          // 当前图片已经在缩略图可视区右侧
          const moveWidth = currentImageDomClientRect.right - scrollContainergClientRect.right + scrollContainergClientRect.width / 2;
          scrollContainer.scrollTo({
            left: scrollContainer.scrollLeft + moveWidth,
            behavior: 'smooth',
          });
        }
      }
    }
  }
});

const preAction = async () => {
  const index = currentIndex.value;
  if (openLoadMore) {
    prevLoadMore.value = true;
    nextLoadMore.value = false;
  }
  if (index > 0) {
    selectImage(index - 1);
  } else if (openLoadMore) {
    emits('loadMore', 'prev');
  }
};

const nextAction = async () => {
  const index = currentIndex.value;
  if (openLoadMore) {
    prevLoadMore.value = false;
    nextLoadMore.value = true;
  }
  if (index < thumbnailImages.value.length - 1) {
    selectImage(index + 1);
  } else if (openLoadMore) {
    emits('loadMore', 'next');
  }
};

const closePreview = () => {
  isActive.value = false;
};

const openPreview = () => {
  isActive.value = true;
};

// 文件下载和预览，预览仅支持pdf/word/excel/ppt
const download = async (image: ViewerImageAndThumbnailItem, isPreview?: boolean) => {
  // 后端要补充文件后缀
  const fileName = extractFileNameWithoutExtension(image.documentName || '') + '.' + image.documentFormat;
  try {
    const { data, msg, code } = (await $postOnClient('/api/iobs/getInIobsUrl', { fileKey: image.uploadPath, storageTypeCode: '02', iobsBucketName: image.bucketName, downloadFlag: !isPreview, fileName, documentFormat: image.documentFormat })) || {};
    if (SUCCESS_CODE === code) {
      const { fileUrl } = data;
      window.open(fileUrl);
    } else {
      message.error(msg || '');
    }
  } catch (err) {
    message.error(err?.message || '请求有误，请稍后重试');
  }
};

const { addListener, translateX, translateY, scale, rotate } = useMouseMove({ moveId: 'previewImg', needListenwheel: true });

const zoomIn = () => {
  scale.value += 0.1; // 每次放大10%
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    // 避免缩小到负值
    scale.value -= 0.1; // 每次缩小10%
  }
};

const rotateImage = (direction: string) => {
  // translateX.value = 0;
  // translateY.value = 0;
  if (direction === 'left') {
    rotate.value = rotate.value - 90;
  } else {
    rotate.value = rotate.value + 90;
  }
};

watch(currentImageUrl, (val) => {
  if (val) {
    // 重置位置
    // scale.value = 1;
    // rotate.value = 0;
    // translateX.value = 0;
    // translateY.value = 0;
    addListener();
  }
});

// 重置图片旋转角度
const resetRotate = () => {
  rotate.value = 0;
};

defineExpose({
  openPreview,
  resetRotate,
});
</script>

<style scoped lang="less">
.image-previewer {
  position: fixed;
  min-height: 100vh; /* 确保容器足够高以显示缩略图 */
  top: 0;
  left: 0;
  z-index: 10000;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0);
  opacity: 0.54;
  z-index: 998;
}

.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 154px; /* 留出空间显示缩略图 */
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  z-index: 999;
}

.preview-img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  transform-origin: center;
  // transition: transform 0.3s ease;
}

.pre-button {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 10%;
  width: 47px;
  height: 47px;
  cursor: pointer;
  z-index: 1;
}

.next-button {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 10%;
  width: 47px;
  height: 47px;
  cursor: pointer;
}

.preview-title {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 700;
}

.control-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 16px;
  color: #ffffff;
  &-item {
    cursor: pointer;
    margin-left: 12px;
  }
  &-line {
    display: inline-block;
    margin-left: 12px;
    width: 1px;
    height: 10px;
    background-color: rgba(151, 151, 151, 1);
  }
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  border: none;
  background: transparent;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.thumbnails-container {
  position: fixed;
  left: 10%;
  right: 10%;
  bottom: 38px;
  box-sizing: border-box;
  z-index: 999;
  .img-container {
    display: flex;
    box-sizing: border-box;
    align-items: flex-end;
    & .img {
      flex-shrink: 0;
      width: 100px; /* 缩略图的宽度 */
      height: 80px;
      cursor: pointer;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      opacity: 0.6;
      transition:
        width 0.3s,
        height 0.3s;
      border-radius: 3px;
      img {
        width: 100%;
        height: 100%;
        border-radius: 3px;
      }
      .img-name {
        opacity: 0.41;
        background-image: linear-gradient(180deg, #787878 0%, #000000 100%);
        box-shadow: 0px -1px 1px 0px rgba(0, 55, 25, 0.24);
        border-bottom-left-radius: 3px;
        border-bottom-right-radius: 3px;
        text-align: center;
      }
    }
    & .img.active {
      opacity: 1;
      height: 96px;
      width: 120px;
      .img-name {
        opacity: 0.41;
        background-image: linear-gradient(180deg, #787878 0%, #000000 100%);
        box-shadow: 0px -1px 1px 0px rgba(0, 55, 25, 0.24);
      }
    }
  }
}

/* 动画样式 */
/* .fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
} */
</style>
