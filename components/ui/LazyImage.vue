<template>
  <div 
    ref="containerRef" 
    :class="containerClass"
    :style="containerStyle"
  >
    <!-- 加载中状态 -->
    <div 
      v-if="isLoading" 
      class="flex items-center justify-center bg-gray-100"
      :style="placeholderStyle"
    >
      <a-spin v-if="showSpinner" size="small" />
      <div v-else class="text-gray-400 text-sm">
        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
        </svg>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div 
      v-else-if="hasError" 
      class="flex flex-col items-center justify-center bg-gray-100 text-gray-500"
      :style="placeholderStyle"
    >
      <svg class="w-8 h-8 mb-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <span class="text-xs">加载失败</span>
      <a-button v-if="allowRetry" size="small" type="link" @click="retry">
        重试
      </a-button>
    </div>
    
    <!-- 实际图片 -->
    <img
      v-else-if="shouldLoad && actualSrc"
      :src="actualSrc"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  src: string;
  alt?: string;
  width?: string | number;
  height?: string | number;
  placeholder?: string;
  fallback?: string;
  lazy?: boolean;
  threshold?: number;
  showSpinner?: boolean;
  allowRetry?: boolean;
  containerClass?: string;
  imageClass?: string;
  preload?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  lazy: true,
  threshold: 0.1,
  showSpinner: true,
  allowRetry: true,
  containerClass: '',
  imageClass: '',
  preload: false,
});

const emit = defineEmits<{
  load: [event: Event];
  error: [event: Event];
  visible: [];
}>();

const containerRef = ref<HTMLElement>();
const isLoading = ref(true);
const hasError = ref(false);
const shouldLoad = ref(!props.lazy || props.preload);
const actualSrc = ref(props.preload ? props.src : '');
const retryCount = ref(0);

// 计算样式
const containerStyle = computed(() => {
  const style: Record<string, string> = {};
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }
  return style;
});

const placeholderStyle = computed(() => {
  return {
    ...containerStyle.value,
    minHeight: props.height ? (typeof props.height === 'number' ? `${props.height}px` : props.height) : '100px',
  };
});

const imageStyle = computed(() => {
  return {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  };
});

// 交叉观察器
let observer: IntersectionObserver | null = null;

const setupIntersectionObserver = () => {
  if (!props.lazy || shouldLoad.value) return;
  
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0];
      if (entry.isIntersecting) {
        shouldLoad.value = true;
        actualSrc.value = props.src;
        emit('visible');
        observer?.disconnect();
      }
    },
    {
      threshold: props.threshold,
      rootMargin: '50px',
    }
  );
  
  if (containerRef.value) {
    observer.observe(containerRef.value);
  }
};

const onLoad = (event: Event) => {
  isLoading.value = false;
  hasError.value = false;
  emit('load', event);
};

const onError = (event: Event) => {
  isLoading.value = false;
  hasError.value = true;
  
  // 尝试使用 fallback 图片
  if (props.fallback && retryCount.value === 0) {
    actualSrc.value = props.fallback;
    retryCount.value++;
    return;
  }
  
  emit('error', event);
};

const retry = () => {
  isLoading.value = true;
  hasError.value = false;
  retryCount.value = 0;
  actualSrc.value = props.src;
};

// 预加载图片
const preloadImage = (src: string) => {
  return new Promise<void>((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

onMounted(() => {
  if (props.preload) {
    preloadImage(props.src)
      .then(() => {
        isLoading.value = false;
      })
      .catch(() => {
        hasError.value = true;
        isLoading.value = false;
      });
  } else {
    setupIntersectionObserver();
  }
});

onUnmounted(() => {
  observer?.disconnect();
});

// 监听 src 变化
watch(() => props.src, (newSrc) => {
  if (newSrc !== actualSrc.value) {
    isLoading.value = true;
    hasError.value = false;
    retryCount.value = 0;
    
    if (shouldLoad.value) {
      actualSrc.value = newSrc;
    }
  }
});
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image {
  transition: opacity 0.3s ease-in-out;
}

.lazy-image.loading {
  opacity: 0;
}

.lazy-image.loaded {
  opacity: 1;
}
</style>
