<template>
  <div>
    <a-modal v-model:open="open" :title="title" width="800px" destroy-on-close :after-close="handleCancel" @ok="handleOk('submit')" @cancel="handleCancel">
      <a-form ref="formRef" :colon="false" :model="eoaData">
        <div class="text-[rgba(0,0,0,0.55)] font-normal mb-[8px]"><VueIcon :icon="IconInformationFont" />基础信息</div>
        <div class="bg-[#f8f8f8] rounded-[4px] px-[16px] py-[16px]">
          <!-- 提供内容插槽给一些特殊的签报用于个性化表单 -->
          <slot v-if="$slots.content" name="content" />
          <div v-else>
            <a-form-item label="签报主题" name="eoaTitle" required :label-col="labelColStyle">
              <a-input v-model:value="eoaData.eoaTitle" placeholder="请输入" :disabled="eoaTitleEdit ? false : !!eoaTitle" />
            </a-form-item>
            <a-form-item label="签报内容" name="eoaContent" required :label-col="labelColStyle">
              <a-textarea v-model:value="eoaData.eoaContent" auto-size />
            </a-form-item>
          </div>
          <a-form-item v-if="approvalChainType === '0'" label="主送领导审批链" name="approveChainList" :label-col="labelColStyle" required>
            <a-form-item-rest>
              <span v-for="(item, index) in eoaData.approveChainList" :key="index">
                <span v-for="(child, idx) in item.flowOwnerTypeList" :key="idx">
                  <div v-if="index !== 0 || (index === 0 && idx !== 0)" class="inline-block w-[20px] text-center">{{ showSymbol(index, idx, item.handType) }}</div>
                  <div class="inline-block w-[268px]">
                    <!-- 非经办人上级领导显示选择框 -->
                    <a-form-item v-if="item.flowOwnerType.split(',')[idx] !== 'E1' && item.flowOwner && Array.isArray(item.flowOwner)" :name="`flowOwner + ${idx + 1}`" :rules="[{ required: true, validator: (rule, value) => validatorFlow(rule, value, index, idx) }]">
                      <a-select ref="selectRef" v-model:value="item.flowOwner[idx] as unknown as []" allow-clear label-in-value :placeholder="item.flowOwnerNameList[idx]" :filter-option="filterOption" show-search :field-names="{ label: 'um', value: 'um' }" :options="item.flowOwnerOptions[idx]" />
                    </a-form-item>
                    <!-- 经办人上级领导显示输入框 -->
                    <a-form-item v-if="item.flowOwnerType.split(',')[idx] === 'E1' && item.flowOwner && Array.isArray(item.flowOwner)" :name="`flowOwner + ${idx + 1}`" :rules="[{ required: true, validator: (rule, value) => validCheckApproveInput(rule, value, index, idx) }]">
                      <a-input v-model:value="item.flowOwner[idx] as string" :placeholder="item.flowOwnerNameList[idx]" allow-clear />
                    </a-form-item>
                  </div>
                </span>
              </span>
            </a-form-item-rest>
          </a-form-item>
          <a-form-item v-if="approvalChainType === '1'" label="审批流程" name="approvalChain" required :label-col="labelColStyle">
            <div class="flex items-center">
              <a-input v-model:value="eoaData.approvalChain" autocomplete="off" placeholder="请点击“去修改”，获取审批流程信息" />
              <a-button type="primary" class="ml-[8px]" @click="goEoa">去修改</a-button>
            </div>
          </a-form-item>
        </div>
        <div v-if="approvalChainType === '0'" class="flex mt-[16px]">
          <div>注：</div>
          <div>
            <div>1、请在录入框中填写/选择审批领导的完整UM号；</div>
            <div>2、如UM账号无法选择，请在核心系统维护。</div>
          </div>
        </div>
        <div v-if="!showUploadbtn" class="text-[rgba(0,0,0,0.55)] font-normal mt-[16px]"><VueIcon :icon="IconPingtaijiekouFont" />签报附件</div>
        <div v-if="!showUploadbtn" class="bg-[#F8F8F8] space-y-[16px] px-[15px] mt-[8px] py-[16px]">
          <a-form-item label="" name="fileInfos" :rules="fileRequired ? [{ required: true, message: '请上传文件' }] : []">
            <a-upload :multiple="false" :show-upload-list="false" :action="handleFileChange" @change="handleFileChange">
              <span v-if="fileRequired" class="required">*</span>
              <a-button>
                <template #icon>
                  <VueIcon :icon="IconTongyongShangchuanFont" />
                </template>
                <span class="ml-[2px]">附件上传</span>
              </a-button>
            </a-upload>
            <div v-for="fileInfo in eoaData.fileInfos" :key="fileInfo.attachUrl" class="text-12px text-[#4E6085]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo)">{{ fileInfo.docoldName }}</span>
              <a-button type="link" @click="deleteFile(fileInfo.attachUrl)">删除</a-button>
            </div>
          </a-form-item>
        </div>
        <div v-if="!showUploadbtn" class="flex my-[16px]">
          <div>注：</div>
          <div v-if="showText">
            <div>1、请在录入框中填写&nbsp;/&nbsp;选择审批领导的完整um号；</div>
            <div>2、如UM账号无法选择，请在核心系统维护；</div>
          </div>
          <div v-else>
            <div>1、附件上传单个文件大小不能超过1024K！建议压缩文件后上传，运行上传附件类型：.jpg/.doc/.docx/.xls/.xlsx/.csv/.txt/.zip/.rar/.ppt/.pptx/..html/.sql/.gif/.bmp；</div>
            <div>2、签报审批通过后，需要进入投保申请页手动提交核保。</div>
          </div>
        </div>
        <div v-if="attachmentList && attachmentList.length">
          <div class="text-[rgba(0,0,0,0.55)] font-normal mt-[16px]"><VueIcon :icon="IconPingtaijiekouFont" />签报附件</div>
          <div class="flex items-center flex-wrap bg-[#F8F8F8] px-[15px] mt-[8px] py-[16px]">
            <template v-for="item in attachmentList" :key="item.attachUrl">
              <div class="mr-[12px] text-12px text-[rgba(0,0,0,0.55)]">
                <VueIcon :icon="IconAttachmentFont" />
                <span>{{ item.docoldName }}</span>
              </div>
            </template>
          </div>
        </div>
      </a-form>
      <template #footer>
        <a-button v-if="showPreviewBtn" type="primary" class="float-left" @click="handleOk('preview')">签报内容预览</a-button>
        <a-button v-if="showResetBtn" @click="handleCancel">重置</a-button>
        <a-button v-if="copyApprovalBtn" :disabled="copyBtnDisabled" @click="copyApproval">复用签报</a-button>
        <a-button key="submit" type="primary" @click="handleOk('submit')">发起签报</a-button>
      </template>
    </a-modal>
    <!-- 签报内容预览 -->
    <a-modal v-model:open="previewOpen" :title="`${title}事项预览`" :footer="null" width="800px" centered destroy-on-close>
      <slot name="previewContent" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { IconPingtaijiekouFont, IconTongyongShangchuanFont, IconAttachmentFont, IconInformationFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import { $postOnClient, usePost } from '@/composables/request';
import { downloadBlob } from '@/utils/tools';
import { SUCCESS_CODE } from '@/utils/constants';

interface OptionType {
  um: string;
  name: string;
  option?: OptionType;
}
interface EoaChainsType {
  flowOwnerNameList: string[];
  handType: string;
  flowOwnerTypeList: string[] | OptionType[];
  seq: number;
  flowOwnerOptions: OptionType[][];
  flowOwner: (OptionType | null | string)[];
  flowOwnerType: string;
}
interface EoaData {
  documentGroupId: string;
  eoaTitle?: string; // 签报主题
  eoaContent?: string; // 签报内容
  fileInfos?: { attachUrl: string; docoldName: string; fileType: string; fileSize: number }[]; // 签报附件
  approvalChain?: string; // 跳转eoa审批链
  templateChainKey?: string; // 跳转eoa审批联key
  approveChainList: EoaChainsType[]; // 审批链
}

interface IAttachmentList {
  attachUrl: string;
  docoldName: string;
}

const props = withDefaults(
  defineProps<{
    title?: string; // 弹窗标题
    eoaTitle?: string; // 签报标题
    eoaContent?: string; // 签报内容
    approvalChainType?: string; // 审批链类型 目前总共有两种 0为通过后端接口获取审批链数据 1为去eoa系统配置页获取
    showPreviewBtn?: boolean; // 是否展示签报内容预览按钮
    eoaType?: string; // 签报类型
    departmentCode?: string; // 机构编码
    totalActualPremium?: string; // 总保费
    showUploadbtn?: boolean; // 是否显示上传附件模块(true为隐藏)
    showResetBtn?: boolean; // 是否显示重置按钮(true为显示)
    showText?: boolean; // 审批链文案特定显示
    fileRequired?: boolean; // 是否必填附件
    copyApprovalBtn?: boolean; // 是否显示复用签报按钮(true为显示)
    eoaTitleEdit?: boolean; // 签报主题是否可编辑（默认false）
    attachmentList?: IAttachmentList[]; // 附件列表
    externParams?: object;
  }>(),
  {
    title: 'EOA签报申请页',
    eoaTitle: '',
    approvalChainType: '0',
    showPreviewBtn: false,
    eoaType: '',
    departmentCode: '',
    fileRequired: false,
    copyApprovalBtn: false,
    eoaTitleEdit: false,
    externParams: () => ({}),
  },
);
// 审批链选择框后面展示对应符号 20 →, 30 |, 120 /, 1110§
interface type {
  [key: number | string]: number | string;
}
// 审批类型标识
// 20 →, 30 |, 120  /, 1110§
const commonHandTypeList: type = { 20: '→', 30: '|', 120: '/', 1110: '§' };
const { gateWay, service } = useRuntimeConfig().public || {};
const emits = defineEmits(['ok', 'reset', 'preview', 'copyApproval', 'getUrl']);
const copyBtnDisabled = defineModel<boolean>('copyBtnDisabled', { default: true });
// 是否展示签报内容弹窗
const open = defineModel<boolean>('open', { default: false });
const previewOpen = defineModel<boolean>('previewOpen', { default: false });
// 表单数据
const eoaData = ref<EoaData>({
  eoaTitle: '',
  eoaContent: '',
  fileInfos: [],
  approveChainList: [],
  documentGroupId: '',
});
// 表单ref
const formRef = ref();
const labelColStyle = {
  style: { width: '130px' },
};
interface dataType {
  uploadPath?: string;
}
interface resType {
  msg: string;
  code: string;
  data: dataType | null;
}
// 监听上传文件
const handleFileChange = async (file: unknown) => {
  if (eoaData.value.fileInfos?.length && eoaData.value.fileInfos?.length > 9) {
    // 附件不超过10个
    message.error('上传的附件不能超过10个');
    return Promise.reject();
  }
  const formData = new FormData();
  formData.append('file', file as Blob);
  formData.append('docGroupId', eoaData.value.documentGroupId);
  formData.append('fileType', props.eoaType);
  formData.append('validFileSize', 'N');
  const url = gateWay + service.administrate + '/iobs/uploadFileByIOBS';
  const res = (await $postOnClient(url, formData)) as resType;
  if (res && res.code === SUCCESS_CODE) {
    const fileData = {
      attachUrl: res.data?.uploadPath || '',
      docoldName: (file as { name: string }).name,
      fileType: props.eoaType,
      fileSize: (file as { size: number }).size,
    };
    eoaData.value.fileInfos?.push(fileData);
    message.success(res?.msg);
  } else {
    message.error(res?.msg);
  }
  return Promise.reject();
};

const downloadFile = async (fileInfo: { docoldName: string; fileType: string; attachUrl: string }) => {
  const url = gateWay + service.accept + '/sys/insurance/downloadFile';
  const blob = await $postOnClient(url, { ...fileInfo });
  // 验证 blob 是否为有效的 Blob
  if (blob && blob instanceof Blob) {
    downloadBlob(blob, fileInfo.docoldName, fileInfo.fileType);
  } else {
    // 处理错误, 例如：
    message.error('下载文件失败，返回结果无效。');
  }
};
// 删除文件
const deleteFile = (attachUrl: string) => {
  if (!eoaData.value.fileInfos) return;
  eoaData.value.fileInfos = eoaData.value.fileInfos.filter((fileInfo) => fileInfo.attachUrl !== attachUrl);
};

const handleOk = async (type: string) => {
  await formRef?.value?.validate();
  if (props.approvalChainType === '0' && eoaData.value.approveChainList?.length > 0) {
    eoaData.value.approveChainList.forEach((item) => {
      if (item.flowOwner?.length > 0) {
        item.flowOwner.forEach((flowOwner, index) => {
          if (typeof flowOwner === 'string') {
            // 经办人上级领导 使用输入框的情况
            item.flowOwnerTypeList[index] = { um: flowOwner, name: flowOwner };
          } else {
            item.flowOwnerTypeList[index] = flowOwner?.option || { um: '', name: '' };
          }
        });
      }
    });
  }
  if (type === 'submit') {
    emits('ok', eoaData.value);
  } else {
    emits('preview', eoaData.value);
  }
};
// 取消清空表单数据
const handleCancel = async () => {
  emits('reset');
  eoaData.value.eoaTitle = '';
  eoaData.value.eoaContent = '';
  eoaData.value.fileInfos = [];
  eoaData.value.documentGroupId = '';
  eoaData.value.approvalChain = '';
  eoaData.value.templateChainKey = '';
  // 清空审批链
  if (eoaData.value.approveChainList?.length > 0) {
    eoaData.value.approveChainList.forEach((item) => {
      if (item.flowOwner?.length > 0) {
        item.flowOwner = item.flowOwner.map(() => null);
      }
    });
  }
};

// 跳转eoa签报配置页面
const goEoa = async () => {
  emits('getUrl');
};
interface EoaEvent {
  data: {
    majorLeaders: string;
    code: string;
    templateChainKey: string;
  };
}
// EOA系统回调
const eoaCallback = (event: EoaEvent) => {
  console.log('EOA传递参数', event);
  const { majorLeaders, templateChainKey, code } = event.data;
  // code 0:成功， 1:失败
  if (Number(code) === 0) {
    eoaData.value.approvalChain = majorLeaders;
    eoaData.value.templateChainKey = templateChainKey;
  }
};
// 获取审批人下拉框列表
const getFlowOwnerList = (flowOwnerType: string) => {
  const url = gateWay + service.administrate + '/eoa/queryEoaApproveUserVOListByRole';
  const params = {
    departmentCode: props.departmentCode,
    flowOwnerType,
  };
  return $postOnClient(url, params).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      return res.data || [];
    }
  });
};
// 获取审批链
const getApproveLinkReq = await usePost<EoaData>(`${gateWay}${service.administrate}/eoa/queryApproveLinkByType`);
const getApproveLink = async () => {
  try {
    const params = {
      eoaType: props.eoaType,
      departmentCode: props.departmentCode,
      totalActualPremium: props.totalActualPremium,
      ...props.externParams,
    };
    const res = await getApproveLinkReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      eoaData.value.approveChainList = res.data?.approveChainList || [];
      if (eoaData.value.approveChainList?.length > 0) {
        for (let i = 0; i < eoaData.value.approveChainList.length; i++) {
          const item = eoaData.value.approveChainList[i];
          item.flowOwner = [];
          item.flowOwnerOptions = [];
          for (let j = 0; j < item.flowOwnerTypeList.length; j++) {
            const flowOwner = item.flowOwnerTypeList[j];
            item.flowOwner.push(null);
            const optionRes = await getFlowOwnerList(flowOwner as string);
            item.flowOwnerOptions.push(optionRes as OptionType[]);
          }
        }
      }
      eoaData.value.documentGroupId = res.data?.documentGroupId;
    } else if (res && res.msg) {
      message.error(res.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 显示下拉框前面的符号
const showSymbol = (index: number, idx: number, handType: string) => {
  let symbolStr = commonHandTypeList[handType];
  if (index > 0 && eoaData.value.approveChainList?.[index].seq !== eoaData.value.approveChainList?.[index - 1].seq) {
    if (index !== 0 && idx === 0) {
      if (handType !== '1110') {
        symbolStr = '→';
      }
    }
  }
  return symbolStr;
};
// 主送领导审批链规则校验
const validatorFlow = (_rule: Rule, value: string, index: number, idx: number) => {
  if (!eoaData.value?.approveChainList?.[index]?.flowOwner[idx]) {
    return Promise.reject('请选择');
  } else {
    return Promise.resolve();
  }
};
const validCheckApproveInput = (_rule: Rule, value: string, index: number, idx: number) => {
  const item: string = eoaData.value?.approveChainList?.[index]?.flowOwner[idx] as string;
  console.log('item', item, eoaData.value?.approveChainList);

  if (!item) {
    return Promise.reject('不能为空');
  } else {
    const req = /^[a-zA-Z0-9]{1,100}$/;
    if (item !== '' && !req.test(item)) {
      if (item.length > 100) {
        return Promise.reject('不能超过100个字符长度!');
      }
      return Promise.reject('请输入数字或字母!');
    }
  }
  return Promise.resolve();
};
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.um.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 复用签报
const copyApproval = () => {
  emits('copyApproval');
};
onMounted(() => {
  window.addEventListener('message', eoaCallback);
  if (props.approvalChainType === '0') {
    getApproveLink();
  }
});

onUnmounted(() => {
  window.removeEventListener('message', eoaCallback);
});

watch(
  () => props.eoaTitle,
  () => {
    if (props?.eoaTitle) {
      eoaData.value.eoaTitle = props.eoaTitle;
    }
    if (props?.eoaContent) {
      eoaData.value.eoaContent = props.eoaContent;
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less">
.required {
  display: inline-block;
  margin-right: 6px;
  color: #f03e3e;
  font-size: 12px;
  font-family: SimSun, sans-serif;
  line-height: 1;
}
</style>
