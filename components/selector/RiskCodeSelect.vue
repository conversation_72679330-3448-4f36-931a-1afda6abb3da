<template>
  <div class="flex w-full">
    <div class="shrink-0 flex gap-x-8px mr-8px" :style="{ width: showSearch ? '70%' : '100%' }">
      <template v-for="level in isMultiTarget ? 4 : 5" :key="level">
        <a-select v-if="level >= startLevel" v-model:value="arrayValue[level - 1]" placeholder="请选择" :mode="level === 4 && isMultiTarget && !isModifyPage ? 'multiple' : undefined" allow-clear :disabled="disabledList[level - 1]" :style="{ width: '100%', height: level === 4 && isMultiTarget ? '100%' : '36px' }" :field-names="{ label: 'encodeValue', value: 'encodeKey' }" :options="level === 4 && isMultiTarget ? multiOption : levelOptions[level - 1]" @change="(value, option) => handleChange(value as string, level, option)" />
      </template>
    </div>
    <div v-if="showSearch && !isMultiTarget" class="w-[30%]">
      <a-select v-model:value="searchValue" :disabled="disabled" style="width: 100%" :options="fiveDictOptions" placeholder="请搜索" :default-active-first-option="false" :filter-option="false" :not-found-content="null" show-search allow-clear :loading="searchLoading" @search="debounceSearch" @change="handleFiveDictChange" @input-key-down="inputKeyDownChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import { debounce } from 'lodash-es';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { $get } from '@/utils/request';

const { gateWay, service } = useRuntimeConfig().public || {};
const emit = defineEmits(['changeValue']);
const riskCode = defineModel<string | undefined>('value', {
  default: undefined,
});
const props = withDefaults(
  defineProps<{
    departmentCode: string;
    defaultValue?: string;
    disabled?: boolean;
    showSearch?: boolean; // 是否展示搜索
    startLevel?: number;
    pageSceneCode?: string;
    isMultiTarget?: boolean; // 是否为多标的
    multiOption?: DefaultOptionType[]; // 多标的option
    defaultMulRisk?: Array<string> | string; // 多标的默认值
    allowEdit?: boolean; // 是否允许修改多标4 5级
    isModifyPage?: boolean; // 批改页面
    allowOtherEdit?: boolean; // 是否允许其他的情况下修改 临时修复标的选择其他的问题
  }>(),
  {
    departmentCode: '',
    defaultValue: '',
    disabled: false,
    showSearch: true,
    startLevel: 1,
    pageSceneCode: '',
    isMultiTarget: false,
    allowEdit: false,
    isModifyPage: false,
    allowOtherEdit: false,
  },
);

const levelOptions = ref<DefaultOptionType[][]>([[], [], [], [], []]);
const arrayValue = ref<Array<undefined | string>>([undefined, undefined, undefined, undefined, undefined]);
const disabledList = ref([false, false, false, false, false]);
const searchValue = ref();

const resetData = () => {
  arrayValue.value[0] = undefined;
  for (let i = 1; i < 5; i++) {
    arrayValue.value[i] = undefined;
    levelOptions.value[i] = [];
  }
};
defineExpose({
  valueList: arrayValue,
  disabledList: disabledList,
  resetData,
});
// const { fetchData } = await useGet('/gateway/icore-agr-an.administrate/encodeDict/getEncodeDictList');

const handleChange = (val: string, level: number, option: DefaultOptionType | DefaultOptionType[]) => {
  if (val) {
    // 正常选择标的
    riskCode.value = val;
    emit('changeValue', val, level, option);
  } else {
    // 清除标的，value设置为上一级
    if (level > 1) {
      riskCode.value = arrayValue.value[level - 2];
      emit('changeValue', arrayValue.value[level - 2], level - 1, option);
    } else {
      riskCode.value = undefined;
      emit('changeValue', undefined, level - 1, option);
    }
  }
  resetLevels(level);
  if (val) {
    fetchLevelOptions(level + 1, val);
  }
};

const resetLevels = (startLevel: number) => {
  for (let i = startLevel; i < 5; i++) {
    arrayValue.value[i] = undefined;
    levelOptions.value[i] = [];
  }
};

const fetchLevelOptions = async (level: number, encodeKey?: string | string[]) => {
  if (level > 5) return; // 目前只支持5级标的
  // 多标到第四级也不需要请求
  if (level === 5 && props.isMultiTarget) return;
  try {
    const res = await $get('/gateway/icore-agr-an.administrate/encodeDict/getEncodeDictList', {
      departmentCode: props.departmentCode,
      level,
      encodeKey: encodeKey,
      pageSceneCode: props.pageSceneCode,
    });

    if (res && res.code === SUCCESS_CODE) {
      const index = level - 1; // 数据存储位置与选择器位置转换
      levelOptions.value[index] = res.data as DefaultOptionType[][];
    }
  } catch (error) {
    console.error('Failed to fetch level options:', error);
  }
};

watchEffect(() => {
  disabledList.value = props.disabled ? [true, true, true, true, true] : [false, false, false, false, false];
  if (props.allowEdit) {
    disabledList.value = [true, true, true, false, false];
  }
});

// watch(
//   () => props.departmentCode,
//   async () => {
//     await nextTick();
//     resetLevels(0);
//     fetchLevelOptions(1, '');
//   },
//   {
//     immediate: true,
//   },
// );

// fetchLevelOptions(1, '');

watch(
  () => props.pageSceneCode,
  () => {
    fetchLevelOptions(1, '');
  },
  { immediate: true },
);

const asyncInitValue = async (values: string[]) => {
  // 重置所有选择器的值和选项
  resetLevels(0);
  // 依次调用接口给每个选择器options赋值
  for (let i = 0; i <= values.length; i++) {
    const encodeKey = values[i];
    const preEncodeKey = i > 0 ? values[i - 1] : '';
    if (encodeKey) {
      await fetchLevelOptions(i + 1, preEncodeKey);

      const options = levelOptions.value[i];
      const option = options.find((opt) => opt.encodeKey === encodeKey);

      if (option) {
        arrayValue.value[i] = encodeKey;
        riskCode.value = encodeKey;
        await fetchLevelOptions(i + 1 + 1, encodeKey);
        emit('changeValue', encodeKey, i + 1, option);
      } else {
        // 如果当前项数据不对，重置后面所有的选择器为空
        resetLevels(i);
        break;
      }
    }
  }
  // 多标初始化数据
  if (props.isMultiTarget && props.defaultMulRisk) {
    if (props.isModifyPage) {
      arrayValue.value[3] = props.defaultMulRisk.length < 8 ? '' : props.defaultMulRisk;
    } else {
      arrayValue.value[3] = props.defaultMulRisk;
    }
  }
  // 如果默认值没有五级，则需要请求最后一个下一级的option
  if (values.length < 5) {
    await fetchLevelOptions(values.length + 1, values.slice(-1)[0]);
  }
};

watch(
  [() => props.defaultValue, () => props.isMultiTarget],
  async () => {
    if (props.allowOtherEdit) {
      if (props.defaultValue === arrayValue.value[4]) return;
    }
    if (props.defaultValue) {
      const res = await $getOnClient<{ riskCode1: string; riskCode2: string; riskCode3: string; riskCode4: string; riskCode5: string }>(`${gateWay}${service.farmer}/encodeDict/findUpperRiskByRiskCode`, { riskCode: props.defaultValue });
      if (res && res.code === SUCCESS_CODE) {
        const { riskCode1 = '', riskCode2 = '', riskCode3 = '', riskCode4 = '', riskCode5 = '' } = res.data;
        // 多标显示前3层
        if (props.isMultiTarget) {
          asyncInitValue([riskCode1, riskCode2, riskCode3]);
        } else {
          asyncInitValue([riskCode1, riskCode2, riskCode3, riskCode4, riskCode5]);
        }
      }
    }
  },
  {
    immediate: true,
  },
);
// 搜索逻辑
interface fiveDictOptionsType {
  value: string;
  label: string;
}
const fiveDictOptions = ref<fiveDictOptionsType[]>([]);
const searchLoading = ref(false);
const handleSearch = async (keywords: string) => {
  if (keywords) {
    searchLoading.value = true;
    const res = await $getOnClient<fiveDictOptionsType[]>('/api/common/getAllEncodeDictList', {
      departmentCode: props.departmentCode,
      keywords,
    });
    fiveDictOptions.value = res?.data || [];
    searchLoading.value = false;
  }
};
const debounceSearch = debounce(handleSearch, 500);
const handleFiveDictChange = (value: SelectValue) => {
  if (value) {
    asyncInitValue(value.toString().split('-'));
  }
};

// 监听重置操作
watchEffect(() => {
  if (!riskCode.value) {
    arrayValue.value = [undefined, undefined, undefined, undefined, undefined];
    searchValue.value = undefined;
  }
});
// 回车键赋值
// const searchOptionVal = ref<undefined | string>(undefined);
// 搜索时，按回车键默认选中第一条数据
const inputKeyDownChange = (e: Event & { code: string }) => {
  if (e.code === 'Enter') {
    const val = fiveDictOptions.value?.[0]?.value;
    if (val) {
      asyncInitValue(val.toString().split('-'));
    }
  }
};
</script>
