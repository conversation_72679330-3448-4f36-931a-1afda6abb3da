<template>
  <div class="flex gap-x-8px">
    <a-cascader v-model:value="selectedRegion" :options="cascaderOptions" placeholder="请选择行政村" allow-clear :style="{ width: '100%' }" :load-data="loadData" :change-on-select="changeOnSelect" @change="handleCascaderChange" />
  </div>
</template>

<script setup lang="ts">
import type { CascaderOptionType } from 'ant-design-vue/es/cascader';
import { ref, defineEmits } from 'vue';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { authDeptCode, initVal, changeOnSelect } = defineProps<{ authDeptCode: string[][]; initVal: string[]; changeOnSelect: boolean }>();

const { gateWay, service } = useRuntimeConfig().public || {};
// const { disabled = false } = defineProps<{ disabled?: boolean }>();
const emit = defineEmits(['changeSelected']);

const selectedRegion = ref<string[]>(['']); // 选择的地区
const cascaderOptions = ref<CascaderOptionType[]>([]); // 级联选项

// const fetchurl = gateWay + service.farmer + '/address';
// 后端要求更换新接口
const fetchurl = gateWay + service.administrate + '/public/queryOldAddress';

// 处理级联选择的变化
const handleCascaderChange = (value: string[]) => {
  emit('changeSelected', value); // 可以调整传递值的格式
};

// 将地区数据格式化为级联结构
const formatRegions = (regions: CascaderOptionType[], isLeaf: boolean) => {
  // 构造区域的层级关系，例如：[{ label: "省", value: "省", children: [...] }, ...]
  return regions.map((region) => ({
    label: region.label,
    value: region.value,
    parentCode: region.parentCode,
    isLeaf,
    loading: false,
    children: [], // 留空，待后面通过API动态填充
  }));
};

const loadData = async (selectedOptions: CascaderOptionType[]) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  const addressTypeMap = new Map([
    [1, 'B'],
    [2, 'C'],
    [3, 'D'],
    [4, 'E'],
  ]);
  const addressType = addressTypeMap.get(selectedOptions.length) || 'A';
  targetOption.loading = true;
  const res = await $getOnClient<CascaderOptionType[]>(fetchurl, {
    addressType: addressType, // 根据层级调整
    parentCode: targetOption.parentCode,
    code: targetOption.value,
  });
  targetOption.loading = false;
  if (res && res.code === SUCCESS_CODE) {
    targetOption.children = formatRegions(res.data, selectedOptions.length === 4);
  }
};

const setFiveCascaderDefaultValue = async () => {
  if (initVal && initVal[4]) {
    let firstItem = cascaderOptions.value.filter((val) => val.value === initVal[0]);
    let secondeItem = firstItem[0].children.filter((val) => val.value === initVal[1]);
    let thirdItem = secondeItem[0].children.filter((val) => val.value === initVal[2]);
    let fourItem = thirdItem[0].children.filter((val) => val.value === initVal[3]);
    await loadData([firstItem[0], secondeItem[0], thirdItem[0], fourItem[0]]);
    firstItem = cascaderOptions.value.find((val) => val.value === initVal[0]);
    secondeItem = firstItem.children?.find((val) => val.value === initVal[1]);
    thirdItem = secondeItem.children?.find((val) => val.value === initVal[2]);
    fourItem = thirdItem.children?.find((val) => val.value === initVal[3]);
    if (fourItem && fourItem.children.some((val) => val.value === initVal[4])) {
      // 第五级赋值
      selectedRegion.value.push(initVal[4]);
    }
    emit('changeSelected', selectedRegion.value);
  }
};

const setFourCascaderDefaultValue = async () => {
  if (initVal && initVal[3]) {
    let firstItem = cascaderOptions.value.filter((val) => val.value === initVal[0]);
    let secondeItem = firstItem[0].children.filter((val) => val.value === initVal[1]);
    let thirdItem = secondeItem[0].children.filter((val) => val.value === initVal[2]);
    await loadData([firstItem[0], secondeItem[0], thirdItem[0]]);
    firstItem = cascaderOptions.value.find((val) => val.value === initVal[0]);
    secondeItem = firstItem.children?.find((val) => val.value === initVal[1]);
    thirdItem = secondeItem.children?.find((val) => val.value === initVal[2]);
    if (thirdItem && thirdItem.children.some((val) => val.value === initVal[3])) {
      // 第四级赋值
      selectedRegion.value.push(initVal[3]);
      if (initVal[4] && initVal[4].length) {
        // 存在第五级
        setFiveCascaderDefaultValue();
      } else {
        emit('changeSelected', selectedRegion.value);
      }
    }
  }
};

const setThirdCascaderDefaultValue = async () => {
  if (initVal && initVal[2]) {
    const item = cascaderOptions.value.filter((val) => val.value === initVal[0]);
    const itemChildren = item[0].children.filter((val) => val.value === initVal[1]);
    await loadData([item[0], itemChildren[0]]);
    const index = cascaderOptions.value.findIndex((val) => val.value === initVal[0]);
    const secondItem = cascaderOptions.value[index].children?.find((val) => val.value === initVal[1]);
    if (secondItem && secondItem.children.some((val) => val.value === initVal[2])) {
      // 第三级赋值
      selectedRegion.value[2] = initVal[2];
      if (initVal[3] && initVal[3].length) {
        // 存在第四级
        setFourCascaderDefaultValue();
      } else {
        emit('changeSelected', selectedRegion.value);
      }
    }
  }
};

const setSecondCascaderDefaultValue = async () => {
  if (initVal && initVal[1]) {
    const item = cascaderOptions.value.filter((val) => val.value === initVal[0]);
    await loadData(item);
    const index = cascaderOptions.value.findIndex((val) => val.value === initVal[0]);
    if (cascaderOptions.value[index].children && cascaderOptions.value[index].children.some((val) => val.value === initVal[1])) {
      // 第二级赋值
      selectedRegion.value[1] = initVal[1];
      if (initVal[2] && initVal[2].length) {
        // 存在第三级
        setThirdCascaderDefaultValue();
      } else {
        emit('changeSelected', selectedRegion.value);
      }
    }
  }
};

const setFirstCascaderDefaultValue = () => {
  if (initVal && initVal[0]) {
    if (authDeptCode && authDeptCode[0] && authDeptCode[0].length && cascaderOptions.value && cascaderOptions.value.length) {
      // 存在权限管控
      // for (const item of cascaderOptions.value) {
      //   if (!authDeptCode[0].includes(item.value)) {
      //     item.disabled = true;
      //   }
      // }
      const provinceList = authDeptCode.map((list) => list[0]);
      cascaderOptions.value = cascaderOptions.value.filter((val) => provinceList.includes(val.value));
    }
    if (cascaderOptions.value.some((val) => val.value === initVal[0] && !val.disabled)) {
      selectedRegion.value = [initVal[0]];
      if (initVal[1] && initVal[1].length) {
        // 存在第二级
        setSecondCascaderDefaultValue();
      } else {
        emit('changeSelected', selectedRegion.value);
      }
    } else {
      selectedRegion.value = [];
    }
  }
};

// 获取所有地区的函数
const initRegions = async () => {
  const res = await $getOnClient<CascaderOptionType[]>(fetchurl, {
    addressType: 'A',
    // parentCode: '',
    code: '100',
  });

  if (res && res.code === SUCCESS_CODE) {
    cascaderOptions.value = formatRegions(res.data, false);
    if (initVal && initVal.length) {
      // 设置默认值
      setFirstCascaderDefaultValue();
    }
  }
};

// 初始化省
initRegions();

// 地区初始值
watch(
  () => initVal,
  async (val) => {
    if (val && val.length) {
      if (cascaderOptions.value.length) {
        // 下拉数据已经加载回来了
        await nextTick();
        setFirstCascaderDefaultValue();
      }
    }
  },
  {
    immediate: true,
  },
);

// 设置权限的地区，在此地区之上不可选
// [[xxx], [xxx,xxx]]代表只有一个省可访问，2个市可访问
watch(
  () => authDeptCode,
  async (val) => {
    if (val && val.length) {
      await nextTick();
      setFirstCascaderDefaultValue();
    }
  },
  {
    immediate: true,
  },
);

const clear = () => {
  selectedRegion.value = [];
};

defineExpose({ clear });
</script>
