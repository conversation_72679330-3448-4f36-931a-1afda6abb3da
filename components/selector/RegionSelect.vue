<template>
  <div class="flex gap-x-8px">
    <a-select v-if="regionLevel >= 1" v-model:value="province" :disabled="disabled" placeholder="请选择省" show-search :options="provinceOptions" allow-clear :style="{ width: '100%' }" :filter-option="filterOption" @change="(value, option) => handleChange(value, option, 'province')" />
    <a-select v-if="regionLevel >= 2" v-model:value="city" :disabled="disabled" placeholder="请选择市" show-search :style="{ width: '100%' }" :options="cityOptions" allow-clear :filter-option="filterOption" @change="(value, option) => handleChange(value, option, 'city')" />
    <a-select v-if="regionLevel >= 3" v-model:value="county" :disabled="disabled" show-search placeholder="请选择区/县" :options="countyOptions" allow-clear :style="{ width: '100%' }" :filter-option="filterOption" @change="(value, option) => handleChange(value, option, 'county')" />
    <a-select v-if="regionLevel >= 4" v-model:value="town" :disabled="disabled" show-search placeholder="请选择乡/镇" :options="townOptions" allow-clear :style="{ width: '100%' }" :filter-option="filterOption" @change="(value, option) => handleChange(value, option, 'town')" />
    <a-select v-if="regionLevel >= 5" v-model:value="village" :disabled="disabled" show-search placeholder="请选择村" :options="villageOptions" allow-clear :style="{ width: '100%' }" :filter-option="filterOption" @change="(value, option) => handleChange(value, option, 'village')" />
  </div>
</template>

<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select';
import type { ModelRef } from 'vue';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};

interface RegionInfo {
  label: string;
  value: string;
  parentCode: string;
}

const { regionLevel = 5, disabled = false, initChange = false } = defineProps<{ regionLevel?: number; disabled?: boolean; initChange?: boolean }>();

const emit = defineEmits(['changeSelected']);

/**
 * @description: '省/市/区/乡/村选择器'
 * 两种使用方法
 * 1.动态绑定五个值，只能获取code
 * 2.通过emit事件传递选中的值，可以获取整个选中信息
 *
 */
const province = defineModel<string>('province'); // 省
const city = defineModel<string>('city'); // 市
const county = defineModel<string>('county'); // 县/区
const town = defineModel<string>('town'); // 乡/镇
const village = defineModel<string>('village'); // 村

const provinceOptions = ref<RegionInfo[]>([]);
const cityOptions = ref<RegionInfo[]>([]);
const countyOptions = ref<RegionInfo[]>([]);
const townOptions = ref<RegionInfo[]>([]);
const villageOptions = ref<RegionInfo[]>([]);

const filterOption = (input: string, option: RegionInfo) => {
  if (input && option) {
    return option.label.toLowerCase().includes(input.toLowerCase());
  } else {
    return false;
  }
};

const handleChange = (value: SelectValue, selected: unknown, type: string) => {
  if (type === 'province') {
    clearRegion(city, cityOptions, 'city');
    clearRegion(county, countyOptions, 'county');
    clearRegion(town, townOptions, 'town');
    clearRegion(village, villageOptions, 'village');
  } else if (type === 'city') {
    clearRegion(county, countyOptions, 'county');
    clearRegion(town, townOptions, 'town');
    clearRegion(village, villageOptions, 'village');
  } else if (type === 'county') {
    clearRegion(town, townOptions, 'town');
    clearRegion(village, villageOptions, 'village');
  } else if (type === 'town') {
    clearRegion(village, villageOptions, 'village');
  }
  emit('changeSelected', selected || {}, type);
};

// const fetchurl = gateWay + service.farmer + '/address';
// 后端要求换新接口
const fetchurl = gateWay + service.administrate + '/public/queryOldAddress';

// 清空某个地区级别的函数
const clearRegion = (model: ModelRef<string | undefined, string, string | undefined, string | undefined>, options: Ref<RegionInfo[]>, type: string) => {
  model.value = undefined;
  options.value = [];
  emit('changeSelected', {}, type);
};

// 重置所有选项的函数
const resetRegion = () => {
  clearRegion(province, provinceOptions, 'province');
  clearRegion(city, cityOptions, 'city');
  clearRegion(county, countyOptions, 'county');
  clearRegion(town, townOptions, 'town');
  clearRegion(village, villageOptions, 'village');
};

watch(province, () => {
  const selected = provinceOptions.value.find((k) => k.value === province.value);
  if (initChange) {
    emit('changeSelected', selected || {}, 'province');
  }
});

watch(
  province,
  (value) => {
    if (value) {
      $getOnClient(fetchurl, {
        addressType: 'B',
        // parentCode: '01',
        code: value,
      }).then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          cityOptions.value = res.data;
          const selected = cityOptions.value.find((k) => k.value === city.value);
          if (!selected) {
            city.value = undefined;
          }
        }
      });
    }
  },
  { immediate: true },
);

watch([cityOptions, city], () => {
  const selected = cityOptions.value.find((k) => k.value === city.value);
  if (initChange) {
    emit('changeSelected', selected || {}, 'city');
  }
});

watch(
  city,
  (value) => {
    if (value) {
      $getOnClient(fetchurl, {
        addressType: 'C',
        // parentCode: province.value,
        code: value,
      }).then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          countyOptions.value = res.data;
          const selected = countyOptions.value.find((k) => k.value === county.value);
          if (!selected) {
            county.value = undefined;
          }
        }
      });
    }
  },
  { immediate: true },
);

watch([countyOptions, county], () => {
  const selected = countyOptions.value.find((k) => k.value === county.value);
  if (initChange) {
    emit('changeSelected', selected || {}, 'county');
  }
});

watch(
  county,
  (value) => {
    if (value) {
      $getOnClient(fetchurl, {
        addressType: 'D',
        // parentCode: city.value,
        code: value,
      }).then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          townOptions.value = res.data;
          const selected = townOptions.value.find((k) => k.value === town.value);
          if (!selected) {
            town.value = undefined;
          }
        }
      });
    }
  },
  { immediate: true },
);

watch([townOptions, town], () => {
  const selected = townOptions.value.find((k) => k.value === town.value);
  if (initChange) {
    emit('changeSelected', selected || {}, 'town');
  }
});

watch(
  town,
  (value) => {
    if (value) {
      $getOnClient(fetchurl, {
        addressType: 'E',
        // parentCode: county.value,
        code: value,
      }).then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          villageOptions.value = res.data;
          const selected = villageOptions.value.find((k) => k.value === village.value);
          if (!selected) {
            village.value = undefined;
          }
        }
      });
    }
  },
  { immediate: true },
);

watch([villageOptions, village], () => {
  const selected = villageOptions.value.find((k) => k.value === village.value);
  if (initChange) {
    emit('changeSelected', selected || {}, 'village');
  }
});

// 完整的地址
onMounted(async () => {
  const res = await $getOnClient(fetchurl, {
    addressType: 'A',
    // parentCode: '',
    code: '100',
  });
  if (res && res.code === SUCCESS_CODE) {
    provinceOptions.value = res.data;
    const selected = provinceOptions.value.find((k) => k.value === province.value);
    if (initChange) {
      emit('changeSelected', selected || {}, 'province');
    }
    if (!selected) {
      province.value = undefined;
    }
  }
});

defineExpose({
  resetRegion,
});
</script>
