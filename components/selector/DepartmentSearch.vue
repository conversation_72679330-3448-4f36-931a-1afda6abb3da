<template>
  <a-input-group>
    <a-row :gutter="8" align="middle">
      <a-col :span="showChildDepart ? 6 : 8">
        <a-input v-model:value="inputValue" :disabled="departmentDisabled" placeholder="请输入" type="number" @blur="handleBlur" />
      </a-col>
      <a-col :span="showChildDepart ? 10 : 16">
        <a-select v-model:value="selectValue" placeholder="请选择" show-search allow-clear :disabled="departmentDisabled || disabledSelect" :options="selectOptions" :style="{ width: '100%' }" :loading="loading" option-filter-prop="label" @change="handleSelectChange" />
      </a-col>
      <a-col v-show="props.showChildDepart" :span="8">
        <a-checkbox v-model:checked="containChildDepart">包含下级</a-checkbox>
      </a-col>
    </a-row>
  </a-input-group>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { debounce } from 'lodash-es';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import { useUserStore } from '@/stores/useUserStore';
import { $get } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const props = withDefaults(
  defineProps<{
    deptCode: string;
    disabled?: boolean;
    disabledSelect?: boolean; // 单独禁用下拉框
    showChildDepart?: boolean;
    unauthorized?: boolean; // 是否需要跨权限查机构
  }>(),
  {
    deptCode: '',
    disabled: false,
    disabledSelect: false,
    showChildDepart: false,
    unauthorized: false,
  },
);
const emit = defineEmits(['changeDeptCode']);
const { userInfo } = useUserStore();
// 是否包含下级机构
const containChildDepart = defineModel<boolean>('containChildDepart', {
  default: false,
});
const loading = defineModel<boolean>('loading', {
  default: false,
});

const inputValue = ref('');
const selectValue = ref<string | number>('');
const selectOptions = ref<DefaultOptionType[]>([]);
// const loading = ref(false);
// 判断输入框是否被禁用
const departmentDisabled = computed(() => props.disabled ?? false);

// 查询机构列表
const fetchDepartmentList = async (params: { deptCode: string; isAllChild: boolean }) => {
  loading.value = true;
  try {
    const apiUrl = props?.unauthorized ? '/api/common/getChildDeptList' : '/api/common/getDeptList';
    const newParams = {
      ...params,
      isAllChild: props?.unauthorized ? true : params.isAllChild,
    };
    const { data, code, msg } = (await $get(apiUrl, newParams)) || {};
    if (code === SUCCESS_CODE) {
      if (Array.isArray(data) && data.length > 0) {
        selectOptions.value = data;
        selectValue.value = selectOptions.value[0]?.value ?? '';
        emit('changeDeptCode', selectValue.value, selectOptions.value[0]?.departmentLevel);
      } else {
        clearDepartmentOptionsAndSelectValue();
      }
    } else {
      inputValue.value = '';
      clearDepartmentOptionsAndSelectValue();
      message.error(msg || '');
    }
  } catch (error) {
    clearDepartmentOptionsAndSelectValue();
    console.error('Failed to fetch department list', error);
  }
  loading.value = false;
};

// 防抖查询机构下拉列表
const debounceFetchDepartmentList = debounce(fetchDepartmentList, 1000);

// 重置机构列表和选中值
const clearDepartmentOptionsAndSelectValue = () => {
  selectOptions.value = [];
  selectValue.value = '';
};

// 判断是否需要查询机构列表
const shouldFetchDepartmentList = (inputCode: string, deptList: string[] | undefined) => {
  const isHeadquarters = inputCode === '2' && deptList?.includes('2'); // 用户有总部权限，输入为2
  return isHeadquarters || (inputCode !== '2' && inputCode?.length >= 3);
};

// 监听输入框变动时，查询机构列表
// watch(inputValue, (val) => {
//   if (shouldFetchDepartmentList(val, userInfo.deptList)) {
//     debounceFetchDepartmentList({ deptCode: val, isAllChild: containChildDepart.value });
//   } else {
//     clearDepartmentOptionsAndSelectValue();
//   }
//   // inputValue为空 清空deptCode
//   if (!inputValue.value) {
//     emit('changeDeptCode', '');
//   }
// });

const handleBlur = (e) => {
  const val = e.target.value;
  if (shouldFetchDepartmentList(val, userInfo.deptList)) {
    fetchDepartmentList({ deptCode: val, isAllChild: containChildDepart.value });
  } else {
    clearDepartmentOptionsAndSelectValue();
  }
  // inputValue为空 清空deptCode
  if (!inputValue.value) {
    emit('changeDeptCode', '');
  }
};

const handleSelectChange = (val: SelectValue, option: DefaultOptionType) => {
  if (option) {
    emit('changeDeptCode', val, option.departmentLevel);
  } else {
    emit('changeDeptCode', val);
  }
};

// 监听入参deptCode变动
watch(
  () => props.deptCode,
  (val) => {
    if (inputValue.value !== val) {
      inputValue.value = props.deptCode || '';
      if (shouldFetchDepartmentList(props.deptCode, userInfo.deptList)) {
        debounceFetchDepartmentList({ deptCode: props.deptCode, isAllChild: containChildDepart.value });
      } else {
        clearDepartmentOptionsAndSelectValue();
      }
      // inputValue为空 清空deptCode
      if (!inputValue.value) {
        emit('changeDeptCode', '');
      }
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}
</style>
