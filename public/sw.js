const CACHE_NAME = 'app-cache-v1';
const API_URL = '/api/manifest/buildAssets';
const PROGRESS_CACHE_NAME = 'cache-progress-v1'; // 存储未上传文件

// 定义需要缓存的静态资源后缀
const CACHEABLE_EXTENSIONS = ['mjs', 'js', 'css', 'png', 'jpg', 'jpeg', 'json', 'svg', 'ico', 'woff', 'woff2', 'ttf', 'eot', 'otf', 'pbf'];

// 进度管理
const progress = {
  async saveProgress(files) {
    const cache = await caches.open(PROGRESS_CACHE_NAME);
    await cache.put(
      new Request('/_progress'),
      new Response(JSON.stringify({
        files,
        timestamp: Date.now()
      }))
    );
  },

  async getProgress() {
    try {
      const cache = await caches.open(PROGRESS_CACHE_NAME);
      const response = await cache.match(new Request('/_progress'));
      if (response) {
        const data = await response.json();
        return data.files || [];
      }
    } catch (error) {
      console.error('Error getting progress:', error);
    }
    return [];
  },

  async clearProgress() {
    const cache = await caches.open(PROGRESS_CACHE_NAME);
    await cache.delete(new Request('/_progress'));
  }
};

let isCaching = false;

let filesObj = {cssFiles: [], jsFiles: []};

// 发送消息到所有客户端
async function sendMessageToClients(message) {
  const clients = await self.clients.matchAll();
  clients.forEach(client => {
    client.postMessage(message);
  });
}

// 获取构建资源列表
async function fetchBuildAssets() {
  try {
    const response = await fetch(API_URL);
    if (!response.ok) throw new Error('Network response was not ok');
    const data = await response.json();
    filesObj = {
      cssFiles: data.cssFiles || [],
      jsFiles: data.jsFiles || []
    }
    return filesObj;
  } catch (error) {
    console.error('Error fetching build assets:', error);
    return { cssFiles: [], jsFiles: [] };
  }
}

async function getFilesObj() {
  return new Promise((resove) => {
    // for (let i = 0; i < 1000; i++) {
    //   filesObj.cssFiles.push(`${i}.css`);
    //   filesObj.jsFiles.push(`${i}.js`);
    // }
    resove(filesObj);
  }) 
}

const requestIdleCallbackCopy = async () => {
  return new Promise(resove => {
    setTimeout(() => {
      resove({});
    }, 1000);
  })
}

// 缓存文件
async function cacheFiles(files) {
  if (isCaching) return;
  isCaching = true;
  
  const cache = await caches.open(CACHE_NAME);
  const baseUrl = self.location.origin;
  
  // 串行处理每个文件
  while (files.length > 0) {
    try {
      const file = files[0];
      const url = new URL('/_nuxt/' + file, baseUrl).href;
      console.log('Caching file:', url);
      
      await requestIdleCallbackCopy();
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch ${url}`);
      }
      
      await cache.put(url, response);
      console.log('Successfully cached:', url);
      
      // 从队列中移除已处理的文件并保存进度
      files.shift();
      await progress.saveProgress(files);
      
    } catch (error) {
      console.error(`Failed to cache file:`, error);
      // 如果文件处理失败，也从队列中移除
      files.shift();
      await progress.saveProgress(files);
    }
  }
  
  // 所有文件处理完成后，清除进度记录
  await progress.clearProgress();
  isCaching = false;
}


// 清理旧缓存
async function cleanupCache(validFiles) {
  const cache = await caches.open(CACHE_NAME);
  const cachedRequests = await cache.keys();
  const validUrls = new Set(validFiles.map(file => new URL(file, self.location.origin).href));

  const deletePromises = cachedRequests.map(async request => {
    const url = request.url;
    if (!validUrls.has(url)) {
      await cache.delete(request);
      console.log('Deleted from cache:', url);
    }
  });

  await Promise.all(deletePromises);
}

// Service Worker 安装事件
self.addEventListener('install', event => {
  console.log('New ServiceWorker installing...');
  // 立即激活新的 Service Worker
  self.skipWaiting();
  
  event.waitUntil(
    fetchBuildAssets()
      .then(({ cssFiles, jsFiles }) => {
        const allFiles = [...cssFiles, ...jsFiles];
        return cleanupCache(allFiles);
      })
      .then(() => {
        console.log('New ServiceWorker installed successfully');
        // 通知客户端新的 Service Worker 已安装
        return sendMessageToClients({ type: 'NEW_SW_INSTALLED' });
      })
  );
});

// Service Worker 激活事件
self.addEventListener('activate', event => {
  console.log('ServiceWorker activating 的订单...');
  
  // 立即接管所有页面
  event.waitUntil(
    Promise.all([
      self.clients.claim(),
      getFilesObj()
        .then(({ cssFiles, jsFiles }) => {
          const allFiles = [...jsFiles, ...cssFiles];
          cacheFiles(allFiles);
          return true;
        })
        .then(() => {
          console.log('ServiceWorker activated and cache cleaned');
          return sendMessageToClients({ type: 'NEW_SW_ACTIVATED' });
        })
    ])
  );
});

// 拦截请求事件
self.addEventListener('fetch', event => {
  // 只处理 GET 请求
  if (event.request.method !== 'GET') return;
  const url = new URL(event.request.url);
  const extension = url.pathname.split('.').pop().toLowerCase();
  console.log('-----fetch listen-----------', url, extension, isCaching);

  // 检查并继续未完成的缓存任务
  if (!isCaching) {
    progress.getProgress().then(remainingFiles => {
      if (remainingFiles.length > 0) {
        console.log('Resuming cache process with remaining files:', remainingFiles.length);
        cacheFiles(remainingFiles);
      }
    });
  }

  // 只缓存指定后缀的静态资源
  if (CACHEABLE_EXTENSIONS.includes(extension)) {
    event.respondWith(
      caches.match(event.request)
        .then(cachedResponse => {
          if (cachedResponse) {
            // 返回缓存的响应
            return cachedResponse;
          }
  
          // 如果缓存中没有，则从网络获取
          return fetch(event.request)
            .then(response => {
              // 检查是否是有效的响应
              if (!response || response.status !== 200 ) {
                return response;
              }
  
              // 克隆响应，因为响应流只能被使用一次
              const responseToCache = response.clone();
  
              // 将新的响应添加到缓存
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });
  
              return response;
            });
        })
    );
  }
});

// 定期更新缓存（每小时检查一次）
// setInterval(() => {
//   fetchBuildAssets()
//     .then(({ cssFiles, jsFiles }) => {
//       const allFiles = [...cssFiles, ...jsFiles];
//       return Promise.all([
//         cacheFiles(allFiles),
//         cleanupCache(allFiles)
//       ]);
//     })
//     .catch(error => {
//       console.error('Error updating cache:', error);
//     });
// }, 60 * 60 * 1000); // 1小时