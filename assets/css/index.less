@import url('./ant-reset.less');

:root {
  font-family: MicrosoftYaHei, '-apple-system', BlinkMacSystemFont;
  font-size: 14px;
  font-weight: 400;
}

// 滚动条样式覆盖
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: rgba(50, 50, 50, 0.1);
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  background-color: rgba(50, 50, 50, 0.3);
}

body {
  padding: 0;
  margin: 0;
  font-size: 14px;
}

// .txdicon {
//   font-size: 14px;
// }

fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}

.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  color: #333;

  &::before {
    position: absolute;
    left: 0;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}

:focus {
  outline: none;
}

@font-face {
  font-family: 'D-DIN-PRO-Regular';
  src: url('./DIN-Regular.otf');
}

@font-face {
  font-family: 'D-DIN-PRO-Medium';
  src: url('./DIN-Medium.otf');
}
// 处理表格换行省略号
.table-ellipsis-multiline {
  display: -webkit-box; /* 必须结合WebKit浏览器 */
  -webkit-line-clamp: 2; /* 限制显示的行数 */
  -webkit-box-orient: vertical; /* 垂直排列盒子 */
  overflow: hidden; /* 隐藏溢出的内容 */
}
// 机构筛选器失焦时的loading蒙层
.ant-spin-nested-loading.search-spin-wrapper-css {
  .ant-spin-blur {
    opacity: 0.8;
  }
}
